# coding=utf-8
"""
因子有效性分析器
分析收集到的因子数据，找出最有效提升胜率的因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class FactorEffectivenessAnalyzer:
    """因子有效性分析器"""
    
    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        
    def analyze_factor_effectiveness(self):
        """分析因子有效性"""
        print('🔍 因子有效性分析器')
        print('=' * 80)
        
        try:
            # 1. 加载数据
            data = self._load_trade_data()
            if data is None or len(data) == 0:
                print('❌ 没有找到交易数据')
                return None
            
            print(f'📊 加载交易数据: {len(data)} 条记录')
            
            # 2. 数据预处理
            processed_data = self._preprocess_data(data)
            if processed_data is None or len(processed_data) == 0:
                print('❌ 数据预处理失败')
                return None
            
            print(f'✅ 数据预处理完成: {len(processed_data)} 条有效记录')
            
            # 3. 分析因子有效性
            factor_analysis = self._analyze_factors(processed_data)
            
            # 4. 生成报告
            self._generate_report(factor_analysis, processed_data)
            
            # 5. 生成优化建议
            optimization_suggestions = self._generate_optimization_suggestions(factor_analysis)
            
            return {
                'factor_analysis': factor_analysis,
                'optimization_suggestions': optimization_suggestions,
                'data_summary': {
                    'total_records': len(data),
                    'valid_records': len(processed_data),
                    'win_rate': processed_data['is_win'].mean() if 'is_win' in processed_data.columns else 0
                }
            }
            
        except Exception as e:
            print(f'❌ 分析过程异常: {e}')
            import traceback
            traceback.print_exc()
            return None
    
    def _load_trade_data(self):
        """加载交易数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询所有买入记录
            query = '''
            SELECT * FROM trades 
            WHERE action = 'BUY' 
            ORDER BY timestamp DESC
            '''
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            return data
            
        except Exception as e:
            print(f'❌ 加载数据失败: {e}')
            return None
    
    def _preprocess_data(self, data):
        """数据预处理"""
        try:
            # 过滤有效数据
            valid_data = data.dropna(subset=['price', 'is_win'])
            
            # 确保数值类型
            numeric_columns = []
            for col in valid_data.columns:
                if col not in ['timestamp', 'symbol', 'action', 'status']:
                    try:
                        valid_data[col] = pd.to_numeric(valid_data[col], errors='coerce')
                        numeric_columns.append(col)
                    except:
                        pass
            
            # 移除全为空值的列
            valid_data = valid_data.dropna(axis=1, how='all')
            
            return valid_data
            
        except Exception as e:
            print(f'❌ 数据预处理失败: {e}')
            return None
    
    def _analyze_factors(self, data):
        """分析因子有效性"""
        try:
            factor_results = {}
            
            # 获取所有数值型因子列
            factor_columns = []
            for col in data.columns:
                if col not in ['timestamp', 'symbol', 'action', 'status', 'is_win', 'profit_pct'] and data[col].dtype in ['float64', 'int64']:
                    factor_columns.append(col)
            
            print(f'🔍 分析 {len(factor_columns)} 个因子...')
            
            for factor in factor_columns:
                try:
                    factor_analysis = self._analyze_single_factor(data, factor)
                    if factor_analysis:
                        factor_results[factor] = factor_analysis
                except Exception as e:
                    print(f'⚠️ 分析因子 {factor} 失败: {e}')
            
            # 按有效性排序
            sorted_factors = sorted(factor_results.items(), 
                                  key=lambda x: x[1]['effectiveness_score'], 
                                  reverse=True)
            
            return dict(sorted_factors)
            
        except Exception as e:
            print(f'❌ 因子分析失败: {e}')
            return {}
    
    def _analyze_single_factor(self, data, factor_name):
        """分析单个因子"""
        try:
            factor_data = data[factor_name].dropna()
            win_data = data['is_win'].dropna()
            
            if len(factor_data) < 10:  # 数据太少
                return None
            
            # 计算分位数
            q25 = factor_data.quantile(0.25)
            q50 = factor_data.quantile(0.50)
            q75 = factor_data.quantile(0.75)
            
            # 分组分析
            low_group = data[data[factor_name] <= q25]['is_win']
            mid_group = data[(data[factor_name] > q25) & (data[factor_name] <= q75)]['is_win']
            high_group = data[data[factor_name] > q75]['is_win']
            
            # 计算各组胜率
            low_win_rate = low_group.mean() if len(low_group) > 0 else 0
            mid_win_rate = mid_group.mean() if len(mid_group) > 0 else 0
            high_win_rate = high_group.mean() if len(high_group) > 0 else 0
            
            # 计算相关性
            correlation = factor_data.corr(win_data) if len(factor_data) == len(win_data) else 0
            
            # 计算有效性评分
            win_rate_spread = max(high_win_rate, low_win_rate) - min(high_win_rate, low_win_rate)
            effectiveness_score = abs(correlation) * 50 + win_rate_spread * 50
            
            return {
                'correlation': correlation,
                'low_win_rate': low_win_rate,
                'mid_win_rate': mid_win_rate,
                'high_win_rate': high_win_rate,
                'win_rate_spread': win_rate_spread,
                'effectiveness_score': effectiveness_score,
                'sample_count': len(factor_data),
                'q25': q25,
                'q50': q50,
                'q75': q75
            }
            
        except Exception as e:
            return None
    
    def _generate_report(self, factor_analysis, data):
        """生成分析报告"""
        try:
            print('\n📋 因子有效性分析报告')
            print('=' * 60)
            
            # 总体统计
            total_trades = len(data)
            overall_win_rate = data['is_win'].mean()
            
            print(f'📊 总体统计:')
            print(f'  总交易数: {total_trades}')
            print(f'  整体胜率: {overall_win_rate:.2%}')
            
            # Top 10 最有效因子
            print(f'\n🏆 Top 10 最有效因子:')
            print('-' * 60)
            
            top_factors = list(factor_analysis.items())[:10]
            
            for i, (factor_name, analysis) in enumerate(top_factors, 1):
                print(f'{i:2d}. {factor_name}')
                print(f'    有效性评分: {analysis["effectiveness_score"]:.2f}')
                print(f'    相关性: {analysis["correlation"]:.3f}')
                print(f'    胜率差异: {analysis["win_rate_spread"]:.2%}')
                print(f'    高值组胜率: {analysis["high_win_rate"]:.2%}')
                print(f'    低值组胜率: {analysis["low_win_rate"]:.2%}')
                print()
            
            # 按类别分析
            self._analyze_by_category(factor_analysis)
            
        except Exception as e:
            print(f'❌ 生成报告失败: {e}')
    
    def _analyze_by_category(self, factor_analysis):
        """按类别分析因子"""
        try:
            print('\n📊 按类别分析:')
            print('-' * 40)
            
            categories = {
                '市场环境': ['market_', 'advance_', 'northbound_'],
                '基本面': ['pe_', 'pb_', 'roe_', 'eps_', 'debt_'],
                '技术面': ['ma_', 'rsi', 'macd', 'kdj_', 'trend_', 'adx_'],
                '资金流向': ['main_force', 'institutional_', 'large_order'],
                '风险': ['beta_', 'volatility', 'var_', 'risk_'],
                '另类数据': ['google_', 'news_', 'esg_', 'sentiment'],
                '行为金融': ['herding_', 'fear_', 'investor_'],
                '机器学习': ['hurst_', 'entropy_', 'regime_']
            }
            
            for category, keywords in categories.items():
                category_factors = []
                for factor_name, analysis in factor_analysis.items():
                    if any(keyword in factor_name.lower() for keyword in keywords):
                        category_factors.append((factor_name, analysis))
                
                if category_factors:
                    avg_effectiveness = np.mean([analysis['effectiveness_score'] for _, analysis in category_factors])
                    best_factor = max(category_factors, key=lambda x: x[1]['effectiveness_score'])
                    
                    print(f'{category}:')
                    print(f'  因子数量: {len(category_factors)}')
                    print(f'  平均有效性: {avg_effectiveness:.2f}')
                    print(f'  最佳因子: {best_factor[0]} (评分: {best_factor[1]["effectiveness_score"]:.2f})')
                    print()
            
        except Exception as e:
            print(f'❌ 类别分析失败: {e}')
    
    def _generate_optimization_suggestions(self, factor_analysis):
        """生成优化建议"""
        try:
            print('\n💡 优化建议:')
            print('=' * 40)
            
            # 选择最有效的因子
            top_factors = list(factor_analysis.items())[:20]
            
            suggestions = {
                'recommended_factors': [],
                'threshold_suggestions': {},
                'strategy_config': {}
            }
            
            print('🎯 推荐使用的因子:')
            
            for i, (factor_name, analysis) in enumerate(top_factors, 1):
                if analysis['effectiveness_score'] > 10:  # 有效性阈值
                    suggestions['recommended_factors'].append(factor_name)
                    
                    # 建议阈值
                    if analysis['high_win_rate'] > analysis['low_win_rate']:
                        # 高值更好
                        suggested_threshold = analysis['q75']
                        direction = '>'
                    else:
                        # 低值更好
                        suggested_threshold = analysis['q25']
                        direction = '<'
                    
                    suggestions['threshold_suggestions'][factor_name] = {
                        'threshold': suggested_threshold,
                        'direction': direction,
                        'expected_win_rate': max(analysis['high_win_rate'], analysis['low_win_rate'])
                    }
                    
                    print(f'{i:2d}. {factor_name}')
                    print(f'    建议阈值: {direction} {suggested_threshold:.3f}')
                    print(f'    预期胜率: {max(analysis["high_win_rate"], analysis["low_win_rate"]):.2%}')
            
            # 生成策略配置代码
            self._generate_strategy_config(suggestions)
            
            return suggestions
            
        except Exception as e:
            print(f'❌ 生成优化建议失败: {e}')
            return {}
    
    def _generate_strategy_config(self, suggestions):
        """生成策略配置代码"""
        try:
            print('\n🔧 生成策略配置:')
            print('-' * 40)
            
            config_code = "# 基于因子分析的优化配置\n"
            config_code += "OPTIMIZED_BUY_THRESHOLDS = {\n"
            
            for factor_name, threshold_info in suggestions['threshold_suggestions'].items():
                config_code += f"    '{factor_name}': {threshold_info['threshold']:.3f},  # 预期胜率: {threshold_info['expected_win_rate']:.2%}\n"
            
            config_code += "}\n"
            
            # 保存配置文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            config_filename = f'优化配置_{timestamp}.py'
            
            with open(config_filename, 'w', encoding='utf-8') as f:
                f.write(config_code)
            
            print(f'✅ 配置文件已保存: {config_filename}')
            print('\n📋 配置预览:')
            print(config_code)
            
        except Exception as e:
            print(f'❌ 生成配置失败: {e}')

def main():
    """主函数"""
    print('🔍 因子有效性分析器')
    print('=' * 80)
    
    analyzer = FactorEffectivenessAnalyzer()
    results = analyzer.analyze_factor_effectiveness()
    
    if results:
        print('\n🎉 分析完成!')
        print(f'📊 分析了 {len(results["factor_analysis"])} 个因子')
        print(f'🎯 推荐使用 {len(results["optimization_suggestions"]["recommended_factors"])} 个有效因子')
        print(f'📈 当前整体胜率: {results["data_summary"]["win_rate"]:.2%}')
        
        print('\n💡 下一步建议:')
        print('1. 查看生成的优化配置文件')
        print('2. 将推荐的因子阈值应用到策略中')
        print('3. 运行回测验证优化效果')
        print('4. 根据结果进一步调整参数')
    else:
        print('❌ 分析失败，请检查数据和配置')

if __name__ == "__main__":
    main()
