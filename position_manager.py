#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
持仓管理器 - 负责维护持仓的买入卖出记录
解决实盘/模拟盘环境下无法通过API获取持仓买入日期的问题
"""
import os
import sqlite3
import datetime
import pandas as pd
import logging
from typing import Dict, List, Optional, Union, Any

class PositionManager:
    """持仓管理器 - 基于SQLite的持仓记录持久化系统"""
    
    def __init__(self, context, db_path='data/positions.db', csv_path='data/positions.csv'):
        """初始化持仓管理器
        
        Args:
            context: 策略上下文
            db_path: SQLite数据库路径
            csv_path: CSV文件路径，用于导入持仓信息
        """
        self.context = context
        self.db_path = db_path
        self.csv_path = csv_path
        self.logger = context.log if hasattr(context, 'log') else logging.getLogger('position_manager')
        self._ensure_db_exists()
        
        # 策略启动时执行一次CSV导入
        if os.path.exists(self.csv_path):
            self.import_from_csv()
        
    def _ensure_db_exists(self):
        """确保数据库和表结构存在"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建持仓记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            buy_date TEXT NOT NULL,
            buy_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            cost REAL NOT NULL,
            highest_price REAL,
            last_update TEXT,
            is_open INTEGER DEFAULT 1,
            sell_date TEXT,
            sell_price REAL,
            profit REAL,
            notes TEXT
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol ON positions(symbol)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_is_open ON positions(is_open)')
        
        conn.commit()
        conn.close()
    
    def _convert_simple_code_to_symbol(self, code):
        """将简化的股票代码转换为带交易所前缀的完整代码
        
        Args:
            code: 简化的股票代码（如600000）
            
        Returns:
            带交易所前缀的完整代码（如SHSE.600000）
        """
        try:
            code = str(code).strip()
            
            # 判断股票代码类型并添加前缀
            if code.startswith('6'):
                return f"SHSE.{code}"  # 上海证券交易所
            elif code.startswith(('0', '3')):
                return f"SZSE.{code}"  # 深圳证券交易所
            else:
                # 如果无法判断，默认返回上海
                self.logger.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法确定股票{code}的交易所，默认使用上海交易所")
                return f"SHSE.{code}"
        except Exception as e:
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 转换股票代码异常: {str(e)}")
            return f"SHSE.{code}"  # 出错时默认返回上海
    
    def import_from_csv(self):
        """从CSV文件导入持仓信息到数据库"""
        try:
            if not os.path.exists(self.csv_path):
                self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV文件不存在: {self.csv_path}")
                return False
                
            self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始从CSV导入持仓信息")
            
            # 读取CSV文件
            df = pd.read_csv(self.csv_path)
            if df.empty:
                self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV文件为空，无需导入")
                return True
                
            # 检查必要的列是否存在
            required_columns = ['code', 'buy_date', 'buy_price', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV文件缺少必要的列: {col}")
                    return False
            
            # 导入计数
            import_count = 0
            update_count = 0
            
            # 处理每一行
            for _, row in df.iterrows():
                try:
                    # 获取简化的股票代码并转换为完整代码
                    simple_code = str(row['code']).strip()
                    symbol = self._convert_simple_code_to_symbol(simple_code)
                    
                    # 获取其他信息
                    buy_date = row['buy_date']
                    buy_price = float(row['buy_price'])
                    volume = int(row['volume'])
                    
                    # 检查数据库中是否已存在该持仓
                    existing = self.get_position_by_symbol(symbol)
                    
                    if existing:
                        # 如果存在且是开仓状态，更新信息
                        if existing.get('is_open', 1) == 1:
                            conn = sqlite3.connect(self.db_path)
                            cursor = conn.cursor()
                            
                            cursor.execute('''
                            UPDATE positions 
                            SET buy_date = ?, buy_price = ?, notes = ?
                            WHERE symbol = ? AND is_open = 1
                            ''', (buy_date, buy_price, f"CSV更新: {simple_code}", symbol))
                            
                            conn.commit()
                            conn.close()
                            
                            update_count += 1
                            self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新{symbol}({simple_code})的持仓记录: 买入日期={buy_date}, 买入价={buy_price}")
                    else:
                        # 如果不存在，新增记录
                        self.record_buy(
                            symbol=symbol,
                            buy_date=buy_date,
                            buy_price=buy_price,
                            volume=volume,
                            cost=buy_price * volume,
                            notes=f"CSV导入: {simple_code}"
                        )
                        import_count += 1
                        self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 导入{symbol}({simple_code})的持仓记录: 买入日期={buy_date}, 买入价={buy_price}, 数量={volume}")
                        
                except Exception as e:
                    self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 处理{row.get('code', '未知')}的CSV记录异常: {str(e)}")
            
            self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV导入完成，新增{import_count}条记录，更新{update_count}条记录")
            return True
            
        except Exception as e:
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - CSV导入异常: {str(e)}")
            import traceback
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return False
        
    def sync_positions(self):
        """同步账户持仓与数据库记录
        
        1. 获取API中的实际持仓
        2. 读取数据库中的持仓记录
        3. 清理已不存在的持仓
        4. 添加新的持仓记录
        
        Returns:
            同步后的持仓信息字典
        """
        try:
            # 获取当前API持仓
            api_positions = self.context.account().positions()
            api_symbols = {p['symbol']: p for p in api_positions} if api_positions else {}
            
            # 获取数据库中的持仓记录
            db_positions = self.get_open_positions()
            db_symbols = {p['symbol']: p for p in db_positions}
            
            # 清理已不存在的持仓
            for symbol in set(db_symbols.keys()) - set(api_symbols.keys()):
                self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 清理已退出持仓: {symbol}")
                self.close_position(symbol)
                
                # 同时清理context中的持仓信息
                if hasattr(self.context, 'positions') and symbol in self.context.positions:
                    del self.context.positions[symbol]
                if hasattr(self.context, 'positions_cost') and symbol in self.context.positions_cost:
                    del self.context.positions_cost[symbol]
            
            # 添加新的持仓记录
            for symbol, position in api_symbols.items():
                if symbol not in db_symbols:
                    self.logger.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 添加新持仓: {symbol}")
                    # 使用30天前作为估计买入日期
                    estimated_buy_date = (self.context.now - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
                    
                    # 记录到数据库
                    self.record_buy(
                        symbol=symbol,
                        buy_date=estimated_buy_date,
                        buy_price=position['vwap'] if 'vwap' in position else position['price'],
                        volume=position['volume'],
                        cost=position['amount'] if 'amount' in position else position['volume'] * position['price'],
                        notes="账户同步持仓"
                    )
                    
                    # 更新context中的持仓信息
                    if hasattr(self.context, 'positions'):
                        self.context.positions[symbol] = position['volume']
                    
                    # 更新context中的成本信息
                    if hasattr(self.context, 'positions_cost'):
                        buy_time = datetime.datetime.strptime(estimated_buy_date, '%Y-%m-%d')
                        self.context.positions_cost[symbol] = {
                            'symbol': symbol,
                            'cost_price': position['vwap'] if 'vwap' in position else position['price'],
                            'buy_time': buy_time,
                            'buy_date': estimated_buy_date,
                            'confirmed_high': position['price'],
                            'confirmed_time': self.context.now,
                            'is_synced': True
                        }
                else:
                    # 更新持仓数量
                    self.update_position_volume(
                        symbol=symbol,
                        volume=position['volume']
                    )
            
            # 返回同步后的持仓信息
            return self.get_open_positions()
            
        except Exception as e:
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 同步持仓异常: {str(e)}")
            import traceback
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return []
    
    def record_buy(self, symbol: str, buy_date: str, buy_price: float, 
                  volume: int, cost: float, notes: str = "") -> int:
        """记录买入交易
        
        Args:
            symbol: 股票代码
            buy_date: 买入日期
            buy_price: 买入价格
            volume: 买入数量
            cost: 成本金额
            notes: 备注信息
            
        Returns:
            记录ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO positions 
        (symbol, buy_date, buy_price, volume, cost, highest_price, last_update, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (symbol, buy_date, buy_price, volume, cost, buy_price, 
              self.context.now.strftime("%Y-%m-%d %H:%M:%S"), notes))
        
        record_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return record_id
        
    def record_sell(self, symbol: str, sell_date: str, sell_price: float, 
                   volume: int, profit: float, notes: str = "") -> bool:
        """记录卖出交易
        
        Args:
            symbol: 股票代码
            sell_date: 卖出日期
            sell_price: 卖出价格
            volume: 卖出数量
            profit: 盈亏金额
            notes: 备注信息
            
        Returns:
            是否成功
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查找未卖出的持仓记录
        cursor.execute('''
        SELECT id, volume FROM positions 
        WHERE symbol = ? AND is_open = 1
        ORDER BY buy_date ASC
        ''', (symbol,))
        
        records = cursor.fetchall()
        if not records:
            conn.close()
            return False
        
        # 按照先进先出原则卖出
        remaining_volume = volume
        for record_id, record_volume in records:
            if remaining_volume <= 0:
                break
                
            sell_volume = min(remaining_volume, record_volume)
            remaining_volume -= sell_volume
            
            if sell_volume == record_volume:
                # 全部卖出
                cursor.execute('''
                UPDATE positions 
                SET is_open = 0, sell_date = ?, sell_price = ?, profit = ?, notes = ?
                WHERE id = ?
                ''', (sell_date, sell_price, profit, notes, record_id))
            else:
                # 部分卖出，需要拆分记录
                cursor.execute('''
                UPDATE positions 
                SET volume = ?
                WHERE id = ?
                ''', (record_volume - sell_volume, record_id))
                
                # 获取原始记录信息
                cursor.execute('''
                SELECT buy_date, buy_price, cost, highest_price 
                FROM positions WHERE id = ?
                ''', (record_id,))
                buy_date, buy_price, cost, highest_price = cursor.fetchone()
                
                # 创建新记录用于卖出部分
                part_cost = (cost / record_volume) * sell_volume
                cursor.execute('''
                INSERT INTO positions 
                (symbol, buy_date, buy_price, volume, cost, highest_price, 
                 last_update, is_open, sell_date, sell_price, profit, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (symbol, buy_date, buy_price, sell_volume, part_cost, 
                      highest_price, self.context.now.strftime("%Y-%m-%d %H:%M:%S"),
                      0, sell_date, sell_price, profit, notes))
        
        conn.commit()
        conn.close()
        return True
    
    def close_position(self, symbol: str) -> bool:
        """关闭持仓记录（不是实际卖出，只是标记为已关闭）
        
        Args:
            symbol: 股票代码
            
        Returns:
            是否成功
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        UPDATE positions 
        SET is_open = 0, last_update = ?
        WHERE symbol = ? AND is_open = 1
        ''', (self.context.now.strftime("%Y-%m-%d %H:%M:%S"), symbol))
        
        affected = cursor.rowcount
        conn.commit()
        conn.close()
        
        return affected > 0
    
    def update_position_volume(self, symbol: str, volume: int) -> bool:
        """更新持仓数量
        
        Args:
            symbol: 股票代码
            volume: 新的持仓数量
            
        Returns:
            是否成功
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取当前持仓总量
        cursor.execute('''
        SELECT SUM(volume) FROM positions 
        WHERE symbol = ? AND is_open = 1
        ''', (symbol,))
        
        current_volume = cursor.fetchone()[0] or 0
        
        if current_volume == volume:
            # 数量没变，不需要更新
            conn.close()
            return True
        
        if current_volume < volume:
            # 持仓增加，添加新记录
            additional_volume = volume - current_volume
            
            # 获取最近的买入价格作为参考
            cursor.execute('''
            SELECT buy_price FROM positions 
            WHERE symbol = ? AND is_open = 1
            ORDER BY buy_date DESC LIMIT 1
            ''', (symbol,))
            
            row = cursor.fetchone()
            if row:
                reference_price = row[0]
            else:
                # 如果没有记录，使用当前价格
                reference_price = self._get_current_price(symbol)
            
            # 添加新记录
            cursor.execute('''
            INSERT INTO positions 
            (symbol, buy_date, buy_price, volume, cost, highest_price, last_update, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, self.context.now.strftime("%Y-%m-%d"), reference_price, additional_volume, 
                  reference_price * additional_volume, reference_price, 
                  self.context.now.strftime("%Y-%m-%d %H:%M:%S"), "持仓数量增加"))
        else:
            # 持仓减少，按先进先出原则减少
            reduce_volume = current_volume - volume
            
            # 获取所有未关闭的持仓记录
            cursor.execute('''
            SELECT id, volume FROM positions 
            WHERE symbol = ? AND is_open = 1
            ORDER BY buy_date ASC
            ''', (symbol,))
            
            records = cursor.fetchall()
            
            # 按照先进先出原则减少持仓
            for record_id, record_volume in records:
                if reduce_volume <= 0:
                    break
                    
                if record_volume <= reduce_volume:
                    # 关闭整个记录
                    cursor.execute('''
                    UPDATE positions 
                    SET is_open = 0, last_update = ?, notes = ?
                    WHERE id = ?
                    ''', (self.context.now.strftime("%Y-%m-%d %H:%M:%S"), "持仓数量减少", record_id))
                    reduce_volume -= record_volume
                else:
                    # 减少部分持仓
                    cursor.execute('''
                    UPDATE positions 
                    SET volume = ?, last_update = ?
                    WHERE id = ?
                    ''', (record_volume - reduce_volume, self.context.now.strftime("%Y-%m-%d %H:%M:%S"), record_id))
                    reduce_volume = 0
        
        conn.commit()
        conn.close()
        return True
    
    def update_highest_price(self, symbol: str, price: float) -> None:
        """更新持仓的最高价
        
        Args:
            symbol: 股票代码
            price: 当前价格
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        UPDATE positions 
        SET highest_price = CASE
            WHEN highest_price IS NULL OR ? > highest_price THEN ?
            ELSE highest_price
        END,
        last_update = ?
        WHERE symbol = ? AND is_open = 1
        ''', (price, price, self.context.now.strftime("%Y-%m-%d %H:%M:%S"), symbol))
        
        conn.commit()
        conn.close()
    
    def get_open_positions(self) -> List[Dict[str, Any]]:
        """获取当前持仓信息
        
        Returns:
            持仓信息列表
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, 
               MIN(buy_date) as buy_date, 
               SUM(volume * buy_price) / SUM(volume) as buy_price, 
               SUM(volume) as total_volume, 
               SUM(cost) as total_cost, 
               MAX(highest_price) as highest_price
        FROM positions
        WHERE is_open = 1
        GROUP BY symbol
        ''')
        
        positions = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return positions
    
    def get_position_by_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取指定股票的持仓信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            持仓信息，如果不存在则返回None
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, 
               MIN(buy_date) as buy_date, 
               SUM(volume * buy_price) / SUM(volume) as buy_price, 
               SUM(volume) as total_volume, 
               SUM(cost) as total_cost, 
               MAX(highest_price) as highest_price
        FROM positions
        WHERE symbol = ? AND is_open = 1
        GROUP BY symbol
        ''', (symbol,))
        
        row = cursor.fetchone()
        conn.close()
        
        return dict(row) if row else None
    
    def get_position_history(self, symbol: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取持仓历史记录
        
        Args:
            symbol: 股票代码，如果为None则获取所有记录
            limit: 最大记录数
            
        Returns:
            历史记录列表
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        if symbol:
            cursor.execute('''
            SELECT * FROM positions
            WHERE symbol = ?
            ORDER BY buy_date DESC
            LIMIT ?
            ''', (symbol, limit))
        else:
            cursor.execute('''
            SELECT * FROM positions
            ORDER BY buy_date DESC
            LIMIT ?
            ''', (limit,))
        
        history = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return history
    
    def get_holding_days(self, symbol: str) -> int:
        """获取持仓天数
        
        Args:
            symbol: 股票代码
            
        Returns:
            持仓天数，如果不存在则返回0
        """
        position = self.get_position_by_symbol(symbol)
        if not position:
            return 0
        
        buy_date = datetime.datetime.strptime(position['buy_date'], '%Y-%m-%d').date()
        today = self.context.now.date()
        
        return (today - buy_date).days
    
    def update_context_positions(self):
        """更新context中的持仓信息
        
        将数据库中的持仓信息同步到context.positions和context.positions_cost
        """
        try:
            # 获取数据库中的持仓记录
            db_positions = self.get_open_positions()
            
            # 确保context中有持仓字典
            if not hasattr(self.context, 'positions'):
                self.context.positions = {}
            if not hasattr(self.context, 'positions_cost'):
                self.context.positions_cost = {}
            
            # 更新context中的持仓信息
            for position in db_positions:
                symbol = position['symbol']
                self.context.positions[symbol] = position['total_volume']
                
                # 更新成本信息
                buy_time = datetime.datetime.strptime(position['buy_date'], '%Y-%m-%d')
                self.context.positions_cost[symbol] = {
                    'symbol': symbol,
                    'cost_price': position['buy_price'],
                    'buy_time': buy_time,
                    'buy_date': position['buy_date'],
                    'confirmed_high': position['highest_price'],
                    'confirmed_time': self.context.now,
                    'is_synced': True
                }
            
            # 清理context中已不存在的持仓
            for symbol in list(self.context.positions.keys()):
                if symbol not in {p['symbol'] for p in db_positions}:
                    del self.context.positions[symbol]
                    
            for symbol in list(self.context.positions_cost.keys()):
                if symbol not in {p['symbol'] for p in db_positions}:
                    del self.context.positions_cost[symbol]
                    
            return True
        except Exception as e:
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 更新context持仓信息异常: {str(e)}")
            import traceback
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return False
    
    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格
        
        Args:
            symbol: 股票代码
            
        Returns:
            当前价格
        """
        try:
            from gm.api import current
            quote = current([symbol])
            if quote and len(quote) > 0:
                return quote[0]['price']
        except:
            pass
            
        # 如果无法获取当前价格，尝试从持仓中获取
        try:
            positions = self.context.account().positions()
            for position in positions:
                if position['symbol'] == symbol:
                    return position['price']
        except:
            pass
            
        # 如果还是无法获取，返回默认值
        return 0.0
    
    def get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """获取所有持仓信息，以字典形式返回
        
        Returns:
            Dict[str, Dict[str, Any]]: 以股票代码为键，持仓信息为值的字典
        """
        try:
            # 获取所有开仓状态的持仓
            positions = self.get_open_positions()
            
            # 转换为字典格式
            position_dict = {}
            for position in positions:
                symbol = position['symbol']
                position_dict[symbol] = {
                    'buy_price': position['buy_price'],
                    'buy_date': position['buy_date'],
                    'volume': position['total_volume'],
                    'cost': position['total_cost'],
                    'highest_price': position.get('highest_price', 0)
                }
                
            return position_dict
            
        except Exception as e:
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取所有持仓信息异常: {str(e)}")
            import traceback
            self.logger.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return {} 