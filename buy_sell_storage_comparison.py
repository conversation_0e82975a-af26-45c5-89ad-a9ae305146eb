# coding=utf-8
"""
买入卖出存储逻辑对比分析
分析为什么卖出能正常保存而买入不能
"""

import re

def analyze_sell_storage_logic():
    """分析卖出存储逻辑"""
    print('🔍 卖出存储逻辑分析')
    print('=' * 60)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 卖出记录保存流程:')
        
        # 查找卖出记录保存代码
        sell_pattern = r'# 如果卖出成功，记录卖出交易到数据库(.*?)except Exception as e:'
        sell_match = re.search(sell_pattern, content, re.DOTALL)
        
        if sell_match:
            sell_code = sell_match.group(1)
            
            print('  ✅ 卖出记录保存位置: 第2426-2454行')
            
            # 分析卖出记录构建
            if 'sell_trade_data = {' in sell_code:
                print('  ✅ 卖出记录构建: 使用sell_trade_data字典')
                
                # 提取字段映射
                field_pattern = r"'([^']+)':\s*([^,}]+)"
                fields = re.findall(field_pattern, sell_code)
                
                print('  📋 卖出记录字段:')
                for field, value in fields[:8]:  # 显示前8个字段
                    print(f'    {field}: {value.strip()}')
                
                # 检查关键字段
                key_fields = ['Timestamp', 'Symbol', 'Action', 'Price', 'Volume']
                for field in key_fields:
                    if any(f[0] == field for f in fields):
                        print(f'  ✅ 关键字段 {field}: 存在')
                    else:
                        print(f'  ❌ 关键字段 {field}: 缺失')
            
            # 分析保存方式
            if 'data_manager.save_trade(sell_trade_data)' in sell_code:
                print('  ✅ 保存方式: 直接调用data_manager.save_trade()')
                print('  ✅ 保存路径: 全局data_manager实例')
            else:
                print('  ❌ 保存方式: 未找到data_manager.save_trade调用')
        
        else:
            print('  ❌ 未找到卖出记录保存代码')
        
        return True
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return False

def analyze_buy_storage_logic():
    """分析买入存储逻辑"""
    print('\n🔍 买入存储逻辑分析')
    print('=' * 60)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 买入记录保存流程:')
        
        # 查找买入记录保存代码
        buy_pattern = r'def save_original_buy_record\(.*?\):(.*?)(?=def|\Z)'
        buy_match = re.search(buy_pattern, content, re.DOTALL)
        
        if buy_match:
            buy_code = buy_match.group(1)
            
            print('  ✅ 买入记录保存位置: save_original_buy_record函数')
            
            # 分析买入记录构建
            if 'buy_record = {' in buy_code:
                print('  ✅ 买入记录构建: 使用buy_record字典')
                
                # 提取字段映射
                field_pattern = r"'([^']+)':\s*([^,}]+)"
                fields = re.findall(field_pattern, buy_code)
                
                print('  📋 买入记录字段:')
                for field, value in fields[:8]:  # 显示前8个字段
                    print(f'    {field}: {value.strip()}')
                
                # 检查关键字段
                key_fields = ['timestamp', 'symbol', 'action', 'price', 'volume']
                for field in key_fields:
                    if any(f[0] == field for f in fields):
                        print(f'  ✅ 关键字段 {field}: 存在')
                    else:
                        print(f'  ❌ 关键字段 {field}: 缺失')
            
            # 分析保存方式
            if 'context.data_manager.save_trade' in buy_code:
                print('  ✅ 主要保存方式: context.data_manager.save_trade()')
                print('  ✅ 保存路径: context.data_manager实例')
            else:
                print('  ❌ 主要保存方式: 未找到context.data_manager.save_trade调用')
            
            if 'save_analysis' in buy_code:
                print('  ✅ 备用保存方式: save_analysis()')
                print('  ✅ 备用路径: 全局save_analysis函数')
            else:
                print('  ❌ 备用保存方式: 未找到save_analysis调用')
        
        else:
            print('  ❌ 未找到买入记录保存代码')
        
        return True
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return False

def compare_storage_differences():
    """对比存储差异"""
    print('\n🔍 买入卖出存储差异对比')
    print('=' * 60)
    
    differences = [
        {
            'aspect': '保存时机',
            'sell': '卖出成功后立即保存',
            'buy': '买入成功后调用save_original_buy_record',
            'issue': '买入多了一层函数调用'
        },
        {
            'aspect': '数据管理器',
            'sell': '直接使用全局data_manager',
            'buy': '使用context.data_manager',
            'issue': 'context.data_manager可能为None'
        },
        {
            'aspect': '字段名格式',
            'sell': '使用大写字段名（Timestamp, Symbol, Action）',
            'buy': '使用小写字段名（timestamp, symbol, action）',
            'issue': '字段名格式不一致'
        },
        {
            'aspect': '保存方法',
            'sell': '只有一种保存方式',
            'buy': '有主要和备用两种保存方式',
            'issue': '逻辑复杂，可能出错'
        },
        {
            'aspect': '错误处理',
            'sell': '在卖出逻辑中处理异常',
            'buy': '在save_original_buy_record中处理异常',
            'issue': '异常可能被掩盖'
        }
    ]
    
    print(f'{"方面":<12} | {"卖出逻辑":<25} | {"买入逻辑":<25} | {"潜在问题"}')
    print('-' * 90)
    
    for diff in differences:
        print(f'{diff["aspect"]:<12} | {diff["sell"]:<25} | {diff["buy"]:<25} | {diff["issue"]}')

def identify_root_cause():
    """识别根本原因"""
    print('\n💡 根本原因识别')
    print('=' * 50)
    
    print('📊 关键差异分析:')
    
    root_causes = [
        {
            'cause': '数据管理器实例不同',
            'description': '卖出使用全局data_manager，买入使用context.data_manager',
            'impact': '如果context.data_manager为None，买入记录保存失败',
            'probability': '高',
            'evidence': 'context.data_manager需要在init中正确赋值'
        },
        {
            'cause': '字段名格式不一致',
            'description': '卖出用大写字段名，买入用小写字段名',
            'impact': '可能导致字段映射问题',
            'probability': '中',
            'evidence': '数据库字段是小写，买入应该正确'
        },
        {
            'cause': '保存逻辑复杂度',
            'description': '买入有条件判断和备用路径',
            'impact': '增加了出错的可能性',
            'probability': '中',
            'evidence': '多层逻辑可能在某个环节失败'
        },
        {
            'cause': '异常处理位置',
            'description': '买入异常在save_original_buy_record中处理',
            'impact': '异常可能被静默处理',
            'probability': '低',
            'evidence': '有日志记录异常'
        }
    ]
    
    for i, cause in enumerate(root_causes, 1):
        print(f'\n{i}. {cause["cause"]} (概率: {cause["probability"]})')
        print(f'   描述: {cause["description"]}')
        print(f'   影响: {cause["impact"]}')
        print(f'   证据: {cause["evidence"]}')

def suggest_alignment_fixes():
    """建议对齐修复"""
    print('\n🔧 建议对齐修复方案')
    print('=' * 50)
    
    fixes = [
        {
            'fix': '统一数据管理器使用',
            'description': '买入也使用全局data_manager，而不是context.data_manager',
            'implementation': '修改save_original_buy_record使用全局data_manager',
            'benefit': '与卖出逻辑保持一致，避免context.data_manager为None的问题'
        },
        {
            'fix': '简化买入保存逻辑',
            'description': '移除条件判断，直接保存',
            'implementation': '参考卖出逻辑，直接调用data_manager.save_trade',
            'benefit': '减少出错环节，提高可靠性'
        },
        {
            'fix': '统一字段名格式',
            'description': '买入也使用大写字段名',
            'implementation': '修改buy_record字段名为大写',
            'benefit': '与卖出记录格式完全一致'
        },
        {
            'fix': '统一异常处理',
            'description': '在买入主逻辑中处理保存异常',
            'implementation': '将保存逻辑移到execute_backup_buy_logic中',
            'benefit': '异常处理更直接，便于调试'
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f'{i}. {fix["fix"]}')
        print(f'   描述: {fix["description"]}')
        print(f'   实现: {fix["implementation"]}')
        print(f'   收益: {fix["benefit"]}')
        print()

def create_aligned_buy_storage():
    """创建对齐的买入存储逻辑"""
    print('\n📝 创建对齐的买入存储逻辑')
    print('=' * 50)
    
    aligned_code = '''
# 对齐卖出逻辑的买入记录保存代码
def save_buy_record_aligned(context, symbol, price, volume, signal_data):
    """保存买入记录（对齐卖出逻辑）"""
    try:
        # 创建买入交易记录（使用与卖出相同的字段格式）
        buy_trade_data = {
            'Timestamp': context.now.strftime('%Y-%m-%d %H:%M:%S%z'),
            'Symbol': symbol,
            'Action': 'BUY',  # 使用大写，与卖出一致
            'Price': price,
            'Volume': volume,
            'Signal_Type': signal_data.get('buy_signal_type', 'trix_reversal'),
            'Signal_Reason': signal_data.get('signal_reason', 'TRIX反转信号'),
        }

        # 直接使用全局data_manager保存（与卖出逻辑一致）
        from scripts.data_manager import get_data_manager
        data_manager = get_data_manager()
        data_manager.save_trade(buy_trade_data)
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已保存买入交易记录: {symbol}")

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 保存买入交易记录异常: {str(e)}")
'''
    
    print('📋 对齐后的买入存储逻辑特点:')
    print('  ✅ 使用大写字段名（与卖出一致）')
    print('  ✅ 直接使用全局data_manager（与卖出一致）')
    print('  ✅ 简化逻辑，移除条件判断')
    print('  ✅ 统一异常处理方式')
    
    # 保存对齐的代码到文件
    with open('aligned_buy_storage.py', 'w', encoding='utf-8') as f:
        f.write(aligned_code)
    
    print('  ✅ 对齐代码已保存到 aligned_buy_storage.py')

def main():
    """主函数"""
    print('🔍 买入卖出存储逻辑对比分析报告')
    print('=' * 60)
    
    # 分析卖出存储逻辑
    sell_success = analyze_sell_storage_logic()
    
    # 分析买入存储逻辑
    buy_success = analyze_buy_storage_logic()
    
    # 对比存储差异
    compare_storage_differences()
    
    # 识别根本原因
    identify_root_cause()
    
    # 建议对齐修复
    suggest_alignment_fixes()
    
    # 创建对齐的买入存储逻辑
    create_aligned_buy_storage()
    
    print(f'\n🎯 对比分析结论')
    print('=' * 40)
    
    if sell_success and buy_success:
        print('✅ 成功分析了买入和卖出的存储逻辑')
        print('🔍 发现了关键差异：数据管理器实例不同')
        print('💡 根本原因：卖出用全局data_manager，买入用context.data_manager')
        print('🔧 建议修复：统一使用全局data_manager')
        print('📝 已生成对齐的买入存储逻辑代码')
    else:
        print('❌ 分析过程中遇到问题')
        print('🔧 建议手动检查代码结构')

if __name__ == '__main__':
    main()
