#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
波动性计算器 - 高效批量计算波动性指标
"""

from gm.api import *
import numpy as np
import pandas as pd
import datetime
from collections import defaultdict

class VolatilityCalculator:
    """波动性计算器 - 高效批量计算波动性指标"""
    
    def __init__(self, context):
        """
        初始化波动性计算器
        
        参数:
        - context: 策略上下文
        """
        self.context = context
        self.volatility_cache = {}  # {(symbol, period): (timestamp, volatility, atr_pct, score)}
        
        # 新增长期缓存，用于存储当日不变的波动性数据
        self.long_term_cache = {}  # {(symbol, period, date): (volatility, atr_pct, score)}
        
        # 延长缓存有效期
        self.cache_expires_seconds = 3600  # 缓存有效期(秒)，延长到1小时
        self.long_cache_expires_seconds = 86400  # 长期缓存有效期(秒)，设为24小时
        
        # 并行计算设置
        self.enable_parallel = self._get_config_value('ENABLE_PARALLEL_COMPUTING', True)
        self.parallel_workers = self._get_config_value('PARALLEL_WORKERS', 12)
        
        # 先尝试获取配置值的方法
        try:
            # 从配置获取波动性参数
            if hasattr(context, 'volatility_period'):
                self.volatility_period = context.volatility_period
            else:
                self.volatility_period = 20
                
            if hasattr(context, 'volatility_threshold'):
                self.volatility_threshold = context.volatility_threshold
            else:
                self.volatility_threshold = 1.0
                
            if hasattr(context, 'atr_threshold'):
                self.atr_threshold = context.atr_threshold
            else:
                self.atr_threshold = 1.5
                
            # 使用默认值设置其他参数
            self.volatility_weight = 0.7
            self.atr_weight = 0.3
            self.min_absolute_volatility = 1.8
            
            # 记录初始化信息
            if hasattr(context, 'log'):
                context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 波动性计算器初始化完成，缓存有效期：{self.cache_expires_seconds/60}分钟，长期缓存：{self.long_cache_expires_seconds/3600}小时，并行计算：{'启用' if self.enable_parallel else '禁用'}")
        except Exception as e:
            # 安全地处理任何初始化错误
            print(f"波动性计算器初始化异常: {str(e)}")
            # 使用默认值
            self.volatility_period = 20
            self.volatility_threshold = 1.0
            self.atr_threshold = 1.5
            self.volatility_weight = 0.7
            self.atr_weight = 0.3
            self.min_absolute_volatility = 1.8
    
    def calculate_volatility(self, symbol, period=None):
        """计算股票的波动性和ATR，使用统一的数据处理逻辑"""
        if period is None:
            period = self._get_config_value('VOLATILITY_PERIOD', 20)
        
        try:
            # 使用统一的历史数据获取函数
            if not isinstance(self.context, str) and hasattr(self.context, 'history_data_manager'):
                hist_data = self.context.history_data_manager.get_unified_history_data(
                    symbol=symbol,
                    frequency='1d',
                    days=self._get_config_value('HISTORY_DATA_DAYS_VOLATILITY', 60),
                    fields=['open', 'high', 'low', 'close', 'volume'],
                    use_realtime_price=True  # 使用实时价格替代当日收盘价
                )
            else:
                # 兼容模式：使用原始history函数获取数据
                hist_data = history(
                    symbol=symbol, 
                    frequency='1d',
                    start_time=self.context.now - datetime.timedelta(days=60),
                    end_time=self.context.now,
                    fields='open,high,low,close,volume',
                    df=True
                )
            
            if hist_data is None or hist_data.empty:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取 {symbol} 的历史数据，无法计算波动性")
                return 0, 0, False
                
            # 验证并修复数据格式
            required_columns = ['open', 'high', 'low', 'close']
            
            # 检查是否有合并的列名
            merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
            if merged_columns:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到 {symbol} 的历史数据有合并列名: {merged_columns}")
                
                # 尝试修复合并的列名
                for merged_col in merged_columns:
                    # 分割合并的列名
                    split_cols = [c.strip() for c in merged_col.split(',')]
                    
                    # 如果数据是一列，但列名包含多个字段，尝试分割数据
                    if len(hist_data.columns) == 1:
                        try:
                            # 检查第一行数据是否为字符串且包含逗号
                            first_row = hist_data.iloc[0, 0]
                            if isinstance(first_row, str) and ',' in first_row:
                                # 数据也是逗号分隔的，需要完全重新解析
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试重新解析 {symbol} 的历史数据")
                                
                                # 将每行数据分割成多列
                                new_data = []
                                for i in range(len(hist_data)):
                                    row_values = hist_data.iloc[i, 0].split(',')
                                    if len(row_values) == len(split_cols):
                                        new_data.append([v.strip() for v in row_values])
                                
                                # 创建新的DataFrame
                                hist_data = pd.DataFrame(new_data, columns=split_cols)
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功重新解析 {symbol} 的历史数据")
                            else:
                                # 只有列名有问题，数据本身格式正确
                                # 重命名列
                                hist_data.columns = split_cols
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功修复 {symbol} 的历史数据列名")
                        except Exception as e:
                            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试修复 {symbol} 的历史数据格式失败: {str(e)}")
                            return 0, 0, False
            
            # 再次检查必要的列是否存在
            missing_columns = [col for col in required_columns if col not in hist_data.columns]
            if missing_columns:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据缺少必要列: {missing_columns}")
                return 0, 0, False
                
            # 确保数据足够
            if len(hist_data) < period:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据不足{period}天，无法准确计算波动性")
                return 0, 0, False
                
            # 确保数值类型正确
            for col in required_columns:
                try:
                    hist_data[col] = hist_data[col].astype(float)
                except Exception as e:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据列 {col} 无法转换为数值类型: {str(e)}")
                    return 0, 0, False
                
            # 计算日收益率
            hist_data['daily_return'] = hist_data['close'].pct_change() * 100
            
            # 计算波动率（收益率的标准差）
            volatility = hist_data['daily_return'].tail(period).std()
            
            # 计算ATR（平均真实波幅）
            hist_data['tr1'] = abs(hist_data['high'] - hist_data['low'])
            hist_data['tr2'] = abs(hist_data['high'] - hist_data['close'].shift(1))
            hist_data['tr3'] = abs(hist_data['low'] - hist_data['close'].shift(1))
            hist_data['tr'] = hist_data[['tr1', 'tr2', 'tr3']].max(axis=1)
            atr = hist_data['tr'].tail(period).mean()
            atr_pct = atr / hist_data['close'].iloc[-1] * 100  # ATR占收盘价的百分比
            
            # 计算综合得分
            volatility_score = self._calculate_volatility_score(volatility, atr_pct)
            
            # 更新缓存
            self.volatility_cache[(symbol, period)] = (self.context.now, volatility, atr_pct, volatility_score)
            
            return volatility, atr_pct, True
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算{symbol}波动性异常: {str(e)}")
            return 0, 0, False
    
    def calculate_batch_volatility(self, symbols, period=None):
        """
        批量计算多只股票的波动性
        
        参数:
        - symbols: 股票代码列表
        - period: 计算周期，默认使用配置值
        
        返回:
        - dict: {symbol: (volatility, atr_pct, score, success)}
        """
        if period is None:
            period = self.volatility_period
        
        if not symbols:
            return {}
            
        # 检查是否使用并行计算
        if self.enable_parallel and len(symbols) > 10:
            return self._parallel_calculate_batch_volatility(symbols, period)
        
        results = {}
        missing_symbols = []
        today = self.context.now.strftime('%Y-%m-%d')
        
        # 先检查长期缓存 - 波动性在一天内变化不大，可以使用长期缓存
        for symbol in symbols:
            # 检查今日长期缓存
            long_cache_key = (symbol, period, today)
            if long_cache_key in self.long_term_cache:
                volatility, atr_pct, score = self.long_term_cache[long_cache_key]
                results[symbol] = (volatility, atr_pct, score, True)
                continue
                
            # 再检查短期缓存
            cache_key = (symbol, period)
            if cache_key in self.volatility_cache:
                timestamp, volatility, atr_pct, score = self.volatility_cache[cache_key]
                
                # 检查短期缓存是否在有效期内
                if (self.context.now - timestamp).total_seconds() < self.cache_expires_seconds:
                    results[symbol] = (volatility, atr_pct, score, True)
                    continue
            
            missing_symbols.append(symbol)
        
        # 如果所有股票都命中缓存，直接返回
        if not missing_symbols:
            return results
        
        try:
            # 批量获取历史数据
            if hasattr(self.context, 'history_data_manager'):
                # 使用优化的历史数据管理器批量获取
                historical_data = self.context.history_data_manager.get_batch_history(
                    symbols=missing_symbols,
                    frequency='1d',
                    count=period + 10,
                    fields=['symbol', 'open', 'high', 'low', 'close']
                )
            else:
                # 兼容模式：逐个获取
                historical_data = {}
                for symbol in missing_symbols:
                    data = self.context.data_fetcher.get_history_data(
                        symbol=symbol,
                        frequency='1d',
                        count=period + 10,
                        fields=['symbol', 'open', 'high', 'low', 'close']
                    )
                    if data is not None and not data.empty:
                        historical_data[symbol] = data
            
            # 向量化计算波动性指标
            for symbol, hist_data in historical_data.items():
                try:
                    if hist_data is None or hist_data.empty:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取 {symbol} 的历史数据，无法计算波动性")
                        results[symbol] = (0, 0, 0, False)
                        continue
                        
                    # 验证并修复数据格式
                    required_columns = ['open', 'high', 'low', 'close']
                    
                    # 检查是否有合并的列名
                    merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
                    if merged_columns:
                        try:
                            # 尝试修复合并的列名
                            for merged_col in merged_columns:
                                # 分割合并的列名
                                split_cols = [c.strip() for c in merged_col.split(',')]
                                
                                # 如果数据是一列，但列名包含多个字段，尝试分割数据
                                if len(hist_data.columns) == 1:
                                    # 检查第一行数据是否为字符串且包含逗号
                                    first_row = hist_data.iloc[0, 0]
                                    if isinstance(first_row, str) and ',' in first_row:
                                        # 数据也是逗号分隔的，需要完全重新解析
                                        # 将每行数据分割成多列
                                        new_data = []
                                        for i in range(len(hist_data)):
                                            row_values = hist_data.iloc[i, 0].split(',')
                                            if len(row_values) == len(split_cols):
                                                new_data.append([v.strip() for v in row_values])
                                        
                                        # 创建新的DataFrame
                                        hist_data = pd.DataFrame(new_data, columns=split_cols)
                                    else:
                                        # 只有列名有问题，数据本身格式正确
                                        # 重命名列
                                        hist_data.columns = split_cols
                        except Exception as e:
                            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 修复 {symbol} 的历史数据格式失败: {str(e)}")
                            results[symbol] = (0, 0, 0, False)
                            continue
                    
                    # 再次检查必要的列是否存在
                    missing_columns = [col for col in required_columns if col not in hist_data.columns]
                    if missing_columns:
                        self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据缺少必要列: {missing_columns}")
                        results[symbol] = (0, 0, 0, False)
                        continue
                        
                    # 确保数据足够
                    if len(hist_data) < period:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据不足{period}天，无法准确计算波动性")
                        results[symbol] = (0, 0, 0, False)
                        continue
                    
                    # 确保数值类型正确
                    for col in required_columns:
                        try:
                            hist_data[col] = hist_data[col].astype(float)
                        except Exception as e:
                            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 的历史数据列 {col} 无法转换为数值类型: {str(e)}")
                            results[symbol] = (0, 0, 0, False)
                            continue
                        
                    # 计算日收益率
                    hist_data['daily_return'] = hist_data['close'].pct_change() * 100
                    
                    # 计算波动率（收益率的标准差）
                    volatility = hist_data['daily_return'].tail(period).std()
                    
                    # 计算ATR（平均真实波幅）
                    hist_data['tr1'] = abs(hist_data['high'] - hist_data['low'])
                    hist_data['tr2'] = abs(hist_data['high'] - hist_data['close'].shift(1))
                    hist_data['tr3'] = abs(hist_data['low'] - hist_data['close'].shift(1))
                    hist_data['tr'] = hist_data[['tr1', 'tr2', 'tr3']].max(axis=1)
                    atr = hist_data['tr'].tail(period).mean()
                    atr_pct = atr / hist_data['close'].iloc[-1] * 100  # ATR占收盘价的百分比
                    
                    # 计算综合得分
                    volatility_score = self._calculate_volatility_score(volatility, atr_pct)
                    
                    # 更新结果
                    results[symbol] = (volatility, atr_pct, volatility_score, True)
                    
                    # 更新缓存
                    cache_key = (symbol, period)
                    self.volatility_cache[cache_key] = (self.context.now, volatility, atr_pct, volatility_score)
                    
                    # 更新长期缓存
                    long_cache_key = (symbol, period, today)
                    self.long_term_cache[long_cache_key] = (volatility, atr_pct, volatility_score)
                    
                except Exception as e:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算{symbol}波动性异常: {str(e)}")
                    results[symbol] = (0, 0, 0, False)
            
            return results
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 批量计算波动性异常: {str(e)}")
            # 对于未计算的股票，设置默认值
            for symbol in missing_symbols:
                if symbol not in results:
                    results[symbol] = (0, 0, 0, False)
            return results
    
    def _parallel_calculate_batch_volatility(self, symbols, period):
        """
        并行计算多只股票的波动性
        
        参数:
        - symbols: 股票代码列表
        - period: 计算周期
        
        返回:
        - dict: {symbol: (volatility, atr_pct, score, success)}
        """
        import concurrent.futures
        
        # 分割股票列表为多个子集
        batch_size = max(1, len(symbols) // self.parallel_workers)
        symbol_batches = [symbols[i:i+batch_size] for i in range(0, len(symbols), batch_size)]
        
        results = {}
        
        # 使用线程池并行计算
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
            # 提交任务
            future_to_batch = {
                executor.submit(self._calculate_volatility_subset, batch, period): batch 
                for batch in symbol_batches
            }
            
            # 获取结果
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_results = future.result()
                results.update(batch_results)
        
        return results
    
    def _calculate_volatility_subset(self, symbols_subset, period):
        """
        计算一个子集的股票波动性
        
        参数:
        - symbols_subset: 股票代码子集
        - period: 计算周期
        
        返回:
        - dict: {symbol: (volatility, atr_pct, score, success)}
        """
        results = {}
        
        # 批量获取历史数据
        if hasattr(self.context, 'history_data_manager'):
            historical_data = self.context.history_data_manager.get_batch_history(
                symbols=symbols_subset,
                frequency='1d',
                count=period + 10,
                fields=['symbol', 'open', 'high', 'low', 'close']  # 修改为列表格式
            )
        else:
            # 逐个获取
            historical_data = {}
            for symbol in symbols_subset:
                data = self.context.data_fetcher.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=period + 10,
                    fields=['symbol', 'open', 'high', 'low', 'close']  # 修改为列表格式
                )
                if data is not None and not data.empty:
                    historical_data[symbol] = data
        
        # 计算每只股票的波动性
        for symbol in symbols_subset:
            try:
                # 如果数据获取失败
                if symbol not in historical_data or historical_data[symbol] is None or historical_data[symbol].empty:
                    results[symbol] = (0, 0, 0, False)
                    continue
                
                hist_data = historical_data[symbol]
                
                # 验证并修复数据格式
                required_columns = ['open', 'high', 'low', 'close']
                
                # 检查是否有合并的列名
                merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
                if merged_columns:
                    try:
                        # 尝试修复合并的列名
                        for merged_col in merged_columns:
                            # 分割合并的列名
                            split_cols = [c.strip() for c in merged_col.split(',')]
                            
                            # 如果数据是一列，但列名包含多个字段，尝试分割数据
                            if len(hist_data.columns) == 1:
                                # 检查第一行数据是否为字符串且包含逗号
                                first_row = hist_data.iloc[0, 0]
                                if isinstance(first_row, str) and ',' in first_row:
                                    # 数据也是逗号分隔的，需要完全重新解析
                                    # 将每行数据分割成多列
                                    new_data = []
                                    for i in range(len(hist_data)):
                                        row_values = hist_data.iloc[i, 0].split(',')
                                        if len(row_values) == len(split_cols):
                                            new_data.append([v.strip() for v in row_values])
                                    
                                    # 创建新的DataFrame
                                    hist_data = pd.DataFrame(new_data, columns=split_cols)
                                else:
                                    # 只有列名有问题，数据本身格式正确
                                    # 重命名列
                                    hist_data.columns = split_cols
                    except Exception as e:
                        results[symbol] = (0, 0, 0, False)
                        continue
                
                # 再次检查必要的列是否存在
                missing_columns = [col for col in required_columns if col not in hist_data.columns]
                if missing_columns:
                    results[symbol] = (0, 0, 0, False)
                    continue
                
                # 确保数据足够
                if len(hist_data) < period:
                    results[symbol] = (0, 0, 0, False)
                    continue
                
                # 确保数值类型正确
                for col in required_columns:
                    try:
                        hist_data[col] = hist_data[col].astype(float)
                    except Exception as e:
                        results[symbol] = (0, 0, 0, False)
                        continue
                
                # 计算日收益率
                hist_data['daily_return'] = hist_data['close'].pct_change() * 100
                
                # 计算波动率
                volatility = hist_data['daily_return'].tail(period).std()
                
                # 计算ATR
                hist_data['tr1'] = abs(hist_data['high'] - hist_data['low'])
                hist_data['tr2'] = abs(hist_data['high'] - hist_data['close'].shift(1))
                hist_data['tr3'] = abs(hist_data['low'] - hist_data['close'].shift(1))
                hist_data['tr'] = hist_data[['tr1', 'tr2', 'tr3']].max(axis=1)
                atr = hist_data['tr'].tail(period).mean()
                atr_pct = atr / hist_data['close'].iloc[-1] * 100
                
                # 计算综合得分
                volatility_score = self._calculate_volatility_score(volatility, atr_pct)
                
                # 更新结果
                results[symbol] = (volatility, atr_pct, volatility_score, True)
                
                # 更新缓存
                with self.lock:
                    self.volatility_cache[(symbol, period)] = (self.context.now, volatility, atr_pct, volatility_score)
                    self.long_term_cache[(symbol, period, self.context.now.strftime('%Y-%m-%d'))] = (volatility, atr_pct, volatility_score)
                    
            except Exception as e:
                results[symbol] = (0, 0, 0, False)
        
        return results
    
    def calculate_market_volatility(self, period=None):
        """计算市场波动率，使用统一的数据处理逻辑"""
        if period is None:
            period = self._get_config_value('VOLATILITY_PERIOD', 20)
        
        try:
            # 获取市场指数符号
            index_symbol = 'SHSE.000300'  # 默认使用沪深300
            if not isinstance(self.context, str) and hasattr(self.context, 'index_symbol'):
                index_symbol = self.context.index_symbol
            
            # 使用统一的历史数据获取函数
            if not isinstance(self.context, str) and hasattr(self.context, 'history_data_manager'):
                hist_data = self.context.history_data_manager.get_unified_history_data(
                    symbol=index_symbol,
                    frequency='1d',
                    days=self._get_config_value('HISTORY_DATA_DAYS_VOLATILITY', 60),
                    fields=['close'],
                    use_realtime_price=True  # 使用实时价格替代当日收盘价
                )
            else:
                # 兼容模式：使用原始history函数获取数据
                hist_data = history(
                    symbol=index_symbol,
                    frequency='1d',
                    start_time=self.context.now - datetime.timedelta(days=60),
                    end_time=self.context.now,
                    fields='close',
                    df=True
                )
            
            if hist_data is None or hist_data.empty:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法获取市场指数历史数据，使用默认波动率2.0%")
                return 2.0  # 默认波动率2.0%
            
            # 验证并修复数据格式
            required_columns = ['close']
            
            # 检查是否有合并的列名
            merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
            if merged_columns:
                try:
                    # 尝试修复合并的列名
                    for merged_col in merged_columns:
                        # 分割合并的列名
                        split_cols = [c.strip() for c in merged_col.split(',')]
                        
                        # 如果数据是一列，但列名包含多个字段，尝试分割数据
                        if len(hist_data.columns) == 1:
                            # 检查第一行数据是否为字符串且包含逗号
                            first_row = hist_data.iloc[0, 0]
                            if isinstance(first_row, str) and ',' in first_row:
                                # 数据也是逗号分隔的，需要完全重新解析
                                # 将每行数据分割成多列
                                new_data = []
                                for i in range(len(hist_data)):
                                    row_values = hist_data.iloc[i, 0].split(',')
                                    if len(row_values) == len(split_cols):
                                        new_data.append([v.strip() for v in row_values])
                                
                                # 创建新的DataFrame
                                hist_data = pd.DataFrame(new_data, columns=split_cols)
                            else:
                                # 只有列名有问题，数据本身格式正确
                                # 重命名列
                                hist_data.columns = split_cols
                except Exception as e:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 修复市场指数历史数据格式失败: {str(e)}")
                    return 2.0  # 默认波动率2.0%
            
            # 再次检查必要的列是否存在
            missing_columns = [col for col in required_columns if col not in hist_data.columns]
            if missing_columns:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据缺少必要列: {missing_columns}")
                return 2.0  # 默认波动率2.0%
            
            # 确保数据足够
            if len(hist_data) < period:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据不足{period}天，使用默认波动率2.0%")
                return 2.0  # 默认波动率2.0%
            
            # 确保数值类型正确
            try:
                hist_data['close'] = hist_data['close'].astype(float)
            except Exception as e:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 市场指数历史数据列 close 无法转换为数值类型: {str(e)}")
                return 2.0  # 默认波动率2.0%
            
            # 计算日收益率
            hist_data['daily_return'] = hist_data['close'].pct_change() * 100
            
            # 计算波动率（收益率的标准差）
            market_volatility = hist_data['daily_return'].tail(period).std()
            
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前市场波动率 {market_volatility:.2f}%")
            return market_volatility
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算市场波动率异常: {str(e)}")
            return 2.0  # 默认波动率2.0%
    
    def _calculate_volatility_score(self, volatility, atr_pct):
        """
        计算波动性综合得分
        
        参数:
        - volatility: 波动率
        - atr_pct: ATR百分比
        
        返回:
        - 综合得分
        """
        # 获取市场平均波动率
        market_volatility = self.context.market_volatility if hasattr(self.context, 'market_volatility') else self._get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
        
        # 计算相对波动率
        if market_volatility > 0:
            relative_volatility = volatility / market_volatility
        else:
            relative_volatility = volatility / 2.0  # 使用默认值2.0作为除数
        
        # 计算波动率得分
        volatility_score = relative_volatility / self.volatility_threshold
        
        # 计算ATR得分
        atr_score = atr_pct / self.atr_threshold
        
        # 计算综合得分
        combined_score = self.volatility_weight * volatility_score + self.atr_weight * atr_score
        
        return combined_score
    
    def get_volatility_allocation_factor(self, volatility, atr_pct):
        """
        根据波动性计算资金分配因子
        
        参数:
        - volatility: 波动率
        - atr_pct: ATR百分比
        
        返回:
        - 资金分配因子
        """
        # 计算综合得分
        score = self._calculate_volatility_score(volatility, atr_pct)
        
        # 获取波动性资金调整参数
        max_volatility_factor = self._get_config_value('MAX_VOLATILITY_FACTOR', 3.0)
        min_volatility_factor = self._get_config_value('MIN_VOLATILITY_FACTOR', 1.0)
        volatility_factor_scale = self._get_config_value('VOLATILITY_FACTOR_SCALE', 1.5)
        
        # 计算资金分配因子(反比例，波动性越高分配越少)
        if score > 0:
            allocation_factor = min_volatility_factor + (max_volatility_factor - min_volatility_factor) * (1 - min(1, score / volatility_factor_scale))
        else:
            allocation_factor = max_volatility_factor
        
        return allocation_factor
    
    def clear_cache(self):
        """清除所有缓存"""
        self.volatility_cache.clear()
        # 新增：清除长期缓存
        self.long_term_cache.clear()
        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 波动性计算器缓存已清除")
    
    def _get_config_value(self, param_name, default=None):
        """从context获取配置参数值"""
        try:
            # 优先直接从context属性获取
            if hasattr(self.context, param_name):
                return getattr(self.context, param_name)
                
            # 然后从context的get_config_value方法获取
            elif hasattr(self.context, 'get_config_value'):
                return self.context.get_config_value(param_name, default)
            
            # 尝试从context._get_config_value方法获取
            elif hasattr(self.context, '_get_config_value'):
                # 确保context是对象而不是字符串
                if not isinstance(self.context, str):
                    return self.context._get_config_value(param_name, default)
            
            # 其次从context.config获取
            elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
                return getattr(self.context.config, param_name)
        except Exception as e:
            # 安全地处理任何错误
            print(f"获取配置值异常 {param_name}: {str(e)}")
            
        # 最后返回默认值
        return default 