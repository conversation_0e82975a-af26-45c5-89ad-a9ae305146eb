@echo off
chcp 65001 > nul
echo Starting Wanhe Strategy Analysis System...
echo.

REM Set Python path
set PYTHON_PATH=python

REM Check if Python exists
%PYTHON_PATH% --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found. Please make sure Python is installed and added to PATH
    pause
    exit /b 1
)

REM Check necessary Python packages
echo Checking necessary Python packages...
%PYTHON_PATH% -c "import pandas" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: pandas package not found, trying to install...
    %PYTHON_PATH% -m pip install pandas
)

%PYTHON_PATH% -c "import numpy" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: numpy package not found, trying to install...
    %PYTHON_PATH% -m pip install numpy
)

%PYTHON_PATH% -c "import matplotlib" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: matplotlib package not found, trying to install...
    %PYTHON_PATH% -m pip install matplotlib
)

%PYTHON_PATH% -c "import seaborn" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: seaborn package not found, trying to install...
    %PYTHON_PATH% -m pip install seaborn
)

%PYTHON_PATH% -c "import sklearn" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: scikit-learn package not found, trying to install...
    %PYTHON_PATH% -m pip install scikit-learn
)

%PYTHON_PATH% -c "import jinja2" > nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: jinja2 package not found, trying to install...
    %PYTHON_PATH% -m pip install jinja2
)

echo.
echo ===============================================================================
echo                           Wanhe Strategy Analysis System - Launcher            
echo ===============================================================================
echo.
echo Please select an operation:
echo.
echo 1. Run complete analysis process
echo 2. Run trade analysis only
echo 3. Run strategy optimization only
echo 4. Generate HTML report only
echo 0. Exit
echo.

set /p choice=Please enter option (0-4): 

if "%choice%"=="1" (
    echo.
    echo Running complete analysis process...
    %PYTHON_PATH% analysis_manager.py --all
) else if "%choice%"=="2" (
    echo.
    echo Running trade analysis...
    %PYTHON_PATH% analysis_manager.py --analyze
) else if "%choice%"=="3" (
    echo.
    echo Running strategy optimization...
    %PYTHON_PATH% analysis_manager.py --optimize
) else if "%choice%"=="4" (
    echo.
    echo Generating HTML report...
    %PYTHON_PATH% analysis_manager.py --html
) else if "%choice%"=="0" (
    echo.
    echo Exiting system...
    exit /b 0
) else (
    echo.
    echo Invalid option, please run again and select a valid option
)

echo.
echo Operation completed
pause 