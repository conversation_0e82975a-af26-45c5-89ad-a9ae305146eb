# coding=utf-8
"""
诊断因子数据流
全面诊断为什么增强因子没有保存到数据库
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def check_database_actual_fields():
    """检查数据库实际字段"""
    print('🔍 检查数据库实际字段')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取trades表所有字段
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        
        print(f'📊 trades表字段总数: {len(columns)}')
        
        # 分类显示字段
        basic_fields = []
        factor_fields = []
        
        for col in columns:
            field_name = col[1]
            field_type = col[2]
            
            if field_name.lower() in ['id', 'timestamp', 'symbol', 'action', 'price', 'volume']:
                basic_fields.append(f'{field_name} ({field_type})')
            else:
                factor_fields.append(f'{field_name} ({field_type})')
        
        print(f'\n📋 基础字段 ({len(basic_fields)}个):')
        for field in basic_fields:
            print(f'  {field}')
        
        print(f'\n📈 因子字段 ({len(factor_fields)}个):')
        for i, field in enumerate(factor_fields):
            print(f'  {field}')
            if i >= 20:  # 只显示前20个
                print(f'  ... 还有{len(factor_fields)-20}个字段')
                break
        
        # 检查关键增强因子字段是否存在
        key_enhanced_fields = ['Current_Price', 'MA3', 'MA7', 'MA20', 'RSI', 'MACD', 'ADX']
        existing_enhanced_fields = []
        missing_enhanced_fields = []
        
        all_field_names = [col[1] for col in columns]
        
        for field in key_enhanced_fields:
            if field in all_field_names:
                existing_enhanced_fields.append(field)
            else:
                missing_enhanced_fields.append(field)
        
        print(f'\n📊 关键增强因子字段检查:')
        print(f'  存在: {len(existing_enhanced_fields)}个 - {existing_enhanced_fields}')
        print(f'  缺失: {len(missing_enhanced_fields)}个 - {missing_enhanced_fields}')
        
        conn.close()
        
        return all_field_names, existing_enhanced_fields, missing_enhanced_fields
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return [], [], []

def check_recent_buy_records():
    """检查最近的买入记录"""
    print('\n🔍 检查最近买入记录的实际数据')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最近5条买入记录的所有字段
        query = "SELECT * FROM trades WHERE action = 'BUY' ORDER BY timestamp DESC LIMIT 5"
        df = pd.read_sql_query(query, conn)
        
        if len(df) == 0:
            print('❌ 没有买入记录')
            return
        
        print(f'📊 最近5条买入记录分析:')
        
        for i, row in df.iterrows():
            print(f'\n记录 {i+1}: {row["symbol"]} @ {row["timestamp"]}')
            print(f'  基础信息: 价格¥{row["price"]:.2f}, 数量{row["volume"]}股')
            
            # 检查所有非基础字段的数据
            non_null_factors = []
            null_factors = []
            
            for col in df.columns:
                if col not in ['id', 'timestamp', 'symbol', 'action', 'price', 'volume']:
                    value = row[col]
                    if pd.notna(value) and value != 0 and value != '':
                        non_null_factors.append((col, value))
                    else:
                        null_factors.append(col)
            
            print(f'  有数据的因子 ({len(non_null_factors)}个):')
            for factor, value in non_null_factors[:10]:  # 显示前10个
                if isinstance(value, float):
                    print(f'    {factor}: {value:.4f}')
                else:
                    print(f'    {factor}: {value}')
            
            if len(non_null_factors) > 10:
                print(f'    ... 还有{len(non_null_factors)-10}个有数据的因子')
            
            print(f'  空值因子: {len(null_factors)}个')
            
            if len(non_null_factors) == 0:
                print(f'  ❌ 该记录没有任何因子数据')
            else:
                print(f'  ✅ 该记录有{len(non_null_factors)}个因子有数据')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def simulate_complete_factor_flow():
    """模拟完整的因子数据流"""
    print('\n🔄 模拟完整因子数据流')
    print('=' * 50)
    
    try:
        print('📊 步骤1: 测试增强因子引擎')
        
        # 导入并测试增强因子引擎
        from enhanced_factor_engine import EnhancedFactorEngine
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [10.0]
        for _ in range(49):
            prices.append(prices[-1] * (1 + np.random.normal(0.001, 0.02)))
        
        test_data = pd.DataFrame({
            'open': np.array(prices) * 1.01,
            'high': np.array(prices) * 1.02,
            'low': np.array(prices) * 0.98,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 50)
        }, index=dates)
        
        engine = EnhancedFactorEngine()
        enhanced_factors = engine.calculate_all_factors(test_data, 'TEST.000001')
        
        print(f'  ✅ 增强因子引擎计算了 {len(enhanced_factors)} 个因子')
        
        # 检查关键数据库字段
        db_fields = ['Current_Price', 'MA3', 'MA7', 'MA20', 'RSI', 'MACD', 'ADX']
        db_field_count = sum(1 for field in db_fields if field in enhanced_factors)
        
        print(f'  ✅ 数据库格式字段: {db_field_count}/{len(db_fields)}个')
        
        print('\n📊 步骤2: 模拟signal_generator数据传递')
        
        # 模拟signal_generator中的数据处理
        analysis_data = {
            'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'Symbol': 'TEST.000001',
            'Current_Price': 10.50,
            'MA3': 10.45,
            'MA7': 10.40,
            'MA20': 10.30,
            'Final_Buy_Signal': 1
        }
        
        # 合并增强因子
        analysis_data.update(enhanced_factors)
        
        print(f'  ✅ analysis_data包含 {len(analysis_data)} 个字段')
        
        # 模拟signal_info构建
        signal_info = {
            'symbol': 'TEST.000001',
            'current_price': 10.50,
            'final_buy_signal': 1,
            'timestamp': datetime.now(),
            'ma3': 10.45,
            'ma7': 10.40,
            'ma20': 10.30
        }
        
        # 合并增强因子
        signal_info.update(enhanced_factors)
        
        print(f'  ✅ signal_info包含 {len(signal_info)} 个字段')
        
        print('\n📊 步骤3: 模拟main.py买入记录构建')
        
        # 模拟买入记录构建
        buy_record = {
            'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S%z'),
            'Symbol': 'TEST.000001',
            'Action': 'BUY',
            'Price': 10.50,
            'Volume': 1000
        }
        
        # 提取增强因子（模拟main.py中的逻辑）
        extracted_factors = {}
        for key, value in signal_info.items():
            if isinstance(value, (int, float)) and not np.isnan(value) and np.isfinite(value):
                if key not in ['final_buy_signal']:
                    # 字段名转换
                    db_key = key.replace('_', '_').title().replace(' ', '_')
                    extracted_factors[db_key] = float(value)
        
        buy_record.update(extracted_factors)
        
        print(f'  ✅ buy_record包含 {len(buy_record)} 个字段')
        
        # 检查关键字段是否在buy_record中
        key_fields_in_record = sum(1 for field in db_fields if field in buy_record)
        print(f'  ✅ 关键数据库字段在buy_record中: {key_fields_in_record}/{len(db_fields)}个')
        
        print('\n📊 步骤4: 验证数据传递完整性')
        
        if key_fields_in_record >= len(db_fields) * 0.8:
            print('  ✅ 数据传递链路正常，因子数据应该能正确保存')
            return True
        else:
            print('  ❌ 数据传递链路有问题，因子数据可能丢失')
            return False
        
    except Exception as e:
        print(f'❌ 模拟失败: {e}')
        return False

def check_strategy_logs():
    """检查策略运行日志"""
    print('\n📋 策略运行日志检查建议')
    print('=' * 50)
    
    print('🔍 需要检查的日志信息:')
    print('1. 因子计算日志:')
    print('   • "计算了XX个增强因子"')
    print('   • "增强因子计算失败"')
    print('   • EnhancedFactorEngine相关错误')
    
    print('\n2. 数据保存日志:')
    print('   • "买入记录包含XX个字段"')
    print('   • "signal_info包含XX个字段"')
    print('   • 数据库写入相关错误')
    
    print('\n3. 异常处理日志:')
    print('   • try-except块中的异常信息')
    print('   • 因子计算被跳过的原因')
    
    print('\n💡 如果没有看到因子计算日志，可能的原因:')
    print('• enhanced_factor_engine.py导入失败')
    print('• signal_generator.py中的因子计算代码未执行')
    print('• 异常被静默处理')

def generate_diagnosis_report():
    """生成诊断报告"""
    print('\n📋 因子数据流诊断报告')
    print('=' * 60)
    
    # 检查数据库字段
    all_fields, existing_enhanced, missing_enhanced = check_database_actual_fields()
    
    # 检查买入记录
    check_recent_buy_records()
    
    # 模拟数据流
    flow_ok = simulate_complete_factor_flow()
    
    # 检查日志建议
    check_strategy_logs()
    
    print(f'\n🎯 诊断结论')
    print('=' * 40)
    
    if len(missing_enhanced) == 0:
        print('✅ 数据库表结构支持增强因子')
    else:
        print(f'❌ 数据库缺少 {len(missing_enhanced)} 个关键增强因子字段')
    
    if flow_ok:
        print('✅ 因子数据流模拟正常')
        print('💡 问题可能在于策略运行时的实际执行')
    else:
        print('❌ 因子数据流模拟异常')
        print('💡 需要修复数据传递链路')
    
    print(f'\n📋 下一步行动:')
    if len(missing_enhanced) > 0:
        print('1. 🔧 修复数据库表结构，添加缺失字段')
    
    if not flow_ok:
        print('2. 🔧 修复因子数据传递链路')
    
    print('3. 🔍 检查策略运行日志，查找因子计算异常')
    print('4. 🧪 运行单步测试验证每个环节')
    print('5. 🔄 重新运行策略验证修复效果')

def main():
    """主函数"""
    print('🔍 因子数据流全面诊断')
    print('=' * 60)
    print(f'诊断时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    generate_diagnosis_report()

if __name__ == '__main__':
    main()
