# coding=utf-8
"""
胜率分析器
基于数据库中的买入指标分析胜率高的买入策略
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class WinRateAnalyzer:
    """胜率分析器"""

    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        self.data_integrity_enabled = True  # 启用数据完整性检查
        
    def analyze_win_rate_strategies(self):
        """分析胜率高的买入策略"""
        print('📊 胜率分析器 - 基于买入指标分析')
        print('=' * 80)
        
        try:
            # 1. 加载交易数据
            data = self._load_trade_data()
            if data is None or len(data) == 0:
                print('❌ 没有找到交易数据')
                return None
            
            print(f'📈 加载交易数据: {len(data)} 条记录')
            
            # 2. 数据预处理
            processed_data = self._preprocess_data(data)
            if processed_data is None or len(processed_data) == 0:
                print('❌ 数据预处理失败')
                return None
            
            print(f'✅ 数据预处理完成: {len(processed_data)} 条有效记录')
            print(f'📊 整体胜率: {processed_data["is_win"].mean():.2%}')
            
            # 3. 分析各类指标的胜率
            analysis_results = {}
            
            # 分析技术指标
            analysis_results['technical'] = self._analyze_technical_indicators(processed_data)
            
            # 分析增强指标
            analysis_results['enhanced'] = self._analyze_enhanced_indicators(processed_data)
            
            # 分析组合策略
            analysis_results['combinations'] = self._analyze_indicator_combinations(processed_data)
            
            # 分析时间因子
            analysis_results['timing'] = self._analyze_timing_factors(processed_data)
            
            # 4. 生成策略建议
            strategy_recommendations = self._generate_strategy_recommendations(analysis_results, processed_data)
            
            # 5. 生成详细报告
            self._generate_detailed_report(analysis_results, strategy_recommendations, processed_data)
            
            return {
                'analysis_results': analysis_results,
                'strategy_recommendations': strategy_recommendations,
                'data_summary': {
                    'total_trades': len(processed_data),
                    'overall_win_rate': processed_data['is_win'].mean(),
                    'profitable_trades': processed_data['is_win'].sum(),
                    'losing_trades': len(processed_data) - processed_data['is_win'].sum()
                }
            }
            
        except Exception as e:
            print(f'❌ 分析过程异常: {e}')
            import traceback
            traceback.print_exc()
            return None
    
    def _load_trade_data(self):
        """加载交易数据并计算胜率"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 查询买入记录
            buy_query = '''
            SELECT * FROM trades
            WHERE action = 'BUY'
            ORDER BY timestamp DESC
            '''

            buy_data = pd.read_sql_query(buy_query, conn)

            # 查询卖出记录
            sell_query = '''
            SELECT symbol, timestamp, price, net_profit_pct_sell, sell_reason
            FROM trades
            WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
            ORDER BY timestamp DESC
            '''

            sell_data = pd.read_sql_query(sell_query, conn)
            conn.close()

            # 为买入记录匹配对应的卖出结果
            buy_data_with_results = self._match_buy_sell_records(buy_data, sell_data)

            return buy_data_with_results

        except Exception as e:
            print(f'❌ 加载数据失败: {e}')
            return None

    def _match_buy_sell_records(self, buy_data, sell_data):
        """匹配买入和卖出记录，计算胜率"""
        try:
            # 转换时间格式，统一时区处理
            buy_data['timestamp'] = pd.to_datetime(buy_data['timestamp']).dt.tz_localize(None)
            sell_data['timestamp'] = pd.to_datetime(sell_data['timestamp']).dt.tz_localize(None)

            # 为每个买入记录找到对应的卖出记录
            buy_data['profit_pct'] = np.nan
            buy_data['is_win'] = np.nan
            buy_data['sell_reason'] = ''

            print(f'🔍 开始匹配 {len(buy_data)} 买入记录和 {len(sell_data)} 卖出记录...')

            matched_count = 0
            for idx, buy_row in buy_data.iterrows():
                # 找到同一股票在买入之后的卖出记录
                matching_sells = sell_data[
                    (sell_data['symbol'] == buy_row['symbol']) &
                    (sell_data['timestamp'] > buy_row['timestamp'])
                ].sort_values('timestamp')

                if len(matching_sells) > 0:
                    # 取最近的一次卖出
                    sell_row = matching_sells.iloc[0]
                    profit_pct = sell_row['net_profit_pct_sell']

                    buy_data.at[idx, 'profit_pct'] = profit_pct
                    buy_data.at[idx, 'is_win'] = 1 if profit_pct > 0 else 0
                    buy_data.at[idx, 'sell_reason'] = sell_row['sell_reason']
                    matched_count += 1

            # 只保留有卖出结果的记录
            completed_trades = buy_data.dropna(subset=['profit_pct'])

            print(f'📊 匹配结果: {len(buy_data)} 买入记录 -> {matched_count} 匹配成功 -> {len(completed_trades)} 完整交易')

            if len(completed_trades) > 0:
                win_rate = completed_trades['is_win'].mean()
                print(f'🎯 初步胜率: {win_rate:.2%}')

            return completed_trades

        except Exception as e:
            print(f'❌ 匹配买卖记录失败: {e}')
            import traceback
            traceback.print_exc()
            return buy_data
    
    def _preprocess_data(self, data):
        """数据预处理"""
        try:
            # 过滤有效数据
            valid_data = data.dropna(subset=['price', 'is_win'])
            
            # 确保数值类型
            numeric_columns = []
            for col in valid_data.columns:
                if col not in ['timestamp', 'symbol', 'action', 'status']:
                    try:
                        valid_data[col] = pd.to_numeric(valid_data[col], errors='coerce')
                        numeric_columns.append(col)
                    except:
                        pass
            
            # 移除全为空值的列
            valid_data = valid_data.dropna(axis=1, how='all')
            
            # 添加时间特征
            if 'timestamp' in valid_data.columns:
                valid_data['timestamp'] = pd.to_datetime(valid_data['timestamp'])
                valid_data['hour'] = valid_data['timestamp'].dt.hour
                valid_data['day_of_week'] = valid_data['timestamp'].dt.dayofweek
                valid_data['month'] = valid_data['timestamp'].dt.month
            
            return valid_data
            
        except Exception as e:
            print(f'❌ 数据预处理失败: {e}')
            return None
    
    def _analyze_technical_indicators(self, data):
        """分析技术指标的胜率"""
        try:
            print('\n🔧 分析技术指标胜率...')
            
            technical_results = {}
            
            # 定义技术指标列（基于实际数据库字段）
            technical_indicators = {
                'RSI': ['rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d'],
                'MACD': ['macd_12_26', 'macd_signal_9', 'macd_histogram', 'macd_slope'],
                'MA': ['ma5', 'ma10', 'ma20', 'ma60', 'ma120'],
                'KDJ': ['kdj_k', 'kdj_d', 'kdj_j'],
                'ATR': ['atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized'],
                'BOLL': ['bb_upper_20', 'bb_middle_20', 'bb_lower_20', 'bb_width_20', 'bb_position_20'],
                'ADX': ['adx_14', 'adx_slope', 'adx_trend_strength'],
                'CCI': ['cci_14', 'cci_commodity_channel'],
                'Williams': ['williams_r'],
                'Stoch': ['stoch_k', 'stoch_d', 'stochastic_rsi']
            }
            
            for indicator_group, columns in technical_indicators.items():
                group_results = {}
                
                for col in columns:
                    if col in data.columns:
                        result = self._analyze_single_indicator(data, col)
                        if result:
                            group_results[col] = result
                
                if group_results:
                    technical_results[indicator_group] = group_results
                    
                    # 找出该组最佳指标
                    best_indicator = max(group_results.items(), 
                                       key=lambda x: x[1]['best_range_win_rate'])
                    print(f'  📈 {indicator_group} 最佳: {best_indicator[0]} (胜率: {best_indicator[1]["best_range_win_rate"]:.2%})')
            
            return technical_results
            
        except Exception as e:
            print(f'❌ 技术指标分析失败: {e}')
            return {}
    
    def _analyze_enhanced_indicators(self, data):
        """分析增强指标的胜率"""
        try:
            print('\n🚀 分析增强指标胜率...')
            
            enhanced_results = {}
            
            # 定义增强指标列（基于实际数据库字段）
            enhanced_indicators = {
                '综合评分': ['enhanced_overall_score', 'overall_score', 'technical_score'],
                '信心度': ['buy_confidence_score', 'buy_signal_strength'],
                '市场环境': ['market_environment_score', 'market_sentiment_score'],
                '基本面': ['fundamental_score', 'pe_ratio', 'pb_ratio', 'roe'],
                '技术面': ['enhanced_technical_score', 'technical_score', 'momentum_score'],
                '资金流向': ['money_flow_score', 'main_force_inflow', 'money_flow_5d', 'money_flow_10d'],
                '风险评分': ['risk_score', 'volatility_score', 'beta_market'],
                '波动性': ['volatility_score_enhanced', 'volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d'],
                '趋势强度': ['trend_score', 'adx_trend_strength', 'trend_strength_buy', 'trend_intensity'],
                '成交量': ['volume_score', 'volume_ma5_ratio', 'volume_ma10_ratio', 'relative_volume']
            }
            
            for indicator_group, columns in enhanced_indicators.items():
                group_results = {}
                
                for col in columns:
                    if col in data.columns:
                        result = self._analyze_single_indicator(data, col)
                        if result:
                            group_results[col] = result
                
                if group_results:
                    enhanced_results[indicator_group] = group_results
                    
                    # 找出该组最佳指标
                    best_indicator = max(group_results.items(), 
                                       key=lambda x: x[1]['best_range_win_rate'])
                    print(f'  🎯 {indicator_group} 最佳: {best_indicator[0]} (胜率: {best_indicator[1]["best_range_win_rate"]:.2%})')
            
            return enhanced_results
            
        except Exception as e:
            print(f'❌ 增强指标分析失败: {e}')
            return {}
    
    def _analyze_single_indicator(self, data, indicator_name):
        """分析单个指标的胜率"""
        try:
            if indicator_name not in data.columns:
                return None
            
            indicator_data = data[indicator_name].dropna()
            if len(indicator_data) < 10:
                return None
            
            # 计算分位数
            q20 = indicator_data.quantile(0.2)
            q40 = indicator_data.quantile(0.4)
            q60 = indicator_data.quantile(0.6)
            q80 = indicator_data.quantile(0.8)
            
            # 分组分析胜率
            ranges = {
                'very_low': data[data[indicator_name] <= q20]['is_win'],
                'low': data[(data[indicator_name] > q20) & (data[indicator_name] <= q40)]['is_win'],
                'medium': data[(data[indicator_name] > q40) & (data[indicator_name] <= q60)]['is_win'],
                'high': data[(data[indicator_name] > q60) & (data[indicator_name] <= q80)]['is_win'],
                'very_high': data[data[indicator_name] > q80]['is_win']
            }
            
            range_win_rates = {}
            for range_name, win_data in ranges.items():
                if len(win_data) > 0:
                    range_win_rates[range_name] = {
                        'win_rate': win_data.mean(),
                        'sample_count': len(win_data),
                        'threshold': {
                            'very_low': f'<= {q20:.2f}',
                            'low': f'{q20:.2f} - {q40:.2f}',
                            'medium': f'{q40:.2f} - {q60:.2f}',
                            'high': f'{q60:.2f} - {q80:.2f}',
                            'very_high': f'> {q80:.2f}'
                        }[range_name]
                    }
            
            # 找出最佳范围
            best_range = max(range_win_rates.items(), key=lambda x: x[1]['win_rate'])
            
            # 计算相关性
            correlation = indicator_data.corr(data.loc[indicator_data.index, 'is_win'])
            
            return {
                'correlation': correlation,
                'range_win_rates': range_win_rates,
                'best_range': best_range[0],
                'best_range_win_rate': best_range[1]['win_rate'],
                'best_range_threshold': best_range[1]['threshold'],
                'best_range_samples': best_range[1]['sample_count'],
                'overall_samples': len(indicator_data)
            }
            
        except Exception as e:
            return None
    
    def _analyze_indicator_combinations(self, data):
        """分析指标组合策略"""
        try:
            print('\n🔗 分析指标组合策略...')
            
            combination_results = {}
            
            # 定义一些有效的指标组合
            combinations = [
                {
                    'name': '综合评分 + 信心度',
                    'indicators': ['enhanced_overall_score', 'buy_confidence_score'],
                    'thresholds': [60, 65]
                },
                {
                    'name': 'RSI + MACD',
                    'indicators': ['rsi', 'macd'],
                    'thresholds': [30, 0]  # RSI > 30, MACD > 0
                },
                {
                    'name': '技术面 + 资金流',
                    'indicators': ['enhanced_technical_score', 'money_flow_score'],
                    'thresholds': [60, 55]
                },
                {
                    'name': '市场环境 + 基本面',
                    'indicators': ['market_environment_score', 'fundamental_score'],
                    'thresholds': [50, 55]
                }
            ]
            
            for combo in combinations:
                result = self._analyze_combination(data, combo)
                if result:
                    combination_results[combo['name']] = result
                    print(f'  🎯 {combo["name"]}: 胜率 {result["win_rate"]:.2%} (样本: {result["sample_count"]})')
            
            return combination_results
            
        except Exception as e:
            print(f'❌ 组合策略分析失败: {e}')
            return {}
    
    def _analyze_combination(self, data, combination):
        """分析单个指标组合"""
        try:
            indicators = combination['indicators']
            thresholds = combination['thresholds']
            
            # 检查所有指标是否存在
            if not all(ind in data.columns for ind in indicators):
                return None
            
            # 构建组合条件
            condition = True
            for i, (indicator, threshold) in enumerate(zip(indicators, thresholds)):
                condition = condition & (data[indicator] >= threshold)
            
            # 计算满足条件的交易
            filtered_data = data[condition]
            
            if len(filtered_data) < 5:  # 样本太少
                return None
            
            win_rate = filtered_data['is_win'].mean()
            sample_count = len(filtered_data)
            
            return {
                'win_rate': win_rate,
                'sample_count': sample_count,
                'conditions': [f'{ind} >= {thr}' for ind, thr in zip(indicators, thresholds)],
                'improvement': win_rate - data['is_win'].mean()
            }
            
        except Exception as e:
            return None
    
    def _analyze_timing_factors(self, data):
        """分析时间因子"""
        try:
            print('\n⏰ 分析时间因子...')
            
            timing_results = {}
            
            # 分析小时
            if 'hour' in data.columns:
                hour_analysis = {}
                for hour in range(9, 16):  # 交易时间
                    hour_data = data[data['hour'] == hour]
                    if len(hour_data) > 5:
                        hour_analysis[f'{hour}:00'] = {
                            'win_rate': hour_data['is_win'].mean(),
                            'sample_count': len(hour_data)
                        }
                
                if hour_analysis:
                    timing_results['hour'] = hour_analysis
                    best_hour = max(hour_analysis.items(), key=lambda x: x[1]['win_rate'])
                    print(f'  🕐 最佳交易时间: {best_hour[0]} (胜率: {best_hour[1]["win_rate"]:.2%})')
            
            # 分析星期
            if 'day_of_week' in data.columns:
                day_names = ['周一', '周二', '周三', '周四', '周五']
                day_analysis = {}
                for day in range(5):  # 工作日
                    day_data = data[data['day_of_week'] == day]
                    if len(day_data) > 5:
                        day_analysis[day_names[day]] = {
                            'win_rate': day_data['is_win'].mean(),
                            'sample_count': len(day_data)
                        }
                
                if day_analysis:
                    timing_results['day_of_week'] = day_analysis
                    best_day = max(day_analysis.items(), key=lambda x: x[1]['win_rate'])
                    print(f'  📅 最佳交易日: {best_day[0]} (胜率: {best_day[1]["win_rate"]:.2%})')
            
            return timing_results
            
        except Exception as e:
            print(f'❌ 时间因子分析失败: {e}')
            return {}
    
    def _generate_strategy_recommendations(self, analysis_results, data):
        """生成策略建议"""
        try:
            print('\n💡 生成策略建议...')
            
            recommendations = []
            overall_win_rate = data['is_win'].mean()
            
            # 基于技术指标的建议（降低阈值到5%提升）
            if 'technical' in analysis_results:
                for group, indicators in analysis_results['technical'].items():
                    for indicator, result in indicators.items():
                        if result['best_range_win_rate'] > overall_win_rate + 0.05:  # 胜率提升5%以上
                            recommendations.append({
                                'type': '技术指标',
                                'strategy': f'当 {indicator} 在 {result["best_range_threshold"]} 范围时买入',
                                'expected_win_rate': result['best_range_win_rate'],
                                'improvement': result['best_range_win_rate'] - overall_win_rate,
                                'sample_count': result['best_range_samples'],
                                'confidence': 'high' if result['best_range_samples'] > 20 else 'medium'
                            })

            # 基于增强指标的建议（降低阈值到5%提升）
            if 'enhanced' in analysis_results:
                for group, indicators in analysis_results['enhanced'].items():
                    for indicator, result in indicators.items():
                        if result['best_range_win_rate'] > overall_win_rate + 0.05:
                            recommendations.append({
                                'type': '增强指标',
                                'strategy': f'当 {indicator} 在 {result["best_range_threshold"]} 范围时买入',
                                'expected_win_rate': result['best_range_win_rate'],
                                'improvement': result['best_range_win_rate'] - overall_win_rate,
                                'sample_count': result['best_range_samples'],
                                'confidence': 'high' if result['best_range_samples'] > 20 else 'medium'
                            })

            # 基于组合策略的建议（降低阈值到3%提升）
            if 'combinations' in analysis_results:
                for combo_name, result in analysis_results['combinations'].items():
                    if result['win_rate'] > overall_win_rate + 0.03:
                        recommendations.append({
                            'type': '组合策略',
                            'strategy': f'{combo_name}: {" AND ".join(result["conditions"])}',
                            'expected_win_rate': result['win_rate'],
                            'improvement': result['improvement'],
                            'sample_count': result['sample_count'],
                            'confidence': 'high' if result['sample_count'] > 15 else 'medium'
                        })

            # 基于时间因子的建议
            if 'timing' in analysis_results:
                if 'day_of_week' in analysis_results['timing']:
                    best_day = max(analysis_results['timing']['day_of_week'].items(),
                                 key=lambda x: x[1]['win_rate'])
                    if best_day[1]['win_rate'] > overall_win_rate + 0.03:
                        recommendations.append({
                            'type': '时间因子',
                            'strategy': f'在{best_day[0]}买入',
                            'expected_win_rate': best_day[1]['win_rate'],
                            'improvement': best_day[1]['win_rate'] - overall_win_rate,
                            'sample_count': best_day[1]['sample_count'],
                            'confidence': 'medium'
                        })
            
            # 按胜率改善程度排序
            recommendations.sort(key=lambda x: x['improvement'], reverse=True)
            
            return recommendations[:10]  # 返回前10个最佳策略
            
        except Exception as e:
            print(f'❌ 生成策略建议失败: {e}')
            return []
    
    def _generate_detailed_report(self, analysis_results, recommendations, data):
        """生成详细报告"""
        try:
            print('\n📋 详细分析报告')
            print('=' * 80)
            
            overall_win_rate = data['is_win'].mean()
            total_trades = len(data)
            
            print(f'📊 总体统计:')
            print(f'  总交易数: {total_trades}')
            print(f'  整体胜率: {overall_win_rate:.2%}')
            print(f'  盈利交易: {data["is_win"].sum()}')
            print(f'  亏损交易: {total_trades - data["is_win"].sum()}')
            
            print(f'\n🏆 Top 5 最佳策略建议:')
            print('-' * 60)
            
            for i, rec in enumerate(recommendations[:5], 1):
                print(f'{i}. 【{rec["type"]}】{rec["strategy"]}')
                print(f'   预期胜率: {rec["expected_win_rate"]:.2%} (提升 {rec["improvement"]:.2%})')
                print(f'   样本数量: {rec["sample_count"]} (可信度: {rec["confidence"]})')
                print()
            
            # 保存详细结果到文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f'胜率分析报告_{timestamp}.txt'
            
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write('胜率分析详细报告\n')
                f.write('=' * 50 + '\n\n')
                f.write(f'分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                f.write(f'总交易数: {total_trades}\n')
                f.write(f'整体胜率: {overall_win_rate:.2%}\n\n')
                
                f.write('策略建议:\n')
                f.write('-' * 30 + '\n')
                for i, rec in enumerate(recommendations, 1):
                    f.write(f'{i}. {rec["strategy"]}\n')
                    f.write(f'   胜率: {rec["expected_win_rate"]:.2%} (提升: {rec["improvement"]:.2%})\n')
                    f.write(f'   样本: {rec["sample_count"]}\n\n')
            
            print(f'✅ 详细报告已保存: {report_filename}')
            
        except Exception as e:
            print(f'❌ 生成详细报告失败: {e}')

def main():
    """主函数"""
    print('📊 胜率分析器 - 基于买入指标分析')
    print('=' * 80)
    
    analyzer = WinRateAnalyzer()
    results = analyzer.analyze_win_rate_strategies()
    
    if results:
        print('\n🎉 分析完成!')
        print(f'📈 发现 {len(results["strategy_recommendations"])} 个高胜率策略')
        print(f'📊 基于 {results["data_summary"]["total_trades"]} 笔交易数据')
        print(f'🎯 当前整体胜率: {results["data_summary"]["overall_win_rate"]:.2%}')
        
        print('\n💡 下一步建议:')
        print('1. 查看生成的详细分析报告')
        print('2. 选择合适的高胜率策略')
        print('3. 在策略中应用这些条件')
        print('4. 回测验证策略效果')
    else:
        print('❌ 分析失败，请检查数据和配置')

if __name__ == "__main__":
    main()
