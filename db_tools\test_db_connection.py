#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

import os
import sys
import sqlite3
import traceback
from pathlib import Path

# 添加父目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from db_tools.config_loader import ConfigLoader
    config_loader = ConfigLoader()
    
    print("=" * 60)
    print("数据库连接测试")
    print("=" * 60)
    
    # 获取数据库文件路径
    db_file = config_loader.get_db_file()
    print(f"数据库文件路径: {db_file}")
    print(f"数据库文件是否存在: {os.path.exists(db_file)}")
    
    # 连接数据库
    print("\n尝试连接数据库...")
    conn = sqlite3.connect(db_file)
    print("成功连接到数据库！")
    
    # 获取数据库表信息
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("\n数据库中的表:")
    for table in tables:
        print(f"- {table[0]}")
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table[0]})")
        columns = cursor.fetchall()
        print(f"  列数: {len(columns)}")
        
        # 获取表中的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
        count = cursor.fetchone()[0]
        print(f"  记录数: {count}")
        print()
    
    # 关闭连接
    conn.close()
    print("数据库连接已关闭")
    
except Exception as e:
    print(f"测试过程中出错: {str(e)}")
    print("详细错误信息:")
    traceback.print_exc()

input("按回车键退出...") 