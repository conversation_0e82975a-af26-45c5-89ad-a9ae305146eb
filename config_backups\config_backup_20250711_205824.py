#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和交易策略配置文件
--------------------

此文件包含策略的所有可配置参数，支持动态加载，修改后自动生效。

基础配置:
- INDEX_SYMBOL: 参考指数代码
- SUBSCRIBE_ALL_MARKET: 是否订阅全市场股票
- CUSTOM_SUBSCRIBE_MODE: 是否启用自定义订阅模式
- CUSTOM_SUBSCRIBE_SYMBOLS: 自定义订阅的股票列表

性能优化相关配置:
- ENABLE_PARALLEL_COMPUTING: 是否启用并行计算
- PARALLEL_WORKERS: 并行计算的线程数
- CACHE_EXPIRES_SECONDS: 普通缓存的有效期(秒)
- LONG_CACHE_EXPIRES_SECONDS: 长期缓存的有效期(秒)
- MAX_ANALYSIS_STOCKS: 每次分析的最大股票数量

买入信号配置:
- ENABLE_BUY_SIGNALS: 是否启用买入信号
- ENABLE_TRIX_BUY_SIGNAL: 是否启用TRIX买入信号
- ENABLE_MA_CROSS_BUY_SIGNAL: 是否启用均线交叉买入信号
- ENABLE_REBOUND_BUY: 是否启用最低价反弹买入信号

卖出信号配置:
- ENABLE_SELL_SIGNALS: 是否启用卖出信号
- ENABLE_TRIX_SELL_SIGNAL: 是否启用TRIX卖出信号
- ENABLE_TRAILING_STOP: 是否启用跟踪止盈
- ENABLE_DYNAMIC_STOP_LOSS: 是否启用动态止损
"""

# =============================================================================
# 交易核心参数
# =============================================================================

# 基础参数
INDEX_SYMBOL = 'SHSE.000300'  # 参考指数
SUBSCRIBE_ALL_MARKET = False  # 是否订阅全市场股票，True表示订阅全市场，False表示只订阅参考指数成分股

# 自定义订阅股票配置
"""
自定义订阅股票配置说明:

- CUSTOM_SUBSCRIBE_MODE: 是否启用自定义订阅模式
  - 设为True时，系统将【只】订阅CUSTOM_SUBSCRIBE_SYMBOLS中指定的股票，完全不会订阅沪深300或其他指数成分股
  - 设为False时，系统将按照默认逻辑订阅指数成分股或全市场股票
  - 默认为False，即使用默认订阅逻辑
  - 注意：启用此模式会完全覆盖INDEX_SYMBOL和SUBSCRIBE_ALL_MARKET的设置

- CUSTOM_SUBSCRIBE_SYMBOLS: 自定义订阅的股票列表
  - 仅在CUSTOM_SUBSCRIBE_MODE为True时生效
  - 格式为股票代码列表，例如['SHSE.600519', 'SZSE.300750']
  - 股票代码前缀说明:
    * SHSE: 上海证券交易所
    * SZSE: 深圳证券交易所
  - 可以添加任意数量的股票，但建议控制在合理范围内
  - 如果列表为空，则不会订阅任何股票
"""
CUSTOM_SUBSCRIBE_MODE = False  # 是否启用自定义订阅模式
CUSTOM_SUBSCRIBE_SYMBOLS = [
    #'SHSE.600099',  # 贵州茅台
    #'SZSE.300750',  # 宁德时代
    #'SHSE.601318',  # 中国平安
    # 可以根据需要添加更多股票
]  # 自定义订阅的股票列表

# =============================================================================
# 股票过滤参数
# =============================================================================

# 股票类型过滤参数
FILTER_ST_STOCKS = True           # 是否过滤ST股票
FILTER_STARTUP_BOARD = True      # 是否过滤创业板股票(3开头)
FILTER_SCIENCE_BOARD = True       # 是否过滤科创板股票(688开头)
FILTER_BEIJING_BOARD = True      # 是否过滤北交所股票(8开头)

# 价格过滤参数
PRICE_FILTER_ENABLED = True       # 是否启用价格过滤
MIN_PRICE_FILTER = 1.0            # 最低价格过滤(元)
MAX_PRICE_FILTER = 10000.0          # 最高价格过滤(元)

# 持仓和资金管理参数
MAX_POSITIONS = 30            # 最大持仓数量
POSITION_RATIO = 0.9          # 仓位比例系数
SINGLE_POSITION_LIMIT = 150000 # 单只股票最大持仓金额（绝对值）
SINGLE_POSITION_RATIO = 0.2   # 单只股票最大持仓比例（占总资金比例）
DYNAMIC_POSITION_MAX_RATIO = 0.2 # 动态调整后单只股票最大持仓比例
MIN_POSITION_LIMIT = 11000    # 单只股票最小持仓金额（绝对值）
MIN_POSITION_RATIO = 0.05     # 单只股票最小持仓比例（占总资金比例）

T_PLUS_1 = True               # 是否遵循A股T+1交易规则

# =============================================================================
# 回测参数
# =============================================================================

# 回测参数
BACKTEST_START_TIME = '2025-02-01 09:30:00'
BACKTEST_END_TIME = '2025-06-30 15:00:00'
BACKTEST_INITIAL_CASH = 1000000
BACKTEST_COMMISSION_RATIO = 0.0003
BACKTEST_SLIPPAGE_RATIO = 0.0002

# 买入检查间隔模式设置
BUY_CHECK_INTERVAL_MODE = False  # 是否启用间隔模式进行买入检查
BUY_CHECK_INTERVAL_MINUTES = 1  # 买入检查的时间间隔(分钟)

# 信号检查和持仓更新参数

SIGNAL_CHECK_INTERVAL = 5     # 卖出信号计算间隔(分钟)
POSITION_CHECK_INTERVAL = 30  # 持仓更新间隔(分钟)

# =============================================================================
# 交易执行参数
# =============================================================================

# 订单类型参数

USE_MARKET_ORDER = True      # 是否使用市价单（True使用市价单，False使用限价单）
PRICE_ADJUST_RATIO = 0.002    # 价格调整比例（限价单时使用）

# =============================================================================
# 信号生成参数
# =============================================================================

# 历史数据获取参数

HISTORY_DATA_DAYS = 80        # 获取历史数据的天数（用于一般技术指标）
HISTORY_DATA_DAYS_VOLATILITY = 90  # 获取历史数据的天数（用于波动性计算）

# 历史数据处理参数
HISTORY_DATA_FORMAT = 'dataframe'  # 历史数据返回格式，可选 'dataframe' 或 'dict'
HISTORY_COLUMN_MAPPING = {         # 历史数据列名映射
    'symbol': 'symbol',
    'open': 'open',
    'high': 'high',
    'low': 'low',
    'close': 'close',
    'volume': 'volume',
    'amount': 'amount',
    'time': 'time'
}
ENABLE_COLUMN_RENAME = True        # 是否启用列名重命名
FIX_COLUMN_NAMES = True            # 是否修复错误的列名格式（如包含分隔符的列名）
USE_REALTIME_PRICE = True          # 是否使用实时价格替代当日收盘价计算TRIX等指标，解决回测与实盘效果不一致问题

# 信号开关参数
ENABLE_BUY_SIGNALS = True     # 是否启用买入信号

"""
买入信号参数说明:

- ENABLE_BUY_SIGNALS: 总开关，控制是否启用所有买入信号检测
  - 设为True时启用买入信号检测，False时完全禁用买入

- ENABLE_TRIX_BUY_SIGNAL: 是否启用TRIX买入信号
  - 当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX时触发

- ENABLE_MA_CROSS_BUY_SIGNAL: 是否启用均线交叉买入信号
  - 短期均线上穿中期均线时触发
  
- ENABLE_TRIX_REVERSAL_SIGNAL: 是否启用TRIX拐点买入信号
  - 昨日TRIX>前日TRIX且前日TRIX<大前日TRIX时触发
  
- ENABLE_TDX_BUY_SIGNAL: 是否启用通达信公式买入信号
  - 基于通达信公式进行买入判断，寻找价格触底企稳的机会
  - 结合价格走势和RSI指标进行判断
"""

# 买入信号参数
ENABLE_TRIX_BUY_SIGNAL = True # 是否启用TRIX买入信号（当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX）
ENABLE_MA_CROSS_BUY_SIGNAL =  False # 是否启用均线交叉买入信号
ENABLE_TRIX_REVERSAL_SIGNAL = False  # 是否启用TRIX拐点买入信号（昨日TRIX>前日TRIX且前日TRIX<大前日TRIX）
ENABLE_TDX_BUY_SIGNAL = False  # 是否启用通达信公式买入信号

# 通达信买入信号参数
"""
通达信买入信号参数说明:

- ENABLE_TDX_BUY_SIGNAL: 是否启用通达信买入信号
  该信号基于以下公式计算:
  L1:=(C+H+L)/3;            # 典型价（收盘价、最高价、最低价三者平均值）
  L2:=MA(L1,16);            # 典型价的16周期均线
  MA1:=LLV(C,3);            # 3周期收盘价最低值
  J1:=LLV(KDJ.J,16);        # KDJ指标J值的16周期最低值
  ZJ:LLV(MA1,15);           # MA1的15周期最低值（关键指标）
  BUY1:ZJ=REF(ZJ,1) AND REF(ZJ,1)<REF(ZJ,2); # 价格企稳形态
  R1:RSI.RSI1;              # RSI指标值
  买入条件: BUY1=1 AND R1>45  # 价格企稳且RSI>45

- TDX_RSI_THRESHOLD: 通达信买入信号中RSI指标的阈值
  默认为45，表示RSI需要大于45才能触发买入信号
  降低此值会增加买入机会但可能增加风险，提高此值会减少买入机会但可能更安全
  
- TDX_L1_PERIOD: 典型价的均线周期
  默认为16，影响L2计算

- TDX_MA1_PERIOD: 收盘价最低值的周期
  默认为3，影响MA1计算

- TDX_J1_PERIOD: KDJ.J最低值的周期
  默认为16，影响J1计算

- TDX_ZJ_PERIOD: MA1最低值的周期
  默认为15，影响ZJ计算

- TDX_RSI_PERIOD: RSI计算周期
  默认为14，影响RSI(R1)值计算

- TDX_DEBUG_MODE: 是否启用通达信买入信号的调试模式
  启用后会输出更多中间计算过程的日志
  默认为False
  
- TDX_BUY_FREQUENCY: 通达信买入信号计算使用的周期
  可选值: 'day'(日线), 'week'(周线), 'month'(月线)
  默认为'day'，即使用日线数据计算买入信号
  不同周期适合不同的交易风格:
    * 日线: 短期交易，适合波段操作，信号较频繁
    * 周线: 中期交易，适合中期趋势跟踪，信号相对稳定
    * 月线: 长期交易，适合长期趋势跟踪，信号最稳定但最少

- TDX_CHECK_MULTIPLE_FREQUENCIES: 是否检查多个周期的通达信买入信号
  设为True时，系统将同时检查日线、周线和月线的买入信号
  设为False时，只检查TDX_BUY_FREQUENCY指定的单一周期
  默认为False，只检查单一周期
  
- TDX_SIGNAL_COMBINE_MODE: 多周期信号组合模式
  仅在TDX_CHECK_MULTIPLE_FREQUENCIES为True时生效
  可选值:
    * 'any': 任一周期出现买入信号即触发买入(最宽松)
    * 'all': 所有周期都出现买入信号才触发买入(最严格)
    * 'specific': 只使用TDX_SPECIFIC_FREQUENCY指定的特定周期
  默认为'any'，即任一周期出现信号即可触发买入
  
- TDX_SPECIFIC_FREQUENCY: 在'specific'组合模式下使用的特定周期
  仅在TDX_SIGNAL_COMBINE_MODE为'specific'时生效
  可选值: 'day', 'week', 'month'
  默认为'day'，即使用日线数据
  
- HISTORY_DATA_DAYS_WEEKLY: 获取周线数据的历史天数
  用于计算周线指标时，历史数据的获取天数
  默认为400天，约合80个交易周
  
- HISTORY_DATA_DAYS_MONTHLY: 获取月线数据的历史天数
  用于计算月线指标时，历史数据的获取天数
  默认为1200天，约合40个交易月
"""
ENABLE_TDX_BUY_SIGNAL = True  # 是否启用通达信公式买入信号
TDX_RSI_THRESHOLD = 45  # 通达信买入信号中RSI指标的阈值
TDX_L1_PERIOD = 16  # 典型价的均线周期
TDX_MA1_PERIOD = 3  # 收盘价最低值的周期
TDX_J1_PERIOD = 16  # KDJ.J最低值的周期
TDX_ZJ_PERIOD = 15  # MA1最低值的周期
TDX_RSI_PERIOD = 14  # RSI计算周期
TDX_DEBUG_MODE = False  # 是否启用通达信买入信号的调试模式

# 通达信买入周期设置
TDX_BUY_FREQUENCY = 'week'  # 使用的周期，可选值: 'day', 'week', 'month'
TDX_CHECK_MULTIPLE_FREQUENCIES = False  # 是否检查多个周期的通达信买入信号
TDX_SIGNAL_COMBINE_MODE = 'any'  # 多周期信号组合模式: 'any', 'all', 'specific'
TDX_SPECIFIC_FREQUENCY = 'day'  # 在'specific'模式下使用的特定周期

# 周期性买入检查设置
ENABLE_WEEKLY_LAST_DAY_CHECK = True  # 是否在每周最后一个交易日进行周线买入检查
ENABLE_MONTHLY_LAST_DAY_CHECK = True  # 是否在每月最后一个交易日进行月线买入检查
PERIODIC_CHECK_TIMES = ['10:30:00', '14:00:00', '14:45:00']  # 周期性买入检查的时间点

# 不同周期的历史数据获取天数
HISTORY_DATA_DAYS_WEEKLY = 400  # 获取周线数据的历史天数，约80个交易周
HISTORY_DATA_DAYS_MONTHLY = 1200  # 获取月线数据的历史天数，约40个交易月

# 反弹买入策略参数
ENABLE_REBOUND_BUY = False       # 是否启用反弹买入策略
REBOUND_PERIOD = 15             # 监控最低价的天数
REBOUND_THRESHOLD = 0.016       # 反弹买入阈值（1.5%）
REBOUND_MAX_THRESHOLD = 0.03    # 反弹买入上限阈值（8%）
REBOUND_VOLUME_FILTER = False    # 是否启用成交量确认
REBOUND_VOLUME_RATIO = 0.6      # 买入时成交量应大于均值的倍数
REBOUND_MA_FILTER = False        # 是否使用均线过滤
REBOUND_MA_PERIOD = 5           # 均线过滤周期
REBOUND_WITH_MARKET_FILTER = False  # 是否考虑大盘环境
REBOUND_MIN_ATR_RATIO = 0.015   # 最小ATR比例(相对价格)
REBOUND_RECENT_LOW_DAYS = 2     # 最低价必须在最近几天内出现


# TRIX指标参数
TRIX_EMA_PERIOD = 2           # TRIX买入信号的EMA周期
USE_TALIB_TRIX = False         # 是否使用talib直接计算TRIX
TRIX_REVERSAL_PERIOD = 3       # TRIX拐点信号的周期设置（使用不同的周期）

# 均线参数
MA_SHORT_PERIOD = 5          # 短期均线周期
MA_MID_PERIOD = 8            # 中期均线周期
MA_LONG_PERIOD = 20           # 长期均线周期

# 技术指标参数
RSI_PERIOD = 14               # RSI计算周期
BOLL_PERIOD = 20              # 布林带计算周期

# =============================================================================
# 波动性相关参数
# =============================================================================

# 波动性筛选参数
VOLATILITY_PERIOD = 7        # 计算波动性的周期
VOLATILITY_THRESHOLD = 1.0    # 波动性阈值(相对市场波动率的倍数)
ATR_THRESHOLD = 1.5           # ATR阈值(占收盘价的百分比)
VOLATILITY_WEIGHT = 0.7       # 波动率在综合得分中的权重
ATR_WEIGHT = 0.3              # ATR在综合得分中的权重
MIN_ABSOLUTE_VOLATILITY = 0.1 # 最小绝对波动率阈值(%)

# 初始市场波动率(后续会动态更新)
DEFAULT_MARKET_VOLATILITY = 2.0

# 波动性资金调整参数
MAX_VOLATILITY_FACTOR = 3.0   # 最大波动性资金调整因子
MIN_VOLATILITY_FACTOR = 1.0   # 最小波动性资金调整因子
VOLATILITY_FACTOR_SCALE = 1.5 # 波动性因子缩放系数

# 波动性计算错误处理
SKIP_VOLATILITY_ERROR_STOCKS = True  # 是否跳过波动性计算出错的股票
DEFAULT_VOLATILITY_VALUE = 2.5       # 当计算出错时使用的默认波动性值
MAX_VOLATILITY_ERRORS = 50           # 每日最大允许的波动性计算错误数量
LOG_VOLATILITY_ERRORS = True         # 是否记录波动性计算错误

# =============================================================================
# 性能优化参数
# =============================================================================

# 性能优化总开关
"""
性能优化总开关说明:
- ENABLE_PERFORMANCE_OPTIMIZATION: 是否启用性能优化设置
  - 设为True时，将应用以下性能优化设置:
    * 禁用额外的风险检查(RISK_CHECK_ENABLED=False)
    * 限制每次买入检查的最大买入股票数量(MAX_BUY_BATCH)
  - 设为False时，将使用更频繁的检查，可能提高交易及时性但增加系统负担
  - 默认为False，在系统负载较高时可考虑设为True
"""
ENABLE_PERFORMANCE_OPTIMIZATION = False  # 是否启用性能优化设置

# ==============================================
# 性能分析参数
# ==============================================
"""
性能分析参数说明:
- ENABLE_PERFORMANCE_PROFILING: 是否启用性能分析
  - 设为True时，系统将记录关键函数的执行时间
  - 设为False时，不会进行性能分析，无额外开销
  - 仅在需要排查性能瓶颈时启用
  
- PROFILING_OUTPUT_FILE: 性能分析结果输出文件
  - 设置为None时输出到日志
  - 设置为文件路径时，将结果写入文件

- PROFILING_THRESHOLD_MS: 性能分析阈值(毫秒)
  - 仅记录执行时间超过此阈值的函数调用
  - 设置较低值可捕获更多信息，但会增加输出量
  - 建议值: 回测模式10ms，实盘模式50ms

- PROFILING_TOP_FUNCTIONS: 性能分析结果展示的函数数量
  - 每次汇总仅展示最耗时的N个函数
  - 设为0则展示所有记录的函数
"""
ENABLE_PERFORMANCE_PROFILING = False  # 是否启用性能分析
PROFILING_OUTPUT_FILE = "data/performance_profile.log"  # 性能分析结果输出文件，指定输出到data目录下
PROFILING_THRESHOLD_MS = 10  # 性能分析阈值(毫秒)，仅记录执行时间超过此阈值的函数调用
PROFILING_TOP_FUNCTIONS = 20  # 性能分析结果展示的函数数量
PROFILING_SUMMARY_INTERVAL = 30  # 性能分析结果汇总间隔(秒)

# ==============================================
# 买入检查时间点配置
# ==============================================
"""
买入检查时间点配置说明:
- BUY_CHECK_TIMES: 每日固定时间点进行买入检查(24小时制，格式:'HH:MM:SS')
  - 系统会在这些指定的时间点调用buy_strategy函数检查买入条件
  - 时间点越多，买入机会越多，但也会增加系统计算负担
  - 时间点设置建议:
    * 开盘后不久(如9:31)可以捕捉开盘机会
    * 上午和下午各设置2-3个检查点较为合理
    * 尾盘时间(如14:30)可以捕捉日内趋势确认的机会
  - 交易风格对应的配置建议:
    * 高频交易: 每15分钟一个检查点(如9:31,9:45,10:00...)
    * 中频交易: 当前默认配置(7个时间点)
    * 低频交易: 每天3-4个检查点(如9:45,11:00,14:00,14:45)
  
  注意: 修改此配置后，如果使用动态配置模式，会在下次配置重载时(09:00或13:00)生效
  如果使用静态配置模式，则需要重启策略才能生效

- BUY_CHECK_INTERVAL_MODE: 是否启用间隔模式进行买入检查
  - 如果设为True，系统将按照BUY_CHECK_INTERVAL_MINUTES指定的分钟间隔进行买入检查
  - 如果设为False，系统将按照BUY_CHECK_TIMES指定的固定时间点进行买入检查
  - 默认为False，使用固定时间点模式

- BUY_CHECK_INTERVAL_MINUTES: 买入检查的时间间隔(分钟)
  - 仅在BUY_CHECK_INTERVAL_MODE为True时生效
  - 系统将从开盘后(9:30)开始，每隔指定的分钟数进行一次买入检查
  - 建议值: 高频交易15分钟，中频交易30分钟，低频交易60分钟
  - 设置过小的值(<10分钟)可能会导致系统负担过重
  - 交易时段范围: 9:30-11:30, 13:00-15:00
"""
# 可以根据需要增加、减少或修改这些时间点
BUY_CHECK_TIMES = [
    '14:00:00',  # 开盘后1分钟，可以捕捉开盘行情
   # '10:00:00',  # 上午10点，早盘趋势初步形成
   # '10:30:00',  # 上午10点半，早盘中段
   # '11:00:00',  # 上午11点，接近午盘前
   # '13:30:00',  # 午盘开始后，可以捕捉午盘开盘行情
   # '14:00:00',  # 下午2点，下午盘趋势形成
   # '14:30:00'   # 下午2点半，尾盘前最后机会
]



# ==============================================
# 持仓摘要输出时间点配置
# ==============================================

# 可以根据需要增加、减少或修改这些时间点
POSITION_SUMMARY_TIMES = [
    #'10:30:00',  # 上午10点半，早盘中段
    #'14:30:00'   # 下午2点半，尾盘前
]

# ==============================================
# 其他性能优化参数
# ==============================================

MAX_BUY_BATCH = 20            # 每次最多买入的股票数量
MAX_ANALYSIS_STOCKS = 300     # 每次分析的最大股票数量
RISK_CHECK_ENABLED = False    # 是否启用风险检查
CACHE_EXPIRE_SECONDS = 3600   # 缓存过期时间(秒)





# =============================================================================
# 日志和数据管理参数
# =============================================================================

# 日志参数
LOG_LEVEL = 'INFO'            # 日志级别
ENABLE_CSV_LOGGING = False    # 禁用CSV日志写入，只保留数据库写入功能

# CSV数据解析参数
CSV_DELIMITER = ','           # CSV文件分隔符
CSV_PARSE_DATES = True        # 是否将日期列解析为日期时间对象
CSV_HEADER = 0                # CSV文件头行索引，0表示第一行是列名
CSV_INDEX_COL = None          # 用作索引的列，None表示使用默认索引

# CSV列名修复参数
FIX_CSV_COLUMN_NAMES = True   # 是否修复CSV列名问题
CSV_COLUMN_SPLIT_CHAR = ','   # CSV列名中可能存在的错误分隔符
EXPECTED_COLUMNS = ['symbol', 'open', 'high', 'low', 'close', 'volume', 'amount', 'time']  # 预期的列名列表
HANDLE_MISSING_COLUMNS = True # 是否处理缺失列（用默认值填充）

# =============================================================================
# API连接参数
# =============================================================================

# API凭证(默认值，可被命令行参数覆盖)
DEFAULT_STRATEGY_ID = '39da9282-3bbd-11f0-8755-d4e98a5e8c02'
DEFAULT_TOKEN = '927022466b9f6476ef82fe30991f521c61feac74'


# ==============================================
# 模式切换与订阅管理参数
# ==============================================
"""
模式切换与订阅管理参数说明:

- ENABLE_MODE_ADAPTIVE: 模式自适应功能设置
  - 0: 启用自动模式自适应（系统自动判断当前运行环境）
  - 1: 强制使用回测模式订阅策略（一次性订阅所有股票）
  - 2: 强制使用模拟盘模式订阅策略（使用批次订阅，批次大小较大）
  - 3: 强制使用实盘模式订阅策略（使用批次订阅，批次大小较小，更频繁轮换）
  - 推荐使用0，让系统自动判断运行环境并优化订阅策略

- SUBSCRIPTION_ROTATION_ENABLED: 是否启用订阅轮换机制
  - 仅在实盘/模拟盘模式下生效，回测模式会忽略此设置
  - 如果设为True，系统将采用轮换订阅机制，每隔一定时间轮换订阅一批股票
  - 如果设为False，系统将尝试一次性订阅所有股票(可能受平台限制)
  - 默认为True，推荐开启以避免超过平台订阅限制

- SUBSCRIPTION_ROTATION_INTERVAL: 订阅轮换间隔(分钟)
  - 仅在SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制系统轮换订阅批次的时间间隔
  - 默认为1分钟，表示每分钟轮换一批股票
  - 降低此值可以更快地轮换完所有股票，但会增加API调用频率
  - 提高此值可以减少API调用，但会延长轮换完所有股票的时间

- LIVE_MODE_BATCH_SIZE: 实盘/模拟盘模式下的批次大小
  - 仅在实盘/模拟盘模式且SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制每批次订阅的股票数量
  - 默认为100，表示每批次订阅100只股票
  - 需要根据平台的订阅限制进行设置，通常掘金量化平台限制为500个订阅
  - 设置过大可能导致超过平台限制，设置过小会增加轮换次数

- MAX_SUBSCRIPTION_LIMIT: 平台最大订阅限制
  - 平台允许的最大订阅数量
  - 默认为50，表示平台最多允许同时订阅50个标的
  - 此参数用于系统自动计算批次数量，确保不超过平台限制
  - 如果平台限制有变化，请相应调整此参数

- SYNC_BUY_CHECK_WITH_ROTATION: 是否将买入检查与订阅轮换同步
  - 仅在实盘/模拟盘模式、SUBSCRIPTION_ROTATION_ENABLED为True且BUY_CHECK_INTERVAL_MODE为True时生效
  - 如果设为True，系统将在每次轮换订阅时同步检查当前批次的买入信号
  - 如果设为False，买入检查将按照BUY_CHECK_INTERVAL_MINUTES设置的间隔独立执行
  - 默认为True，推荐开启以优化性能，避免重复计算
  - 此参数主要用于高频交易场景，如果您使用的是中低频策略，可以设为False

- BACKTEST_FULL_SUBSCRIPTION: 回测模式是否一次性订阅所有股票
  - 仅在回测模式下生效
  - 如果设为True，回测模式将一次性订阅所有股票池中的股票
  - 如果设为False，回测模式也将使用轮换订阅机制(通常不推荐)
  - 默认为True，推荐开启以充分利用回测模式的无限制特性
  - 只有在特殊情况下(如模拟实盘环境进行回测)才需要设为False
"""
ENABLE_MODE_ADAPTIVE = 0           # 模式自适应功能(0:自动 1:回测 2:模拟盘 3:实盘)
SUBSCRIPTION_ROTATION_ENABLED = True  # 启用订阅轮换机制
SUBSCRIPTION_ROTATION_INTERVAL = 1    # 订阅轮换间隔(分钟)
LIVE_MODE_BATCH_SIZE = 50             # 实盘/模拟盘模式下的批次大小（根据账户限制设置为10）
MAX_SUBSCRIPTION_LIMIT = 50           # 平台最大订阅限制（根据账户实际限制调整为50）
MAX_SUBSCRIPTION_PER_BATCH = 50       # 每次订阅操作最多的股票数量
SYNC_BUY_CHECK_WITH_ROTATION = True   # 将买入检查与订阅轮换同步
BACKTEST_FULL_SUBSCRIPTION = True     # 回测模式一次性订阅所有股票

# 控制回测模式是否只在开始时订阅一次，后续跳过订阅刷新
# 设置为True时，回测模式下只在第一次订阅股票，后续的订阅刷新操作将被跳过
# 这可以减少回测过程中的重复订阅操作，提高回测效率
# 设置为False时，回测模式下会按照常规逻辑进行订阅刷新
BACKTEST_SUBSCRIBE_ONCE = True        # 回测模式只在开始时订阅一次

# 您可以根据需要添加更多参数... 

# 启用并行计算功能
ENABLE_PARALLEL_COMPUTING = True

# 并行计算的线程数
PARALLEL_WORKERS = 16

# 常规缓存有效期（秒）
CACHE_EXPIRES_SECONDS = 1800  # 30分钟

# 长期缓存有效期（秒）
LONG_CACHE_EXPIRES_SECONDS = 86400  # 24小时

# 历史数据管理配置
HISTORY_CACHE_SIZE = 50  # 历史数据缓存大小（每个频率的最大条目数）
ENABLE_HISTORY_PREFETCH = True  # 是否启用历史数据预获取
HISTORY_PREFETCH_DAYS = 60  # 预获取的历史数据天数
HISTORY_PREFETCH_BATCH_SIZE = 50  # 每批预获取的股票数量

# =============================================================================
# 异常处理参数
# =============================================================================

# 通用异常处理
MAX_RETRY_COUNT = 3           # 操作失败时的最大重试次数
RETRY_DELAY_SECONDS = 1       # 重试前的延迟时间(秒)
ENABLE_GRACEFUL_ERROR_HANDLING = True  # 启用优雅的错误处理

# 数据解析异常处理
HANDLE_PARSE_ERRORS = True    # 是否处理数据解析异常
SKIP_PROBLEMATIC_STOCKS = True  # 遇到解析问题时是否跳过该股票
MAX_PARSE_ERRORS_PER_DAY = 100  # 每日最大允许的解析错误数量
LOG_PARSE_ERRORS = True       # 是否记录解析错误 

# 特定错误处理
FIX_COLUMN_INDEX_ERROR = True  # 修复"None of [Index...] are in the [columns]"类型的错误
COLUMN_ERROR_ACTION = 'rename'  # 列错误处理方式: 'rename'(重命名), 'skip'(跳过), 'default'(使用默认值)

# =============================================================================
# 卖出信号配置
# =============================================================================

# 卖出信号总开关
ENABLE_SELL_SIGNALS = True    # 是否启用卖出信号

# 跟踪止盈卖出信号
ENABLE_TRAILING_STOP = True   # 是否启用跟踪止盈卖出信号
TRAILING_STOP = 0.035         # 跟踪止盈阈值(1.5%)

# 固定止盈卖出信号
ENABLE_FIXED_PROFIT_STOP = False  # 是否启用固定止盈卖出信号
FIXED_PROFIT_RATIO = 5.36        # 固定止盈比例(10%)
FIXED_PROFIT_PRIORITY = 1.5      # 固定止盈优先级(介于跟踪止盈和动态止损之间)

"""
固定止盈与移动止盈的区别与优势说明:

1. 固定止盈(Fixed Profit Stop):
   - 原理: 当股票价格上涨达到预设的固定盈利比例时(如10%)触发卖出
   - 特点: 目标价格固定，一旦达到即触发卖出
   - 优势: 确保在达到特定盈利目标时锁定利润，不受后续价格波动影响
   - 适用场景: 适合短期交易、波动较大的市场或有明确盈利目标的交易

2. 移动止盈(Trailing Stop):
   - 原理: 根据股票价格的上涨动态调整止盈点，当价格从最高点回落特定比例(如1.5%)时触发卖出
   - 特点: 目标价格随市场价格变动而移动，会随着股价上涨而抬高
   - 优势: 允许利润持续增长，同时保护已实现的部分利润，最大化趋势行情收益
   - 适用场景: 适合中长期趋势交易、持续上涨行情

3. 两者结合使用的优势:
   - 双重保障: 既能在达到固定盈利目标时锁定利润，又能在趋势行情中追踪最大收益
   - 灵活性: 根据不同市场环境和个股特性，可能先触发固定止盈或移动止盈
   - 风险控制: 提供更全面的盈利管理策略，避免因单一止盈方式带来的局限性

4. 参数设置建议:
   - 固定止盈比例(FIXED_PROFIT_RATIO): 一般设置为10%-30%，根据个人风险偏好调整
   - 移动止盈阈值(TRAILING_STOP): 一般设置为1%-5%，波动性大的股票可适当放宽
   - 优先级设置: 通常移动止盈优先级高于固定止盈，以便在趋势行情中充分获利
"""

# TRIX死叉卖出信号
ENABLE_TRIX_SELL_SIGNAL = False # 是否启用TRIX死叉卖出信号
TRIX_SELL_EMA_PERIOD = 7      # TRIX卖出信号的EMA周期

# 动态止损卖出信号
ENABLE_DYNAMIC_STOP_LOSS = True # 是否启用动态止损卖出信号
DYNAMIC_STOP_LOSS_RATIO = 0.015  # 动态止损比例(1.5%)

# 固定止损卖出信号
ENABLE_FIXED_STOP_LOSS = True   # 是否启用固定止损卖出信号
FIXED_STOP_LOSS_RATIO = 0.005    # 固定止损比例(3%)

# 卖出信号检查间隔(分钟)  ``
SELL_SIGNAL_CHECK_INTERVAL = 1  # 卖出信号检查的时间间隔

# 最小持仓时间(天)
MIN_HOLDING_DAYS = 1          # 最小持仓天数，用于T+1规则

# 最大持仓时间(天)
MAX_HOLDING_DAYS = 25         # 最大持仓天数，超过此天数将触发卖出信号

# 卖出信号优先级
SELL_SIGNAL_PRIORITY = {
    'trailing_stop': 1,       # 跟踪止盈优先级最高
    'fixed_profit_stop': 1.5, # 固定止盈优先级次之
    'fixed_stop_loss': 1.8,   # 固定止损优先级第三
    'dynamic_stop_loss': 2,   # 动态止损再次之
    'trix_death_cross': 3,    # TRIX死叉优先级最低
    'max_holding_days': 2.5   # 最大持仓天数优先级
}

# 卖出信号组合模式
SELL_SIGNAL_MODE = 'any'      # 'any': 任一信号触发即卖出, 'all': 所有信号都触发才卖出

# 卖出信号日志记录
ENABLE_SELL_SIGNAL_LOG = True # 是否记录卖出信号日志
SELL_SIGNAL_LOG_LEVEL = 'INFO' # 卖出信号日志级别

# =============================================================================
# 持仓摘要配置
# =============================================================================

ENABLE_POSITION_SUMMARY = False    # 是否启用持仓摘要
POSITION_SUMMARY_TIMES = [         # 持仓摘要输出时间点
    '10:30:00',  # 上午输出时间点
    '14:30:00'   # 下午输出时间点
]
POSITION_SUMMARY_DETAIL_LEVEL = 'normal'  # 持仓摘要详细程度
POSITION_SUMMARY_FOR_EMPTY = False  # 是否在无持仓时输出摘要

# 性能优化配置 

# =============================================================================
# 反弹买入策略配置说明
# =============================================================================
"""
反弹买入策略配置说明:

这是一个与移动止盈策略相反的买入策略，通过监控股票从最低价反弹的程度来产生买入信号。

- ENABLE_REBOUND_BUY: 是否启用反弹买入策略
  - 设为True时启用反弹买入信号检测
  - 设为False时禁用此类信号

- REBOUND_PERIOD: 监控最低价的天数
  - 在这个周期内寻找最低价点
  - 建议值为5-20天，视市场状况调整
  - 较短周期(5-10天)适合快速反应的交易
  - 较长周期(10-20天)适合中期趋势反转捕捉

- REBOUND_THRESHOLD: 反弹买入阈值
  - 股价从最低点反弹超过此比例时触发信号
  - 默认为0.025(2.5%)
  - 设置过小会产生过多信号，过大可能错过机会
  - 建议根据个股波动特性调整，高波动股票可设置更高值

- REBOUND_MAX_THRESHOLD: 反弹买入上限阈值
  - 股价从最低点反弹超过此比例时不再触发买入信号
  - 默认为0.08(8%)
  - 设置此上限可避免追高买入，控制风险
  - 建议根据市场环境和个股特性调整，震荡市可设置较低值(如5%)，强势市场可适当放宽(如10%)

- REBOUND_VOLUME_FILTER: 是否启用成交量确认
  - 设为True时，要求成交量配合价格反弹
  - 设为False时，仅考虑价格因素
  - 通常建议开启以避免无量反弹陷阱

- REBOUND_VOLUME_RATIO: 买入时成交量应大于均值的倍数
  - 仅在REBOUND_VOLUME_FILTER为True时生效
  - 当日成交量需大于N日平均成交量的倍数
  - 默认为1.5倍，表示比近期平均成交量放大50%

- REBOUND_MA_FILTER: 是否使用均线过滤
  - 设为True时，需要股价站上指定均线
  - 设为False时，不考虑均线位置
  - 建议开启以避免在下跌趋势中过早买入

- REBOUND_MA_PERIOD: 均线过滤周期
  - 仅在REBOUND_MA_FILTER为True时生效
  - 默认使用5日均线作为参考
  - 建议短期交易使用5日或10日，中期交易使用20日

- REBOUND_WITH_MARKET_FILTER: 是否考虑大盘环境
  - 设为True时，会参考大盘指数走势
  - 设为False时，只考虑个股情况
  - 建议开启以避免在大盘下跌过程中买入

- REBOUND_MIN_ATR_RATIO: 最小ATR比例
  - 要求股票的波动性(ATR)达到价格的一定比例
  - 过低的波动率可能导致反弹信号不明显
  - 默认为0.015(1.5%)，表示ATR至少达到价格的1.5%
""" 