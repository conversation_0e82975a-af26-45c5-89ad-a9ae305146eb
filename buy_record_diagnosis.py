# coding=utf-8
"""
买入记录缺失诊断脚本
分析为什么数据库中没有买入记录
"""

import sqlite3
import os
import re

def check_database_structure():
    """检查数据库结构"""
    print('🔍 数据库结构检查')
    print('=' * 50)
    
    db_files = ['trading_data.db', 'data/trades.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f'\n📁 检查数据库: {db_file}')
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 检查表结构
                cursor.execute("PRAGMA table_info(trades)")
                columns = cursor.fetchall()
                
                if columns:
                    print(f'  表结构 (共{len(columns)}个字段):')
                    for col in columns:
                        print(f'    {col[1]} ({col[2]})')
                    
                    # 检查action字段的值
                    cursor.execute("SELECT DISTINCT action FROM trades")
                    actions = cursor.fetchall()
                    print(f'  action字段的值: {[a[0] for a in actions]}')
                    
                    # 统计各种操作的数量
                    cursor.execute("SELECT action, COUNT(*) FROM trades GROUP BY action")
                    action_counts = cursor.fetchall()
                    print(f'  操作统计:')
                    for action, count in action_counts:
                        print(f'    {action}: {count}条')
                else:
                    print(f'  ❌ 表不存在或为空')
                
                conn.close()
                
            except Exception as e:
                print(f'  ❌ 检查失败: {e}')

def analyze_buy_record_flow():
    """分析买入记录流程"""
    print('\n🔍 买入记录流程分析')
    print('=' * 50)
    
    # 检查main.py中的买入记录保存逻辑
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 买入记录保存流程检查:')
        
        # 1. 检查买入执行函数
        if 'execute_backup_buy_logic' in content:
            print('  ✅ 发现买入执行函数: execute_backup_buy_logic')
        else:
            print('  ❌ 未发现买入执行函数')
        
        # 2. 检查买入记录保存函数
        if 'save_original_buy_record' in content:
            print('  ✅ 发现买入记录保存函数: save_original_buy_record')
        else:
            print('  ❌ 未发现买入记录保存函数')
        
        # 3. 检查数据管理器调用
        data_manager_calls = re.findall(r'context\.data_manager\.save_trade', content)
        print(f'  数据管理器调用次数: {len(data_manager_calls)}')
        
        # 4. 检查直接保存调用
        save_analysis_calls = re.findall(r'save_analysis\(', content)
        print(f'  直接保存调用次数: {len(save_analysis_calls)}')
        
        # 5. 检查买入记录构建
        buy_record_pattern = r"'Action':\s*'BUY'"
        buy_record_constructions = re.findall(buy_record_pattern, content)
        print(f'  买入记录构建次数: {len(buy_record_constructions)}')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_data_manager_configuration():
    """检查数据管理器配置"""
    print('\n🔍 数据管理器配置检查')
    print('=' * 50)
    
    try:
        with open('scripts/data_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查字段映射
        print('📊 字段映射检查:')
        
        # 查找TRADE_FIELDNAMES定义
        trade_fields_match = re.search(r'TRADE_FIELDNAMES\s*=\s*\[(.*?)\]', content, re.DOTALL)
        if trade_fields_match:
            fields_text = trade_fields_match.group(1)
            # 提取字段名
            fields = re.findall(r"'([^']+)'", fields_text)
            print(f'  定义的字段数量: {len(fields)}')
            
            # 检查关键字段
            key_fields = ['Action', 'Symbol', 'Price', 'Volume', 'Timestamp']
            for field in key_fields:
                if field in fields:
                    print(f'  ✅ 关键字段 {field}: 存在')
                else:
                    print(f'  ❌ 关键字段 {field}: 缺失')
        
        # 检查数据库文件路径
        db_file_match = re.search(r"DB_FILE\s*=\s*['\"]([^'\"]+)['\"]", content)
        if db_file_match:
            db_path = db_file_match.group(1)
            print(f'  数据库文件路径: {db_path}')
            print(f'  文件是否存在: {os.path.exists(db_path)}')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def check_field_mapping_issue():
    """检查字段映射问题"""
    print('\n🔍 字段映射问题检查')
    print('=' * 50)
    
    # 检查买入记录构建中的字段名
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找买入记录构建代码
        buy_record_pattern = r"buy_record\s*=\s*{(.*?)}"
        matches = re.findall(buy_record_pattern, content, re.DOTALL)
        
        if matches:
            print('📊 买入记录字段映射:')
            for i, match in enumerate(matches, 1):
                print(f'  买入记录构建 {i}:')
                # 提取字段映射
                field_mappings = re.findall(r"'([^']+)':\s*([^,}]+)", match)
                for field, value in field_mappings:
                    print(f'    {field}: {value.strip()}')
        
        # 检查数据库中实际的字段名
        if os.path.exists('data/trades.db'):
            conn = sqlite3.connect('data/trades.db')
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(trades)")
            db_columns = cursor.fetchall()
            
            print(f'\n📊 数据库实际字段:')
            db_field_names = [col[1] for col in db_columns]
            for field in db_field_names:
                print(f'    {field}')
            
            # 检查字段名匹配问题
            print(f'\n🔍 字段名匹配检查:')
            expected_fields = ['Action', 'Symbol', 'Price', 'Volume', 'Timestamp']
            for field in expected_fields:
                if field.lower() in [f.lower() for f in db_field_names]:
                    matching_field = next(f for f in db_field_names if f.lower() == field.lower())
                    if matching_field == field:
                        print(f'  ✅ {field}: 完全匹配')
                    else:
                        print(f'  ⚠️ {field}: 大小写不匹配 (数据库中为: {matching_field})')
                else:
                    print(f'  ❌ {field}: 不存在')
            
            conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def suggest_solutions():
    """建议解决方案"""
    print('\n💡 问题诊断和解决建议')
    print('=' * 50)
    
    solutions = [
        {
            'problem': '字段名大小写不匹配',
            'description': '买入记录中使用 Action，数据库中是 action',
            'solution': '修改买入记录构建中的字段名为小写'
        },
        {
            'problem': '数据库文件路径不一致',
            'description': '代码保存到 data/trades.db，但胜率分析器查找 trading_data.db',
            'solution': '统一数据库文件路径或修改胜率分析器路径'
        },
        {
            'problem': '买入记录保存失败',
            'description': '买入记录保存过程中出现异常但未被记录',
            'solution': '增强异常处理和日志记录'
        },
        {
            'problem': '数据管理器未正确初始化',
            'description': 'context.data_manager 可能为 None',
            'solution': '检查数据管理器初始化逻辑'
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f'{i}. {solution["problem"]}')
        print(f'   问题: {solution["description"]}')
        print(f'   解决: {solution["solution"]}')
        print()

def main():
    """主函数"""
    print('🔍 买入记录缺失诊断报告')
    print('=' * 60)
    
    # 检查数据库结构
    check_database_structure()
    
    # 分析买入记录流程
    analyze_buy_record_flow()
    
    # 检查数据管理器配置
    check_data_manager_configuration()
    
    # 检查字段映射问题
    check_field_mapping_issue()
    
    # 建议解决方案
    suggest_solutions()
    
    print(f'\n📋 诊断总结:')
    print('=' * 40)
    print('🔍 已完成买入记录缺失的全面诊断')
    print('💡 发现了字段映射和路径不一致问题')
    print('🔧 建议按照上述方案进行修复')
    print('📊 修复后重新运行策略进行测试')

if __name__ == '__main__':
    main()
