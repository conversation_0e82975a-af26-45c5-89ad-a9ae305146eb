# coding=utf-8
"""
策略工具函数模块
统一管理重复使用的工具函数，保持功能逻辑完整
"""

import time
import numpy as np
import pandas as pd
from datetime import datetime
from config import get_config_value

class StrategyUtils:
    """策略工具类 - 统一管理常用功能"""
    
    def __init__(self, context):
        self.context = context
        self._log_config_cache = None
        self._log_config_cache_time = 0
        
    # ==================== 日志工具 ====================
    
    def get_timestamp(self):
        """获取格式化时间戳"""
        return self.context.now.strftime('%Y-%m-%d %H:%M:%S')
    
    def log_with_timestamp(self, level, message):
        """带时间戳的日志记录"""
        timestamp = self.get_timestamp()
        log_func = getattr(self.context.log, level)
        log_func(f"{timestamp} - {message}")
    
    def get_log_config(self):
        """获取日志配置（带缓存）"""
        current_time = time.time()
        # 缓存5秒，避免频繁读取配置
        if (self._log_config_cache is None or 
            current_time - self._log_config_cache_time > 5):
            
            try:
                logging_config = get_config_value('LOGGING_CONFIG', {})
                logging_presets = get_config_value('LOGGING_PRESETS', {})
                
                log_mode = logging_config.get('mode', 1)
                if log_mode in logging_presets:
                    current_log_config = logging_presets[log_mode].copy()
                    current_log_config.update(logging_config)
                else:
                    current_log_config = logging_presets[1].copy()
                    current_log_config.update(logging_config)
                
                self._log_config_cache = current_log_config
                self._log_config_cache_time = current_time
            except:
                # 默认配置
                self._log_config_cache = {
                    'show_progress': True,
                    'show_debug_details': False,
                    'show_data_format_check': False,
                    'show_trix_calculation': False,
                    'show_individual_results': False,
                    'progress_interval': 100,
                    'max_individual_logs': 0,
                }
        
        return self._log_config_cache
    
    def should_log(self, log_type, index=0):
        """判断是否应该记录日志"""
        config = self.get_log_config()
        
        if log_type == 'progress':
            return config.get('show_progress', True)
        elif log_type == 'debug':
            return config.get('show_debug_details', False)
        elif log_type == 'data_format':
            return config.get('show_data_format_check', False) and index < config.get('max_individual_logs', 0)
        elif log_type == 'trix_calc':
            return config.get('show_trix_calculation', False) and index < config.get('max_individual_logs', 0)
        elif log_type == 'individual':
            return config.get('show_individual_results', False) and index < config.get('max_individual_logs', 0)
        elif log_type == 'analysis_detail':
            return config.get('show_debug_details', False) and index < config.get('max_individual_logs', 0)
        
        return False
    
    # ==================== 数据获取工具 ====================
    
    def get_stock_data_unified(self, symbol, count=60, fields=None, use_cache=True):
        """统一的股票数据获取接口"""
        if fields is None:
            fields = ['open', 'high', 'low', 'close', 'volume']
        
        try:
            # 检查高级优化器缓存
            if (use_cache and hasattr(self.context, 'advanced_optimizer') and 
                self.context.advanced_optimizer):
                cached_data = self.context.advanced_optimizer.get_cached_data(
                    symbol, count, '1d', fields
                )
                if cached_data is not None:
                    return cached_data
            
            # 获取数据
            from main import history_n
            data = history_n(
                symbol=symbol, 
                count=count, 
                frequency='1d', 
                fields=fields, 
                end_time=self.context.now
            )
            
            # 缓存数据
            if (use_cache and data is not None and 
                hasattr(self.context, 'advanced_optimizer') and 
                self.context.advanced_optimizer):
                self.context.advanced_optimizer.cache_data(
                    symbol, count, '1d', fields, data
                )
            
            return data
            
        except Exception as e:
            if self.should_log('debug'):
                self.log_with_timestamp('error', f"获取{symbol}数据失败: {e}")
            return None
    
    def process_realtime_price(self, symbol, data, analysis_index=0):
        """处理实时价格替代逻辑"""
        try:
            use_realtime_price = get_config_value('USE_REALTIME_PRICE', True)
            if not use_realtime_price:
                return data
            
            # 检查是否在交易时间
            from main import is_trading_hour
            if not is_trading_hour(self.context.now.time()):
                return data
            
            # 获取当前价格
            from main import current
            current_data = current(symbols=symbol)
            if not current_data or len(current_data) == 0:
                return data
            
            current_price = current_data[0]['price']
            
            # 替换最后一个收盘价
            if hasattr(data, 'columns'):
                original_price = data['close'].iloc[-1]
                data = data.copy()
                data.loc[data.index[-1], 'close'] = current_price
            else:
                original_price = data[-1]['close']
                data = data.copy()
                data[-1]['close'] = current_price
            
            # 记录日志
            if (self.should_log('individual', analysis_index) or 
                self.should_log('progress')):
                self.log_with_timestamp('info', 
                    f"🎯 {symbol} 使用实时价格 {current_price:.2f} 替代当日收盘价 {original_price:.2f}")
            
            return data
            
        except Exception as e:
            if self.should_log('debug', analysis_index):
                self.log_with_timestamp('warning', f"⚠️ {symbol} 获取实时价格异常: {str(e)}")
            return data
    
    # ==================== TRIX计算工具 ====================
    
    def calculate_trix_unified(self, close_prices, period=None, use_talib=None):
        """统一的TRIX计算函数"""
        try:
            # 获取配置参数
            if period is None:
                period = get_config_value('TRIX_EMA_PERIOD', 3)
            if use_talib is None:
                use_talib = get_config_value('USE_TALIB_TRIX', False)
            
            import talib
            
            if use_talib:
                # 使用talib直接计算TRIX
                trix = talib.TRIX(close_prices, timeperiod=period)
            else:
                # 使用自定义计算方法
                ema1 = talib.EMA(close_prices, timeperiod=period)
                ema2 = talib.EMA(ema1, timeperiod=period)
                ema3 = talib.EMA(ema2, timeperiod=period)
                
                # 计算TRIX线
                trix = np.zeros_like(close_prices)
                for i in range(1, len(ema3)):
                    if ema3[i-1] != 0:  # 避免除以零
                        trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
            
            return trix
        except Exception as e:
            # 返回零数组作为fallback
            return np.array([0])
    
    def get_cached_trix(self, symbol, close_prices, period=None, use_talib=None):
        """获取缓存的TRIX计算结果"""
        try:
            # 初始化TRIX缓存
            if not hasattr(self.context, 'trix_cache'):
                self.context.trix_cache = {}
            
            # 生成缓存键
            today_str = self.context.now.strftime('%Y-%m-%d')
            trix_symbol_key = f'{symbol}_{today_str}'
            
            # 检查缓存
            if trix_symbol_key in self.context.trix_cache:
                return self.context.trix_cache[trix_symbol_key]
            
            # 计算TRIX
            trix = self.calculate_trix_unified(close_prices, period, use_talib)
            
            # 缓存结果
            self.context.trix_cache[trix_symbol_key] = trix
            
            return trix
            
        except Exception as e:
            return self.calculate_trix_unified(close_prices, period, use_talib)
    
    # ==================== 数据验证工具 ====================
    
    def validate_data_format(self, data, symbol, required_columns=None, analysis_index=0):
        """验证数据格式"""
        if data is None:
            if self.should_log('debug', analysis_index):
                self.log_with_timestamp('info', f"❌ {symbol} 数据为None，跳过")
            return False, None
        
        if len(data) < 30:
            if self.should_log('debug', analysis_index):
                self.log_with_timestamp('info', f"❌ {symbol} 数据不足30天({len(data)}天)，跳过")
            return False, None
        
        # 转换为DataFrame格式
        if isinstance(data, list):
            if len(data) == 0:
                if self.should_log('debug', analysis_index):
                    self.log_with_timestamp('info', f"❌ {symbol} 数据为空列表，跳过")
                return False, None
            
            try:
                data = pd.DataFrame(data)
                if self.should_log('data_format', analysis_index):
                    self.log_with_timestamp('info', f"🔍 {symbol} 已转换list为DataFrame，形状: {data.shape}")
            except Exception as convert_error:
                if self.should_log('debug', analysis_index):
                    self.log_with_timestamp('error', f"❌ {symbol} 数据转换失败: {convert_error}")
                return False, None
        
        # 检查必需的列
        if required_columns is None:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        if hasattr(data, 'columns'):
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                if self.should_log('debug', analysis_index):
                    self.log_with_timestamp('info', f"❌ {symbol} 缺少列: {missing_columns}")
                return False, None
            
            # 检查数据是否包含NaN或无效值
            if data.isnull().any().any():
                # 尝试清理数据
                data = data.dropna()
                if len(data) < 30:
                    if self.should_log('debug', analysis_index):
                        self.log_with_timestamp('info', f"❌ {symbol} 清理后数据不足30天({len(data)}天)，跳过")
                    return False, None
        
        return True, data
    
    # ==================== 性能统计工具 ====================
    
    def update_performance_stats(self, operation_type, duration=None):
        """更新性能统计"""
        try:
            if not hasattr(self.context, 'performance_stats'):
                self.context.performance_stats = {}
            
            if operation_type not in self.context.performance_stats:
                self.context.performance_stats[operation_type] = {
                    'count': 0,
                    'total_time': 0,
                    'avg_time': 0
                }
            
            stats = self.context.performance_stats[operation_type]
            stats['count'] += 1
            
            if duration is not None:
                stats['total_time'] += duration
                stats['avg_time'] = stats['total_time'] / stats['count']
                
        except Exception as e:
            pass  # 静默处理，不影响主逻辑

# 全局工具实例缓存
_utils_cache = {}

def get_strategy_utils(context):
    """获取策略工具实例（单例模式）"""
    context_id = id(context)
    if context_id not in _utils_cache:
        _utils_cache[context_id] = StrategyUtils(context)
    return _utils_cache[context_id]
