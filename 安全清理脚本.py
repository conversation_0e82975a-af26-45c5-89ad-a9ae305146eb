#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
安全清理脚本
只清理明确无用的文件，确保策略核心功能不受影响
"""

import os
import shutil
import datetime
from pathlib import Path

def safe_cleanup():
    """安全清理操作"""
    print("🧹 万和策略安全清理工具")
    print("=" * 50)
    
    base_path = Path(".")
    backup_path = base_path / "safe_cleanup_backup"
    
    # 创建备份目录
    if not backup_path.exists():
        backup_path.mkdir()
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = backup_path / f"backup_{timestamp}"
    backup_dir.mkdir()
    
    # 明确可以安全删除的文件（临时文件和重复分析脚本）
    safe_delete_files = [
        # TRIX测试文件
        "TRIX买入逻辑修复完成总结.md",
        "TRIX买入逻辑最终修复报告.md", 
        "TRIX买入逻辑问题分析报告.md",
        "TRIX修复最终验证.py",
        "TRIX实际数据测试.py",
        "TRIX逻辑修复验证.py",
        
        # 重复的分析脚本
        "analyze_backtest_fixed.py",
        "analyze_backtest_performance_decline.py",
        "analyze_debug_impact.py",
        "analyze_existing_data.py",
        "analyze_latest_results.py",
        "analyze_optimization_direction.py",
        "analyze_performance_decline.py",
        "analyze_performance_issues.py",
        
        # 调试脚本
        "debug_config.py",
        "debug_detailed.py", 
        "debug_enhancement_report.py",
        "debug_field_mapping.py",
        "debug_multifactor_scores.py",
        
        # 诊断脚本
        "diagnose_data_transfer.py",
        "diagnose_factor_data_flow.py",
        "diagnose_multifactor_issue.py",
        
        # 验证脚本
        "verify_config_changes.py",
        "verify_config_fix.py",
        "verify_emergency_fix.py",
        "verify_optimization.py",
        
        # 临时修复脚本
        "emergency_diagnosis.py",
        "emergency_factor_fix.py",
        "fix_analysis_table_structure.py",
        "fix_database_schema.py",
        "fix_verification.py",
        
        # 重复的优化脚本
        "apply_data_driven_optimization.py",
        "apply_final_optimization.py",
        "comprehensive_optimization_summary.py",
        "final_optimization_summary.py",
        
        # 测试文件
        "test_trix_reversal.py",
        
        # 报告文件（可以重新生成）
        "买入卖出逻辑修复完成报告.md",
        "买入卖出逻辑分析报告.md",
        "买入逻辑修复验证.py",
        "修复总结.md",
        "策略代码分析和修复报告.md",
        "策略修复完成报告.md"
    ]
    
    # 检查文件是否存在并删除
    deleted_files = []
    for filename in safe_delete_files:
        file_path = base_path / filename
        if file_path.exists():
            # 备份文件
            shutil.copy2(file_path, backup_dir / filename)
            # 删除文件
            file_path.unlink()
            deleted_files.append(filename)
            print(f"✅ 删除: {filename}")
    
    # 清理部分配置备份（保留最新10个）
    config_backups = list(base_path.glob("config_backup_*.py"))
    if len(config_backups) > 10:
        # 按修改时间排序
        config_backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除旧的备份
        old_backups = config_backups[10:]
        for backup_file in old_backups:
            shutil.copy2(backup_file, backup_dir / backup_file.name)
            backup_file.unlink()
            deleted_files.append(backup_file.name)
            print(f"✅ 删除旧配置备份: {backup_file.name}")
    
    # 清理__pycache__目录
    pycache_dirs = list(base_path.glob("**/__pycache__"))
    for pycache_dir in pycache_dirs:
        if pycache_dir.exists():
            shutil.rmtree(pycache_dir)
            print(f"✅ 删除缓存目录: {pycache_dir}")
    
    print(f"\n📊 清理统计:")
    print(f"  删除文件: {len(deleted_files)}个")
    print(f"  备份位置: {backup_dir}")
    print(f"  清理的缓存目录: {len(pycache_dirs)}个")
    
    # 生成清理报告
    report_content = f"""# 安全清理报告

## 清理时间
{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理统计
- 删除文件: {len(deleted_files)}个
- 清理缓存目录: {len(pycache_dirs)}个
- 备份位置: {backup_dir}

## 删除的文件列表
"""
    
    for filename in deleted_files:
        report_content += f"- {filename}\n"
    
    report_content += f"""
## 清理效果
- 减少了临时文件和重复脚本
- 保留了所有核心功能文件
- 清理了编译缓存，释放空间
- 项目结构更加清晰

## 下一步建议
1. 验证策略核心功能正常
2. 考虑进一步的目录重构
3. 建立文件管理规范
4. 定期清理临时文件

## 安全保障
- 所有删除的文件都已备份
- 未触及任何核心策略文件
- 可以随时从备份恢复
"""
    
    report_path = base_path / "安全清理报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📋 清理报告已生成: {report_path}")
    print(f"\n🎉 安全清理完成！")
    print(f"如有问题，可从 {backup_dir} 恢复文件")
    
    return len(deleted_files)

if __name__ == "__main__":
    safe_cleanup()
