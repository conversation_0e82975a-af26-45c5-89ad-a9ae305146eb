#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sqlite3
import pandas as pd
import argparse
import logging
from datetime import datetime
import shutil

# 配置日志 - 使用独立logger而不是basicConfig
logger = logging.getLogger('db_manager')
logger.setLevel(logging.INFO)
# 清除已有的处理器
if logger.handlers:
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
# 添加处理器
file_handler = logging.FileHandler("db_manager.log", encoding='utf-8')
console_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)
logger.addHandler(file_handler)
logger.addHandler(console_handler)
# 禁用传播
logger.propagate = False

# 统一定义数据路径常量
DATA_DIR = 'data'
BACKUP_DIR = 'backups'

# 确保目录存在
for directory in [DATA_DIR, BACKUP_DIR]:
    os.makedirs(directory, exist_ok=True)

# 定义文件路径
DB_FILE = os.path.join(DATA_DIR, 'trades.db')
TRADE_LOG_FILE = os.path.join(DATA_DIR, 'trade_log.csv')
ANALYSIS_LOG_FILE = os.path.join(DATA_DIR, 'analysis_log.csv')

def check_db_exists():
    """检查数据库文件是否存在"""
    if not os.path.exists(DB_FILE):
        logger.warning(f"数据库文件不存在: {DB_FILE}")
        return False
    return True

def backup_database():
    """备份数据库文件"""
    if not check_db_exists():
        return False
    
    try:
        backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(BACKUP_DIR, f"db_backup_{backup_time}")
        os.makedirs(backup_dir, exist_ok=True)
        
        # 备份数据库文件
        backup_file = os.path.join(backup_dir, os.path.basename(DB_FILE))
        shutil.copy2(DB_FILE, backup_file)
        
        logger.info(f"数据库备份成功: {backup_file}")
        return backup_file
    except Exception as e:
        logger.error(f"备份数据库失败: {str(e)}")
        return False

def get_db_info():
    """获取数据库信息，包括表、记录数等"""
    if not check_db_exists():
        return None
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        db_info = {
            "file_path": os.path.abspath(DB_FILE),
            "file_size": f"{os.path.getsize(DB_FILE) / (1024 * 1024):.2f} MB",
            "tables": {}
        }
        
        # 获取每个表的记录数和字段
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [column[1] for column in cursor.fetchall()]
            
            # 获取最早和最新的记录时间（如果有timestamp字段）
            time_range = {}
            if "timestamp" in columns:
                cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                min_time, max_time = cursor.fetchone()
                time_range = {
                    "earliest": min_time,
                    "latest": max_time
                }
            
            db_info["tables"][table] = {
                "record_count": count,
                "columns": columns,
                "time_range": time_range
            }
        
        conn.close()
        return db_info
    except Exception as e:
        logger.error(f"获取数据库信息失败: {str(e)}")
        return None

def clear_table(table_name, before_date=None):
    """清空指定表的数据
    
    Args:
        table_name: 表名
        before_date: 删除此日期之前的数据（可选，格式：YYYY-MM-DD）
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        if not cursor.fetchone():
            logger.warning(f"表不存在: {table_name}")
            conn.close()
            return False
        
        # 获取删除前的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        before_count = cursor.fetchone()[0]
        
        # 执行删除操作
        if before_date:
            sql = f"DELETE FROM {table_name} WHERE timestamp < ?"
            cursor.execute(sql, (before_date,))
        else:
            cursor.execute(f"DELETE FROM {table_name}")
        
        # 获取删除后的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        after_count = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        deleted_count = before_count - after_count
        logger.info(f"已从表 {table_name} 中删除 {deleted_count} 条记录")
        return True
    except Exception as e:
        logger.error(f"清空表 {table_name} 失败: {str(e)}")
        return False

def optimize_database():
    """优化数据库（VACUUM）"""
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 获取优化前的文件大小
        before_size = os.path.getsize(DB_FILE) / (1024 * 1024)  # MB
        
        # 执行VACUUM操作
        cursor.execute("VACUUM")
        
        conn.commit()
        conn.close()
        
        # 获取优化后的文件大小
        after_size = os.path.getsize(DB_FILE) / (1024 * 1024)  # MB
        
        saved = before_size - after_size
        logger.info(f"数据库优化完成: 优化前 {before_size:.2f} MB, 优化后 {after_size:.2f} MB, 节省 {saved:.2f} MB")
        return True
    except Exception as e:
        logger.error(f"优化数据库失败: {str(e)}")
        return False

def check_database_integrity():
    """检查数据库完整性"""
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA integrity_check")
        result = cursor.fetchone()[0]
        
        conn.close()
        
        if result == "ok":
            logger.info("数据库完整性检查通过")
            return True
        else:
            logger.warning(f"数据库完整性检查失败: {result}")
            return False
    except Exception as e:
        logger.error(f"检查数据库完整性失败: {str(e)}")
        return False

def export_table_to_csv(table_name, output_file=None):
    """将表导出为CSV文件
    
    Args:
        table_name: 表名
        output_file: 输出文件路径（可选）
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        
        # 如果未指定输出文件，则使用默认路径
        if not output_file:
            output_dir = os.path.join(DATA_DIR, 'exports')
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
        
        # 查询数据并导出到CSV
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql_query(query, conn)
        df.to_csv(output_file, index=False)
        
        conn.close()
        
        logger.info(f"表 {table_name} 已导出到 {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"导出表 {table_name} 失败: {str(e)}")
        return False

def clear_all_tables():
    """清空数据库中的所有表数据"""
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        if not tables:
            logger.warning("数据库中没有表")
            conn.close()
            return False
            
        # 记录清空前的总记录数
        total_before_count = 0
        table_counts = {}
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_before_count += count
            table_counts[table] = count
        
        # 禁用外键约束（如果有）
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # 清空所有表
        for table in tables:
            cursor.execute(f"DELETE FROM {table}")
            logger.info(f"已清空表 {table}，原有记录数: {table_counts[table]}")
        
        # 重新启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        conn.commit()
        conn.close()
        
        logger.info(f"已清空所有表，共删除 {total_before_count} 条记录")
        return True
    except Exception as e:
        logger.error(f"清空所有表失败: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库管理工具')
    parser.add_argument('--info', action='store_true', help='显示数据库信息')
    parser.add_argument('--backup', action='store_true', help='备份数据库')
    parser.add_argument('--clear', metavar='TABLE', help='清空指定表的数据')
    parser.add_argument('--clear-all', action='store_true', help='清空所有表的数据')
    parser.add_argument('--before', metavar='DATE', help='删除指定日期之前的数据 (格式: YYYY-MM-DD)')
    parser.add_argument('--optimize', action='store_true', help='优化数据库')
    parser.add_argument('--check', action='store_true', help='检查数据库完整性')
    parser.add_argument('--export', metavar='TABLE', help='将表导出为CSV文件')
    parser.add_argument('--output', metavar='FILE', help='导出文件路径')
    
    args = parser.parse_args()
    
    # 如果没有参数，显示帮助信息
    if len(vars(args)) == 0 or all(v is None or v is False for v in vars(args).values()):
        parser.print_help()
        return
    
    # 显示数据库信息
    if args.info:
        db_info = get_db_info()
        if db_info:
            print("\n=== 数据库信息 ===")
            print(f"文件路径: {db_info['file_path']}")
            print(f"文件大小: {db_info['file_size']}")
            print("\n表信息:")
            for table, info in db_info['tables'].items():
                print(f"  - {table}: {info['record_count']} 条记录")
                if info['time_range'] and info['time_range']['earliest']:
                    print(f"    时间范围: {info['time_range']['earliest']} 至 {info['time_range']['latest']}")
    
    # 备份数据库
    if args.backup:
        backup_file = backup_database()
        if backup_file:
            print(f"\n数据库已备份到: {backup_file}")
    
    # 清空表数据
    if args.clear:
        if args.backup:
            backup_database()  # 在清空前先备份
        success = clear_table(args.clear, args.before)
        if success:
            print(f"\n表 {args.clear} 已清空" + (f" ({args.before} 之前的数据)" if args.before else ""))
    
    # 清空所有表数据
    if args.clear_all:
        if args.backup:
            backup_database()  # 在清空前先备份
            
        success = clear_all_tables()
        if success:
            print("\n所有表已清空")
    
    # 优化数据库
    if args.optimize:
        success = optimize_database()
        if success:
            print("\n数据库优化完成")
    
    # 检查数据库完整性
    if args.check:
        success = check_database_integrity()
        if success:
            print("\n数据库完整性检查通过")
    
    # 导出表为CSV
    if args.export:
        output_file = export_table_to_csv(args.export, args.output)
        if output_file:
            print(f"\n表 {args.export} 已导出到: {output_file}")

if __name__ == "__main__":
    main() 