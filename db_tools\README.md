# 万和策略分析系统 - 数据库管理工具

这个文件夹包含了万和策略分析系统的数据库管理工具及相关启动脚本。通过这套工具，您可以方便地管理、分析和维护策略系统的数据库。

## 功能概述

数据库管理工具提供以下功能：

1. **数据库信息** - 查看数据库基本信息、表结构和数据统计，包括表数量、记录数、字段信息等
2. **数据分析** - 选择表和列进行数据分析，生成统计图表和分布报告
3. **修复缺失数据** - 交互式选择表、列和修复策略，处理缺失值（支持平均值、中位数、众数等填充方式）
4. **批量处理数据** - 进行批量删除或更新操作，支持条件筛选
5. **合并数据库** - 合并两个数据库，支持不同的合并策略（覆盖或追加）
6. **生成数据库报告** - 生成完整的数据库报告，包括表结构、数据统计和健康状况
7. **备份数据库** - 备份当前数据库，支持自动命名和存储路径配置
8. **优化数据库** - 优化数据库性能，包括索引重建、空间回收等操作

## 快速开始

### Windows用户

#### 数据库管理工具

1. **直接启动**：
   - 双击 `start_db_manager.bat` 文件启动数据库管理工具

2. **创建桌面快捷方式**：
   - 运行 `create_shortcut.bat`
   - 在桌面创建名为"万和策略分析系统 - 数据库管理工具"的快捷方式
   - 之后可直接通过桌面快捷方式启动

#### 数据分析工具（直接启动）

1. **直接启动**：
   - 双击 `start_analysis.bat` 文件直接启动数据分析功能

2. **创建桌面快捷方式**：
   - 运行 `create_analysis_shortcut.bat`
   - 在桌面创建名为"万和策略分析系统 - 数据分析工具"的快捷方式
   - 之后可直接通过桌面快捷方式启动数据分析功能

### 其他系统用户（Linux/Mac）

运行以下命令启动数据库管理工具：

```bash
cd db_tools
python start_db_manager.py
```

或者直接启动数据分析功能：

```bash
cd db_tools
python start_analysis.py
```

## 使用指南

### 首次使用

1. 首次运行时，系统会自动检查依赖库是否已安装
2. 如果缺少依赖库，系统会提示您安装
3. 系统会自动创建必要的目录结构（如备份目录、报告目录等）
4. 如果数据库文件不存在，系统会提示您创建新的数据库

### 功能使用详解

#### 1. 数据库信息

查看数据库的基本信息，包括：
- 数据库文件路径和大小
- 表数量和列表
- 每个表的记录数和字段信息
- 时间范围（如果表中包含timestamp字段）

#### 2. 数据分析

对数据进行统计分析：
- 选择要分析的表和列
- 系统会生成描述性统计信息（均值、中位数、标准差等）
- 自动生成数据分布图表（直方图、箱线图等）
- 分析结果保存在配置的输出目录中

> **提示**：您可以使用 `start_analysis.bat` 或 `start_analysis.py` 直接启动数据分析功能，无需先进入数据库管理工具。

#### 3. 修复缺失数据

处理表中的缺失值：
- 选择要修复的表和列
- 选择填充策略（均值、中位数、众数、零值、前值填充）
- 系统会显示受影响的记录数
- 确认后执行修复操作

#### 4. 批量处理数据

批量更新或删除数据：
- 选择要处理的表
- 选择操作类型（更新或删除）
- 输入SQL WHERE条件语句
- 对于更新操作，输入JSON格式的更新参数
- 系统会显示受影响的记录数
- 确认后执行操作

#### 5. 合并数据库

合并两个数据库：
- 输入目标数据库路径（合并到此数据库）
- 输入源数据库路径（从此数据库合并）
- 选择合并策略（覆盖重复记录或追加所有记录）
- 系统会显示合并进度和结果

#### 6. 生成数据库报告

生成完整的数据库报告：
- 可选择报告输出路径（默认使用配置文件中的设置）
- 系统会生成HTML格式的报告
- 报告包含数据库结构、表统计、数据分布图表等信息

#### 7. 备份数据库

创建数据库备份：
- 系统会自动生成带时间戳的备份文件
- 备份文件保存在配置的备份目录中
- 备份完成后显示备份文件路径

#### 8. 优化数据库

优化数据库性能：
- 系统会执行VACUUM操作回收空间
- 重建索引提高查询性能
- 显示优化前后的数据库大小对比

## 配置文件说明

配置文件`config.ini`包含以下设置：

### Database 部分

```ini
[Database]
# 数据库文件路径，可以是绝对路径或相对于项目根目录的相对路径
db_file = data/trades.db

# 数据库备份目录，可以是绝对路径或相对于项目根目录的相对路径
backup_dir = backups
```

### Reports 部分

```ini
[Reports]
# 报告输出目录，可以是绝对路径或相对于项目根目录的相对路径
report_dir = reports

# 报告文件名格式，支持日期格式化
report_name_format = db_report_%Y%m%d_%H%M%S.html
```

### Analysis 部分

```ini
[Analysis]
# 分析结果输出目录，可以是绝对路径或相对于项目根目录的相对路径
output_dir = analysis

# 是否在分析时自动生成图表
generate_plots = true

# 图表DPI设置（影响图表质量和文件大小）
plot_dpi = 100
```

### UI 部分

```ini
[UI]
# 是否使用彩色终端输出
use_color = true

# 终端输出语言
language = zh_CN
```

## 文件说明

- `start_db_manager.py` - 数据库管理工具Python启动脚本
- `start_db_manager.bat` - Windows批处理文件，启动数据库管理工具
- `create_shortcut.bat` - 创建数据库管理工具桌面快捷方式的批处理文件
- `start_analysis.py` - 数据分析功能Python启动脚本
- `start_analysis.bat` - Windows批处理文件，直接启动数据分析功能
- `create_analysis_shortcut.bat` - 创建数据分析工具桌面快捷方式的批处理文件
- `config.ini` - 配置文件，包含数据库路径、输出目录等设置
- `config_loader.py` - 配置加载模块，用于读取和解析配置文件
- `README.md` - 本说明文件
- `CHANGELOG.md` - 版本更新记录
- `快速入门.txt` - 简洁的中文快速入门指南

## 常见问题

### 无法找到Python解释器

**问题**：启动脚本报错，无法找到Python解释器
**解决方案**：
1. 确保已安装Python 3.6或更高版本
2. 手动编辑`start_db_manager.bat`文件，将其中的Python路径修改为您系统上的Python路径
   ```batch
   set PYTHON_PATH=C:\您的Python安装路径\python.exe
   ```
3. 或者将Python安装目录添加到系统PATH环境变量中

### 依赖库安装失败

**问题**：启动时提示依赖库安装失败
**解决方案**：
1. 手动安装所需的依赖库：
   ```bash
   pip install seaborn pandas numpy matplotlib colorama
   ```
2. 如果安装速度慢，可以尝试使用国内镜像源：
   ```bash
   pip install seaborn pandas numpy matplotlib colorama -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```
3. 如果某个库安装失败，可以单独安装：
   ```bash
   pip install 库名
   ```

### 数据库文件不存在

**问题**：提示数据库文件不存在
**解决方案**：
1. 检查配置文件中的`db_file`设置是否正确
2. 如果是首次使用，系统会提示您创建新的数据库，按照提示操作即可
3. 如果需要使用现有数据库，请确保路径正确，或者修改配置文件中的路径

### 无法创建桌面快捷方式

**问题**：运行`create_shortcut.bat`后未能创建桌面快捷方式
**解决方案**：
1. 确保您有桌面的写入权限
2. 尝试以管理员身份运行`create_shortcut.bat`
3. 或者手动创建快捷方式，指向`start_db_manager.bat`文件

### 图表无法显示

**问题**：数据分析功能中的图表无法显示
**解决方案**：
1. 确保已正确安装matplotlib和seaborn库
2. 检查配置文件中的`generate_plots`设置是否为`true`
3. 尝试更新matplotlib库：
   ```bash
   pip install --upgrade matplotlib
   ```

### 中文显示乱码

**问题**：报告或终端中的中文显示为乱码
**解决方案**：
1. 确保系统支持UTF-8编码
2. 对于Windows用户，可以在命令提示符中执行：
   ```
   chcp 65001
   ```
   然后再运行启动脚本

## 技术支持

如果您遇到其他问题或需要技术支持，请联系系统管理员或开发团队。

## 配置说明

### 数据库路径配置

默认情况下，数据库文件路径配置为 `data/trades.db`（相对于项目根目录）。如果您的数据库文件位于其他位置，请修改 `config.ini` 文件中的 `db_file` 设置：

```ini
[Database]
# 数据库文件路径，可以是绝对路径或相对于项目根目录的相对路径
db_file = data/trades.db
```

如果遇到"找不到数据库文件"的错误，请检查：
1. 数据库文件是否存在于指定位置
2. 配置文件中的路径是否正确
3. 如果使用相对路径，请确保从正确的目录启动工具

您可以运行 `test_db_connection.bat` 来测试数据库连接是否正常。 