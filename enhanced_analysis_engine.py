# coding=utf-8
"""
增强分析引擎
基于全面的增强因子进行深度分析
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EnhancedAnalysisEngine:
    """增强分析引擎"""
    
    def __init__(self):
        self.db_path = 'data/trades.db'
        self.factor_categories = {
            'market_environment': [
                'market_index_change', 'market_volume_ratio', 'market_sentiment_score',
                'sector_relative_strength', 'northbound_flow'
            ],
            'fundamental': [
                'pe_ratio_current', 'pb_ratio', 'roe_ttm', 'debt_to_equity',
                'eps_growth_yoy', 'revenue_growth_yoy'
            ],
            'money_flow': [
                'main_force_inflow', 'institutional_ownership', 'large_order_ratio',
                'retail_sentiment'
            ],
            'technical_enhanced': [
                'volume_weighted_price', 'price_volume_divergence', 'trend_intensity',
                'realized_volatility', 'vwap_distance'
            ],
            'event_driven': [
                'earnings_announcement', 'analyst_upgrade', 'policy_announcement',
                'index_inclusion'
            ],
            'time_series': [
                'seasonal_pattern', 'day_of_week_effect', 'business_cycle_stage'
            ],
            'risk': [
                'beta_stability', 'liquidity_risk', 'downside_deviation',
                'value_at_risk'
            ]
        }
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print('🎯 增强因子综合分析')
        print('=' * 80)
        
        # 1. 加载增强数据
        enhanced_data = self._load_enhanced_data()
        if enhanced_data is None or len(enhanced_data) == 0:
            print('❌ 没有增强数据可供分析')
            return None
        
        print(f'📊 加载增强数据: {len(enhanced_data):,} 条记录')
        
        # 2. 分类别分析因子效果
        category_results = {}
        for category, factors in self.factor_categories.items():
            print(f'\n📈 分析 {category} 类别因子...')
            result = self._analyze_factor_category(enhanced_data, category, factors)
            category_results[category] = result
        
        # 3. 多因子组合分析
        print('\n🔍 多因子组合分析...')
        combination_results = self._analyze_factor_combinations(enhanced_data)
        
        # 4. 市场环境分层分析
        print('\n🌍 市场环境分层分析...')
        environment_results = self._analyze_by_market_environment(enhanced_data)
        
        # 5. 时间序列分析
        print('\n⏰ 时间序列效应分析...')
        time_series_results = self._analyze_time_series_effects(enhanced_data)
        
        # 6. 风险收益优化分析
        print('\n⚖️ 风险收益优化分析...')
        risk_return_results = self._analyze_risk_return_optimization(enhanced_data)
        
        # 7. 生成综合报告
        comprehensive_report = {
            'category_analysis': category_results,
            'combination_analysis': combination_results,
            'environment_analysis': environment_results,
            'time_series_analysis': time_series_results,
            'risk_return_analysis': risk_return_results
        }
        
        # 8. 保存分析结果
        self._save_analysis_results(comprehensive_report)
        
        # 9. 生成优化建议
        self._generate_optimization_recommendations(comprehensive_report)
        
        return comprehensive_report
    
    def _load_enhanced_data(self):
        """加载增强数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 检查是否有增强字段
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(trades)")
            columns = [row[1] for row in cursor.fetchall()]
            
            # 检查增强字段是否存在
            enhanced_fields = []
            for category, factors in self.factor_categories.items():
                for factor in factors:
                    if factor in columns:
                        enhanced_fields.append(factor)
            
            if not enhanced_fields:
                print('⚠️ 数据库中没有增强字段，请先运行增强数据收集')
                return None
            
            # 加载数据
            query = f'''
            SELECT timestamp, symbol, action, price, profit_pct, is_win,
                   {', '.join(enhanced_fields)}
            FROM trades 
            WHERE action = 'BUY'
            ORDER BY timestamp DESC
            '''
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            print(f'✅ 成功加载 {len(enhanced_fields)} 个增强字段')
            return data
            
        except Exception as e:
            print(f'❌ 加载增强数据失败: {e}')
            return None
    
    def _analyze_factor_category(self, data, category_name, factors):
        """分析因子类别效果"""
        try:
            results = {
                'category': category_name,
                'factor_count': 0,
                'effective_factors': [],
                'top_factors': [],
                'category_score': 0
            }
            
            factor_performances = []
            
            for factor in factors:
                if factor not in data.columns:
                    continue
                
                # 过滤有效数据
                valid_data = data[(data[factor].notna()) & (data[factor] != 0)]
                if len(valid_data) < 20:
                    continue
                
                results['factor_count'] += 1
                
                # 分析因子效果
                performance = self._analyze_single_factor(valid_data, factor)
                if performance:
                    factor_performances.append(performance)
                    
                    # 判断是否为有效因子
                    if abs(performance['win_rate_improvement']) > 5:
                        results['effective_factors'].append({
                            'factor': factor,
                            'improvement': performance['win_rate_improvement'],
                            'significance': performance['significance_score']
                        })
            
            # 排序并选择top因子
            if factor_performances:
                factor_performances.sort(key=lambda x: abs(x['win_rate_improvement']), reverse=True)
                results['top_factors'] = factor_performances[:3]
                
                # 计算类别评分
                avg_improvement = np.mean([f['win_rate_improvement'] for f in factor_performances])
                results['category_score'] = max(0, min(100, 50 + avg_improvement))
            
            return results
            
        except Exception as e:
            print(f'分析因子类别 {category_name} 失败: {e}')
            return None
    
    def _analyze_single_factor(self, data, factor):
        """分析单个因子"""
        try:
            # 分位数分组
            q25 = data[factor].quantile(0.25)
            q75 = data[factor].quantile(0.75)
            
            low_group = data[data[factor] <= q25]
            high_group = data[data[factor] >= q75]
            
            if len(low_group) < 10 or len(high_group) < 10:
                return None
            
            # 计算胜率
            low_win_rate = (low_group['is_win'].sum() / len(low_group)) * 100
            high_win_rate = (high_group['is_win'].sum() / len(high_group)) * 100
            
            # 计算收益
            low_avg_return = low_group['profit_pct'].mean()
            high_avg_return = high_group['profit_pct'].mean()
            
            # 计算改善效果
            win_rate_improvement = high_win_rate - low_win_rate
            return_improvement = high_avg_return - low_avg_return
            
            # 计算显著性
            significance_score = self._calculate_significance(low_group, high_group)
            
            return {
                'factor': factor,
                'win_rate_improvement': win_rate_improvement,
                'return_improvement': return_improvement,
                'significance_score': significance_score,
                'low_win_rate': low_win_rate,
                'high_win_rate': high_win_rate,
                'sample_size': len(low_group) + len(high_group)
            }
            
        except Exception as e:
            print(f'分析因子 {factor} 失败: {e}')
            return None
    
    def _calculate_significance(self, group1, group2):
        """计算统计显著性"""
        try:
            from scipy import stats
            
            # t检验
            t_stat, p_value = stats.ttest_ind(group1['profit_pct'], group2['profit_pct'])
            
            # 转换为显著性评分
            if p_value < 0.01:
                return 90
            elif p_value < 0.05:
                return 70
            elif p_value < 0.1:
                return 50
            else:
                return 30
                
        except:
            # 简化的显著性评估
            size_score = min(50, (len(group1) + len(group2)) / 10)
            return size_score
    
    def _analyze_factor_combinations(self, data):
        """分析因子组合效果"""
        try:
            print('  🔍 分析双因子组合...')
            
            combinations = []
            
            # 选择最有效的因子进行组合分析
            effective_factors = []
            for category, factors in self.factor_categories.items():
                for factor in factors:
                    if factor in data.columns:
                        performance = self._analyze_single_factor(data, factor)
                        if performance and abs(performance['win_rate_improvement']) > 3:
                            effective_factors.append(factor)
            
            # 分析双因子组合（限制数量避免过度计算）
            for i, factor1 in enumerate(effective_factors[:10]):
                for factor2 in effective_factors[i+1:10]:
                    combo_result = self._analyze_two_factor_combination(data, factor1, factor2)
                    if combo_result:
                        combinations.append(combo_result)
            
            # 排序并返回最佳组合
            combinations.sort(key=lambda x: x['combined_improvement'], reverse=True)
            
            return {
                'total_combinations': len(combinations),
                'top_combinations': combinations[:5],
                'best_combination': combinations[0] if combinations else None
            }
            
        except Exception as e:
            print(f'分析因子组合失败: {e}')
            return {}
    
    def _analyze_two_factor_combination(self, data, factor1, factor2):
        """分析双因子组合"""
        try:
            # 过滤有效数据
            valid_data = data[
                (data[factor1].notna()) & (data[factor1] != 0) &
                (data[factor2].notna()) & (data[factor2] != 0)
            ]
            
            if len(valid_data) < 50:
                return None
            
            # 双因子分组
            f1_median = valid_data[factor1].median()
            f2_median = valid_data[factor2].median()
            
            # 四个象限
            high_high = valid_data[
                (valid_data[factor1] >= f1_median) & 
                (valid_data[factor2] >= f2_median)
            ]
            low_low = valid_data[
                (valid_data[factor1] < f1_median) & 
                (valid_data[factor2] < f2_median)
            ]
            
            if len(high_high) < 10 or len(low_low) < 10:
                return None
            
            # 计算组合效果
            high_high_win_rate = (high_high['is_win'].sum() / len(high_high)) * 100
            low_low_win_rate = (low_low['is_win'].sum() / len(low_low)) * 100
            
            combined_improvement = high_high_win_rate - low_low_win_rate
            
            return {
                'factor1': factor1,
                'factor2': factor2,
                'combined_improvement': combined_improvement,
                'high_high_win_rate': high_high_win_rate,
                'low_low_win_rate': low_low_win_rate,
                'sample_size': len(high_high) + len(low_low)
            }
            
        except Exception as e:
            return None
    
    def _analyze_by_market_environment(self, data):
        """按市场环境分层分析"""
        try:
            if 'market_sentiment_score' not in data.columns:
                return {'error': '缺少市场环境数据'}
            
            # 按市场情绪分层
            sentiment_data = data[data['market_sentiment_score'].notna()]
            
            bull_market = sentiment_data[sentiment_data['market_sentiment_score'] > 70]
            bear_market = sentiment_data[sentiment_data['market_sentiment_score'] < 30]
            neutral_market = sentiment_data[
                (sentiment_data['market_sentiment_score'] >= 30) & 
                (sentiment_data['market_sentiment_score'] <= 70)
            ]
            
            results = {}
            for market_type, market_data in [
                ('bull_market', bull_market),
                ('bear_market', bear_market),
                ('neutral_market', neutral_market)
            ]:
                if len(market_data) > 20:
                    win_rate = (market_data['is_win'].sum() / len(market_data)) * 100
                    avg_return = market_data['profit_pct'].mean()
                    
                    results[market_type] = {
                        'sample_size': len(market_data),
                        'win_rate': win_rate,
                        'avg_return': avg_return
                    }
            
            return results
            
        except Exception as e:
            print(f'市场环境分析失败: {e}')
            return {}
    
    def _analyze_time_series_effects(self, data):
        """分析时间序列效应"""
        try:
            results = {}
            
            # 转换时间戳
            data['datetime'] = pd.to_datetime(data['timestamp'])
            data['hour'] = data['datetime'].dt.hour
            data['weekday'] = data['datetime'].dt.weekday
            data['month'] = data['datetime'].dt.month
            
            # 小时效应
            hourly_performance = data.groupby('hour').agg({
                'is_win': 'mean',
                'profit_pct': 'mean'
            }).reset_index()
            
            best_hour = hourly_performance.loc[hourly_performance['is_win'].idxmax(), 'hour']
            worst_hour = hourly_performance.loc[hourly_performance['is_win'].idxmin(), 'hour']
            
            results['hourly_effect'] = {
                'best_hour': int(best_hour),
                'worst_hour': int(worst_hour),
                'hour_performance': hourly_performance.to_dict('records')
            }
            
            # 星期效应
            weekly_performance = data.groupby('weekday').agg({
                'is_win': 'mean',
                'profit_pct': 'mean'
            }).reset_index()
            
            results['weekly_effect'] = {
                'performance': weekly_performance.to_dict('records')
            }
            
            return results
            
        except Exception as e:
            print(f'时间序列分析失败: {e}')
            return {}
    
    def _analyze_risk_return_optimization(self, data):
        """分析风险收益优化"""
        try:
            # 计算风险调整收益
            data['risk_adjusted_return'] = data['profit_pct'] / (data.get('downside_deviation', 0.02) + 0.001)
            
            # 按风险调整收益分组
            top_risk_adjusted = data.nlargest(int(len(data) * 0.2), 'risk_adjusted_return')
            bottom_risk_adjusted = data.nsmallest(int(len(data) * 0.2), 'risk_adjusted_return')
            
            results = {
                'top_risk_adjusted': {
                    'win_rate': (top_risk_adjusted['is_win'].sum() / len(top_risk_adjusted)) * 100,
                    'avg_return': top_risk_adjusted['profit_pct'].mean(),
                    'avg_risk': top_risk_adjusted.get('downside_deviation', pd.Series([0.02])).mean()
                },
                'bottom_risk_adjusted': {
                    'win_rate': (bottom_risk_adjusted['is_win'].sum() / len(bottom_risk_adjusted)) * 100,
                    'avg_return': bottom_risk_adjusted['profit_pct'].mean(),
                    'avg_risk': bottom_risk_adjusted.get('downside_deviation', pd.Series([0.02])).mean()
                }
            }
            
            return results
            
        except Exception as e:
            print(f'风险收益分析失败: {e}')
            return {}
    
    def _save_analysis_results(self, results):
        """保存分析结果"""
        try:
            import json
            
            # 保存为JSON文件
            with open('enhanced_analysis_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            print('💾 分析结果已保存到: enhanced_analysis_results.json')
            
        except Exception as e:
            print(f'保存分析结果失败: {e}')
    
    def _generate_optimization_recommendations(self, results):
        """生成优化建议"""
        print('\n💡 基于增强分析的优化建议:')
        print('=' * 80)
        
        # 基于类别分析的建议
        category_analysis = results.get('category_analysis', {})
        
        print('🎯 最有效的因子类别:')
        category_scores = []
        for category, result in category_analysis.items():
            if result and 'category_score' in result:
                category_scores.append((category, result['category_score']))
        
        category_scores.sort(key=lambda x: x[1], reverse=True)
        
        for i, (category, score) in enumerate(category_scores[:3], 1):
            print(f'  {i}. {category}: {score:.1f}分')
        
        # 基于组合分析的建议
        combination_analysis = results.get('combination_analysis', {})
        best_combo = combination_analysis.get('best_combination')
        
        if best_combo:
            print(f'\n🔥 最佳因子组合:')
            print(f'  {best_combo["factor1"]} + {best_combo["factor2"]}')
            print(f'  胜率提升: {best_combo["combined_improvement"]:.1f}%')
        
        # 基于时间序列的建议
        time_analysis = results.get('time_series_analysis', {})
        hourly_effect = time_analysis.get('hourly_effect', {})
        
        if hourly_effect:
            print(f'\n⏰ 最佳买入时间:')
            print(f'  推荐时间: {hourly_effect.get("best_hour", "未知")}点')
            print(f'  避免时间: {hourly_effect.get("worst_hour", "未知")}点')
        
        print('\n🎉 增强分析完成!')

def main():
    """主函数"""
    engine = EnhancedAnalysisEngine()
    results = engine.run_comprehensive_analysis()
    return results

if __name__ == "__main__":
    main()
