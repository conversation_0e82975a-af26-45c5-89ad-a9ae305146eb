# -*- coding: utf-8 -*-
"""
掘金平台数据获取脚本
"""


# 历史行情数据获取脚本
def get_comprehensive_history_data():
    """获取全A股3年历史数据"""
    import sqlite3
    from datetime import datetime, timedelta
    
    # 获取股票列表
    symbols = get_all_stocks()  # 需要实现获取全A股列表的函数
    start_date = '2021-01-01'
    end_date = '2024-12-31'
    
    conn = sqlite3.connect('data/enhanced_market_data.db')
    
    success_count = 0
    total_count = len(symbols)
    
    for i, symbol in enumerate(symbols):
        try:
            print(f'获取 {symbol} 数据 ({i+1}/{total_count})')
            
            # 获取历史数据
            data = history(
                symbol=symbol,
                frequency='1d',
                start_time=start_date,
                end_time=end_date,
                fields='open,high,low,close,volume,amount,turn,pct_chg,amplitude',
                adjust=ADJUST_PREV,
                df=True
            )
            
            if data is not None and len(data) > 0:
                # 保存到数据库
                for _, row in data.iterrows():
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_market_data 
                        (symbol, trade_date, open_price, high_price, low_price, close_price, 
                         volume, amount, turnover_rate, pct_change, amplitude)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol, row.name.date(), row['open'], row['high'], row['low'], 
                        row['close'], row['volume'], row['amount'], row['turn'], 
                        row['pct_chg'], row['amplitude']
                    ))
                
                conn.commit()
                success_count += 1
            
        except Exception as e:
            print(f'获取 {symbol} 数据失败: {e}')
            continue
    
    conn.close()
    print(f'✅ 历史数据获取完成: {success_count}/{total_count}')



# 财务数据获取脚本
def get_comprehensive_financial_data():
    """获取财务数据"""
    import sqlite3
    
    symbols = get_all_stocks()
    conn = sqlite3.connect('data/enhanced_market_data.db')
    
    for symbol in symbols:
        try:
            # 获取财务指标
            financial_data = get_fundamentals(
                table='trading_derivative_indicator',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='PETTM,PBLF,PCTTM,PSTTM,ROETTM,ROATTM'
            )
            
            # 获取财务报表数据
            income_data = get_fundamentals(
                table='income_statement',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='REVENUE,NETPROFIT,GROSSPROFIT'
            )
            
            # 获取资产负债表数据
            balance_data = get_fundamentals(
                table='balance_sheet',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='TOTALASSETS,TOTALLIAB'
            )
            
            # 合并数据并保存
            if financial_data is not None and len(financial_data) > 0:
                for _, row in financial_data.iterrows():
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO financial_data 
                        (symbol, report_date, pe_ttm, pb_lf, ps_ttm, pcf_ttm, roe_ttm, roa_ttm)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol, row['pub_date'], row['PETTM'], row['PBLF'], 
                        row['PCTTM'], row['PSTTM'], row['ROETTM'], row['ROATTM']
                    ))
                
                conn.commit()
            
        except Exception as e:
            print(f'获取 {symbol} 财务数据失败: {e}')
            continue
    
    conn.close()
    print('✅ 财务数据获取完成')
