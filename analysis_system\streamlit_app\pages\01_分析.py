import streamlit as st
import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入工具函数
from utils.data_loader import load_analysis_results

st.set_page_config(page_title="交易分析", page_icon="📊", layout="wide")

st.title("交易数据详细分析")

# 检查分析结果是否存在
analysis_file = "reports/trade_analysis_results.csv"
if not os.path.exists(analysis_file):
    st.warning("未找到分析结果文件。请先在主页运行交易分析。")
    st.stop()

# 加载分析结果
try:
    df = load_analysis_results()
    if df is None:
        st.error("加载分析结果失败")
        st.stop()
    st.success(f"成功加载分析数据: {len(df)}条交易记录")
except Exception as e:
    st.error(f"加载分析数据失败: {str(e)}")
    st.stop()

# 交易概览
st.header("交易概览")
col1, col2, col3, col4 = st.columns(4)

# 计算买入金额和卖出金额以及实际收益率
df['Buy_Amount'] = df['Buy_Price'] * df['Volume']
df['Sell_Amount'] = df['Sell_Price'] * df['Volume']
df['Profit_Amount'] = df['Sell_Amount'] - df['Buy_Amount']
df['Actual_Profit_Pct'] = (df['Profit_Amount'] / df['Buy_Amount'] * 100)

# 计算关键指标
total_trades = len(df)
winning_trades = len(df[df['Actual_Profit_Pct'] > 0])
losing_trades = len(df[df['Actual_Profit_Pct'] <= 0])
win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
avg_profit = df['Actual_Profit_Pct'].mean()
avg_win = df[df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if winning_trades > 0 else 0
avg_loss = df[df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if losing_trades > 0 else 0

with col1:
    st.metric("总交易次数", f"{total_trades}")
with col2:
    st.metric("胜率", f"{win_rate:.2f}%")
with col3:
    st.metric("平均单笔收益", f"{avg_profit:.2f}%")
with col4:
    st.metric("盈亏比", f"{abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "∞")

# 添加更详细的收益率统计
st.subheader("单笔交易收益率统计")
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric("最大单笔收益", f"{df['Actual_Profit_Pct'].max():.2f}%")
with col2:
    st.metric("最大单笔亏损", f"{df['Actual_Profit_Pct'].min():.2f}%")
with col3:
    st.metric("收益率中位数", f"{df['Actual_Profit_Pct'].median():.2f}%")
with col4:
    st.metric("收益率标准差", f"{df['Actual_Profit_Pct'].std():.2f}%")

# 高级分析选项
st.header("高级分析")

with st.expander("收益分布分析"):
    # 收益分布统计
    profit_stats = df['Actual_Profit_Pct'].describe().to_frame().T
    profit_stats['skew'] = df['Actual_Profit_Pct'].skew()
    profit_stats['kurtosis'] = df['Actual_Profit_Pct'].kurtosis()
    
    # 添加中文列名映射
    stats_cn = {
        'count': '样本数',
        'mean': '均值',
        'std': '标准差',
        'min': '最小值',
        '25%': '25%分位数',
        '50%': '中位数',
        '75%': '75%分位数',
        'max': '最大值',
        'skew': '偏度',
        'kurtosis': '峰度'
    }
    
    # 创建带有双列标题的数据框
    profit_stats_display = profit_stats.copy()
    # 重命名行索引
    profit_stats_display.index = ['收益率统计']
    # 创建多级列，包含英文和中文
    profit_stats_display.columns = pd.MultiIndex.from_tuples(
        [(col, stats_cn.get(col, col)) for col in profit_stats.columns]
    )
    
    st.dataframe(profit_stats_display)
    
    # 最佳和最差交易
    st.subheader("最佳交易")
    best_trades = df.nlargest(5, 'Actual_Profit_Pct')
    # 在UI中展示前，可以添加自定义列名或者格式化
    st.dataframe(best_trades)
    
    st.subheader("最差交易")
    worst_trades = df.nsmallest(5, 'Actual_Profit_Pct')
    st.dataframe(worst_trades)

with st.expander("按股票分析"):
    if 'Symbol' in df.columns:
        # 计算每个股票的统计信息
        symbol_stats = df.groupby('Symbol').agg({
            'Actual_Profit_Pct': ['count', 'mean', 'std', 'min', 'max'],
            'Buy_Price': 'mean',
            'Sell_Price': 'mean'
        })
        symbol_stats.columns = ['交易次数', '平均收益率', '收益标准差', '最小收益', '最大收益', '平均买入价', '平均卖出价']
        symbol_stats['胜率'] = df[df['Actual_Profit_Pct'] > 0].groupby('Symbol').size() / symbol_stats['交易次数'] * 100
        
        # 显示股票统计表
        st.dataframe(symbol_stats.sort_values('交易次数', ascending=False))
    else:
        st.info("数据中没有股票代码信息，无法进行股票分析")

# 添加交易列表部分
st.header("交易列表")

# 添加筛选选项
filter_col1, filter_col2, filter_col3 = st.columns(3)

with filter_col1:
    # 筛选股票
    if 'Symbol' in df.columns:
        symbols = ['全部'] + sorted(df['Symbol'].unique().tolist())
        selected_symbol = st.selectbox('选择股票', symbols)

with filter_col2:
    # 筛选交易结果
    results = ['全部', '盈利', '亏损']
    selected_result = st.selectbox('交易结果', results)

with filter_col3:
    # 排序方式
    sort_options = ['按时间降序', '按时间升序', '按收益率降序', '按收益率升序']
    sort_by = st.selectbox('排序方式', sort_options)

# 应用筛选条件
filtered_df = df.copy()

# 计算买入金额和卖出金额
filtered_df['Buy_Amount'] = filtered_df['Buy_Price'] * filtered_df['Volume']
filtered_df['Sell_Amount'] = filtered_df['Sell_Price'] * filtered_df['Volume']
filtered_df['Profit_Amount'] = filtered_df['Sell_Amount'] - filtered_df['Buy_Amount']
# 重新计算正确的收益率百分比
filtered_df['Actual_Profit_Pct'] = (filtered_df['Profit_Amount'] / filtered_df['Buy_Amount'] * 100)

if selected_symbol != '全部':
    filtered_df = filtered_df[filtered_df['Symbol'] == selected_symbol]

if selected_result == '盈利':
    filtered_df = filtered_df[filtered_df['Actual_Profit_Pct'] > 0]
elif selected_result == '亏损':
    filtered_df = filtered_df[filtered_df['Actual_Profit_Pct'] <= 0]

# 应用排序
if sort_by == '按时间降序':
    filtered_df = filtered_df.sort_values('Sell_Time', ascending=False)
elif sort_by == '按时间升序':
    filtered_df = filtered_df.sort_values('Sell_Time', ascending=True)
elif sort_by == '按收益率降序':
    filtered_df = filtered_df.sort_values('Actual_Profit_Pct', ascending=False)
elif sort_by == '按收益率升序':
    filtered_df = filtered_df.sort_values('Actual_Profit_Pct', ascending=True)

# 显示筛选后的交易列表
st.write(f"显示 {len(filtered_df)} 条交易记录")

# 选择要显示的列
display_columns = [
    'Symbol', 'Buy_Time', 'Sell_Time', 'Buy_Price', 'Sell_Price', 
    'Volume', 'Buy_Amount', 'Sell_Amount', 'Profit_Amount', 'Actual_Profit_Pct', 'Holding_Hours', 'Sell_Reason'
]

# 重命名列以便于显示
display_df = filtered_df[display_columns].copy()
display_df.columns = [
    '股票代码', '买入时间', '卖出时间', '买入价格', '卖出价格', 
    '交易量', '买入金额', '卖出金额', '盈利金额', '收益率(%)', '持仓时间(小时)', '卖出原因'
]

# 格式化数值列
display_df['收益率(%)'] = display_df['收益率(%)'].map('{:.4f}%'.format)
display_df['买入价格'] = display_df['买入价格'].map('{:.2f}'.format)
display_df['卖出价格'] = display_df['卖出价格'].map('{:.2f}'.format)
display_df['买入金额'] = display_df['买入金额'].map('{:.2f}'.format)
display_df['卖出金额'] = display_df['卖出金额'].map('{:.2f}'.format)
display_df['盈利金额'] = display_df['盈利金额'].map('{:.2f}'.format)
display_df['持仓时间(小时)'] = display_df['持仓时间(小时)'].map('{:.1f}'.format)

# 显示交易列表
st.dataframe(display_df, use_container_width=True)

# 导出功能
st.header("数据导出")
if st.button("导出交易数据"):
    with st.spinner("正在准备导出数据..."):
        try:
            # 准备CSV数据
            csv = df.to_csv(index=False).encode('utf-8')
            
            # 创建下载按钮
            st.download_button(
                label="下载交易数据 (CSV)",
                data=csv,
                file_name=f"trade_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime='text/csv',
            )
            
            st.success("数据已准备好，点击上方按钮下载")
        except Exception as e:
            st.error(f"准备导出数据时出错: {str(e)}") 