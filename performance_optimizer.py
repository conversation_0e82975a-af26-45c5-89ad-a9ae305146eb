# coding=utf-8
"""
性能优化管理器
在不破坏现有逻辑的前提下，提供各种性能优化功能
"""

import threading
import time
import gc
import psutil
import os
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor, as_completed
import sqlite3
import queue
from datetime import datetime, timedelta

class PerformanceOptimizer:
    """性能优化管理器"""
    
    def __init__(self, context):
        self.context = context
        self.log = context.log

        # 从配置读取参数（统一配置接口）
        self.config = self._get_config()

        # 基础性能优化开关
        self.enable_batch_db_write = self.config.get('enable_batch_data_fetch', True)
        self.enable_indicator_cache = self.config.get('enable_trix_cache', True)
        self.enable_parallel_processing = self.config.get('enable_parallel_processing', False)
        self.enable_memory_optimization = self.config.get('enable_memory_pool', True)

        # 🚀 高级性能优化开关（合并自advanced_performance_optimizer）
        self.enable_data_preload = self.config.get('enable_data_preload', True)
        self.enable_symbol_cache = self.config.get('enable_symbol_cache', True)
        self.enable_calculation_cache = self.config.get('enable_calculation_cache', True)
        self.enable_vectorized_calculation = self.config.get('enable_vectorized_calculation', True)

        # 配置参数
        self.db_batch_size = self.config.get('batch_size', 1000)
        self.indicator_cache_size = self.config.get('calculation_cache_size', 10000)
        self.max_workers = getattr(context, 'MAX_WORKER_THREADS', 4)
        self.parallel_batch_size = getattr(context, 'PARALLEL_BATCH_SIZE', 50)

        # 🚀 高级优化参数
        self.preload_days = self.config.get('preload_days', 100)
        self.symbol_cache_duration = self.config.get('symbol_cache_duration', 3600)
        self.memory_pool_size = self.config.get('memory_pool_size', 50)

        # 初始化组件
        self._init_database_optimizer()
        self._init_indicator_cache()
        self._init_parallel_processor()
        self._init_memory_optimizer()

        # 🚀 初始化高级优化组件
        self._init_advanced_components()

        self.log.info(f"统一性能优化管理器初始化完成")
        self.log.info(f"  批量数据库写入: {'启用' if self.enable_batch_db_write else '禁用'}")
        self.log.info(f"  指标缓存: {'启用' if self.enable_indicator_cache else '禁用'}")
        self.log.info(f"  并行处理: {'启用' if self.enable_parallel_processing else '禁用'}")
        self.log.info(f"  内存优化: {'启用' if self.enable_memory_optimization else '禁用'}")
        self.log.info(f"  数据预加载: {'启用' if self.enable_data_preload else '禁用'}")
        self.log.info(f"  向量化计算: {'启用' if self.enable_vectorized_calculation else '禁用'}")

    def _get_config(self):
        """获取性能优化配置"""
        try:
            from config import get_config_value
            return get_config_value('PERFORMANCE_OPTIMIZATION', {})
        except:
            return {}

    def _init_advanced_components(self):
        """初始化高级优化组件"""
        # 数据缓存系统
        self.data_cache = {}
        self.symbol_cache = {}
        self.calculation_cache = {}

        # 性能统计
        self.performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'data_fetch_time': 0,
            'calculation_time': 0,
            'total_optimizations': 0
        }

        # 内存池
        from collections import deque
        import numpy as np
        self.memory_pool = deque(maxlen=self.memory_pool_size)

        # 预分配内存
        if self.enable_memory_optimization:
            try:
                for _ in range(self.memory_pool_size):
                    self.memory_pool.append(np.zeros(100))
            except Exception as e:
                self.log.debug(f"内存池初始化异常: {e}")
    
    def _init_database_optimizer(self):
        """初始化数据库优化器"""
        if self.enable_batch_db_write:
            self.db_write_queue = queue.Queue()
            self.db_write_thread = threading.Thread(target=self._batch_db_writer, daemon=True)
            self.db_write_thread.start()
            self.log.info(f"数据库批量写入已启用，批次大小: {self.db_batch_size}")
    
    def _init_indicator_cache(self):
        """初始化指标缓存"""
        if self.enable_indicator_cache:
            self.indicator_cache = OrderedDict()
            self.cache_lock = threading.Lock()
            self.cache_expire_seconds = getattr(self.context, 'INDICATOR_CACHE_EXPIRE_SECONDS', 1800)
            self.log.info(f"指标缓存已启用，缓存大小: {self.indicator_cache_size}")
    
    def _init_parallel_processor(self):
        """初始化并行处理器"""
        if self.enable_parallel_processing:
            self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
            self.log.info(f"并行处理已启用，最大线程数: {self.max_workers}")
    
    def _init_memory_optimizer(self):
        """初始化内存优化器"""
        if self.enable_memory_optimization:
            self.last_cleanup_time = time.time()
            self.cleanup_interval = getattr(self.context, 'AUTO_CLEANUP_INTERVAL', 300)
            self.max_memory_mb = getattr(self.context, 'MAX_MEMORY_USAGE_MB', 2048)
            self.log.info(f"内存优化已启用，清理间隔: {self.cleanup_interval}秒")
    
    def queue_database_write(self, table_name, data):
        """队列化数据库写入"""
        if self.enable_batch_db_write:
            self.db_write_queue.put((table_name, data, time.time()))
            return True
        return False
    
    def _batch_db_writer(self):
        """批量数据库写入线程"""
        batch = []
        last_write_time = time.time()
        
        while True:
            try:
                # 等待数据或超时
                try:
                    item = self.db_write_queue.get(timeout=1.0)
                    batch.append(item)
                except queue.Empty:
                    pass
                
                # 检查是否需要写入
                current_time = time.time()
                should_write = (
                    len(batch) >= self.db_batch_size or
                    (batch and current_time - last_write_time > 5.0)  # 5秒超时
                )
                
                if should_write and batch:
                    self._execute_batch_write(batch)
                    batch.clear()
                    last_write_time = current_time
                    
            except Exception as e:
                self.log.error(f"批量数据库写入错误: {e}")
                time.sleep(1)
    
    def _execute_batch_write(self, batch):
        """执行批量写入"""
        try:
            # 按表名分组
            tables = defaultdict(list)
            for table_name, data, timestamp in batch:
                tables[table_name].append(data)
            
            # 批量写入每个表
            for table_name, data_list in tables.items():
                if table_name == 'trades':
                    self._batch_write_trades(data_list)
                elif table_name == 'analysis':
                    self._batch_write_analysis(data_list)
                    
            self.log.debug(f"批量写入完成: {len(batch)}条记录")
            
        except Exception as e:
            self.log.error(f"批量写入执行失败: {e}")
    
    def _batch_write_trades(self, data_list):
        """批量写入交易记录"""
        try:
            # 使用真正的批量SQL写入
            from scripts.data_manager import get_data_manager
            data_manager = get_data_manager()

            if hasattr(data_manager, 'save_trades_batch'):
                # 使用批量写入方法
                success = data_manager.save_trades_batch(data_list)
                if success:
                    self.log.debug(f"批量写入{len(data_list)}条交易记录成功")
                else:
                    self.log.error(f"批量写入{len(data_list)}条交易记录失败")
            else:
                # 回退到单条写入
                for data in data_list:
                    try:
                        from scripts.data_manager import save_trade
                        save_trade(data)
                    except Exception as e:
                        self.log.error(f"写入交易记录失败: {e}")

        except Exception as e:
            self.log.error(f"批量写入交易记录异常: {e}")
            # 回退到单条写入
            for data in data_list:
                try:
                    from scripts.data_manager import save_trade
                    save_trade(data)
                except Exception as e2:
                    self.log.error(f"回退写入交易记录失败: {e2}")
    
    def get_cached_indicator(self, cache_key):
        """获取缓存的指标"""
        if not self.enable_indicator_cache:
            return None
            
        with self.cache_lock:
            if cache_key in self.indicator_cache:
                value, timestamp = self.indicator_cache[cache_key]
                # 检查是否过期
                if time.time() - timestamp < self.cache_expire_seconds:
                    # 移到末尾（LRU）
                    self.indicator_cache.move_to_end(cache_key)
                    return value
                else:
                    # 过期，删除
                    del self.indicator_cache[cache_key]
        return None
    
    def cache_indicator(self, cache_key, value):
        """缓存指标计算结果"""
        if not self.enable_indicator_cache:
            return
            
        with self.cache_lock:
            # 检查缓存大小
            while len(self.indicator_cache) >= self.indicator_cache_size:
                # 删除最旧的项
                self.indicator_cache.popitem(last=False)
            
            # 添加新项
            self.indicator_cache[cache_key] = (value, time.time())
    
    def parallel_process_stocks(self, stock_list, process_func, *args, **kwargs):
        """并行处理股票列表"""
        if not self.enable_parallel_processing or len(stock_list) < self.parallel_batch_size:
            # 如果不启用并行处理或股票数量太少，使用串行处理
            return [process_func(stock, *args, **kwargs) for stock in stock_list]
        
        results = []
        try:
            # 分批并行处理
            for i in range(0, len(stock_list), self.parallel_batch_size):
                batch = stock_list[i:i + self.parallel_batch_size]
                
                # 提交任务
                futures = []
                for stock in batch:
                    future = self.thread_pool.submit(process_func, stock, *args, **kwargs)
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        results.append(result)
                    except Exception as e:
                        self.log.warning(f"并行处理任务失败: {e}")
                        results.append(None)
            
            return results
            
        except Exception as e:
            self.log.error(f"并行处理失败: {e}")
            # 回退到串行处理
            return [process_func(stock, *args, **kwargs) for stock in stock_list]
    
    def check_and_optimize_memory(self):
        """检查并优化内存使用"""
        if not self.enable_memory_optimization:
            return
            
        current_time = time.time()
        if current_time - self.last_cleanup_time < self.cleanup_interval:
            return
        
        try:
            # 获取当前内存使用
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > self.max_memory_mb:
                self.log.warning(f"内存使用过高: {memory_mb:.1f}MB > {self.max_memory_mb}MB，开始清理")
                
                # 清理指标缓存
                if self.enable_indicator_cache:
                    with self.cache_lock:
                        # 清理一半的缓存
                        items_to_remove = len(self.indicator_cache) // 2
                        for _ in range(items_to_remove):
                            if self.indicator_cache:
                                self.indicator_cache.popitem(last=False)
                
                # 强制垃圾回收
                gc.collect()
                
                # 再次检查内存
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                self.log.info(f"内存清理完成: {memory_mb:.1f}MB -> {new_memory_mb:.1f}MB")
            
            self.last_cleanup_time = current_time
            
        except Exception as e:
            self.log.error(f"内存优化失败: {e}")
    
    def get_performance_stats(self):
        """获取性能统计信息"""
        stats = {
            'database_queue_size': self.db_write_queue.qsize() if self.enable_batch_db_write else 0,
            'indicator_cache_size': len(self.indicator_cache) if self.enable_indicator_cache else 0,
            'memory_usage_mb': 0,
            'thread_pool_active': 0
        }
        
        try:
            # 内存使用
            process = psutil.Process(os.getpid())
            stats['memory_usage_mb'] = process.memory_info().rss / 1024 / 1024
            
            # 线程池状态
            if self.enable_parallel_processing:
                stats['thread_pool_active'] = self.thread_pool._threads.__len__()
                
        except Exception as e:
            self.log.error(f"获取性能统计失败: {e}")
        
        return stats
    
    def shutdown(self):
        """关闭性能优化器"""
        try:
            # 等待数据库写入队列清空
            if self.enable_batch_db_write:
                while not self.db_write_queue.empty():
                    time.sleep(0.1)

            # 关闭线程池
            if self.enable_parallel_processing:
                self.thread_pool.shutdown(wait=True)

            # 🚀 清理高级优化组件
            self._cleanup_advanced_components()

            self.log.info("统一性能优化器已关闭")

        except Exception as e:
            self.log.error(f"关闭性能优化器失败: {e}")

    def _cleanup_advanced_components(self):
        """清理高级优化组件"""
        try:
            # 清理缓存
            if hasattr(self, 'data_cache'):
                self.data_cache.clear()
            if hasattr(self, 'symbol_cache'):
                self.symbol_cache.clear()
            if hasattr(self, 'calculation_cache'):
                self.calculation_cache.clear()

            # 清理内存池
            if hasattr(self, 'memory_pool'):
                self.memory_pool.clear()

        except Exception as e:
            self.log.debug(f"清理高级组件异常: {e}")

    # ==================== 🚀 高级优化功能（合并自advanced_performance_optimizer） ====================

    def get_cached_data(self, symbol, count, frequency='1d', fields=None):
        """获取缓存的历史数据"""
        if not self.enable_data_preload:
            return None

        cache_key = f"{symbol}_{count}_{frequency}_{fields}"
        current_time = time.time()

        # 检查缓存
        if cache_key in self.data_cache:
            cache_entry = self.data_cache[cache_key]
            # 检查缓存是否过期（5分钟）
            if current_time - cache_entry['timestamp'] < 300:
                self.performance_stats['cache_hits'] += 1
                return cache_entry['data']

        self.performance_stats['cache_misses'] += 1
        return None

    def cache_data(self, symbol, count, frequency, fields, data):
        """缓存历史数据"""
        if not self.enable_data_preload:
            return

        cache_key = f"{symbol}_{count}_{frequency}_{fields}"
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }

        # 限制缓存大小
        if len(self.data_cache) > 1000:
            # 删除最旧的缓存
            oldest_key = min(self.data_cache.keys(),
                           key=lambda k: self.data_cache[k]['timestamp'])
            del self.data_cache[oldest_key]

    def get_cached_calculation(self, calc_type, symbol, params):
        """获取缓存的计算结果"""
        if not self.enable_calculation_cache:
            return None

        cache_key = f"{calc_type}_{symbol}_{hash(str(params))}"

        if cache_key in self.calculation_cache:
            self.performance_stats['cache_hits'] += 1
            return self.calculation_cache[cache_key]

        self.performance_stats['cache_misses'] += 1
        return None

    def cache_calculation(self, calc_type, symbol, params, result):
        """缓存计算结果"""
        if not self.enable_calculation_cache:
            return

        cache_key = f"{calc_type}_{symbol}_{hash(str(params))}"
        self.calculation_cache[cache_key] = result

        # 限制缓存大小
        if len(self.calculation_cache) > self.indicator_cache_size:
            # 删除一半的缓存
            keys_to_delete = list(self.calculation_cache.keys())[:len(self.calculation_cache)//2]
            for key in keys_to_delete:
                del self.calculation_cache[key]

# 全局性能优化器实例
_performance_optimizer = None

def get_performance_optimizer(context=None):
    """获取性能优化器实例"""
    global _performance_optimizer
    if _performance_optimizer is None and context is not None:
        _performance_optimizer = PerformanceOptimizer(context)
    return _performance_optimizer
