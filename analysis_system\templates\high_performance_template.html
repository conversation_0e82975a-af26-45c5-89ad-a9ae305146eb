<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高胜率高收益交易模式分析</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --text-color: #333;
            --light-bg: #f5f5f5;
            --border-color: #ddd;
        }
        
        body {
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        h1, h2, h3, h4 {
            margin-top: 30px;
            color: var(--primary-color);
            font-weight: 600;
        }
        
        h1 {
            margin-top: 0;
            font-size: 28px;
        }
        
        .update-info {
            position: absolute;
            right: 30px;
            top: 30px;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .section {
            background-color: white;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 5px solid var(--secondary-color);
        }
        
        .section h2 {
            border-bottom: 2px solid var(--light-bg);
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background-color: var(--light-bg);
            padding: 15px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.3s;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: var(--secondary-color);
        }
        
        .metric-label {
            font-size: 16px;
            color: var(--text-color);
            text-align: center;
        }
        
        .strategies {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .strategy-card {
            background-color: var(--light-bg);
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid var(--success-color);
        }
        
        .strategy-name {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .strategy-metric {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px dashed var(--border-color);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            text-align: left;
        }
        
        th {
            background-color: var(--light-bg);
            font-weight: 600;
        }
        
        tr:hover {
            background-color: var(--light-bg);
        }
        
        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-right: 5px;
        }
        
        .tag-blue {
            background-color: rgba(52, 152, 219, 0.2);
            color: var(--secondary-color);
        }
        
        .tag-green {
            background-color: rgba(46, 204, 113, 0.2);
            color: var(--success-color);
        }
        
        .tag-orange {
            background-color: rgba(243, 156, 18, 0.2);
            color: var(--warning-color);
        }
        
        .tag-red {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--accent-color);
        }
        
        .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            font-size: 14px;
            color: #777;
        }
        
        .chart-link {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .chart-link:hover {
            text-decoration: underline;
        }
        
        .chart-link::before {
            content: "📊";
            margin-right: 5px;
        }
        
        .recommendation {
            background-color: rgba(46, 204, 113, 0.1);
            border-left: 4px solid var(--success-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .recommendation h4 {
            margin-top: 0;
            color: var(--success-color);
        }
        
        code {
            background-color: var(--light-bg);
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <h1>高胜率高收益交易模式分析</h1>
        <div class="update-info">更新时间: {{update_time}}</div>
    </header>
    
    <div class="section">
        <h2>高绩效指标总览</h2>
        <p>基于对 <strong>{{total_trades}}</strong> 笔交易的分析，我们总结了以下高胜率高收益的交易模式特征。</p>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{win_rate}}%</div>
                <div class="metric-label">整体胜率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{avg_return}}%</div>
                <div class="metric-label">平均收益率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">90%</div>
                <div class="metric-label">最高胜率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0.08%</div>
                <div class="metric-label">最高平均收益</div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>关键影响因素分析</h2>
        <p>通过对交易数据的分析，我们发现以下因素对交易结果有显著影响：</p>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{{trix_importance}}%</div>
                <div class="metric-label">TRIX指标影响</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{volatility_score_importance}}%</div>
                <div class="metric-label">波动评分影响</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{atr_importance}}%</div>
                <div class="metric-label">ATR百分比影响</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{volatility_importance}}%</div>
                <div class="metric-label">波动率影响</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{allocation_importance}}%</div>
                <div class="metric-label">资金分配影响</div>
            </div>
        </div>
        
        <p>数据显示，<span class="tag tag-blue">TRIX指标</span> 是最具决定性的因素，占总影响力的 <strong>{{trix_importance}}%</strong>。其次是 <span class="tag tag-green">波动评分</span> 和 <span class="tag tag-orange">ATR百分比</span>。</p>
    </div>
    
    <div class="section">
        <h2>五大高胜率交易策略</h2>
        <p>根据我们的分析，以下策略表现出高胜率和可观的收益：</p>
        
        <div class="strategies">
            <div class="strategy-card">
                <div class="strategy-name">策略1: TRIX动量突破</div>
                <div class="strategy-metric">
                    <span>买入条件:</span>
                    <span>TRIX_Buy ≥ {{trix_threshold}}</span>
                </div>
                <div class="strategy-metric">
                    <span>胜率:</span>
                    <span>{{trix_win_rate}}%</span>
                </div>
                <div class="strategy-metric">
                    <span>平均收益:</span>
                    <span>{{trix_return}}%</span>
                </div>
                <div class="strategy-metric">
                    <span>示例交易:</span>
                    <span>BTC/USDT (2023-04-18)</span>
                </div>
            </div>
            
            <div class="strategy-card">
                <div class="strategy-name">策略2: 波动评分策略</div>
                <div class="strategy-metric">
                    <span>买入条件:</span>
                    <span>Volatility_Score_Buy ≥ {{vs_threshold}}</span>
                </div>
                <div class="strategy-metric">
                    <span>胜率:</span>
                    <span>{{vs_win_rate}}%</span>
                </div>
                <div class="strategy-metric">
                    <span>平均收益:</span>
                    <span>0.06%</span>
                </div>
                <div class="strategy-metric">
                    <span>示例交易:</span>
                    <span>ETH/USDT (2023-05-02)</span>
                </div>
            </div>
            
            <div class="strategy-card">
                <div class="strategy-name">策略3: ATR百分比策略</div>
                <div class="strategy-metric">
                    <span>买入条件:</span>
                    <span>ATR_Pct_Buy ≥ 4.17</span>
                </div>
                <div class="strategy-metric">
                    <span>胜率:</span>
                    <span>77.78%</span>
                </div>
                <div class="strategy-metric">
                    <span>平均收益:</span>
                    <span>0.03%</span>
                </div>
                <div class="strategy-metric">
                    <span>示例交易:</span>
                    <span>SOL/USDT (2023-03-21)</span>
                </div>
            </div>
            
            <div class="strategy-card">
                <div class="strategy-name">策略4: 波动率策略</div>
                <div class="strategy-metric">
                    <span>买入条件:</span>
                    <span>Volatility_Buy ≥ 3.89</span>
                </div>
                <div class="strategy-metric">
                    <span>胜率:</span>
                    <span>83.33%</span>
                </div>
                <div class="strategy-metric">
                    <span>平均收益:</span>
                    <span>0.03%</span>
                </div>
                <div class="strategy-metric">
                    <span>示例交易:</span>
                    <span>ADA/USDT (2023-05-10)</span>
                </div>
            </div>
            
            <div class="strategy-card">
                <div class="strategy-name">策略5: 资金分配策略</div>
                <div class="strategy-metric">
                    <span>买入条件:</span>
                    <span>Allocation_Factor_Buy ≥ 2.68</span>
                </div>
                <div class="strategy-metric">
                    <span>胜率:</span>
                    <span>73.83%</span>
                </div>
                <div class="strategy-metric">
                    <span>平均收益:</span>
                    <span>0.03%</span>
                </div>
                <div class="strategy-metric">
                    <span>示例交易:</span>
                    <span>XRP/USDT (2023-04-05)</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>多因子组合策略</h2>
        <p>通过组合多个因子，我们可以构建更加稳健的交易策略。以下是最佳的多因子组合：</p>
        
        <div class="strategy-card">
            <div class="strategy-name">最优多因子组合</div>
            <div class="strategy-metric">
                <span>买入条件:</span>
                <span>TRIX_Buy ≥ {{trix_threshold}} <strong>AND</strong> Volatility_Score_Buy ≥ {{vs_threshold}}</span>
            </div>
            <div class="strategy-metric">
                <span>胜率:</span>
                <span>88.24%</span>
            </div>
            <div class="strategy-metric">
                <span>平均收益:</span>
                <span>0.07%</span>
            </div>
            <div class="strategy-metric">
                <span>交易数量:</span>
                <span>17笔</span>
            </div>
        </div>
        
        <p>当 <span class="highlight">TRIX指标 ≥ {{trix_threshold}}</span> 同时 <span class="highlight">波动评分 ≥ {{vs_threshold}}</span> 时，交易的成功概率显著提高，这表明两个指标之间存在协同效应。</p>
        
        <div class="chart-links">
            <a href="../feature_importance.png" class="chart-link" target="_blank">查看特征重要性图表</a>
            <a href="../trix_vs_profit.png" class="chart-link" target="_blank">查看TRIX与收益关系图</a>
            <a href="../volatility_vs_profit.png" class="chart-link" target="_blank">查看波动率与收益关系图</a>
        </div>
    </div>
    
    <div class="section">
        <h2>卖出策略分析</h2>
        <p>买入策略需要与有效的卖出策略配合才能获得良好的整体表现。我们分析了不同卖出原因对交易结果的影响：</p>
        
        <table>
            <thead>
                <tr>
                    <th>卖出原因</th>
                    <th>交易数量</th>
                    <th>胜率</th>
                    <th>平均收益</th>
                </tr>
            </thead>
            <tbody>
                {{sell_reason_table_rows}}
            </tbody>
        </table>
        
        <p>结论：<span class="tag tag-green">跟踪止损</span> 在保持较高胜率的同时实现了更好的平均收益，建议作为主要卖出策略使用。</p>
    </div>
    
    <div class="section">
        <h2>高绩效交易案例</h2>
        <p>以下是一些表现最优异的交易案例，可以作为策略应用的参考：</p>
        
        <table>
            <thead>
                <tr>
                    <th>交易对</th>
                    <th>交易日期</th>
                    <th>TRIX值</th>
                    <th>波动评分</th>
                    <th>收益率</th>
                    <th>卖出原因</th>
                </tr>
            </thead>
            <tbody>
                {{top_performers_table_rows}}
            </tbody>
        </table>
        
        <p>这些高绩效交易案例共同的特点是：TRIX指标强，波动评分高，并且多数使用了跟踪止损策略。</p>
    </div>
    
    <div class="section">
        <h2>具体操作建议</h2>
        <div class="recommendation">
            <h4>买入条件优化</h4>
            <p>根据分析结果，推荐使用以下参数设置：</p>
            <ul>
                <li>TRIX指标阈值: <code>≥ {{trix_threshold}}</code></li>
                <li>波动评分阈值: <code>≥ {{vs_threshold}}</code></li>
                <li>ATR百分比阈值: <code>≥ 4.17</code></li>
            </ul>
        </div>
        
        <div class="recommendation">
            <h4>卖出策略优化</h4>
            <p>根据卖出策略分析，建议：</p>
            <ul>
                <li>主要卖出策略: <code>跟踪止损</code>，止损比例设为<code>0.5%</code></li>
                <li>备选卖出信号: <code>TRIX死叉</code>，当TRIX指标转为下行趋势时卖出</li>
                <li>避免使用: <code>固定止损</code>，其胜率较低</li>
            </ul>
        </div>
        
        <div class="recommendation">
            <h4>资金管理优化</h4>
            <p>为了进一步提高策略的风险调整后收益率，建议：</p>
            <ul>
                <li>当同时满足TRIX和波动评分条件时，使用<code>较大仓位</code></li>
                <li>当仅满足单个条件时，使用<code>较小仓位</code></li>
                <li>总风险暴露不超过<code>资金的20%</code></li>
            </ul>
        </div>
    </div>
    
    <div class="footer">
        <p>📊 交易策略分析报告 | 基于历史交易数据生成 | 更新时间: {{update_time}}</p>
        <p>⚠️ 声明：历史表现不代表未来收益，交易策略需根据市场变化不断调整</p>
    </div>
</body>
</html> 