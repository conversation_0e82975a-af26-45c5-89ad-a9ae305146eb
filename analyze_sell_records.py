# coding=utf-8
"""
直接分析1808条卖出记录
基于实际卖出数据优化策略
"""

import sqlite3
import pandas as pd
import numpy as np

def analyze_sell_records():
    """分析卖出记录"""
    print('📊 分析1808条卖出记录')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有卖出记录
        query = """
        SELECT 
            timestamp, symbol, price, sell_reason,
            cost_price_sell, net_profit_pct_sell, holding_hours,
            max_profit_pct, final_drawdown_pct,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy
        FROM trades 
        WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 卖出记录总数: {len(df)} 条')
        
        # 计算总体胜率
        wins = len(df[df['net_profit_pct_sell'] > 0])
        total = len(df)
        win_rate = wins / total * 100
        
        avg_profit = df['net_profit_pct_sell'].mean()
        median_profit = df['net_profit_pct_sell'].median()
        
        winning_trades = df[df['net_profit_pct_sell'] > 0]
        losing_trades = df[df['net_profit_pct_sell'] <= 0]
        
        avg_win = winning_trades['net_profit_pct_sell'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades['net_profit_pct_sell'].mean()) if len(losing_trades) > 0 else 0
        
        print(f'\n🎯 实际策略表现:')
        print(f'   总胜率: {win_rate:.1f}% ({wins}/{total})')
        print(f'   平均收益: {avg_profit:.2f}%')
        print(f'   中位数收益: {median_profit:.2f}%')
        print(f'   平均盈利: {avg_win:.2f}%')
        print(f'   平均亏损: {avg_loss:.2f}%')
        print(f'   盈亏比: {avg_win/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
        
        return df
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def analyze_sell_reasons(df):
    """分析卖出原因"""
    print(f'\n📋 卖出原因详细分析')
    print('=' * 50)
    
    # 统计各种卖出原因
    sell_reason_analysis = df.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', 'median', lambda x: (x > 0).mean() * 100],
        'holding_hours': ['mean', 'median']
    }).round(2)
    
    sell_reason_analysis.columns = ['交易数', '平均收益%', '中位收益%', '胜率%', '平均持仓h', '中位持仓h']
    
    # 按胜率排序
    sell_reason_analysis = sell_reason_analysis.sort_values('胜率%', ascending=False)
    
    print(f'📊 各卖出原因表现 (按胜率排序):')
    print(sell_reason_analysis.to_string())
    
    # 分析最佳和最差卖出原因
    best_reason = sell_reason_analysis.index[0]
    worst_reason = sell_reason_analysis.index[-1]
    
    print(f'\n🏆 最佳卖出方式: {best_reason}')
    print(f'   胜率: {sell_reason_analysis.loc[best_reason, "胜率%"]:.1f}%')
    print(f'   平均收益: {sell_reason_analysis.loc[best_reason, "平均收益%"]:.2f}%')
    print(f'   交易数: {sell_reason_analysis.loc[best_reason, "交易数"]}')
    
    print(f'\n📉 最差卖出方式: {worst_reason}')
    print(f'   胜率: {sell_reason_analysis.loc[worst_reason, "胜率%"]:.1f}%')
    print(f'   平均收益: {sell_reason_analysis.loc[worst_reason, "平均收益%"]:.2f}%')
    print(f'   交易数: {sell_reason_analysis.loc[worst_reason, "交易数"]}')
    
    return sell_reason_analysis

def analyze_profit_distribution(df):
    """分析收益分布"""
    print(f'\n📊 收益分布分析')
    print('=' * 40)
    
    # 定义收益区间
    profit_ranges = [
        ('大亏 (<-5%)', df['net_profit_pct_sell'] < -5),
        ('中亏 (-5% to -2%)', (df['net_profit_pct_sell'] >= -5) & (df['net_profit_pct_sell'] < -2)),
        ('小亏 (-2% to 0%)', (df['net_profit_pct_sell'] >= -2) & (df['net_profit_pct_sell'] < 0)),
        ('小盈 (0% to 2%)', (df['net_profit_pct_sell'] >= 0) & (df['net_profit_pct_sell'] < 2)),
        ('中盈 (2% to 5%)', (df['net_profit_pct_sell'] >= 2) & (df['net_profit_pct_sell'] < 5)),
        ('大盈 (>5%)', df['net_profit_pct_sell'] >= 5)
    ]
    
    print(f'📈 收益分布:')
    for range_name, condition in profit_ranges:
        count = condition.sum()
        percentage = count / len(df) * 100
        print(f'   {range_name}: {count}笔 ({percentage:.1f}%)')
    
    # 分析极值
    max_profit = df['net_profit_pct_sell'].max()
    min_profit = df['net_profit_pct_sell'].min()
    
    print(f'\n📊 极值分析:')
    print(f'   最大盈利: {max_profit:.2f}%')
    print(f'   最大亏损: {min_profit:.2f}%')
    
    # 分析分位数
    quantiles = [0.1, 0.25, 0.5, 0.75, 0.9]
    print(f'\n📋 收益分位数:')
    for q in quantiles:
        value = df['net_profit_pct_sell'].quantile(q)
        print(f'   {int(q*100)}%分位数: {value:.2f}%')

def analyze_holding_time_performance(df):
    """分析持仓时间表现"""
    print(f'\n⏰ 持仓时间与收益关系')
    print('=' * 50)
    
    # 转换为天数
    df['holding_days'] = df['holding_hours'] / 24
    
    # 按持仓时间分组
    time_ranges = [
        ('超短线 (<1天)', df['holding_days'] < 1),
        ('短线 (1-3天)', (df['holding_days'] >= 1) & (df['holding_days'] < 3)),
        ('中短线 (3-7天)', (df['holding_days'] >= 3) & (df['holding_days'] < 7)),
        ('中线 (1-2周)', (df['holding_days'] >= 7) & (df['holding_days'] < 14)),
        ('长线 (>2周)', df['holding_days'] >= 14)
    ]
    
    print(f'📊 不同持仓时间的表现:')
    for range_name, condition in time_ranges:
        subset = df[condition]
        if len(subset) > 0:
            wins = len(subset[subset['net_profit_pct_sell'] > 0])
            win_rate = wins / len(subset) * 100
            avg_profit = subset['net_profit_pct_sell'].mean()
            median_profit = subset['net_profit_pct_sell'].median()
            
            print(f'   {range_name}: {len(subset)}笔, 胜率{win_rate:.1f}%, 平均{avg_profit:.2f}%, 中位{median_profit:.2f}%')
    
    # 找出最佳持仓时间
    best_performance = []
    for range_name, condition in time_ranges:
        subset = df[condition]
        if len(subset) >= 10:  # 至少10笔交易
            win_rate = (subset['net_profit_pct_sell'] > 0).mean() * 100
            avg_profit = subset['net_profit_pct_sell'].mean()
            best_performance.append((range_name, win_rate, avg_profit, len(subset)))
    
    if best_performance:
        best_performance.sort(key=lambda x: x[1], reverse=True)  # 按胜率排序
        print(f'\n🏆 最佳持仓时间: {best_performance[0][0]}')
        print(f'   胜率: {best_performance[0][1]:.1f}%')
        print(f'   平均收益: {best_performance[0][2]:.2f}%')

def analyze_score_effectiveness(df):
    """分析评分有效性"""
    print(f'\n📊 多因子评分有效性分析')
    print('=' * 50)
    
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    # 分离盈利和亏损交易
    winning_trades = df[df['net_profit_pct_sell'] > 0]
    losing_trades = df[df['net_profit_pct_sell'] <= 0]
    
    print(f'📈 盈利交易 vs 亏损交易的评分对比:')
    
    score_effectiveness = {}
    
    for col in score_columns:
        if col in df.columns:
            win_values = winning_trades[col].dropna()
            loss_values = losing_trades[col].dropna()
            
            if len(win_values) > 10 and len(loss_values) > 10:
                win_avg = win_values.mean()
                loss_avg = loss_values.mean()
                diff = win_avg - loss_avg
                
                # 计算效果显著性
                combined_std = np.sqrt((win_values.var() + loss_values.var()) / 2)
                significance_score = abs(diff) / combined_std if combined_std > 0 else 0
                
                score_effectiveness[col] = {
                    'win_avg': win_avg,
                    'loss_avg': loss_avg,
                    'diff': diff,
                    'significance': significance_score
                }
                
                significance_level = "🔥高" if significance_score > 0.5 else "🔸中" if significance_score > 0.2 else "🔹低"
                direction = "📈" if diff > 0 else "📉"
                
                print(f'   {col}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} ({direction}{diff:+.3f}) [有效性:{significance_level}]')
    
    # 找出最有效的评分指标
    if score_effectiveness:
        most_effective = max(score_effectiveness.items(), key=lambda x: x[1]['significance'])
        print(f'\n🎯 最有效的评分指标: {most_effective[0]}')
        print(f'   差异: {most_effective[1]["diff"]:+.3f}')
        print(f'   有效性评分: {most_effective[1]["significance"]:.2f}')
    
    return score_effectiveness

def generate_data_driven_recommendations(df, sell_reason_analysis, score_effectiveness):
    """生成数据驱动的建议"""
    print(f'\n💡 基于实际数据的优化建议')
    print('=' * 60)
    
    current_win_rate = (df['net_profit_pct_sell'] > 0).mean() * 100
    current_avg_profit = df['net_profit_pct_sell'].mean()
    
    print(f'📊 当前策略实际表现:')
    print(f'   实际胜率: {current_win_rate:.1f}%')
    print(f'   实际平均收益: {current_avg_profit:.2f}%')
    
    # 卖出策略优化建议
    print(f'\n🎯 卖出策略优化建议:')
    
    best_sell_reason = sell_reason_analysis.index[0]
    best_win_rate = sell_reason_analysis.loc[best_sell_reason, '胜率%']
    
    if best_win_rate > current_win_rate + 10:
        print(f'   1. 优化卖出策略: 重点使用"{best_sell_reason}"方式')
        print(f'      当前最佳胜率: {best_win_rate:.1f}%')
        print(f'      提升潜力: +{best_win_rate - current_win_rate:.1f}%')
    
    # 持仓时间优化
    short_term = df[df['holding_hours'] <= 72]  # 3天内
    if len(short_term) > 0:
        short_win_rate = (short_term['net_profit_pct_sell'] > 0).mean() * 100
        if short_win_rate > current_win_rate + 5:
            print(f'   2. 缩短持仓时间: 3天内卖出胜率更高 ({short_win_rate:.1f}%)')
    
    # 评分阈值优化
    if score_effectiveness:
        print(f'\n📊 多因子阈值优化建议:')
        
        # 基于盈利交易的评分分布设定阈值
        winning_trades = df[df['net_profit_pct_sell'] > 0]
        
        suggested_thresholds = {}
        for score_name, data in score_effectiveness.items():
            if data['significance'] > 0.2:  # 只对有效的指标提建议
                win_values = winning_trades[score_name].dropna()
                if len(win_values) > 20:
                    # 使用25%分位数作为建议阈值
                    suggested_threshold = max(0.1, win_values.quantile(0.25))
                    suggested_thresholds[score_name] = suggested_threshold
                    
                    print(f'   {score_name}: 建议阈值 {suggested_threshold:.3f} (基于盈利交易25%分位数)')
        
        if suggested_thresholds:
            print(f'\n⚙️ 建议的配置更新:')
            print(f'```python')
            print(f'# 基于{len(df)}条实际交易优化')
            print(f'MULTIFACTOR_THRESHOLDS = {{')
            
            score_mapping = {
                'overall_score': 'min_overall_score',
                'technical_score': 'min_technical_score',
                'momentum_score': 'min_momentum_score',
                'volume_score': 'min_volume_score',
                'volatility_score': 'min_volatility_score',
                'trend_score': 'min_trend_score',
                'buy_signal_strength': 'min_buy_signal_strength',
                'risk_adjusted_score': 'min_risk_adjusted_score'
            }
            
            for score_name, config_key in score_mapping.items():
                if score_name in suggested_thresholds:
                    threshold = suggested_thresholds[score_name]
                    print(f"    '{config_key}': {threshold:.3f},")
                else:
                    print(f"    '{config_key}': 0.20,  # 默认值")
            
            print(f'}}')
            print(f'```')
    
    # 预期效果
    potential_improvement = best_win_rate - current_win_rate if best_win_rate > current_win_rate else 0
    print(f'\n📈 预期优化效果:')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    print(f'   优化后预期胜率: {current_win_rate + potential_improvement:.1f}%')
    print(f'   预期提升: +{potential_improvement:.1f}%')

def main():
    """主函数"""
    print('🚀 基于1808条实际卖出记录的策略优化')
    print('=' * 60)
    
    # 分析卖出记录
    df = analyze_sell_records()
    
    if df is not None and len(df) > 0:
        # 分析卖出原因
        sell_reason_analysis = analyze_sell_reasons(df)
        
        # 分析收益分布
        analyze_profit_distribution(df)
        
        # 分析持仓时间
        analyze_holding_time_performance(df)
        
        # 分析评分有效性
        score_effectiveness = analyze_score_effectiveness(df)
        
        # 生成优化建议
        generate_data_driven_recommendations(df, sell_reason_analysis, score_effectiveness)
        
        print(f'\n🎯 总结')
        print('=' * 40)
        print('✅ 基于1808条实际交易的分析完成')
        print('📊 已识别真实的策略表现')
        print('🎯 已生成数据驱动的优化建议')
        print('')
        print('🚀 这些建议基于真实交易结果，具有很高的可信度')
    else:
        print('❌ 无法获取卖出记录数据')

if __name__ == '__main__':
    main()
