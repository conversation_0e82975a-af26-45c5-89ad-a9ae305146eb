# coding=utf-8
"""
增强指标计算器
基于实时股价数据计算各种技术指标，严格避免未来数据泄露
所有计算都基于当前时点及之前的历史数据
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EnhancedIndicatorsCalculator:
    """增强指标计算器"""
    
    def __init__(self):
        self.name = "EnhancedIndicatorsCalculator"
    
    def calculate_all_indicators(self, symbol, data, current_price, current_time):
        """
        计算所有增强指标

        参数:
        symbol: 股票代码
        data: 历史数据DataFrame (包含open, high, low, close, volume)
        current_price: 当前价格
        current_time: 当前时间

        返回:
        dict: 所有指标的字典
        """
        # 设置时间上下文用于未来函数检查
        self._current_time = current_time

        indicators = {}
        
        try:
            # 确保数据格式正确
            if data is None or len(data) == 0:
                return self._get_default_indicators()
            
            # 转换数据类型
            data = data.copy()
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # 移除NaN行
            data = data.dropna()
            
            if len(data) < 2:
                return self._get_default_indicators()
            
            # 1. 价格相关指标
            indicators.update(self._calculate_price_indicators(data, current_price))
            
            # 2. 成交量指标
            indicators.update(self._calculate_volume_indicators(data))
            
            # 3. 技术形态指标
            indicators.update(self._calculate_support_resistance(data, current_price))
            
            # 4. 均线系统指标
            indicators.update(self._calculate_ma_indicators(data, current_price))
            
            # 5. 波动性指标
            indicators.update(self._calculate_volatility_indicators(data))
            
            # 6. 时间序列指标
            indicators.update(self._calculate_time_indicators(current_time))
            
            # 7. 技术指标
            indicators.update(self._calculate_technical_indicators(data))
            
            # 8. 风险控制指标
            indicators.update(self._calculate_risk_indicators(data))
            
            # 9. 形态识别指标
            indicators.update(self._calculate_pattern_indicators(data))
            
            # 10. 综合评分指标
            indicators.update(self._calculate_composite_scores(indicators))
            
            return indicators
            
        except Exception as e:
            print(f"计算指标时出错 {symbol}: {e}")
            return self._get_default_indicators()
    
    def _calculate_price_indicators(self, data, current_price):
        """计算价格相关指标"""
        indicators = {}
        
        try:
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            open_prices = data['open'].values
            
            # 当日涨跌幅
            if len(close_prices) >= 2:
                prev_close = close_prices[-2]
                indicators['price_change_pct'] = (current_price - prev_close) / prev_close * 100
            
            # 价格动量
            for period in [3, 5, 10]:
                if len(close_prices) >= period + 1:
                    past_price = close_prices[-(period + 1)]
                    indicators[f'price_momentum_{period}d'] = (current_price - past_price) / past_price * 100
            
            # 跳空幅度
            if len(data) >= 2:
                today_open = open_prices[-1]
                yesterday_close = close_prices[-2]
                indicators['gap_pct'] = (today_open - yesterday_close) / yesterday_close * 100
            
            # 🚨 未来函数检查：当日价格数据保护
            allow_intraday_price = True

            # 检查是否传入了当前时间参数
            if hasattr(self, '_current_time') and self._current_time:
                # 如果在收盘前，不使用当日的高低价数据
                allow_intraday_price = self._current_time.time() >= datetime.time(14, 45)  # 14:45后允许

            # 日内振幅 (需要当日高低价数据)
            if len(data) >= 1 and allow_intraday_price:
                today_high = high_prices[-1]
                today_low = low_prices[-1]
                today_open = open_prices[-1]
                if today_open > 0:
                    indicators['intraday_range_pct'] = (today_high - today_low) / today_open * 100
            elif not allow_intraday_price:
                indicators['intraday_range_pct'] = None  # 未来函数保护

            # 收盘价位置 (需要当日高低收价数据)
            if len(data) >= 1 and allow_intraday_price:
                today_high = high_prices[-1]
                today_low = low_prices[-1]
                today_close = close_prices[-1]
                if today_high > today_low:
                    indicators['close_to_high_pct'] = (today_close - today_low) / (today_high - today_low) * 100
                    indicators['close_to_low_pct'] = (today_high - today_close) / (today_high - today_low) * 100
            elif not allow_intraday_price:
                indicators['close_to_high_pct'] = None  # 未来函数保护
                indicators['close_to_low_pct'] = None   # 未来函数保护
            
        except Exception as e:
            print(f"计算价格指标时出错: {e}")
        
        return indicators
    
    def _calculate_volume_indicators(self, data):
        """计算成交量指标"""
        indicators = {}
        
        try:
            volume_data = data['volume'].values
            close_prices = data['close'].values
            
            if len(volume_data) == 0:
                return indicators
            
            current_volume = volume_data[-1]
            
            # 均量比
            for period in [5, 10, 20]:
                if len(volume_data) >= period:
                    volume_ma = np.mean(volume_data[-period:])
                    if volume_ma > 0:
                        indicators[f'volume_ma{period}_ratio'] = current_volume / volume_ma
            
            # 量价相关性
            if len(volume_data) >= 10 and len(close_prices) >= 10:
                price_changes = np.diff(close_prices[-10:])
                volume_changes = np.diff(volume_data[-10:])
                if len(price_changes) > 0 and len(volume_changes) > 0:
                    correlation = np.corrcoef(price_changes, volume_changes)[0, 1]
                    indicators['volume_price_correlation'] = correlation if not np.isnan(correlation) else 0
            
            # 成交量动量
            for period in [3, 5]:
                if len(volume_data) >= period + 1:
                    past_volume = volume_data[-(period + 1)]
                    if past_volume > 0:
                        indicators[f'volume_momentum_{period}d'] = (current_volume - past_volume) / past_volume * 100
            
            # 估算换手率 (简化计算)
            indicators['turnover_rate_est'] = current_volume / 1000000  # 简化处理
            
        except Exception as e:
            print(f"计算成交量指标时出错: {e}")
        
        return indicators
    
    def _calculate_support_resistance(self, data, current_price):
        """计算支撑阻力位"""
        indicators = {}
        
        try:
            high_prices = data['high'].values
            low_prices = data['low'].values
            
            # 不同周期的支撑阻力位
            for period in [5, 10, 20]:
                if len(data) >= period:
                    recent_highs = high_prices[-period:]
                    recent_lows = low_prices[-period:]
                    
                    resistance = np.max(recent_highs)
                    support = np.min(recent_lows)
                    
                    indicators[f'resistance_level_{period}d'] = resistance
                    indicators[f'support_level_{period}d'] = support
                    
                    # 距离支撑阻力位的百分比
                    if period <= 10:  # 只计算短期的距离
                        if support > 0:
                            indicators[f'support_distance_pct_{period}d'] = (current_price - support) / current_price * 100
                        if resistance > 0:
                            indicators[f'resistance_distance_pct_{period}d'] = (resistance - current_price) / current_price * 100
            
        except Exception as e:
            print(f"计算支撑阻力位时出错: {e}")
        
        return indicators
    
    def _calculate_ma_indicators(self, data, current_price):
        """计算均线系统指标"""
        indicators = {}
        
        try:
            close_prices = data['close'].values
            
            # 计算不同周期均线
            ma_periods = [5, 10, 20]
            ma_values = {}
            
            for period in ma_periods:
                if len(close_prices) >= period:
                    ma = talib.SMA(close_prices, timeperiod=period)
                    if len(ma) > 0 and not np.isnan(ma[-1]):
                        ma_values[period] = ma[-1]
                        
                        # 价格距均线距离
                        indicators[f'ma{period}_distance_pct'] = (current_price - ma[-1]) / ma[-1] * 100
                        
                        # 均线斜率
                        if len(ma) >= 3:
                            slope = (ma[-1] - ma[-3]) / ma[-3] * 100
                            indicators[f'ma{period}_slope'] = slope
            
            # 均线排列得分
            if len(ma_values) >= 3:
                ma5 = ma_values.get(5, 0)
                ma10 = ma_values.get(10, 0)
                ma20 = ma_values.get(20, 0)
                
                score = 0
                if ma5 > ma10: score += 1
                if ma10 > ma20: score += 1
                if ma5 > ma20: score += 1
                if current_price > ma5: score += 1
                if current_price > ma10: score += 1
                if current_price > ma20: score += 1
                
                indicators['ma_alignment_score'] = score / 6 * 100  # 转换为百分比
            
        except Exception as e:
            print(f"计算均线指标时出错: {e}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, data):
        """计算波动性指标"""
        indicators = {}
        
        try:
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            
            # 不同周期的波动率
            for period in [3, 5, 10, 20]:
                if len(close_prices) >= period + 1:
                    returns = np.diff(close_prices[-period-1:]) / close_prices[-period-1:-1]
                    volatility = np.std(returns) * 100
                    indicators[f'volatility_{period}d'] = volatility
            
            # 波动率比值
            if 'volatility_5d' in indicators and 'volatility_20d' in indicators:
                if indicators['volatility_20d'] > 0:
                    indicators['volatility_ratio_5_20'] = indicators['volatility_5d'] / indicators['volatility_20d']
            
            # 不同周期的ATR
            for period in [3, 5, 10]:
                if len(data) >= period:
                    atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=period)
                    if len(atr) > 0 and not np.isnan(atr[-1]):
                        indicators[f'atr_{period}d'] = atr[-1]
            
            # 标准化ATR
            if 'atr_10d' in indicators and len(close_prices) > 0:
                indicators['atr_normalized'] = indicators['atr_10d'] / close_prices[-1] * 100
            
        except Exception as e:
            print(f"计算波动性指标时出错: {e}")
        
        return indicators
    
    def _calculate_time_indicators(self, current_time):
        """计算时间序列指标"""
        indicators = {}
        
        try:
            if isinstance(current_time, str):
                current_time = pd.to_datetime(current_time)
            
            # 基础时间指标
            indicators['hour_of_day'] = current_time.hour
            indicators['minute_of_hour'] = current_time.minute
            indicators['day_of_week'] = current_time.weekday()  # 0=Monday
            indicators['day_of_month'] = current_time.day
            indicators['week_of_year'] = current_time.isocalendar()[1]
            indicators['month_of_year'] = current_time.month
            indicators['quarter_of_year'] = (current_time.month - 1) // 3 + 1
            
            # 市场时段
            hour = current_time.hour
            if 9 <= hour < 11:
                session = '开盘'
            elif 11 <= hour < 13:
                session = '午盘'
            elif 13 <= hour < 15:
                session = '尾盘'
            else:
                session = '盘外'
            indicators['market_session'] = session
            
            # 距离收盘时间
            if 9 <= hour < 15:
                if hour < 11 or (hour == 11 and current_time.minute < 30):
                    # 上午时段
                    close_time = current_time.replace(hour=11, minute=30, second=0)
                    minutes_to_close = (close_time - current_time).total_seconds() / 60
                else:
                    # 下午时段
                    close_time = current_time.replace(hour=15, minute=0, second=0)
                    minutes_to_close = (close_time - current_time).total_seconds() / 60
                indicators['time_to_close_minutes'] = max(0, int(minutes_to_close))
            else:
                indicators['time_to_close_minutes'] = 0
            
            # 特殊日期标识
            import calendar
            last_day = calendar.monthrange(current_time.year, current_time.month)[1]
            indicators['is_month_end'] = 1 if current_time.day >= last_day - 2 else 0
            indicators['is_quarter_end'] = 1 if current_time.month in [3, 6, 9, 12] and indicators['is_month_end'] else 0
            indicators['is_year_end'] = 1 if current_time.month == 12 and indicators['is_month_end'] else 0
            
        except Exception as e:
            print(f"计算时间指标时出错: {e}")
        
        return indicators
    
    def _calculate_technical_indicators(self, data):
        """计算技术指标"""
        indicators = {}

        try:
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            volume_data = data['volume'].values

            # RSI指标
            for period in [3, 5, 10, 20]:
                if len(close_prices) >= period + 1:
                    rsi = talib.RSI(close_prices, timeperiod=period)
                    if len(rsi) > 0 and not np.isnan(rsi[-1]):
                        indicators[f'rsi_{period}d'] = rsi[-1]

            # MACD指标
            if len(close_prices) >= 26:
                macd, macd_signal, macd_hist = talib.MACD(close_prices, fastperiod=12, slowperiod=26, signalperiod=9)
                if len(macd) > 0:
                    indicators['macd_12_26'] = macd[-1] if not np.isnan(macd[-1]) else 0
                    indicators['macd_signal_9'] = macd_signal[-1] if not np.isnan(macd_signal[-1]) else 0
                    indicators['macd_histogram'] = macd_hist[-1] if not np.isnan(macd_hist[-1]) else 0

                    # MACD斜率
                    if len(macd) >= 3:
                        indicators['macd_slope'] = (macd[-1] - macd[-3]) if not np.isnan(macd[-1]) and not np.isnan(macd[-3]) else 0
                        indicators['macd_signal_slope'] = (macd_signal[-1] - macd_signal[-3]) if not np.isnan(macd_signal[-1]) and not np.isnan(macd_signal[-3]) else 0

            # 布林带指标
            if len(close_prices) >= 20:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close_prices, timeperiod=20)
                if len(bb_upper) > 0:
                    indicators['bb_upper_20'] = bb_upper[-1] if not np.isnan(bb_upper[-1]) else 0
                    indicators['bb_middle_20'] = bb_middle[-1] if not np.isnan(bb_middle[-1]) else 0
                    indicators['bb_lower_20'] = bb_lower[-1] if not np.isnan(bb_lower[-1]) else 0

                    if bb_upper[-1] > bb_lower[-1]:
                        indicators['bb_width_20'] = (bb_upper[-1] - bb_lower[-1]) / bb_middle[-1] * 100
                        indicators['bb_position_20'] = (close_prices[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) * 100

                        # 布林带收缩度
                        if len(bb_upper) >= 5:
                            prev_width = (bb_upper[-5] - bb_lower[-5]) / bb_middle[-5] * 100
                            current_width = indicators['bb_width_20']
                            indicators['bb_squeeze'] = (current_width - prev_width) / prev_width * 100

            # 动量指标
            for period in [3, 5, 10]:
                if len(close_prices) >= period + 1:
                    momentum = talib.MOM(close_prices, timeperiod=period)
                    roc = talib.ROC(close_prices, timeperiod=period)
                    if len(momentum) > 0:
                        indicators[f'momentum_{period}d'] = momentum[-1] if not np.isnan(momentum[-1]) else 0
                    if len(roc) > 0:
                        indicators[f'roc_{period}d'] = roc[-1] if not np.isnan(roc[-1]) else 0

            # ADX趋势强度
            if len(data) >= 14:
                adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)
                di_plus = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=14)
                di_minus = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=14)

                if len(adx) > 0:
                    indicators['adx_14'] = adx[-1] if not np.isnan(adx[-1]) else 0
                    indicators['di_plus_14'] = di_plus[-1] if not np.isnan(di_plus[-1]) else 0
                    indicators['di_minus_14'] = di_minus[-1] if not np.isnan(di_minus[-1]) else 0

                    # ADX斜率
                    if len(adx) >= 3:
                        indicators['adx_slope'] = (adx[-1] - adx[-3]) if not np.isnan(adx[-1]) and not np.isnan(adx[-3]) else 0

            # 资金流向指标
            if len(data) >= 10:
                # 简化的资金流计算
                typical_price = (high_prices + low_prices + close_prices) / 3
                money_flow = typical_price * volume_data

                for period in [5, 10]:
                    if len(money_flow) >= period:
                        mf_avg = np.mean(money_flow[-period:])
                        indicators[f'money_flow_{period}d'] = mf_avg

                # 累积分布线 (简化)
                if len(data) >= 5:
                    ad_line = np.cumsum(((close_prices - low_prices) - (high_prices - close_prices)) / (high_prices - low_prices) * volume_data)
                    indicators['accumulation_distribution'] = ad_line[-1] if len(ad_line) > 0 else 0

        except Exception as e:
            print(f"计算技术指标时出错: {e}")

        return indicators

    def _calculate_risk_indicators(self, data):
        """计算风险控制指标"""
        indicators = {}

        try:
            close_prices = data['close'].values

            # 计算收益率
            if len(close_prices) >= 2:
                returns = np.diff(close_prices) / close_prices[:-1]

                # 不同周期的最大回撤
                for period in [3, 5, 10]:
                    if len(returns) >= period:
                        period_returns = returns[-period:]
                        cumulative_returns = np.cumprod(1 + period_returns)
                        running_max = np.maximum.accumulate(cumulative_returns)
                        drawdown = (cumulative_returns - running_max) / running_max
                        max_drawdown = np.min(drawdown) * 100
                        indicators[f'max_drawdown_{period}d'] = max_drawdown

                # 5日风险调整收益指标
                if len(returns) >= 5:
                    period_returns = returns[-5:]
                    mean_return = np.mean(period_returns)
                    std_return = np.std(period_returns)

                    if std_return > 0:
                        indicators['sharpe_ratio_5d'] = mean_return / std_return * np.sqrt(252)  # 年化

                        # 索提诺比率 (只考虑下行风险)
                        negative_returns = period_returns[period_returns < 0]
                        if len(negative_returns) > 0:
                            downside_std = np.std(negative_returns)
                            indicators['sortino_ratio_5d'] = mean_return / downside_std * np.sqrt(252)

                        # 卡玛比率
                        max_dd_5d = indicators.get('max_drawdown_5d', -1)
                        if max_dd_5d < 0:
                            indicators['calmar_ratio_5d'] = mean_return * 252 / abs(max_dd_5d)

        except Exception as e:
            print(f"计算风险指标时出错: {e}")

        return indicators

    def _calculate_pattern_indicators(self, data):
        """计算形态识别指标"""
        indicators = {}

        try:
            open_prices = data['open'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            close_prices = data['close'].values

            if len(data) >= 3:
                # 使用talib的形态识别函数
                doji = talib.CDLDOJI(open_prices, high_prices, low_prices, close_prices)
                hammer = talib.CDLHAMMER(open_prices, high_prices, low_prices, close_prices)
                engulfing = talib.CDLENGULFING(open_prices, high_prices, low_prices, close_prices)

                indicators['pattern_doji'] = 1 if len(doji) > 0 and doji[-1] != 0 else 0
                indicators['pattern_hammer'] = 1 if len(hammer) > 0 and hammer[-1] != 0 else 0
                indicators['pattern_engulfing'] = 1 if len(engulfing) > 0 and engulfing[-1] != 0 else 0

                if len(data) >= 5:
                    morning_star = talib.CDLMORNINGSTAR(open_prices, high_prices, low_prices, close_prices)
                    evening_star = talib.CDLEVENINGSTAR(open_prices, high_prices, low_prices, close_prices)

                    indicators['pattern_morning_star'] = 1 if len(morning_star) > 0 and morning_star[-1] != 0 else 0
                    indicators['pattern_evening_star'] = 1 if len(evening_star) > 0 and evening_star[-1] != 0 else 0

                # 形态总得分
                pattern_score = sum([
                    indicators.get('pattern_doji', 0),
                    indicators.get('pattern_hammer', 0),
                    indicators.get('pattern_engulfing', 0),
                    indicators.get('pattern_morning_star', 0)
                ])
                indicators['pattern_score_total'] = pattern_score

        except Exception as e:
            print(f"计算形态指标时出错: {e}")

        return indicators

    def _calculate_composite_scores(self, indicators):
        """计算综合评分指标"""
        scores = {}

        try:
            # 技术面得分
            technical_components = []
            if 'rsi_5d' in indicators:
                rsi_score = 100 - abs(indicators['rsi_5d'] - 50)  # RSI越接近50分数越高
                technical_components.append(rsi_score)

            if 'macd_histogram' in indicators:
                macd_score = 50 + indicators['macd_histogram'] * 10  # MACD柱状图为正加分
                technical_components.append(max(0, min(100, macd_score)))

            if technical_components:
                scores['technical_score'] = np.mean(technical_components)

            # 动量得分
            momentum_components = []
            for period in [3, 5, 10]:
                if f'roc_{period}d' in indicators:
                    roc_score = 50 + indicators[f'roc_{period}d']  # 正收益率加分
                    momentum_components.append(max(0, min(100, roc_score)))

            if momentum_components:
                scores['momentum_score'] = np.mean(momentum_components)

            # 波动性得分
            volatility_components = []
            if 'volatility_5d' in indicators:
                vol_score = min(100, indicators['volatility_5d'] * 10)  # 适度波动性
                volatility_components.append(vol_score)

            if 'atr_normalized' in indicators:
                atr_score = min(100, indicators['atr_normalized'] * 5)
                volatility_components.append(atr_score)

            if volatility_components:
                scores['volatility_score_enhanced'] = np.mean(volatility_components)

            # 趋势得分
            trend_components = []
            if 'adx_14' in indicators:
                adx_score = min(100, indicators['adx_14'])  # ADX越高趋势越强
                trend_components.append(adx_score)

            if 'ma_alignment_score' in indicators:
                trend_components.append(indicators['ma_alignment_score'])

            if trend_components:
                scores['trend_score'] = np.mean(trend_components)

            # 成交量得分
            volume_components = []
            if 'volume_ma5_ratio' in indicators:
                vol_ratio_score = min(100, indicators['volume_ma5_ratio'] * 50)  # 成交量放大加分
                volume_components.append(vol_ratio_score)

            if volume_components:
                scores['volume_score'] = np.mean(volume_components)

            # 总体得分
            all_scores = [
                scores.get('technical_score', 50),
                scores.get('momentum_score', 50),
                scores.get('volatility_score_enhanced', 50),
                scores.get('trend_score', 50),
                scores.get('volume_score', 50)
            ]
            scores['overall_score'] = np.mean(all_scores)

        except Exception as e:
            print(f"计算综合得分时出错: {e}")

        return scores

    def _get_default_indicators(self):
        """返回默认指标值"""
        return {
            'price_change_pct': 0.0,
            'intraday_range_pct': 0.0,
            'volume_ma5_ratio': 1.0,
            'hour_of_day': 9,
            'day_of_week': 0,
            'market_session': '开盘',
            'technical_score': 50.0,
            'overall_score': 50.0
        }
