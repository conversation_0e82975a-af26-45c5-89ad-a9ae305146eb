# coding=utf-8
"""
分析重新回测后的数据
评估优化效果并给出进一步建议
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def analyze_latest_performance():
    """分析最新的策略表现"""
    print('📊 分析重新回测后的最新数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT 
            timestamp, symbol, action, price,
            sell_reason, net_profit_pct_sell, holding_hours,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score
        FROM trades 
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 总交易记录: {len(df)} 条')
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        
        # 分析时间分布
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        latest_time = df['timestamp'].max()
        earliest_time = df['timestamp'].min()
        
        print(f'   时间范围: {earliest_time} 到 {latest_time}')
        print(f'   数据跨度: {(latest_time - earliest_time).days} 天')
        
        # 分析最近的活动
        recent_24h = df[df['timestamp'] >= (datetime.now() - timedelta(hours=24))]
        recent_7d = df[df['timestamp'] >= (datetime.now() - timedelta(days=7))]
        
        print(f'   最近24小时: {len(recent_24h)} 条记录')
        print(f'   最近7天: {len(recent_7d)} 条记录')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, None, None

def compare_before_after_optimization(sell_records):
    """对比优化前后的表现"""
    print(f'\n📊 优化前后表现对比')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录，无法进行对比分析')
        return
    
    # 计算当前总体胜率
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    current_wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    current_total = len(completed_trades)
    current_win_rate = current_wins / current_total * 100
    
    current_avg_profit = completed_trades['net_profit_pct_sell'].mean()
    current_avg_win = completed_trades[completed_trades['net_profit_pct_sell'] > 0]['net_profit_pct_sell'].mean()
    current_avg_loss = abs(completed_trades[completed_trades['net_profit_pct_sell'] <= 0]['net_profit_pct_sell'].mean())
    
    print(f'📈 当前策略表现:')
    print(f'   总胜率: {current_win_rate:.1f}% ({current_wins}/{current_total})')
    print(f'   平均收益: {current_avg_profit:.2f}%')
    print(f'   平均盈利: {current_avg_win:.2f}%')
    print(f'   平均亏损: {current_avg_loss:.2f}%')
    print(f'   盈亏比: {current_avg_win/current_avg_loss:.2f}' if current_avg_loss > 0 else '   盈亏比: N/A')
    
    # 与之前的24.7%胜率对比
    previous_win_rate = 24.7
    improvement = current_win_rate - previous_win_rate
    
    print(f'\n📊 优化效果评估:')
    print(f'   优化前胜率: {previous_win_rate:.1f}%')
    print(f'   优化后胜率: {current_win_rate:.1f}%')
    
    if improvement > 0:
        print(f'   ✅ 胜率提升: +{improvement:.1f}%')
        if improvement >= 10:
            print(f'   🎉 优化效果显著!')
        elif improvement >= 5:
            print(f'   📈 优化效果良好')
        else:
            print(f'   📊 优化效果一般')
    elif improvement < 0:
        print(f'   ❌ 胜率下降: {improvement:.1f}%')
        print(f'   💡 需要进一步调整策略')
    else:
        print(f'   ➡️ 胜率无变化')
    
    return current_win_rate, improvement

def analyze_sell_reasons_new(sell_records):
    """分析新的卖出原因分布"""
    print(f'\n📋 卖出原因分析 (优化后)')
    print('=' * 50)
    
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    # 统计卖出原因
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', lambda x: (x > 0).mean() * 100],
        'holding_hours': 'mean'
    }).round(2)
    
    sell_reason_stats.columns = ['交易数', '平均收益%', '胜率%', '平均持仓h']
    sell_reason_stats = sell_reason_stats.sort_values('胜率%', ascending=False)
    
    print(f'📊 各卖出原因表现:')
    print(sell_reason_stats.to_string())
    
    # 检查优化效果
    print(f'\n🎯 优化效果检查:')
    
    # 检查固定止损是否减少
    fixed_stop_loss = completed_trades[completed_trades['sell_reason'] == '固定止损']
    if len(fixed_stop_loss) > 0:
        fixed_stop_rate = (fixed_stop_loss['net_profit_pct_sell'] > 0).mean() * 100
        print(f'   固定止损: {len(fixed_stop_loss)}笔, 胜率{fixed_stop_rate:.1f}%')
        if fixed_stop_rate > 0:
            print(f'   ✅ 固定止损胜率改善 (之前0%)')
        else:
            print(f'   ⚠️ 固定止损胜率仍为0%，建议进一步放宽')
    else:
        print(f'   ✅ 没有固定止损交易 (优化成功)')
    
    # 检查时间止损是否消除
    time_stop_loss = completed_trades[completed_trades['sell_reason'] == '时间止损']
    if len(time_stop_loss) > 0:
        print(f'   ⚠️ 仍有时间止损: {len(time_stop_loss)}笔')
        print(f'   💡 建议检查时间止损配置')
    else:
        print(f'   ✅ 时间止损已消除 (优化成功)')
    
    # 检查跟踪止盈表现
    trailing_stop = completed_trades[completed_trades['sell_reason'] == '跟踪止盈']
    if len(trailing_stop) > 0:
        trailing_rate = (trailing_stop['net_profit_pct_sell'] > 0).mean() * 100
        print(f'   跟踪止盈: {len(trailing_stop)}笔, 胜率{trailing_rate:.1f}%')
        if trailing_rate >= 70:
            print(f'   ✅ 跟踪止盈表现优秀')
        else:
            print(f'   💡 跟踪止盈胜率可进一步优化')

def analyze_multifactor_effectiveness(buy_records):
    """分析多因子策略有效性"""
    print(f'\n🎯 多因子策略有效性分析')
    print('=' * 50)
    
    if len(buy_records) == 0:
        print('⚠️ 没有买入记录')
        return
    
    # 分析多因子评分分布
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    print(f'📊 多因子评分分布 (基于{len(buy_records)}条买入记录):')
    
    for col in score_columns:
        if col in buy_records.columns:
            values = buy_records[col].dropna()
            if len(values) > 0:
                mean_val = values.mean()
                median_val = values.median()
                min_val = values.min()
                max_val = values.max()
                
                print(f'   {col}: 均值{mean_val:.3f}, 中位{median_val:.3f}, 范围[{min_val:.3f}, {max_val:.3f}]')
    
    # 检查当前阈值的满足情况
    from config import get_config_value
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    print(f'\n📋 阈值满足情况分析:')
    
    score_mapping = {
        'min_overall_score': 'overall_score',
        'min_technical_score': 'technical_score',
        'min_momentum_score': 'momentum_score',
        'min_volume_score': 'volume_score',
        'min_volatility_score': 'volatility_score',
        'min_trend_score': 'trend_score',
        'min_buy_signal_strength': 'buy_signal_strength',
        'min_risk_adjusted_score': 'risk_adjusted_score'
    }
    
    total_satisfied = 0
    for threshold_key, score_col in score_mapping.items():
        if threshold_key in thresholds and score_col in buy_records.columns:
            threshold_value = thresholds[threshold_key]
            values = buy_records[score_col].dropna()
            
            if len(values) > 0:
                satisfied_count = len(values[values >= threshold_value])
                satisfaction_rate = satisfied_count / len(values) * 100
                
                print(f'   {score_col}: {satisfied_count}/{len(values)} ({satisfaction_rate:.1f}%) >= {threshold_value}')
                
                if satisfaction_rate >= 80:
                    total_satisfied += 1

def generate_optimization_recommendations(current_win_rate, improvement, sell_records):
    """生成进一步的优化建议"""
    print(f'\n💡 进一步优化建议')
    print('=' * 50)
    
    print(f'📊 当前状况评估:')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    print(f'   优化效果: {improvement:+.1f}%')
    
    recommendations = []
    
    # 基于胜率水平给出建议
    if current_win_rate < 30:
        recommendations.extend([
            '🚨 胜率仍然偏低，需要大幅调整',
            '建议进一步放宽止损条件 (从2.5%到3.5%)',
            '考虑延长最大持仓时间 (从20天到30天)',
            '降低多因子阈值，增加买入机会'
        ])
    elif current_win_rate < 40:
        recommendations.extend([
            '📊 胜率有所改善，但仍有提升空间',
            '微调止损参数，减少过早卖出',
            '优化跟踪止盈参数',
            '分析表现最好的股票特征'
        ])
    elif current_win_rate < 50:
        recommendations.extend([
            '📈 胜率接近目标，进行精细化调整',
            '优化仓位管理策略',
            '加强风险控制机制',
            '考虑行业轮动策略'
        ])
    else:
        recommendations.extend([
            '🎉 胜率表现优秀，保持当前策略',
            '监控策略稳定性',
            '考虑适当提高仓位',
            '探索更多优质标的'
        ])
    
    # 基于卖出原因分析给出建议
    if len(sell_records) > 0:
        completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
        if len(completed_trades) > 0:
            # 检查固定止损情况
            fixed_stop_loss = completed_trades[completed_trades['sell_reason'] == '固定止损']
            if len(fixed_stop_loss) > 0:
                fixed_rate = (fixed_stop_loss['net_profit_pct_sell'] > 0).mean() * 100
                if fixed_rate == 0:
                    recommendations.append('⚠️ 固定止损胜率仍为0%，建议放宽到3.5%')
            
            # 检查持仓时间分布
            avg_holding = completed_trades['holding_hours'].mean()
            if avg_holding < 72:  # 少于3天
                recommendations.append('📊 平均持仓时间较短，考虑延长持仓')
    
    # 输出建议
    print(f'\n🎯 具体优化建议:')
    for i, rec in enumerate(recommendations, 1):
        print(f'   {i}. {rec}')
    
    # 生成配置调整建议
    if current_win_rate < 35:
        print(f'\n⚙️ 建议的配置调整:')
        print(f'```python')
        print(f'# 进一步优化配置')
        print(f'FIXED_STOP_LOSS_RATIO = 0.035      # 从2.5%放宽到3.5%')
        print(f'DYNAMIC_STOP_LOSS_RATIO = 0.035    # 从2.5%放宽到3.5%')
        print(f'MAX_HOLDING_DAYS = 30              # 从20天延长到30天')
        print(f'')
        print(f'# 进一步降低多因子阈值')
        print(f'MULTIFACTOR_THRESHOLDS = {{')
        print(f'    "min_overall_score": 0.12,     # 从0.15降到0.12')
        print(f'    "min_technical_score": 0.08,   # 从0.10降到0.08')
        print(f'    "min_trend_score": 0.35,       # 从0.40降到0.35')
        print(f'}}')
        print(f'```')

def main():
    """主函数"""
    print('🚀 分析重新回测后的策略表现')
    print('=' * 60)
    
    # 分析最新表现
    df, buy_records, sell_records = analyze_latest_performance()
    
    if df is not None:
        # 对比优化前后表现
        current_win_rate, improvement = compare_before_after_optimization(sell_records)
        
        # 分析卖出原因
        analyze_sell_reasons_new(sell_records)
        
        # 分析多因子有效性
        analyze_multifactor_effectiveness(buy_records)
        
        # 生成优化建议
        if current_win_rate is not None:
            generate_optimization_recommendations(current_win_rate, improvement, sell_records)
        
        print(f'\n🎯 总结')
        print('=' * 40)
        print('✅ 重新回测数据分析完成')
        print('📊 已评估优化效果')
        print('💡 已生成进一步优化建议')
        print('')
        print('🚀 根据分析结果继续优化策略')
    else:
        print('❌ 无法获取回测数据')

if __name__ == '__main__':
    main()
