#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和交易策略配置文件
--------------------

此文件包含策略的所有可配置参数，支持动态加载，修改后自动生效。

性能优化相关配置:
- ENABLE_PARALLEL_COMPUTING: 是否启用并行计算
- PARALLEL_WORKERS: 并行计算的线程数
- CACHE_EXPIRES_SECONDS: 普通缓存的有效期(秒)
- LONG_CACHE_EXPIRES_SECONDS: 长期缓存的有效期(秒)
- MAX_ANALYSIS_STOCKS: 每次分析的最大股票数量

买入信号配置:
- ENABLE_BUY_SIGNALS: 是否启用买入信号
- ENABLE_TRIX_BUY_SIGNAL: 是否启用TRIX买入信号
- ENABLE_MA_CROSS_BUY_SIGNAL: 是否启用均线交叉买入信号

卖出信号配置:
- ENABLE_SELL_SIGNALS: 是否启用卖出信号
- ENABLE_TRIX_SELL_SIGNAL: 是否启用TRIX卖出信号
- ENABLE_TRAILING_STOP: 是否启用跟踪止盈
- ENABLE_DYNAMIC_STOP_LOSS: 是否启用动态止损
"""

# =============================================================================
# 交易核心参数
# =============================================================================

# 基础参数
INDEX_SYMBOL = 'SHSE.000300'  # 参考指数
SUBSCRIBE_ALL_MARKET = False  # 是否订阅全市场股票，True表示订阅全市场，False表示只订阅参考指数成分股

# =============================================================================
# 股票过滤参数
# =============================================================================

# 股票类型过滤参数
FILTER_ST_STOCKS = True           # 是否过滤ST股票
FILTER_STARTUP_BOARD = True      # 是否过滤创业板股票(3开头)
FILTER_SCIENCE_BOARD = True       # 是否过滤科创板股票(688开头)
FILTER_BEIJING_BOARD = False      # 是否过滤北交所股票(8开头)

# 价格过滤参数
PRICE_FILTER_ENABLED = True       # 是否启用价格过滤
MIN_PRICE_FILTER = 1.0            # 最低价格过滤(元)
MAX_PRICE_FILTER = 100.0          # 最高价格过滤(元)

# 持仓和资金管理参数
MAX_POSITIONS = 30            # 最大持仓数量
POSITION_RATIO = 0.9          # 仓位比例系数
SINGLE_POSITION_LIMIT = 150000 # 单只股票最大持仓金额（绝对值）
SINGLE_POSITION_RATIO = 0.08   # 单只股票最大持仓比例（占总资金比例）
DYNAMIC_POSITION_MAX_RATIO = 0.15 # 动态调整后单只股票最大持仓比例
MIN_POSITION_LIMIT = 1000    # 单只股票最小持仓金额（绝对值）
MIN_POSITION_RATIO = 0.03     # 单只股票最小持仓比例（占总资金比例）

# 止盈止损参数
TRAILING_STOP = 0.015         # 跟踪止盈阈值(1.5%)
MIN_HOLDING_DAYS = 1          # 进行跟踪止盈检查所需的最小持仓天数
T_PLUS_1 = True               # 是否遵循A股T+1交易规则

# =============================================================================
# 回测参数
# =============================================================================

# 回测参数
BACKTEST_START_TIME = '2025-06-01 09:30:00'
BACKTEST_END_TIME = '2025-06-11 15:00:00'
BACKTEST_INITIAL_CASH = 1000000
BACKTEST_COMMISSION_RATIO = 0.0003
BACKTEST_SLIPPAGE_RATIO = 0.0002

# 买入检查间隔模式设置
BUY_CHECK_INTERVAL_MODE = True  # 是否启用间隔模式进行买入检查
BUY_CHECK_INTERVAL_MINUTES = 1  # 买入检查的时间间隔(分钟)

# 信号检查和持仓更新参数

SIGNAL_CHECK_INTERVAL = 1     # 卖出信号计算间隔(分钟)
POSITION_CHECK_INTERVAL = 30  # 持仓更新间隔(分钟)

# =============================================================================
# 交易执行参数
# =============================================================================

# 订单类型参数

USE_MARKET_ORDER = True      # 是否使用市价单（True使用市价单，False使用限价单）
PRICE_ADJUST_RATIO = 0.002    # 价格调整比例（限价单时使用）

# =============================================================================
# 信号生成参数
# =============================================================================

# 历史数据获取参数

HISTORY_DATA_DAYS = 50        # 获取历史数据的天数（用于一般技术指标）
HISTORY_DATA_DAYS_VOLATILITY = 60  # 获取历史数据的天数（用于波动性计算）

# 历史数据处理参数
HISTORY_DATA_FORMAT = 'dataframe'  # 历史数据返回格式，可选 'dataframe' 或 'dict'
HISTORY_COLUMN_MAPPING = {         # 历史数据列名映射
    'symbol': 'symbol',
    'open': 'open',
    'high': 'high',
    'low': 'low',
    'close': 'close',
    'volume': 'volume',
    'amount': 'amount',
    'time': 'time'
}
ENABLE_COLUMN_RENAME = True        # 是否启用列名重命名
FIX_COLUMN_NAMES = True            # 是否修复错误的列名格式（如包含分隔符的列名）

# 信号开关参数
ENABLE_BUY_SIGNALS = True     # 是否启用买入信号
ENABLE_SELL_SIGNALS = True    # 是否启用卖出信号

# 买入信号参数
ENABLE_TRIX_BUY_SIGNAL = True # 是否启用TRIX买入信号（当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX）
ENABLE_MA_CROSS_BUY_SIGNAL = False # 是否启用均线交叉买入信号



# 卖出信号参数
ENABLE_TRAILING_STOP = True   # 是否启用跟踪止盈卖出信号
ENABLE_TRIX_SELL_SIGNAL = True # 是否启用TRIX死叉卖出信号
ENABLE_DYNAMIC_STOP_LOSS = True # 是否启用动态止损

# TRIX指标参数
TRIX_EMA_PERIOD = 3           # TRIX的EMA周期
USE_TALIB_TRIX = True        # 是否使用talib直接计算TRIX（True使用talib，False使用自定义计算）

# 均线参数
MA_SHORT_PERIOD = 3           # 短期均线周期
MA_MID_PERIOD = 7             # 中期均线周期
MA_LONG_PERIOD = 20           # 长期均线周期

# 技术指标参数
RSI_PERIOD = 14               # RSI计算周期
BOLL_PERIOD = 20              # 布林带计算周期

# =============================================================================
# 波动性相关参数
# =============================================================================

# 波动性筛选参数
VOLATILITY_PERIOD = 20        # 计算波动性的周期
VOLATILITY_THRESHOLD = 1.0    # 波动性阈值(相对市场波动率的倍数)
ATR_THRESHOLD = 1.5           # ATR阈值(占收盘价的百分比)
VOLATILITY_WEIGHT = 0.7       # 波动率在综合得分中的权重
ATR_WEIGHT = 0.3              # ATR在综合得分中的权重
MIN_ABSOLUTE_VOLATILITY = 1.8 # 最小绝对波动率阈值(%)

# 初始市场波动率(后续会动态更新)
DEFAULT_MARKET_VOLATILITY = 2.0

# 波动性资金调整参数
MAX_VOLATILITY_FACTOR = 3.0   # 最大波动性资金调整因子
MIN_VOLATILITY_FACTOR = 1.0   # 最小波动性资金调整因子
VOLATILITY_FACTOR_SCALE = 1.5 # 波动性因子缩放系数

# 波动性计算错误处理
SKIP_VOLATILITY_ERROR_STOCKS = True  # 是否跳过波动性计算出错的股票
DEFAULT_VOLATILITY_VALUE = 2.5       # 当计算出错时使用的默认波动性值
MAX_VOLATILITY_ERRORS = 50           # 每日最大允许的波动性计算错误数量
LOG_VOLATILITY_ERRORS = True         # 是否记录波动性计算错误

# =============================================================================
# 性能优化参数
# =============================================================================

# 性能优化总开关
"""
性能优化总开关说明:
- ENABLE_PERFORMANCE_OPTIMIZATION: 是否启用性能优化设置
  - 设为True时，将应用以下性能优化设置:
    * 禁用额外的风险检查(RISK_CHECK_ENABLED=False)
    * 限制每次买入检查的最大买入股票数量(MAX_BUY_BATCH)
  - 设为False时，将使用更频繁的检查，可能提高交易及时性但增加系统负担
  - 默认为False，在系统负载较高时可考虑设为True
"""
ENABLE_PERFORMANCE_OPTIMIZATION = False  # 是否启用性能优化设置

# ==============================================
# 性能分析参数
# ==============================================
"""
性能分析参数说明:
- ENABLE_PERFORMANCE_PROFILING: 是否启用性能分析
  - 设为True时，系统将记录关键函数的执行时间
  - 设为False时，不会进行性能分析，无额外开销
  - 仅在需要排查性能瓶颈时启用
  
- PROFILING_OUTPUT_FILE: 性能分析结果输出文件
  - 设置为None时输出到日志
  - 设置为文件路径时，将结果写入文件

- PROFILING_THRESHOLD_MS: 性能分析阈值(毫秒)
  - 仅记录执行时间超过此阈值的函数调用
  - 设置较低值可捕获更多信息，但会增加输出量
  - 建议值: 回测模式10ms，实盘模式50ms

- PROFILING_TOP_FUNCTIONS: 性能分析结果展示的函数数量
  - 每次汇总仅展示最耗时的N个函数
  - 设为0则展示所有记录的函数
"""
ENABLE_PERFORMANCE_PROFILING = False  # 是否启用性能分析
PROFILING_OUTPUT_FILE = "data/performance_profile.log"  # 性能分析结果输出文件，指定输出到data目录下
PROFILING_THRESHOLD_MS = 10  # 性能分析阈值(毫秒)，仅记录执行时间超过此阈值的函数调用
PROFILING_TOP_FUNCTIONS = 20  # 性能分析结果展示的函数数量
PROFILING_SUMMARY_INTERVAL = 30  # 性能分析结果汇总间隔(秒)

# ==============================================
# 买入检查时间点配置
# ==============================================
"""
买入检查时间点配置说明:
- BUY_CHECK_TIMES: 每日固定时间点进行买入检查(24小时制，格式:'HH:MM:SS')
  - 系统会在这些指定的时间点调用buy_strategy函数检查买入条件
  - 时间点越多，买入机会越多，但也会增加系统计算负担
  - 时间点设置建议:
    * 开盘后不久(如9:31)可以捕捉开盘机会
    * 上午和下午各设置2-3个检查点较为合理
    * 尾盘时间(如14:30)可以捕捉日内趋势确认的机会
  - 交易风格对应的配置建议:
    * 高频交易: 每15分钟一个检查点(如9:31,9:45,10:00...)
    * 中频交易: 当前默认配置(7个时间点)
    * 低频交易: 每天3-4个检查点(如9:45,11:00,14:00,14:45)
  
  注意: 修改此配置后，如果使用动态配置模式，会在下次配置重载时(09:00或13:00)生效
  如果使用静态配置模式，则需要重启策略才能生效

- BUY_CHECK_INTERVAL_MODE: 是否启用间隔模式进行买入检查
  - 如果设为True，系统将按照BUY_CHECK_INTERVAL_MINUTES指定的分钟间隔进行买入检查
  - 如果设为False，系统将按照BUY_CHECK_TIMES指定的固定时间点进行买入检查
  - 默认为False，使用固定时间点模式

- BUY_CHECK_INTERVAL_MINUTES: 买入检查的时间间隔(分钟)
  - 仅在BUY_CHECK_INTERVAL_MODE为True时生效
  - 系统将从开盘后(9:30)开始，每隔指定的分钟数进行一次买入检查
  - 建议值: 高频交易15分钟，中频交易30分钟，低频交易60分钟
  - 设置过小的值(<10分钟)可能会导致系统负担过重
  - 交易时段范围: 9:30-11:30, 13:00-15:00
"""
# 可以根据需要增加、减少或修改这些时间点
BUY_CHECK_TIMES = [
    '09:31:00',  # 开盘后1分钟，可以捕捉开盘行情
    '10:00:00',  # 上午10点，早盘趋势初步形成
    '10:30:00',  # 上午10点半，早盘中段
    '11:00:00',  # 上午11点，接近午盘前
    '13:30:00',  # 午盘开始后，可以捕捉午盘开盘行情
    '14:00:00',  # 下午2点，下午盘趋势形成
    '14:30:00'   # 下午2点半，尾盘前最后机会
]



# ==============================================
# 持仓摘要输出时间点配置
# ==============================================

# 可以根据需要增加、减少或修改这些时间点
POSITION_SUMMARY_TIMES = [
    #'10:30:00',  # 上午10点半，早盘中段
    #'14:30:00'   # 下午2点半，尾盘前
]

# ==============================================
# 其他性能优化参数
# ==============================================

MAX_BUY_BATCH = 5            # 每次最多买入的股票数量
MAX_ANALYSIS_STOCKS = 500     # 每次分析的最大股票数量
RISK_CHECK_ENABLED = False    # 是否启用风险检查
CACHE_EXPIRE_SECONDS = 3600   # 缓存过期时间(秒)





# =============================================================================
# 日志和数据管理参数
# =============================================================================

# 日志参数
LOG_LEVEL = 'INFO'            # 日志级别
ENABLE_CSV_LOGGING = False    # 禁用CSV日志写入，只保留数据库写入功能

# CSV数据解析参数
CSV_DELIMITER = ','           # CSV文件分隔符
CSV_PARSE_DATES = True        # 是否将日期列解析为日期时间对象
CSV_HEADER = 0                # CSV文件头行索引，0表示第一行是列名
CSV_INDEX_COL = None          # 用作索引的列，None表示使用默认索引

# CSV列名修复参数
FIX_CSV_COLUMN_NAMES = True   # 是否修复CSV列名问题
CSV_COLUMN_SPLIT_CHAR = ','   # CSV列名中可能存在的错误分隔符
EXPECTED_COLUMNS = ['symbol', 'open', 'high', 'low', 'close', 'volume', 'amount', 'time']  # 预期的列名列表
HANDLE_MISSING_COLUMNS = True # 是否处理缺失列（用默认值填充）

# =============================================================================
# API连接参数
# =============================================================================

# API凭证(默认值，可被命令行参数覆盖)
DEFAULT_STRATEGY_ID = '39da9282-3bbd-11f0-8755-d4e98a5e8c02'
DEFAULT_TOKEN = '927022466b9f6476ef82fe30991f521c61feac74'


# ==============================================
# 模式切换与订阅管理参数
# ==============================================
"""
模式切换与订阅管理参数说明:

- ENABLE_MODE_ADAPTIVE: 模式自适应功能设置
  - 0: 启用自动模式自适应（系统自动判断当前运行环境）
  - 1: 强制使用回测模式订阅策略（一次性订阅所有股票）
  - 2: 强制使用模拟盘模式订阅策略（使用批次订阅，批次大小较大）
  - 3: 强制使用实盘模式订阅策略（使用批次订阅，批次大小较小，更频繁轮换）
  - 推荐使用0，让系统自动判断运行环境并优化订阅策略

- SUBSCRIPTION_ROTATION_ENABLED: 是否启用订阅轮换机制
  - 仅在实盘/模拟盘模式下生效，回测模式会忽略此设置
  - 如果设为True，系统将采用轮换订阅机制，每隔一定时间轮换订阅一批股票
  - 如果设为False，系统将尝试一次性订阅所有股票(可能受平台限制)
  - 默认为True，推荐开启以避免超过平台订阅限制

- SUBSCRIPTION_ROTATION_INTERVAL: 订阅轮换间隔(分钟)
  - 仅在SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制系统轮换订阅批次的时间间隔
  - 默认为1分钟，表示每分钟轮换一批股票
  - 降低此值可以更快地轮换完所有股票，但会增加API调用频率
  - 提高此值可以减少API调用，但会延长轮换完所有股票的时间

- LIVE_MODE_BATCH_SIZE: 实盘/模拟盘模式下的批次大小
  - 仅在实盘/模拟盘模式且SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制每批次订阅的股票数量
  - 默认为100，表示每批次订阅100只股票
  - 需要根据平台的订阅限制进行设置，通常掘金量化平台限制为500个订阅
  - 设置过大可能导致超过平台限制，设置过小会增加轮换次数

- MAX_SUBSCRIPTION_LIMIT: 平台最大订阅限制
  - 平台允许的最大订阅数量
  - 默认为50，表示平台最多允许同时订阅50个标的
  - 此参数用于系统自动计算批次数量，确保不超过平台限制
  - 如果平台限制有变化，请相应调整此参数

- SYNC_BUY_CHECK_WITH_ROTATION: 是否将买入检查与订阅轮换同步
  - 仅在实盘/模拟盘模式、SUBSCRIPTION_ROTATION_ENABLED为True且BUY_CHECK_INTERVAL_MODE为True时生效
  - 如果设为True，系统将在每次轮换订阅时同步检查当前批次的买入信号
  - 如果设为False，买入检查将按照BUY_CHECK_INTERVAL_MINUTES设置的间隔独立执行
  - 默认为True，推荐开启以优化性能，避免重复计算
  - 此参数主要用于高频交易场景，如果您使用的是中低频策略，可以设为False

- BACKTEST_FULL_SUBSCRIPTION: 回测模式是否一次性订阅所有股票
  - 仅在回测模式下生效
  - 如果设为True，回测模式将一次性订阅所有股票池中的股票
  - 如果设为False，回测模式也将使用轮换订阅机制(通常不推荐)
  - 默认为True，推荐开启以充分利用回测模式的无限制特性
  - 只有在特殊情况下(如模拟实盘环境进行回测)才需要设为False
"""
ENABLE_MODE_ADAPTIVE = 0           # 模式自适应功能(0:自动 1:回测 2:模拟盘 3:实盘)
SUBSCRIPTION_ROTATION_ENABLED = True  # 启用订阅轮换机制
SUBSCRIPTION_ROTATION_INTERVAL = 1    # 订阅轮换间隔(分钟)
LIVE_MODE_BATCH_SIZE = 50             # 实盘/模拟盘模式下的批次大小（根据账户限制设置为10）
MAX_SUBSCRIPTION_LIMIT = 50           # 平台最大订阅限制（根据账户实际限制调整为50）
MAX_SUBSCRIPTION_PER_BATCH = 50       # 每次订阅操作最多的股票数量
SYNC_BUY_CHECK_WITH_ROTATION = True   # 将买入检查与订阅轮换同步
BACKTEST_FULL_SUBSCRIPTION = True     # 回测模式一次性订阅所有股票


# 您可以根据需要添加更多参数... 

# 启用并行计算功能
ENABLE_PARALLEL_COMPUTING = True

# 并行计算的线程数
PARALLEL_WORKERS = 16

# 常规缓存有效期（秒）
CACHE_EXPIRES_SECONDS = 1800  # 30分钟

# 长期缓存有效期（秒）
LONG_CACHE_EXPIRES_SECONDS = 86400  # 24小时

# 历史数据管理配置
HISTORY_CACHE_SIZE = 500  # 历史数据缓存大小（每个频率的最大条目数）
ENABLE_HISTORY_PREFETCH = True  # 是否启用历史数据预获取
HISTORY_PREFETCH_DAYS = 60  # 预获取的历史数据天数
HISTORY_PREFETCH_BATCH_SIZE = 50  # 每批预获取的股票数量

# =============================================================================
# 异常处理参数
# =============================================================================

# 通用异常处理
MAX_RETRY_COUNT = 3           # 操作失败时的最大重试次数
RETRY_DELAY_SECONDS = 1       # 重试前的延迟时间(秒)
ENABLE_GRACEFUL_ERROR_HANDLING = True  # 启用优雅的错误处理

# 数据解析异常处理
HANDLE_PARSE_ERRORS = True    # 是否处理数据解析异常
SKIP_PROBLEMATIC_STOCKS = True  # 遇到解析问题时是否跳过该股票
MAX_PARSE_ERRORS_PER_DAY = 100  # 每日最大允许的解析错误数量
LOG_PARSE_ERRORS = True       # 是否记录解析错误

# 特定错误处理
FIX_COLUMN_INDEX_ERROR = True  # 修复"None of [Index...] are in the [columns]"类型的错误
COLUMN_ERROR_ACTION = 'rename'  # 列错误处理方式: 'rename'(重命名), 'skip'(跳过), 'default'(使用默认值)

# 性能优化配置 