# coding=utf-8
"""
性能基准测试工具
测试各种优化方案的效果
"""

import time
import os
import json
from datetime import datetime

class PerformanceBenchmark:
    """性能基准测试"""
    
    def __init__(self):
        self.results = {}
        self.test_configs = {
            'baseline': {
                'name': '基准测试（无优化）',
                'config': {
                    'mode': 3,  # 详细模式
                    'enable_batch_data_fetch': False,
                    'enable_trix_cache': False,
                    'enable_data_preload': False,
                    'enable_vectorized_calculation': False,
                    'batch_size': 1
                }
            },
            'basic_optimization': {
                'name': '基础优化',
                'config': {
                    'mode': 2,  # 详细模式
                    'enable_batch_data_fetch': True,
                    'enable_trix_cache': True,
                    'enable_data_preload': False,
                    'enable_vectorized_calculation': False,
                    'batch_size': 100
                }
            },
            'advanced_optimization': {
                'name': '高级优化',
                'config': {
                    'mode': 1,  # 极简模式
                    'enable_batch_data_fetch': True,
                    'enable_trix_cache': True,
                    'enable_data_preload': True,
                    'enable_vectorized_calculation': True,
                    'batch_size': 1000
                }
            }
        }
    
    def update_config(self, config_updates):
        """更新配置文件"""
        try:
            # 读取配置文件
            with open('config.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新配置
            for key, value in config_updates.items():
                if key == 'mode':
                    # 更新日志模式
                    import re
                    pattern = r"'mode':\s*\d+"
                    replacement = f"'mode': {value}"
                    content = re.sub(pattern, replacement, content)
                elif key == 'batch_size':
                    # 更新批量大小
                    pattern = r"'batch_size':\s*\d+"
                    replacement = f"'batch_size': {value}"
                    content = re.sub(pattern, replacement, content)
                elif key.startswith('enable_'):
                    # 更新开关
                    pattern = rf"'{key}':\s*(True|False)"
                    replacement = f"'{key}': {value}"
                    content = re.sub(pattern, replacement, content)
            
            # 写回配置文件
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
            return False
    
    def run_test(self, test_name, duration=60):
        """运行单个测试"""
        print(f'\n🧪 开始测试: {self.test_configs[test_name]["name"]}')
        print('=' * 50)
        
        # 更新配置
        config = self.test_configs[test_name]['config']
        if not self.update_config(config):
            print(f"❌ 配置更新失败，跳过测试: {test_name}")
            return None
        
        print(f"✅ 配置已更新:")
        for key, value in config.items():
            print(f"  {key}: {value}")
        
        print(f"\n🚀 请在掘金客户端运行策略 {duration} 秒...")
        print(f"📊 观察以下指标:")
        print(f"  - 预筛选耗时")
        print(f"  - 买入检查耗时") 
        print(f"  - 内存使用情况")
        print(f"  - 日志输出量")
        
        # 等待用户输入测试结果
        print(f"\n⏱️ 请运行策略并记录性能数据...")
        
        try:
            prefilter_time = float(input("预筛选耗时(秒): "))
            buy_check_time = float(input("买入检查耗时(秒): "))
            memory_usage = float(input("内存使用(MB): "))
            log_lines = int(input("日志行数: "))
            
            result = {
                'test_name': test_name,
                'config': config,
                'prefilter_time': prefilter_time,
                'buy_check_time': buy_check_time,
                'total_time': prefilter_time + buy_check_time,
                'memory_usage': memory_usage,
                'log_lines': log_lines,
                'timestamp': datetime.now().isoformat()
            }
            
            self.results[test_name] = result
            print(f"✅ 测试结果已记录")
            return result
            
        except (ValueError, KeyboardInterrupt):
            print(f"❌ 测试被取消或输入无效")
            return None
    
    def run_all_tests(self):
        """运行所有测试"""
        print('🚀 性能基准测试开始')
        print('=' * 60)
        
        for test_name in self.test_configs.keys():
            result = self.run_test(test_name)
            if result:
                print(f"✅ {test_name} 测试完成")
            else:
                print(f"❌ {test_name} 测试失败")
            
            # 询问是否继续
            if test_name != list(self.test_configs.keys())[-1]:
                continue_test = input("\n继续下一个测试? (y/n): ").lower()
                if continue_test != 'y':
                    break
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成性能测试报告"""
        if not self.results:
            print("❌ 没有测试结果可生成报告")
            return
        
        print('\n📊 性能测试报告')
        print('=' * 80)
        
        # 表格头
        print(f"{'测试名称':<20} {'预筛选(s)':<12} {'买入检查(s)':<12} {'总耗时(s)':<12} {'内存(MB)':<10} {'日志行数':<10}")
        print('-' * 80)
        
        # 基准测试结果
        baseline = None
        for test_name, result in self.results.items():
            if test_name == 'baseline':
                baseline = result
            
            print(f"{self.test_configs[test_name]['name']:<20} "
                  f"{result['prefilter_time']:<12.2f} "
                  f"{result['buy_check_time']:<12.2f} "
                  f"{result['total_time']:<12.2f} "
                  f"{result['memory_usage']:<10.1f} "
                  f"{result['log_lines']:<10}")
        
        # 性能提升对比
        if baseline:
            print('\n📈 性能提升对比 (相对于基准测试)')
            print('=' * 60)
            
            for test_name, result in self.results.items():
                if test_name == 'baseline':
                    continue
                
                prefilter_improvement = (baseline['prefilter_time'] - result['prefilter_time']) / baseline['prefilter_time'] * 100
                total_improvement = (baseline['total_time'] - result['total_time']) / baseline['total_time'] * 100
                memory_reduction = (baseline['memory_usage'] - result['memory_usage']) / baseline['memory_usage'] * 100
                log_reduction = (baseline['log_lines'] - result['log_lines']) / baseline['log_lines'] * 100
                
                print(f"\n{self.test_configs[test_name]['name']}:")
                print(f"  预筛选提升: {prefilter_improvement:+.1f}%")
                print(f"  总体提升: {total_improvement:+.1f}%")
                print(f"  内存优化: {memory_reduction:+.1f}%")
                print(f"  日志减少: {log_reduction:+.1f}%")
        
        # 保存结果到文件
        self.save_results()
        
        # 推荐配置
        self.recommend_config()
    
    def save_results(self):
        """保存测试结果到文件"""
        try:
            filename = f"performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def recommend_config(self):
        """推荐最佳配置"""
        if not self.results:
            return
        
        print('\n💡 配置推荐')
        print('=' * 40)
        
        # 找到最快的配置
        fastest_test = min(self.results.items(), key=lambda x: x[1]['total_time'])
        
        print(f"🚀 最快配置: {self.test_configs[fastest_test[0]]['name']}")
        print(f"   总耗时: {fastest_test[1]['total_time']:.2f}秒")
        
        # 找到内存使用最少的配置
        lowest_memory = min(self.results.items(), key=lambda x: x[1]['memory_usage'])
        
        print(f"💾 最省内存: {self.test_configs[lowest_memory[0]]['name']}")
        print(f"   内存使用: {lowest_memory[1]['memory_usage']:.1f}MB")
        
        print(f"\n📋 推荐使用场景:")
        print(f"  🏃 追求极致性能: 高级优化 + 模式1")
        print(f"  ⚖️ 平衡性能调试: 基础优化 + 模式2")
        print(f"  🔍 问题排查: 基准配置 + 模式3")

def main():
    """主函数"""
    benchmark = PerformanceBenchmark()
    
    print('🧪 策略性能基准测试工具')
    print('=' * 50)
    print('📋 测试说明:')
    print('  1. 工具会自动切换不同的优化配置')
    print('  2. 每个配置需要您在掘金客户端运行策略')
    print('  3. 记录性能数据并输入到工具中')
    print('  4. 最后生成详细的性能对比报告')
    
    choice = input('\n选择测试模式 (1=单个测试, 2=全部测试): ').strip()
    
    if choice == '1':
        # 单个测试
        print('\n可用测试:')
        for i, (key, config) in enumerate(benchmark.test_configs.items(), 1):
            print(f"  {i}. {config['name']}")
        
        try:
            test_index = int(input('选择测试 (1-3): ')) - 1
            test_names = list(benchmark.test_configs.keys())
            if 0 <= test_index < len(test_names):
                benchmark.run_test(test_names[test_index])
                benchmark.generate_report()
            else:
                print('❌ 无效选择')
        except ValueError:
            print('❌ 无效输入')
    
    elif choice == '2':
        # 全部测试
        benchmark.run_all_tests()
    
    else:
        print('❌ 无效选择')

if __name__ == '__main__':
    main()
