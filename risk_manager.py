# coding=utf-8
from gm.api import *
import numpy as np
import datetime
import talib

class RiskManager:
    def __init__(self, context):
        self.context = context
        self.max_drawdown_limit = 0.15  # 最大回撤限制放宽到15%
        self.position_limit_pct = 0.3   # 单个股票持仓比例限制放宽到30%
        self.market_risk_window = 20    # 市场风险评估窗口
        self.base_trailing_stop = 0.02  # 基础跟踪止盈阈值
        
    def calculate_trailing_stop_threshold(self, profit_pct, volatility):
        """计算动态跟踪止盈阈值"""
        # 根据盈利情况动态调整
        if profit_pct >= 0.15:  # 盈利超过15%
            threshold = self.base_trailing_stop * 0.8  # 收紧止盈
        elif profit_pct >= 0.1:  # 盈利超过10%
            threshold = self.base_trailing_stop * 0.9
        elif profit_pct >= 0.05:  # 盈利超过5%
            threshold = self.base_trailing_stop
        else:
            threshold = self.base_trailing_stop * 1.2  # 放宽止盈
            
        # 根据波动率调整阈值
        threshold *= (1 + volatility)
        
        return min(threshold, 0.05)  # 最大止盈阈值5%
        
    def check_position_limit(self, positions):
        """检查持仓限制"""
        # 获取账户总资产
        account = self.context.account()
        total_asset = account.cash.available + sum(p['market_value'] for p in positions)
        
        excess_positions = []
        for position in positions:
            # 检查单个持仓比例
            position_ratio = float(position['market_value']) / total_asset
            if position_ratio > self.position_limit_pct:
                excess_positions.append(position)
                
        # 检查总持仓数量限制
        if len(positions) > self.context.max_positions:
            # 按收益率排序，保留表现最好的
            sorted_positions = sorted(
                positions,
                key=lambda x: (x['price'] - self.context.positions_cost.get(x['symbol'], {}).get('cost_price', x['price'])) 
                            / self.context.positions_cost.get(x['symbol'], {}).get('cost_price', 1)
            )
            excess_positions.extend(sorted_positions[self.context.max_positions:])
        
        return list({p['symbol']: p for p in excess_positions}.values())  # 去重
        
    def validate_trading_time(self, current_time):
        """验证交易时间"""
        if not isinstance(current_time, datetime.time):
            return False
            
        morning_session = (
            datetime.time(9, 30) <= current_time <= datetime.time(11, 30)
        )
        afternoon_session = (
            datetime.time(13, 0) <= current_time <= datetime.time(14, 59)  # 延长交易时间到14:59
        )
        return morning_session or afternoon_session
                
    def check_holding_period(self, buy_date, now_date, symbol=None):
        """检查持仓周期
        
        Args:
            buy_date: 买入日期，可以是字符串格式('YYYY-MM-DD')或datetime对象
            now_date: 当前日期，可以是字符串格式('YYYY-MM-DD')或datetime对象
            symbol: 股票代码，用于从持仓管理器获取持仓天数
            
        Returns:
            是否满足持仓周期要求
        """
        try:
            # 检查是否有持仓管理器可用
            if hasattr(self.context, 'position_manager') and symbol is not None:
                # 使用持仓管理器获取持仓天数
                holding_days = self.context.position_manager.get_holding_days(symbol)
                if holding_days == 0:
                    # 如果持仓管理器中没有记录，使用传统方法
                    pass
                else:
                    # 检查是否满足T+1规则
                    if self.context.t_plus_1 and holding_days < 1:
                        self.context.log.info(f"{symbol} 持仓不满1天，遵循T+1规则，不能卖出")
                        return False
                    return True
            
            # 确保日期格式正确
            if buy_date is None:
                self.context.log.warning(f"{symbol} 买入日期为空，无法检查持仓周期")
                return True  # 无法确定持仓周期，默认允许交易
            
            # 转换日期格式
            if isinstance(buy_date, datetime.datetime):
                buy_date_str = buy_date.strftime('%Y-%m-%d')
            elif isinstance(buy_date, str):
                # 确保字符串格式正确
                try:
                    datetime.datetime.strptime(buy_date, '%Y-%m-%d')
                    buy_date_str = buy_date
                except ValueError:
                    self.context.log.warning(f"{symbol} 买入日期格式异常: {buy_date}，使用当前日期")
                    buy_date_str = self.context.now.strftime('%Y-%m-%d')
            else:
                self.context.log.warning(f"{symbol} 买入日期格式异常: {type(buy_date)}，使用当前日期")
                buy_date_str = self.context.now.strftime('%Y-%m-%d')
                
            if isinstance(now_date, datetime.datetime):
                now_date_str = now_date.strftime('%Y-%m-%d')
            elif isinstance(now_date, str):
                try:
                    datetime.datetime.strptime(now_date, '%Y-%m-%d')
                    now_date_str = now_date
                except ValueError:
                    self.context.log.warning(f"当前日期格式异常: {now_date}，使用系统当前日期")
                    now_date_str = self.context.now.strftime('%Y-%m-%d')
            else:
                self.context.log.warning(f"当前日期格式异常: {type(now_date)}，使用系统当前日期")
                now_date_str = self.context.now.strftime('%Y-%m-%d')
            
            # 如果没有持仓管理器或持仓管理器中没有记录，使用传统方法
            try:
                trading_days = get_trading_dates('SHSE', buy_date_str, now_date_str)
                if not trading_days or len(trading_days) < 2:
                    if self.context.t_plus_1:
                        self.context.log.info(f"{symbol} 持仓不满1天，遵循T+1规则，不能卖出")
                        return False
                    return True  # 持仓时间太短，继续持有
                    
                # 检查是否超过最大持仓时间（5个交易日）
                if len(trading_days) > 5:
                    self.context.log.warning(f"持仓时间过长: {len(trading_days)}天")
                    return True  # 继续持有，让跟踪止盈来处理
                    
                return True
            except Exception as e:
                self.context.log.error(f"获取交易日期异常: {str(e)}")
                # 如果无法获取交易日期，尝试直接计算日期差
                try:
                    buy_date_obj = datetime.datetime.strptime(buy_date_str, '%Y-%m-%d').date()
                    now_date_obj = datetime.datetime.strptime(now_date_str, '%Y-%m-%d').date()
                    days_diff = (now_date_obj - buy_date_obj).days
                    
                    if self.context.t_plus_1 and days_diff < 1:
                        self.context.log.info(f"{symbol} 持仓不满1天，遵循T+1规则，不能卖出")
                        return False
                    return True
                except Exception as e2:
                    self.context.log.error(f"计算日期差异异常: {str(e2)}")
                    return True  # 出错时默认允许交易
            
        except Exception as e:
            self.context.log.error(f"检查持仓周期异常: {str(e)}")
            import traceback
            self.context.log.error(f"检查持仓周期异常堆栈: {traceback.format_exc()}")
            return True
        
    def update_high_price(self, cost_info, current_price):
        """更新最高价"""
        try:
            if not isinstance(current_price, (int, float)):
                self.context.log.error(f"current_price类型错误: {type(current_price)}")
                return False
                
            # 确保cost_info包含必要的字段
            if 'confirmed_high' not in cost_info:
                cost_info['confirmed_high'] = current_price
            if 'confirmed_time' not in cost_info:
                cost_info['confirmed_time'] = self.context.now
            if 'buy_time' not in cost_info:
                cost_info['buy_time'] = self.context.now
                
            old_high = float(cost_info['confirmed_high'])
            
            # 如果当前价格创新高，更新最高价和时间
            if current_price > old_high:
                cost_info['confirmed_high'] = current_price
                cost_info['confirmed_time'] = self.context.now
                cost_info['last_update'] = self.context.now.strftime('%Y-%m-%d %H:%M:%S')
                return True
                
            return False
            
        except Exception as e:
            self.context.log.error(f"更新最高价异常: {str(e)}, cost_info: {cost_info}")
            return False
            
    def check_market_risk(self):
        """检查市场整体风险"""
        try:
            # 获取指数数据
            index_data = history(symbol=self.context.index_symbol, 
                               frequency='1d', 
                               start_time=self.context.now - datetime.timedelta(days=30),
                               end_time=self.context.now,
                               fields='close,volume',
                               df=True)
                               
            if index_data is None or len(index_data) < self.market_risk_window:
                return True  # 数据不足时允许交易
                
            # 计算指数趋势
            close_prices = index_data['close'].values
            ema1 = talib.EMA(close_prices, timeperiod=3)  # 改为3日EMA
            ema2 = talib.EMA(ema1, timeperiod=3)         # 改为3日EMA
            ema3 = talib.EMA(ema2, timeperiod=3)         # 改为3日EMA
            
            # 计算指数波动率
            returns = np.diff(np.log(close_prices))
            volatility = np.std(returns) * np.sqrt(252)
            
            # 放宽市场风险判断条件
            is_high_risk = (
                volatility > 0.45 or  # 波动率阈值提高到45%
                (ema1[-1] < ema2[-1] * 0.95 and ema2[-1] < ema3[-1] * 0.95)  # 允许一定程度的下跌
            )
            
            if is_high_risk:
                self.context.log.warning(f"市场风险较高 - 波动率: {volatility:.2%}, EMA1/2/3: {ema1[-1]:.2f}/{ema2[-1]:.2f}/{ema3[-1]:.2f}")
                
            return not is_high_risk
            
        except Exception as e:
            self.context.log.error(f"检查市场风险异常: {str(e)}")
            return True  # 发生异常时允许交易
            
    def calculate_dynamic_stop_loss(self, position):
        """计算动态止损点"""
        try:
            symbol = position['symbol']
            current_price = position['price']
            cost_price = self.context.positions_cost[symbol]['cost_price']
            
            # 获取历史波动率
            hist_data = history(symbol=symbol, 
                              frequency='1d',
                              start_time=self.context.now - datetime.timedelta(days=30),
                              end_time=self.context.now,
                              fields='close',
                              df=True)
                              
            if hist_data is None:
                return self.context.stop_loss
                
            returns = np.diff(np.log(hist_data['close'].values))
            volatility = np.std(returns) * np.sqrt(252)
            
            # 放宽止损条件
            dynamic_stop = max(
                self.context.stop_loss * 0.8,  # 降低基础止损点
                volatility * 1.2,        # 降低波动率影响
                abs(current_price - cost_price) / cost_price * 0.4  # 降低盈利保护要求
            )
            
            return min(dynamic_stop, 0.15)  # 最大止损放宽到15%
            
        except Exception as e:
            self.context.log.error(f"计算动态止损点异常: {str(e)}")
            return self.context.stop_loss * 1.5  # 发生异常时使用更宽松的止损

    def check_trailing_stop(self, symbol, position, cost_info):
        """检查跟踪止盈"""
        try:
            # 获取最新行情
            quote = current([symbol])
            if not quote or len(quote) == 0:
                return False
                
            current_price = quote[0]['price']
            
            # 更新最高价
            if current_price > cost_info['highest_price']:
                cost_info['highest_price'] = current_price
                
            # 计算回撤比例
            drawdown = (cost_info['highest_price'] - current_price) / cost_info['highest_price']
            
            # 计算持仓天数
            buy_time = cost_info.get('buy_time')
            holding_days = -1
            if buy_time and isinstance(buy_time, datetime.datetime):
                holding_days = (self.context.now - buy_time).days
                
            # A股T+1交易规则：持仓未满一天的股票不能卖出
            if hasattr(self.context, 't_plus_1') and self.context.t_plus_1 and holding_days < 1:
                self.context.log.info(f"跟踪止盈检测 - {symbol}: 持仓未满1天，根据A股T+1规则不能卖出")
                return False
            
            # 获取日内分钟数据用于确认趋势
            intraday_data = history(symbol=symbol,
                                  frequency='1m',
                                  start_time=self.context.now.replace(hour=9, minute=30),
                                  end_time=self.context.now,
                                  fields='close,high,low,volume',
                                  df=True)
                                  
            if intraday_data is not None and len(intraday_data) > 0:
                # 计算分钟级别指标
                closes = intraday_data['close'].values
                volumes = intraday_data['volume'].values
                
                # 计算分钟MACD
                macd, signal, hist = talib.MACD(closes)
                
                # 计算分钟RSI
                rsi = talib.RSI(closes, timeperiod=14)
                
                # 计算成交量变化
                volume_ma5 = talib.SMA(volumes, timeperiod=5)
                volume_ratio = volumes[-1] / volume_ma5[-1] if len(volume_ma5) > 0 else 1
                
                # 记录详细的止盈检测日志
                self.context.log.info(f"""
                跟踪止盈检测 - {symbol}:
                当前价格: {current_price:.3f}
                最高价: {cost_info['highest_price']:.3f}
                买入价: {cost_info['cost_price']:.3f}
                回撤比例: {drawdown:.2%}
                持仓天数: {holding_days}天 (T+1交易规则)
                分钟MACD: {macd[-1]:.3f}
                分钟RSI: {rsi[-1]:.2f}
                成交量比: {volume_ratio:.2f}
                """)
                
                # 止盈条件:
                # 1. 回撤超过2%
                # 2. MACD死叉或RSI超买
                # 3. 成交量放大
                if (drawdown > 0.02 and  # 回撤超过2%
                    (macd[-1] < signal[-1] or  # MACD死叉
                     rsi[-1] > 75) and  # RSI超买
                    volume_ratio > 1.2):  # 成交量放大
                    return True
                    
        except Exception as e:
            self.context.log.error(f"检查跟踪止盈异常 - {symbol}: {str(e)}")
            
        return False
