# coding=utf-8
"""
删除功能后的修复验证脚本
检查是否还有对已删除函数的调用
"""

import re
import os

def check_deleted_function_calls():
    """检查是否还有对已删除函数的调用"""
    print('🔍 删除功能后的修复验证')
    print('=' * 50)
    
    # 已删除的函数列表
    deleted_functions = [
        'initialize_enhanced_indicators',
        'calculate_amplitude',
        'position_summary',
        'setup_position_summary_schedules',
        'update_confirmed_lows'
    ]
    
    # 已删除的变量/属性
    deleted_variables = [
        'enhanced_buy_integration',
        'confirmed_lows',
        'AMPLITUDE_FILTER_ENABLED',
        'ENABLE_POSITION_SUMMARY',
        'ENABLE_REBOUND_BUY'
    ]
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 检查已删除函数的调用:')
        found_issues = []
        
        for func in deleted_functions:
            # 查找函数调用
            pattern = rf'{func}\s*\('
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ❌ 发现对已删除函数 {func} 的调用: {len(matches)}处')
                found_issues.append(f'函数调用: {func}')
                
                # 显示具体位置
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if re.search(pattern, line):
                        print(f'    第{i+1}行: {line.strip()}')
            else:
                print(f'  ✅ 未发现对已删除函数 {func} 的调用')
        
        print('\n📊 检查已删除变量的引用:')
        for var in deleted_variables:
            # 查找变量引用（排除注释）
            pattern = rf'(?<!#.*){var}'
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ⚠️ 发现对已删除变量 {var} 的引用: {len(matches)}处')
                # 这些可能是正常的，比如在注释中
            else:
                print(f'  ✅ 未发现对已删除变量 {var} 的引用')
        
        return found_issues
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return ['检查失败']

def check_import_statements():
    """检查是否还有对已删除模块的导入"""
    print('\n📦 检查已删除模块的导入:')
    print('-' * 30)
    
    deleted_modules = [
        'enhanced_buy_integration',
        'EnhancedBuyIntegration'
    ]
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        found_issues = []
        for module in deleted_modules:
            pattern = rf'from.*{module}|import.*{module}'
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ❌ 发现对已删除模块 {module} 的导入')
                found_issues.append(f'模块导入: {module}')
                
                # 显示具体位置
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if re.search(pattern, line):
                        print(f'    第{i+1}行: {line.strip()}')
            else:
                print(f'  ✅ 未发现对已删除模块 {module} 的导入')
        
        return found_issues
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return ['检查失败']

def check_config_references():
    """检查配置文件中是否还有已删除功能的配置项"""
    print('\n⚙️ 检查配置文件中的已删除配置项:')
    print('-' * 40)
    
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 应该已删除的配置项
        deleted_configs = [
            'AMPLITUDE_FILTER_ENABLED',
            'MA_FILTER_ENABLED',
            'ENABLE_MA_CROSS_BUY_SIGNAL',
            'ENABLE_TRIX_REVERSAL_SIGNAL',
            'ENABLE_CSV_LOGGING',
            'ENABLE_POSITION_SUMMARY',
            'ENABLE_ENHANCED_BUY_SIGNALS',
            'ENABLE_ENHANCED_STOCK_FILTER',
            'ENABLE_REBOUND_BUY'
        ]
        
        found_configs = []
        for config in deleted_configs:
            pattern = rf'^{config}\s*='
            matches = re.findall(pattern, content, re.MULTILINE)
            if matches:
                print(f'  ⚠️ 配置项 {config} 仍然存在（可能已注释）')
                found_configs.append(config)
            else:
                print(f'  ✅ 配置项 {config} 已正确删除')
        
        return found_configs
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return ['检查失败']

def verify_core_functionality():
    """验证核心功能是否完整"""
    print('\n✅ 核心功能完整性验证:')
    print('-' * 30)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 核心函数检查
        core_functions = [
            'init',
            'on_bar',
            'on_tick',
            'analyze_single_symbol',
            'calculate_trix_unified',
            'log_with_timestamp',
            'get_stock_data_unified',
            'validate_data_format_unified',
            'handle_exception_unified'
        ]
        
        missing_functions = []
        for func in core_functions:
            pattern = rf'def\s+{func}\s*\('
            if re.search(pattern, content):
                print(f'  ✅ 核心函数 {func} 存在')
            else:
                print(f'  ❌ 核心函数 {func} 缺失')
                missing_functions.append(func)
        
        return missing_functions
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return ['检查失败']

def generate_fix_summary():
    """生成修复总结"""
    print('\n📋 修复操作总结:')
    print('=' * 40)
    
    fixes_applied = [
        '✅ 删除 initialize_enhanced_indicators() 函数调用',
        '✅ 删除 setup_position_summary_schedules() 函数',
        '✅ 删除持仓摘要调度设置',
        '✅ 删除配置变更时的持仓摘要重新设置',
        '✅ 删除无持仓时的持仓摘要检查',
        '✅ 清理所有对已删除函数的引用'
    ]
    
    for fix in fixes_applied:
        print(f'  {fix}')

def main():
    """主函数"""
    print('🔧 删除功能后的修复验证报告')
    print('=' * 60)
    
    # 检查已删除函数的调用
    function_issues = check_deleted_function_calls()
    
    # 检查导入语句
    import_issues = check_import_statements()
    
    # 检查配置引用
    config_issues = check_config_references()
    
    # 验证核心功能
    missing_functions = verify_core_functionality()
    
    # 生成修复总结
    generate_fix_summary()
    
    # 总结验证结果
    print(f'\n🎯 验证结果总结:')
    print('=' * 40)
    
    total_issues = len(function_issues) + len(import_issues) + len(missing_functions)
    
    if total_issues == 0:
        print('✅ 所有检查通过，修复完成！')
        print('✅ 未发现对已删除函数的调用')
        print('✅ 未发现对已删除模块的导入')
        print('✅ 核心功能完整')
        print('✅ 策略可以正常运行')
    else:
        print(f'⚠️ 发现 {total_issues} 个问题需要处理:')
        
        if function_issues:
            print(f'  • 函数调用问题: {len(function_issues)}个')
            for issue in function_issues:
                print(f'    - {issue}')
        
        if import_issues:
            print(f'  • 导入问题: {len(import_issues)}个')
            for issue in import_issues:
                print(f'    - {issue}')
        
        if missing_functions:
            print(f'  • 缺失核心函数: {len(missing_functions)}个')
            for func in missing_functions:
                print(f'    - {func}')
    
    # 配置项状态（信息性）
    if config_issues:
        print(f'\nℹ️ 配置文件状态: {len(config_issues)}个已删除配置项仍存在（已注释）')
    
    print(f'\n💡 下一步:')
    if total_issues == 0:
        print('  🚀 可以重新运行策略进行测试')
        print('  📊 建议进行回测验证功能完整性')
    else:
        print('  🔧 需要修复发现的问题')
        print('  🔄 修复后重新运行验证')

if __name__ == '__main__':
    main()
