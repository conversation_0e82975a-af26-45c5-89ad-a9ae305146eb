=============================================
    万和策略分析系统 - 数据库管理工具
           快速入门指南
=============================================

【启动方式】

1. 数据库管理工具（全功能）：
   - 双击 start_db_manager.bat 文件直接启动
   - 或运行 create_shortcut.bat 创建桌面快捷方式

2. 数据分析工具（直接进入分析功能）：
   - 双击 start_analysis.bat 文件直接启动
   - 或运行 create_analysis_shortcut.bat 创建桌面快捷方式

【主要功能】

1. 数据库信息 - 查看数据库结构和统计信息
2. 数据分析 - 分析表数据并生成图表报告
3. 修复缺失数据 - 处理数据库中的缺失值
4. 批量处理数据 - 批量更新或删除数据
5. 合并数据库 - 将两个数据库合并
6. 生成数据库报告 - 生成完整的HTML格式报告
7. 备份数据库 - 创建数据库备份
8. 优化数据库 - 提高数据库性能

【首次使用提示】

1. 系统会自动检查必要的Python库是否已安装
2. 如提示缺少依赖库，请按照提示进行安装
3. 首次运行时会创建必要的目录结构
4. 如数据库文件不存在，系统会提示创建

【配置文件】

配置文件位于 config.ini，可修改以下设置：
- 数据库文件路径
- 备份目录
- 报告输出目录
- 分析结果输出目录
- 界面设置

【常见问题】

如遇到问题，请参阅 README.md 文件中的"常见问题"部分

## 常见问题解决

### 找不到数据库文件

如果遇到"找不到数据库文件"的错误，请检查：
1. 数据库文件是否存在于 data/trades.db（相对于项目根目录）
2. 如果数据库文件位于其他位置，请修改 config.ini 文件中的 db_file 设置
3. 您可以运行 test_db_connection.bat 来测试数据库连接是否正常

【详细文档】

更详细的使用说明请参阅 README.md 文件 