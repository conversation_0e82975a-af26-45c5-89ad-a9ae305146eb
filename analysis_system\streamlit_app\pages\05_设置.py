import streamlit as st
import os
import json
import pandas as pd
from datetime import datetime
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据加载工具
from utils.data_loader import clear_database, clear_csv_data

st.set_page_config(page_title="系统设置", page_icon="⚙️", layout="wide")

st.title("系统设置与配置")

# 创建配置文件路径
config_file = "analysis_system/streamlit_app/config.json"
config_dir = os.path.dirname(config_file)

# 确保配置目录存在
os.makedirs(config_dir, exist_ok=True)

# 默认配置
default_config = {
    "data_path": "data",
    "reports_path": "reports",
    "performance": {
        "use_cached_data": True,
        "parallel_processing": True,
        "num_workers": 4
    },
    "analysis": {
        "min_trades": 10,
        "significance_level": 0.05,
        "use_advanced_metrics": True
    },
    "optimization": {
        "random_state": 42,
        "test_size": 0.3,
        "n_estimators": 100,
        "max_depth": 5
    },
    "visualization": {
        "theme": "default",
        "dpi": 100,
        "save_format": "png"
    },
    "localization": {
        "language": "zh_CN",
        "date_format": "YYYY-MM-DD",
        "time_format": "HH:mm:ss",
        "show_english_labels": True
    }
}

# 加载现有配置或创建新配置
if os.path.exists(config_file):
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # 检查并添加可能缺少的新配置选项
        if "localization" not in config:
            config["localization"] = default_config["localization"]
            
    except Exception as e:
        st.error(f"加载配置文件失败: {str(e)}")
        config = default_config
else:
    config = default_config

# 保存配置函数
def save_config():
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)
        st.success("配置已保存!")
        return True
    except Exception as e:
        st.error(f"保存配置失败: {str(e)}")
        return False

# 创建设置界面
st.header("基本设置")

col1, col2 = st.columns(2)
with col1:
    config["data_path"] = st.text_input("数据路径", config["data_path"])
with col2:
    config["reports_path"] = st.text_input("报告路径", config["reports_path"])

# 本地化设置
st.header("本地化设置")
local_col1, local_col2 = st.columns(2)

with local_col1:
    languages = {
        "zh_CN": "中文(简体)",
        "zh_TW": "中文(繁体)",
        "en_US": "英文(美国)"
    }
    
    selected_language = config["localization"]["language"]
    config["localization"]["language"] = st.selectbox(
        "界面语言", 
        list(languages.keys()),
        format_func=lambda x: languages[x],
        index=list(languages.keys()).index(selected_language) if selected_language in languages else 0,
        help="设置应用界面的显示语言"
    )
    
    config["localization"]["show_english_labels"] = st.checkbox(
        "显示英文标签", 
        config["localization"].get("show_english_labels", True),
        help="启用后在中文旁显示英文标签，便于对照理解"
    )

with local_col2:
    date_formats = {
        "YYYY-MM-DD": "2023-01-31",
        "MM/DD/YYYY": "01/31/2023",
        "DD/MM/YYYY": "31/01/2023"
    }
    
    selected_date_format = config["localization"]["date_format"]
    config["localization"]["date_format"] = st.selectbox(
        "日期格式", 
        list(date_formats.keys()),
        format_func=lambda x: f"{x} (例: {date_formats[x]})",
        index=list(date_formats.keys()).index(selected_date_format) if selected_date_format in date_formats else 0,
        help="设置日期的显示格式"
    )
    
    time_formats = {
        "HH:mm:ss": "14:30:00",
        "hh:mm:ss a": "02:30:00 PM",
        "HH:mm": "14:30"
    }
    
    selected_time_format = config["localization"]["time_format"]
    config["localization"]["time_format"] = st.selectbox(
        "时间格式", 
        list(time_formats.keys()),
        format_func=lambda x: f"{x} (例: {time_formats[x]})",
        index=list(time_formats.keys()).index(selected_time_format) if selected_time_format in time_formats else 0,
        help="设置时间的显示格式"
    )

st.header("性能设置")
perf_col1, perf_col2 = st.columns(2)

with perf_col1:
    config["performance"]["use_cached_data"] = st.checkbox(
        "使用缓存数据", 
        config["performance"]["use_cached_data"],
        help="启用后可加快重复分析速度，但可能使用过时数据"
    )
    
    config["performance"]["parallel_processing"] = st.checkbox(
        "并行处理", 
        config["performance"]["parallel_processing"],
        help="启用多核并行处理以加速分析"
    )

with perf_col2:
    config["performance"]["num_workers"] = st.slider(
        "工作线程数", 
        1, 16, 
        config["performance"]["num_workers"],
        help="并行处理时使用的工作线程数量"
    )

st.header("分析设置")
analysis_col1, analysis_col2 = st.columns(2)

with analysis_col1:
    config["analysis"]["min_trades"] = st.number_input(
        "最小交易数", 
        1, 100, 
        config["analysis"]["min_trades"],
        help="分析股票时要求的最小交易次数"
    )
    
    config["analysis"]["significance_level"] = st.slider(
        "显著性水平", 
        0.01, 0.1, 
        float(config["analysis"]["significance_level"]), 
        0.01,
        format="%.2f",
        help="统计分析的显著性水平"
    )

with analysis_col2:
    config["analysis"]["use_advanced_metrics"] = st.checkbox(
        "使用高级指标", 
        config["analysis"]["use_advanced_metrics"],
        help="启用后将计算更多高级统计指标"
    )

st.header("优化设置")
opt_col1, opt_col2 = st.columns(2)

with opt_col1:
    config["optimization"]["random_state"] = st.number_input(
        "随机种子", 
        1, 1000, 
        config["optimization"]["random_state"],
        help="设置随机数生成器的种子，确保结果可重现"
    )
    
    config["optimization"]["test_size"] = st.slider(
        "测试集比例", 
        0.1, 0.5, 
        float(config["optimization"]["test_size"]), 
        0.05,
        format="%.2f",
        help="用于模型验证的测试集比例"
    )

with opt_col2:
    config["optimization"]["n_estimators"] = st.slider(
        "树的数量", 
        50, 500, 
        config["optimization"]["n_estimators"], 
        50,
        help="随机森林或梯度提升树的数量"
    )
    
    config["optimization"]["max_depth"] = st.slider(
        "最大深度", 
        3, 15, 
        config["optimization"]["max_depth"],
        help="决策树的最大深度"
    )

st.header("可视化设置")
vis_col1, vis_col2 = st.columns(2)

with vis_col1:
    theme_options = {
        "default": "默认主题",
        "dark": "暗色主题",
        "light": "亮色主题",
        "colorblind": "色盲友好主题"
    }
    
    selected_theme = config["visualization"]["theme"]
    config["visualization"]["theme"] = st.selectbox(
        "图表主题", 
        list(theme_options.keys()),
        format_func=lambda x: theme_options[x],
        index=list(theme_options.keys()).index(selected_theme) if selected_theme in theme_options else 0,
        help="设置图表的视觉主题"
    )
    
    config["visualization"]["dpi"] = st.slider(
        "图表DPI", 
        72, 300, 
        config["visualization"]["dpi"],
        help="图表的分辨率，影响清晰度和文件大小"
    )

with vis_col2:
    format_options = {
        "png": "PNG (无损压缩)",
        "jpg": "JPG (有损压缩)",
        "svg": "SVG (矢量格式)",
        "pdf": "PDF (文档格式)"
    }
    
    selected_format = config["visualization"]["save_format"]
    config["visualization"]["save_format"] = st.selectbox(
        "图表保存格式", 
        list(format_options.keys()),
        format_func=lambda x: format_options[x],
        index=list(format_options.keys()).index(selected_format) if selected_format in format_options else 0,
        help="保存图表时使用的文件格式"
    )

# 保存按钮
if st.button("保存设置", type="primary"):
    if save_config():
        st.balloons()

# 数据管理部分
st.header("数据管理", divider="red")
st.warning("⚠️ 以下操作将删除数据，请谨慎操作!")

data_mgmt_col1, data_mgmt_col2 = st.columns(2)

with data_mgmt_col1:
    st.subheader("数据库管理")
    if st.button("清空数据库", help="清空数据库中的所有交易记录，但保留表结构"):
        success, message = clear_database()
        if success:
            st.success(message)
        else:
            st.error(message)

with data_mgmt_col2:
    st.subheader("CSV数据管理")
    if st.button("清空CSV数据", help="删除所有导入的CSV数据文件"):
        success, message = clear_csv_data()
        if success:
            st.success(message)
        else:
            st.error(message)

# 显示配置文件内容（高级用户）
with st.expander("查看配置文件内容"):
    st.code(json.dumps(config, indent=4), language="json")
    
    # 提供下载配置文件的选项
    st.download_button(
        label="下载配置文件",
        data=json.dumps(config, indent=4),
        file_name="config.json",
        mime="application/json",
        help="下载当前配置为JSON文件"
    )
    
    # 上传配置文件
    uploaded_config = st.file_uploader("上传配置文件", type=["json"], help="上传JSON格式的配置文件")
    if uploaded_config is not None:
        try:
            uploaded_content = json.loads(uploaded_config.getvalue().decode())
            if st.button("应用上传的配置"):
                config = uploaded_content
                if save_config():
                    st.success("已应用上传的配置文件")
                    st.rerun()
        except Exception as e:
            st.error(f"无法解析上传的配置文件: {str(e)}")

# 添加页脚
st.divider()
st.caption(f"系统版本: v1.0.0 | 最后更新: {datetime.now().strftime('%Y-%m-%d')}") 