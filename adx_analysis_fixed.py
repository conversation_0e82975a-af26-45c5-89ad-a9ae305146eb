# coding=utf-8
"""
ADX因子深度分析脚本 (修复版)
正确匹配买入-卖出数据进行分析
"""
import sqlite3
import pandas as pd
import numpy as np

def analyze_adx_effectiveness():
    """分析ADX因子有效性"""
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配的ADX数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.adx,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.adx IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 ADX匹配数据分析 ({len(df)}条记录):')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的ADX数据')
            return None
        
        # ADX值分布分析
        adx_stats = df['adx'].describe()
        print(f'\n📈 ADX值分布:')
        print(f'   均值: {adx_stats["mean"]:.2f}')
        print(f'   中位数: {adx_stats["50%"]:.2f}')
        print(f'   标准差: {adx_stats["std"]:.2f}')
        print(f'   范围: [{adx_stats["min"]:.2f}, {adx_stats["max"]:.2f}]')
        
        # 收益分布分析
        profit_stats = df['net_profit_pct_sell'].describe()
        print(f'\n💰 收益分布:')
        print(f'   均值: {profit_stats["mean"]:.2f}%')
        print(f'   中位数: {profit_stats["50%"]:.2f}%')
        print(f'   胜率: {(df["net_profit_pct_sell"] > 0).mean() * 100:.1f}%')
        
        # 按时段分析ADX
        if 'buy_hour' in df.columns:
            hourly_adx = df.groupby('buy_hour').agg({
                'adx': ['mean', 'count'],
                'net_profit_pct_sell': ['mean', lambda x: (x > 0).mean() * 100]
            }).round(2)
            
            print(f'\n🕐 各时段ADX和收益分析:')
            print(f'时段  ADX均值  样本数  平均收益%  胜率%')
            print(f'-' * 45)
            
            for hour in sorted(hourly_adx.index):
                adx_mean = hourly_adx.loc[hour, ('adx', 'mean')]
                count = hourly_adx.loc[hour, ('adx', 'count')]
                profit_mean = hourly_adx.loc[hour, ('net_profit_pct_sell', 'mean')]
                win_rate = hourly_adx.loc[hour, ('net_profit_pct_sell', '<lambda>')]
                
                print(f'{hour:02d}:00 {adx_mean:7.1f} {count:6.0f} {profit_mean:8.2f} {win_rate:6.1f}')
        
        # ADX阈值效果分析
        thresholds = [20, 25, 30, 35, 40]
        print(f'\n🎯 ADX阈值效果分析:')
        
        for threshold in thresholds:
            strong_trend = df[df['adx'] >= threshold]
            weak_trend = df[df['adx'] < threshold]
            
            if len(strong_trend) > 10 and len(weak_trend) > 10:
                strong_profit = strong_trend['net_profit_pct_sell'].mean()
                weak_profit = weak_trend['net_profit_pct_sell'].mean()
                strong_win_rate = (strong_trend['net_profit_pct_sell'] > 0).mean() * 100
                weak_win_rate = (weak_trend['net_profit_pct_sell'] > 0).mean() * 100
                
                print(f'   ADX>={threshold}: 胜率{strong_win_rate:.1f}%, 收益{strong_profit:.2f}% (样本{len(strong_trend)})')
                print(f'   ADX<{threshold}: 胜率{weak_win_rate:.1f}%, 收益{weak_profit:.2f}% (样本{len(weak_trend)})')
                print(f'   差异: 胜率{strong_win_rate-weak_win_rate:+.1f}%, 收益{strong_profit-weak_profit:+.2f}%')
                print()
        
        return df
        
    except Exception as e:
        print(f'❌ ADX分析失败: {e}')
        return None

def optimize_adx_thresholds(df):
    """优化ADX阈值"""
    if df is None or len(df) == 0:
        print('⚠️ 没有数据进行ADX阈值优化')
        return None
    
    print(f'🔧 ADX阈值优化:')
    
    # 测试不同阈值组合
    best_threshold = None
    best_performance = -999
    
    results = []
    
    for low_threshold in [15, 20, 25]:
        for high_threshold in [30, 35, 40, 45, 50]:
            if high_threshold <= low_threshold:
                continue
            
            # 分析该阈值组合的效果
            medium_adx = df[(df['adx'] >= low_threshold) & (df['adx'] < high_threshold)]
            
            if len(medium_adx) > 30:  # 确保样本量足够
                avg_profit = medium_adx['net_profit_pct_sell'].mean()
                win_rate = (medium_adx['net_profit_pct_sell'] > 0).mean() * 100
                sample_count = len(medium_adx)
                
                # 综合评分 (胜率权重60%, 收益权重40%)
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                results.append({
                    'range': f'[{low_threshold}, {high_threshold})',
                    'win_rate': win_rate,
                    'avg_profit': avg_profit,
                    'sample_count': sample_count,
                    'score': score
                })
                
                if score > best_performance:
                    best_performance = score
                    best_threshold = (low_threshold, high_threshold)
    
    # 按评分排序显示结果
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f'   ADX区间        胜率%   收益%   样本数  评分')
    print(f'   ' + '-' * 45)
    
    for result in results:
        print(f'   {result["range"]:<12} {result["win_rate"]:5.1f} {result["avg_profit"]:7.2f} {result["sample_count"]:6d} {result["score"]:5.1f}')
    
    if best_threshold:
        print(f'\n🏆 最优ADX阈值: [{best_threshold[0]}, {best_threshold[1]})')
        print(f'   综合评分: {best_performance:.1f}')
        
        # 分析最优区间的详细表现
        optimal_data = df[(df['adx'] >= best_threshold[0]) & (df['adx'] < best_threshold[1])]
        if len(optimal_data) > 0:
            opt_win_rate = (optimal_data['net_profit_pct_sell'] > 0).mean() * 100
            opt_avg_profit = optimal_data['net_profit_pct_sell'].mean()
            opt_sample = len(optimal_data)
            
            print(f'   详细表现: 胜率{opt_win_rate:.1f}%, 平均收益{opt_avg_profit:.2f}%, 样本{opt_sample}')
    
    return best_threshold, results

def analyze_cci_adx_combination():
    """分析CCI+ADX组合效果"""
    print(f'\n🔗 CCI+ADX组合分析:')
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取同时有CCI和ADX的数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.cci,
                b.adx,
                s.net_profit_pct_sell,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.cci IS NOT NULL
            AND b.adx IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print('⚠️ 没有CCI+ADX组合数据')
            return
        
        print(f'📊 CCI+ADX组合数据: {len(df)}条')
        
        # 基于之前的最优发现分析组合
        # CCI最优: [20, 30), ADX待分析
        
        combinations = [
            ('高CCI+高ADX', (df['cci'] >= 20) & (df['cci'] < 30) & (df['adx'] >= 25)),
            ('高CCI+中ADX', (df['cci'] >= 20) & (df['cci'] < 30) & (df['adx'] >= 20) & (df['adx'] < 30)),
            ('高CCI+低ADX', (df['cci'] >= 20) & (df['cci'] < 30) & (df['adx'] < 20)),
            ('中CCI+高ADX', (df['cci'] >= 15) & (df['cci'] < 25) & (df['adx'] >= 25)),
            ('低CCI+高ADX', (df['cci'] < 20) & (df['adx'] >= 25)),
        ]
        
        print(f'\n🎯 CCI+ADX组合效果:')
        print(f'组合类型        胜率%   收益%   样本数')
        print(f'-' * 40)
        
        for combo_name, condition in combinations:
            combo_data = df[condition]
            
            if len(combo_data) > 10:
                win_rate = (combo_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = combo_data['net_profit_pct_sell'].mean()
                sample_count = len(combo_data)
                
                print(f'{combo_name:<12} {win_rate:5.1f} {avg_profit:7.2f} {sample_count:6d}')
        
    except Exception as e:
        print(f'❌ CCI+ADX组合分析失败: {e}')

def analyze_adx_distribution_insights(df):
    """分析ADX分布洞察"""
    if df is None or len(df) == 0:
        return
    
    print(f'\n💡 ADX分布洞察:')
    
    # 分析ADX强度分类
    very_strong = df[df['adx'] > 40]  # 极强趋势
    strong = df[(df['adx'] >= 25) & (df['adx'] <= 40)]  # 强趋势
    weak = df[df['adx'] < 25]  # 弱趋势
    
    categories = [
        ('极强趋势 (ADX>40)', very_strong),
        ('强趋势 (ADX 25-40)', strong),
        ('弱趋势 (ADX<25)', weak)
    ]
    
    for cat_name, cat_data in categories:
        if len(cat_data) > 5:
            win_rate = (cat_data['net_profit_pct_sell'] > 0).mean() * 100
            profit = cat_data['net_profit_pct_sell'].mean()
            print(f'   {cat_name}: 胜率{win_rate:.1f}%, 收益{profit:.2f}% (样本{len(cat_data)})')
    
    # 分析当前策略使用的ADX范围
    current_range = df[df['adx'] >= 25]
    if len(current_range) > 0:
        cr_win_rate = (current_range['net_profit_pct_sell'] > 0).mean() * 100
        cr_profit = current_range['net_profit_pct_sell'].mean()
        print(f'   当前策略范围 (ADX≥25): 胜率{cr_win_rate:.1f}%, 收益{cr_profit:.2f}% (样本{len(current_range)})')

def main():
    """主函数"""
    print('🚀 ADX因子深度分析 (修复版)')
    print('=' * 50)
    
    # 执行ADX分析
    df = analyze_adx_effectiveness()
    
    if df is not None:
        # 优化ADX阈值
        best_threshold, results = optimize_adx_thresholds(df)
        
        # 分析ADX分布洞察
        analyze_adx_distribution_insights(df)
        
        # 分析CCI+ADX组合
        analyze_cci_adx_combination()
        
        print(f'\n🎯 ADX优化总结:')
        if best_threshold:
            print(f'✅ 建议ADX阈值: [{best_threshold[0]}, {best_threshold[1]})')
            print(f'✅ 当前策略可以考虑调整ADX范围')
        else:
            print(f'⚠️ 未找到明显更优的ADX阈值')
        
        print(f'✅ ADX因子分析完成，数据已保存')
        
        # 保存分析结果
        if len(df) > 0:
            df.to_csv('adx_analysis_results.csv', index=False, encoding='utf-8-sig')
            print(f'📊 详细数据已保存到: adx_analysis_results.csv')
    
    else:
        print(f'❌ ADX分析失败，请检查数据')

if __name__ == '__main__':
    main()
