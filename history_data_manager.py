#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史数据管理器 - 高效缓存和批量获取历史数据
"""

from gm.api import *
import datetime
import numpy as np
import pandas as pd
import time
from collections import defaultdict
import threading

class HistoryDataManager:
    """历史数据管理器 - 一次性获取并管理历史数据"""
    
    def __init__(self, context, cache_size=500):
        """
        初始化历史数据管理器
        
        Args:
            context: 策略上下文
            cache_size: 缓存大小（每个频率缓存的最大条目数）
        """
        self.context = context
        self.cache_size = cache_size
        self.history_data_cache = defaultdict(dict)  # {frequency: {symbol: (timestamp, DataFrame)}}
        self.cache_hits = 0
        self.cache_misses = 0
        self.lock = threading.RLock()  # 使用可重入锁保护缓存访问
        
        # 缓存过期时间（秒）
        self.cache_expire_seconds = {
            '1d': 86400,  # 日线数据缓存1天
            '60m': 3600,  # 60分钟数据缓存1小时
            '30m': 1800,  # 30分钟数据缓存30分钟
            '15m': 900,   # 15分钟数据缓存15分钟
            '5m': 300,    # 5分钟数据缓存5分钟
            '1m': 60      # 1分钟数据缓存1分钟
        }
        
        # 内存监控
        self.memory_usage = 0
        self.memory_limit = self._get_config_value('MEMORY_LIMIT_MB', 1000) * 1024 * 1024  # 默认限制1GB
        
        # LRU缓存跟踪
        self.access_order = defaultdict(dict)  # {frequency: {symbol: access_timestamp}}
        
        # 日志输出
        if hasattr(context, 'log'):
            self.log = context.log
        else:
            import logging
            self.log = logging.getLogger(__name__)
        
        # 批量获取优化
        self.enable_batch_fetch = self._get_config_value('ENABLE_BATCH_DATA_FETCH', True)
        self.batch_fetch_size = self._get_config_value('BATCH_FETCH_SIZE', 50)
        self.pending_requests = defaultdict(list)  # {frequency: [(symbol, count, fields, callback)]}
        self.batch_fetch_timer = None
        self.batch_fetch_delay = 0.1  # 100ms延迟收集批量请求

        # 记录初始化信息
        self.log.info(f"历史数据管理器初始化完成，缓存大小：{cache_size}，内存限制：{self.memory_limit/(1024*1024):.1f}MB")
        if self.enable_batch_fetch:
            self.log.info(f"批量数据获取已启用，批次大小：{self.batch_fetch_size}")
    
    def get_history_data_batch(self, symbol_list, frequency, count, fields=None):
        """
        批量获取多个股票的历史数据

        Args:
            symbol_list: 股票代码列表
            frequency: 数据频率
            count: 数据条数
            fields: 获取的字段

        Returns:
            dict: {symbol: DataFrame} 股票历史数据字典
        """
        if not self.enable_batch_fetch or len(symbol_list) <= 1:
            # 不启用批量获取或股票数量太少，使用单个获取
            result = {}
            for symbol in symbol_list:
                result[symbol] = self.get_history_data(symbol, frequency, count, fields)
            return result

        result = {}
        cache_misses = []

        # 首先检查缓存
        with self.lock:
            for symbol in symbol_list:
                cache_key = f"{symbol}_{frequency}_{count}"
                if frequency in self.history_data_cache and cache_key in self.history_data_cache[frequency]:
                    cached_timestamp, cached_data = self.history_data_cache[frequency][cache_key]

                    # 检查缓存是否过期
                    if time.time() - cached_timestamp < self.cache_expire_seconds.get(frequency, 3600):
                        # 更新访问时间
                        self.access_order[frequency][cache_key] = time.time()
                        self.cache_hits += 1

                        if fields:
                            result[symbol] = cached_data[fields].tail(count) if all(f in cached_data.columns for f in fields) else None
                        else:
                            result[symbol] = cached_data.tail(count)
                    else:
                        cache_misses.append(symbol)
                else:
                    cache_misses.append(symbol)

        # 批量获取缓存未命中的数据
        if cache_misses:
            try:
                # 使用批量API获取数据（如果支持）
                batch_data = self._batch_fetch_from_source(cache_misses, frequency, count, fields)

                # 更新缓存和结果
                with self.lock:
                    for symbol in cache_misses:
                        if symbol in batch_data and batch_data[symbol] is not None:
                            hist_data = batch_data[symbol]

                            # 更新缓存
                            cache_key = f"{symbol}_{frequency}_{count}"
                            if frequency not in self.history_data_cache:
                                self.history_data_cache[frequency] = {}

                            self.history_data_cache[frequency][cache_key] = (time.time(), hist_data)
                            self.access_order[frequency][cache_key] = time.time()

                            # 添加到结果
                            if fields:
                                result[symbol] = hist_data[fields].tail(count) if all(f in hist_data.columns for f in fields) else hist_data.tail(count)
                            else:
                                result[symbol] = hist_data.tail(count)
                        else:
                            result[symbol] = None

                self.cache_misses += len(cache_misses)

            except Exception as e:
                self.log.error(f"批量获取历史数据失败: {e}")
                # 回退到单个获取
                for symbol in cache_misses:
                    try:
                        result[symbol] = self.get_history_data(symbol, frequency, count, fields)
                    except Exception as e2:
                        self.log.error(f"获取{symbol}历史数据失败: {e2}")
                        result[symbol] = None

        return result

    def _batch_fetch_from_source(self, symbol_list, frequency, count, fields):
        """从数据源批量获取数据"""
        try:
            # 检查data_fetcher是否支持批量获取
            if hasattr(self.context.data_fetcher, 'get_history_data_batch'):
                return self.context.data_fetcher.get_history_data_batch(
                    symbol_list=symbol_list,
                    frequency=frequency,
                    count=count,
                    fields=fields
                )
            else:
                # 回退到单个获取，但使用并行处理
                result = {}
                if hasattr(self.context, 'performance_optimizer') and self.context.performance_optimizer:
                    # 使用性能优化器的并行处理
                    def fetch_single(symbol):
                        return self.context.data_fetcher.get_history_data(
                            symbol=symbol,
                            frequency=frequency,
                            count=count,
                            fields=fields
                        )

                    results = self.context.performance_optimizer.parallel_process_stocks(
                        symbol_list, fetch_single
                    )

                    for symbol, data in zip(symbol_list, results):
                        result[symbol] = data
                else:
                    # 串行获取
                    for symbol in symbol_list:
                        result[symbol] = self.context.data_fetcher.get_history_data(
                            symbol=symbol,
                            frequency=frequency,
                            count=count,
                            fields=fields
                        )

                return result

        except Exception as e:
            self.log.error(f"批量数据源获取失败: {e}")
            raise

    def get_history_data(self, symbol, frequency, count, fields=None, start_time=None):
        """
        获取历史数据，优先从缓存获取，缓存不存在则从数据源获取
        
        Args:
            symbol: 股票代码
            frequency: 数据频率
            count: 数据条数
            fields: 获取的字段
            start_time: 开始时间(可选)
            
        Returns:
            DataFrame: 历史数据
        """
        with self.lock:
            # 处理fields参数，确保格式正确
            if fields is not None:
                if isinstance(fields, str) and ',' in fields:
                    fields = [f.strip() for f in fields.split(',')]
                elif isinstance(fields, str):
                    fields = [fields]
            
            # 检查缓存
            cache_key = symbol
            if frequency in self.history_data_cache and cache_key in self.history_data_cache[frequency]:
                timestamp, cached_data = self.history_data_cache[frequency][cache_key]
                
                # 检查缓存是否过期
                cache_expire = self.cache_expire_seconds.get(frequency, 3600)  # 默认1小时
                if (time.time() - timestamp) < cache_expire:
                    # 检查缓存数据是否满足需求
                    if len(cached_data) >= count:
                        self.cache_hits += 1
                        
                        # 如果指定了字段，只返回指定字段
                        if fields:
                            # 检查是否有合并的列名
                            merged_columns = [col for col in cached_data.columns if isinstance(col, str) and ',' in col]
                            if merged_columns:
                                # 尝试修复缓存中的数据
                                cached_data = self._fix_merged_columns(cached_data, symbol)
                            
                            # 确保所有请求的字段都存在
                            missing_fields = [f for f in fields if f not in cached_data.columns]
                            if missing_fields:
                                self.log.warning(f"缓存数据缺少请求的字段: {missing_fields}，将重新获取数据")
                                # 不使用缓存，继续执行后面的代码获取新数据
                                self.cache_hits -= 1  # 撤销缓存命中计数
                                self.cache_misses += 1  # 增加缓存未命中计数
                            else:
                                # 所有字段都存在，返回缓存数据
                                return cached_data[fields].tail(count)
                        else:
                            # 没有指定字段，返回所有列
                            return cached_data.tail(count)
            
            # 缓存未命中，从数据源获取
            self.cache_misses += 1
            
            try:
                # 获取历史数据
                if start_time:
                    hist_data = self.context.data_fetcher.get_history_data(
                        symbol=symbol,
                        frequency=frequency,
                        count=count,
                        fields=fields,
                        start_time=start_time
                    )
                else:
                    hist_data = self.context.data_fetcher.get_history_data(
                        symbol=symbol,
                        frequency=frequency,
                        count=count,
                        fields=fields
                    )
                
                if hist_data is not None and not hist_data.empty:
                    # 检查是否有合并的列名
                    merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
                    if merged_columns:
                        hist_data = self._fix_merged_columns(hist_data, symbol)
                    
                    # 更新缓存
                    if frequency not in self.history_data_cache:
                        self.history_data_cache[frequency] = {}
                    
                    # 检查是否需要进行缓存淘汰
                    self._check_and_evict_cache(frequency)
                    
                    # 更新缓存
                    self.history_data_cache[frequency][cache_key] = (time.time(), hist_data)
                    
                return hist_data
            except Exception as e:
                self.log.error(f"获取{symbol}的{frequency}历史数据异常: {str(e)}")
                return None
                
    def _check_and_evict_cache(self, frequency):
        """
        检查并淘汰缓存
        
        Args:
            frequency: 数据频率
        """
        # 如果该频率的缓存超过了限制，进行LRU淘汰
        if len(self.history_data_cache[frequency]) >= self.cache_size:
            # 按照最后访问时间排序
            sorted_cache = sorted(
                self.history_data_cache[frequency].items(),
                key=lambda x: x[1][0]  # 按timestamp排序
            )
            
            # 移除最早的20%缓存
            to_remove = int(len(sorted_cache) * 0.2)
            for i in range(to_remove):
                if i < len(sorted_cache):
                    symbol = sorted_cache[i][0]
                    del self.history_data_cache[frequency][symbol]
            
            self.log.info(f"缓存淘汰: 从{frequency}频率移除了{to_remove}条最早的缓存")
            
        # 检查内存使用情况
        memory_usage = self._get_memory_usage()
        memory_limit = self._get_memory_limit()
        
        # 如果内存使用超过80%限制，进行更激进的淘汰
        if memory_usage > memory_limit * 0.8:
            # 统计所有频率的缓存数量
            total_entries = sum(len(cache) for cache in self.history_data_cache.values())
            
            # 计算需要淘汰的比例
            evict_ratio = min(0.5, (memory_usage - memory_limit * 0.7) / (memory_limit * 0.3))
            to_remove = int(total_entries * evict_ratio)
            
            # 从所有频率中淘汰
            removed = 0
            for freq in list(self.history_data_cache.keys()):
                if removed >= to_remove:
                    break
                    
                # 按照最后访问时间排序
                sorted_cache = sorted(
                    self.history_data_cache[freq].items(),
                    key=lambda x: x[1][0]  # 按timestamp排序
                )
                
                # 移除部分缓存
                freq_to_remove = min(len(sorted_cache), to_remove - removed)
                for i in range(freq_to_remove):
                    if i < len(sorted_cache):
                        symbol = sorted_cache[i][0]
                        del self.history_data_cache[freq][symbol]
                        removed += 1
            
            self.log.warning(f"内存使用过高({memory_usage:.2f}MB/{memory_limit:.2f}MB), 淘汰了{removed}条缓存")
    
    def _update_cache(self, frequency, symbol, data):
        """
        更新缓存
        
        Args:
            frequency: 数据频率
            symbol: 股票代码
            data: 数据DataFrame
        """
        # 估计数据内存占用
        estimated_size = self._estimate_dataframe_size(data)
        
        # 检查内存限制
        if self.memory_usage + estimated_size > self.memory_limit:
            # 需要释放内存
            self._free_memory(estimated_size)
        
        # 检查缓存大小，如果超过限制则清理最久未使用的数据（LRU策略）
        if frequency in self.history_data_cache and len(self.history_data_cache[frequency]) >= self.cache_size:
            # 按访问时间排序，删除最久未使用的
            if frequency in self.access_order and self.access_order[frequency]:
                lru_items = sorted(self.access_order[frequency].items(), key=lambda x: x[1])
                oldest_key = lru_items[0][0]
                
                # 估计将要删除的数据大小
                old_data = self.history_data_cache[frequency].get(oldest_key)
                if old_data:
                    _, df = old_data
                    old_size = self._estimate_dataframe_size(df)
                    self.memory_usage = max(0, self.memory_usage - old_size)
                
                # 删除最久未使用的缓存项
                del self.history_data_cache[frequency][oldest_key]
                del self.access_order[frequency][oldest_key]
                
                self.log.debug(f"缓存满，删除最久未使用的数据: {oldest_key}")
        
        # 更新缓存和访问时间
        self.history_data_cache[frequency][symbol] = (time.time(), data)
        self.access_order[frequency][symbol] = time.time()
        
        # 更新内存使用统计
        self.memory_usage += estimated_size
        
        # 定期记录内存使用情况
        if self.cache_hits + self.cache_misses > 0 and (self.cache_hits + self.cache_misses) % 100 == 0:
            self.log.info(f"当前内存使用: {self.memory_usage/(1024*1024):.2f}MB / {self.memory_limit/(1024*1024):.2f}MB")
    
    def _estimate_dataframe_size(self, df):
        """
        估计DataFrame的内存占用
        
        Args:
            df: pandas DataFrame
            
        Returns:
            int: 估计的内存占用（字节）
        """
        if df is None or df.empty:
            return 0
            
        try:
            # 使用pandas内置方法估计大小
            return df.memory_usage(deep=True).sum()
        except:
            # 简单估计：行数 * 列数 * 8字节
            return len(df) * len(df.columns) * 8
    
    def _free_memory(self, required_size):
        """
        释放内存以满足新数据的需求
        
        Args:
            required_size: 需要的内存大小（字节）
        """
        self.log.info(f"内存不足，开始释放缓存。当前: {self.memory_usage/(1024*1024):.2f}MB，需要: {required_size/(1024*1024):.2f}MB")
        
        # 合并所有频率的访问时间
        all_items = []
        for freq, items in self.access_order.items():
            for symbol, access_time in items.items():
                all_items.append((freq, symbol, access_time))
        
        # 按访问时间排序
        all_items.sort(key=lambda x: x[2])
        
        # 从最久未使用的开始释放
        freed_memory = 0
        for freq, symbol, _ in all_items:
            if freed_memory >= required_size:
                break
                
            if freq in self.history_data_cache and symbol in self.history_data_cache[freq]:
                _, df = self.history_data_cache[freq][symbol]
                size = self._estimate_dataframe_size(df)
                
                # 删除缓存项
                del self.history_data_cache[freq][symbol]
                del self.access_order[freq][symbol]
                
                freed_memory += size
                self.memory_usage = max(0, self.memory_usage - size)
                self.log.debug(f"释放缓存: {freq} {symbol}, 大小: {size/(1024*1024):.2f}MB")
        
        self.log.info(f"内存释放完成，释放了 {freed_memory/(1024*1024):.2f}MB，当前使用: {self.memory_usage/(1024*1024):.2f}MB")
    
    def clear_cache(self, frequency=None, symbol=None):
        """
        清除缓存
        
        Args:
            frequency: 指定要清除的频率，如果为None则清除所有频率
            symbol: 指定要清除的股票代码，如果为None则清除指定频率的所有股票
        """
        with self.lock:
            if frequency is None:
                # 清除所有缓存
                self.history_data_cache.clear()
                self.log.info("已清除所有历史数据缓存")
            elif symbol is None:
                # 清除指定频率的所有缓存
                if frequency in self.history_data_cache:
                    self.history_data_cache[frequency].clear()
                    self.log.info(f"已清除{frequency}频率的所有历史数据缓存")
            else:
                # 清除指定频率和股票的缓存
                if frequency in self.history_data_cache and symbol in self.history_data_cache[frequency]:
                    del self.history_data_cache[frequency][symbol]
                    self.log.info(f"已清除{symbol}的{frequency}频率历史数据缓存")
    
    def get_cache_stats(self):
        """获取缓存统计信息"""
        total_items = sum(len(cache) for cache in self.history_data_cache.values())
        stats = {
            'total_items': total_items,
            'hits': self.cache_hits,
            'misses': self.cache_misses,
            'hit_ratio': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
            'items_by_frequency': {freq: len(cache) for freq, cache in self.history_data_cache.items()}
        }
        return stats
    
    def print_cache_stats(self):
        """打印缓存统计信息"""
        stats = self.get_cache_stats()
        self.log.info(f"历史数据缓存统计:")
        self.log.info(f"总缓存项: {stats['total_items']}")
        self.log.info(f"缓存命中: {stats['hits']}")
        self.log.info(f"缓存未命中: {stats['misses']}")
        self.log.info(f"命中率: {stats['hit_ratio']*100:.2f}%")
        self.log.info(f"各频率缓存项: {stats['items_by_frequency']}")
    
    def batch_prefetch(self, symbols, frequency='1d', count=60, fields=None):
        """
        批量预获取多个股票的历史数据并缓存，使用并行处理提高效率
        
        Args:
            symbols: 股票代码列表
            frequency: 数据频率
            count: 获取的数据条数
            fields: 获取的字段
        
        Returns:
            bool: 是否成功预获取
        """
        if not symbols:
            return False
        
        try:
            self.log.info(f"开始批量预获取{len(symbols)}只股票的{frequency}历史数据...")
            start_time = time.time()
            
            # 检查是否可以使用并行处理
            enable_parallel = self._get_config_value('ENABLE_PARALLEL_HISTORY', False)
            max_workers = self._get_config_value('PARALLEL_WORKERS', 4)
            
            # 过滤已缓存的股票
            with self.lock:
                cached_symbols = []
                missing_symbols = []
                
                for symbol in symbols:
                    cache_key = symbol
                    if (frequency in self.history_data_cache and 
                        cache_key in self.history_data_cache[frequency] and
                        time.time() - self.history_data_cache[frequency][cache_key][0] < self.cache_expire_seconds.get(frequency, 1800)):
                        cached_symbols.append(symbol)
                    else:
                        missing_symbols.append(symbol)
            
            if not missing_symbols:
                self.log.info(f"所有{len(symbols)}只股票的{frequency}数据都已缓存，无需预获取")
                return True
                
            self.log.info(f"需要预获取{len(missing_symbols)}只股票的{frequency}数据")
            
            # 使用并行处理加速数据获取
            if enable_parallel and len(missing_symbols) > 5:
                # 使用线程池并行获取数据
                import concurrent.futures
                
                # 分批处理，避免创建过多线程
                batch_size = min(50, max(10, len(missing_symbols) // max_workers))
                batches = [missing_symbols[i:i+batch_size] for i in range(0, len(missing_symbols), batch_size)]
                
                success_count = 0
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 为每个批次创建一个任务
                    future_to_batch = {
                        executor.submit(self._prefetch_batch, batch, frequency, count, fields): i 
                        for i, batch in enumerate(batches)
                    }
                    
                    # 收集结果
                    for future in concurrent.futures.as_completed(future_to_batch):
                        batch_success = future.result()
                        success_count += batch_success
            else:
                # 串行处理
                success_count = self._prefetch_batch(missing_symbols, frequency, count, fields)
            
            end_time = time.time()
            total_success = success_count + len(cached_symbols)
            self.log.info(f"批量预获取完成，成功获取{total_success}/{len(symbols)}只股票的历史数据，耗时{end_time-start_time:.2f}秒")
            
            # 输出内存使用情况
            self.log.info(f"当前内存使用: {self.memory_usage/(1024*1024):.2f}MB / {self.memory_limit/(1024*1024):.2f}MB")
            
            return total_success > 0
            
        except Exception as e:
            self.log.error(f"批量预获取历史数据异常: {str(e)}")
            return False
    
    def _prefetch_batch(self, symbols, frequency, count, fields):
        """
        预获取一批股票的历史数据
        
        Args:
            symbols: 股票代码列表
            frequency: 数据频率
            count: 获取的数据条数
            fields: 获取的字段
            
        Returns:
            int: 成功获取的股票数量
        """
        success_count = 0
        
        try:
            # 尝试使用批量API (如果可用)
            batch_api_available = False
            
            if not batch_api_available:
                # 单独获取每只股票数据
                for symbol in symbols:
                    try:
                        data = self.get_history_data(symbol, frequency, count, fields)
                        if data is not None and not data.empty:
                            success_count += 1
                    except Exception as e:
                        self.log.error(f"预获取{symbol}历史数据异常: {str(e)}")
        
        except Exception as e:
            self.log.error(f"批量预获取数据异常: {str(e)}")
        
        return success_count
    
    def get_history_data_old(self, symbol, frequency, count, fields):
        """
        获取单个股票的历史数据(兼容原接口)
        
        参数:
        - symbol: 股票代码
        - frequency: 频率
        - count: 数据条数
        - fields: 字段列表
        
        返回:
        - DataFrame: 历史数据
        """
        self.total_requests += 1
        
        # 检查缓存
        cache_key = (symbol, frequency, fields, count)
        if cache_key in self.data_cache:
            timestamp, data = self.data_cache[cache_key]
            
            # 检查缓存是否在当前交易日且数据新鲜(15分钟内)
            cache_valid = (
                self.context.now.date() == timestamp.date() and
                (self.context.now - timestamp).total_seconds() < 900  # 15分钟内的缓存有效
            )
            
            if cache_valid:
                self.cache_hits += 1
                # 每1000次请求记录一次缓存统计
                if self.total_requests % 1000 == 0:
                    self._log_cache_stats()
                return data
        
        # 缓存未命中，获取数据
        self.cache_misses += 1
        try:
            # 根据用途选择不同的默认天数
            if count is None or count <= 0:
                if 'volatility' in str(fields).lower() or any(f in str(fields).lower() for f in ['atr', 'tr']):
                    # 用于波动性计算的历史数据
                    count = self._get_config_value('HISTORY_DATA_DAYS_VOLATILITY', 60)
                else:
                    # 用于一般技术指标的历史数据
                    count = self._get_config_value('HISTORY_DATA_DAYS', 50)
            
            data = history_n(
                symbol=symbol,
                frequency=frequency,
                count=count,
                fields=fields,
                skip_suspended=True,
                fill_missing='Last',
                adjust=ADJUST_PREV,
                df=True
            )
            
            # 更新缓存
            if data is not None and not data.empty:
                self.data_cache[cache_key] = (self.context.now, data)
                
                # 缓存大小管理
                if len(self.data_cache) > self.cache_size:
                    # 移除最旧的缓存项
                    oldest_keys = sorted(
                        self.data_cache.keys(), 
                        key=lambda k: self.data_cache[k][0]
                    )[:len(self.data_cache) - self.cache_size]
                    
                    for key in oldest_keys:
                        del self.data_cache[key]
            
            return data
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取{symbol}历史数据失败: {str(e)}")
            return None
    
    def get_batch_history(self, symbols, frequency, count, fields=None):
        """
        批量获取多只股票历史数据，使用并行处理提高效率
        
        参数:
        - symbols: 股票代码列表
        - frequency: 频率
        - count: 数据条数
        - fields: 字段列表
        
        返回:
        - dict: {symbol: DataFrame} 历史数据字典
        """
        if not symbols:
            return {}
            
        with self.lock:
            results = {}
            missing_symbols = []
            
            # 从缓存中检索数据
            for symbol in symbols:
                cache_key = symbol
                if frequency in self.history_data_cache and cache_key in self.history_data_cache[frequency]:
                    timestamp, cached_data = self.history_data_cache[frequency][cache_key]
                    
                    # 检查缓存是否过期
                    cache_expire = self.cache_expire_seconds.get(frequency, 1800)  # 默认30分钟
                    if time.time() - timestamp < cache_expire:
                        # 缓存有效，检查数据是否足够
                        if len(cached_data) >= count:
                            self.cache_hits += 1
                            # 更新访问时间（LRU跟踪）
                            self.access_order[frequency][cache_key] = time.time()
                            
                            # 如果指定了字段，筛选字段
                            if fields:
                                required_fields = fields.split(',') if isinstance(fields, str) else fields
                                available_fields = [f for f in required_fields if f in cached_data.columns]
                                if len(available_fields) == len(required_fields):
                                    results[symbol] = cached_data[required_fields].tail(count)
                                    continue
                            else:
                                results[symbol] = cached_data.tail(count)
                                continue
                
                # 缓存未命中或数据不足
                missing_symbols.append(symbol)
                self.cache_misses += 1
            
            # 如果所有数据都在缓存中，直接返回
            if not missing_symbols:
                return results
            
            # 批量获取缺失数据
            self.log.info(f"批量获取{len(missing_symbols)}只股票的{frequency}历史数据...")
            start_time = time.time()
            
            # 使用并行处理加速数据获取
            try:
                # 检查是否可以使用并行处理
                enable_parallel = self._get_config_value('ENABLE_PARALLEL_HISTORY', True)
                max_workers = self._get_config_value('PARALLEL_WORKERS', 4)
                
                if enable_parallel and len(missing_symbols) > 5:
                    # 使用线程池并行获取数据
                    import concurrent.futures
                    
                    # 分批处理，避免创建过多线程
                    batch_size = min(50, max(10, len(missing_symbols) // max_workers))
                    batches = [missing_symbols[i:i+batch_size] for i in range(0, len(missing_symbols), batch_size)]
                    
                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # 为每个批次创建一个任务
                        future_to_batch = {
                            executor.submit(self._fetch_batch_data, batch, frequency, count, fields): i 
                            for i, batch in enumerate(batches)
                        }
                        
                        # 收集结果
                        for future in concurrent.futures.as_completed(future_to_batch):
                            batch_results = future.result()
                            if batch_results:
                                results.update(batch_results)
                else:
                    # 串行处理
                    batch_results = self._fetch_batch_data(missing_symbols, frequency, count, fields)
                    if batch_results:
                        results.update(batch_results)
                
            except Exception as e:
                self.log.error(f"批量获取历史数据异常: {str(e)}")
                # 如果并行处理失败，回退到串行处理
                for symbol in missing_symbols:
                    try:
                        data = self.get_history_data(symbol, frequency, count, fields)
                        if data is not None and not data.empty:
                            results[symbol] = data
                    except Exception as symbol_e:
                        self.log.error(f"获取{symbol}历史数据异常: {str(symbol_e)}")
            
            end_time = time.time()
            self.log.info(f"批量获取完成，成功获取{len(results)-len(symbols)+len(missing_symbols)}/{len(missing_symbols)}只股票的历史数据，耗时{end_time-start_time:.2f}秒")
            
            return results
    
    def _fetch_batch_data(self, symbols, frequency, count, fields):
        """
        获取一批股票的历史数据
        
        参数:
        - symbols: 股票代码列表
        - frequency: 频率
        - count: 数据条数
        - fields: 字段列表
        
        返回:
        - dict: {symbol: DataFrame} 历史数据字典
        """
        results = {}
        
        try:
            # 尝试使用批量API (如果可用)
            batch_api_available = False
            
            if not batch_api_available:
                # 单独获取每只股票数据
                for symbol in symbols:
                    try:
                        data = None
                        
                        # 使用context的data_fetcher获取数据
                        if hasattr(self.context, 'data_fetcher'):
                            data = self.context.data_fetcher.get_history_data(
                                symbol=symbol,
                                frequency=frequency,
                                count=max(count, 100),  # 多获取一些数据以备后用
                                fields=fields
                            )
                        else:
                            # 直接使用API获取数据
                            data = history_n(
                                symbol=symbol,
                                frequency=frequency,
                                count=max(count, 100),
                                fields=fields,
                                skip_suspended=True,
                                fill_missing='Last',
                                adjust=ADJUST_PREV,
                                df=True
                            )
                        
                        # 缓存数据并添加到结果
                        if data is not None and not data.empty:
                            self._update_cache(frequency, symbol, data)
                            results[symbol] = data
                    except Exception as e:
                        self.log.error(f"获取{symbol}的{frequency}历史数据异常: {str(e)}")
        
        except Exception as e:
            self.log.error(f"批量获取历史数据异常: {str(e)}")
        
        return results
    
    def _log_cache_stats(self):
        """记录缓存统计信息"""
        # 计算缓存命中率
        hit_rate = self.cache_hits / max(1, self.total_requests) * 100
        # 计算内存占用
        cache_size_bytes = sum(
            data.memory_usage(deep=True).sum() 
            for _, (_, data) in self.data_cache.items()
        )
        cache_size_mb = cache_size_bytes / (1024 * 1024)
        
        # 计算距离上次统计的时间
        time_since_last_stats = (datetime.datetime.now() - self.last_cache_stats_time).total_seconds()
        self.last_cache_stats_time = datetime.datetime.now()
        
        self.context.log.info(f"""
        {self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 历史数据缓存统计:
        总请求数: {self.total_requests}
        缓存命中数: {self.cache_hits}
        缓存未命中数: {self.cache_misses}
        缓存命中率: {hit_rate:.2f}%
        当前缓存条目数: {len(self.data_cache)}
        缓存内存占用: {cache_size_mb:.2f}MB
        统计间隔: {time_since_last_stats:.1f}秒
        """)
    
    def _get_config_value(self, param_name, default=None):
        """从context获取配置参数值"""
        try:
            # 首先检查context是否为字符串，如果是则直接返回默认值
            if isinstance(self.context, str):
                return default
            
            # 优先直接从context属性获取
            if hasattr(self.context, param_name):
                return getattr(self.context, param_name)
            
            # 然后从context的get_config_value方法获取
            elif hasattr(self.context, 'get_config_value'):
                return self.context.get_config_value(param_name, default)
            
            # 尝试从context._get_config_value方法获取
            elif hasattr(self.context, '_get_config_value'):
                return self.context._get_config_value(param_name, default)
            
            # 其次从context.config获取
            elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
                return getattr(self.context.config, param_name)
            
            # 尝试从全局函数获取（如果存在）
            elif 'get_config_value' in globals() and callable(globals()['get_config_value']):
                return globals()['get_config_value'](param_name, default)
        except Exception as e:
            # 安全地处理任何错误
            print(f"获取配置值异常 {param_name}: {str(e)}")
            
        # 最后返回默认值
        return default 

    def _get_memory_usage(self):
        """
        获取当前内存使用情况
        
        Returns:
            float: 当前内存使用量(MB)
        """
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # 转换为MB
        
    def _get_memory_limit(self):
        """
        获取内存限制，默认为系统内存的50%
        
        Returns:
            float: 内存限制(MB)
        """
        import psutil
        
        system_memory = psutil.virtual_memory().total / 1024 / 1024  # 转换为MB
        return system_memory * 0.5  # 使用系统内存的50%作为限制
        
    def log_cache_stats(self):
        """
        输出缓存使用统计
        """
        total_entries = 0
        for freq, cache in self.history_data_cache.items():
            total_entries += len(cache)
        
        hit_rate = 0
        if (self.cache_hits + self.cache_misses) > 0:
            hit_rate = self.cache_hits / (self.cache_hits + self.cache_misses) * 100
            
        memory_usage = self._get_memory_usage()
        memory_limit = self._get_memory_limit()
        memory_usage_percent = memory_usage / memory_limit * 100 if memory_limit > 0 else 0
        
        self.log.info(f"历史数据缓存统计:")
        self.log.info(f"- 总缓存条目: {total_entries}")
        self.log.info(f"- 缓存命中次数: {self.cache_hits}")
        self.log.info(f"- 缓存未命中次数: {self.cache_misses}")
        self.log.info(f"- 缓存命中率: {hit_rate:.2f}%")
        self.log.info(f"- 内存使用: {memory_usage:.2f}MB / {memory_limit:.2f}MB ({memory_usage_percent:.2f}%)")
        
        # 按频率统计
        self.log.info("按频率统计:")
        for freq, cache in self.history_data_cache.items():
            self.log.info(f"- {freq}: {len(cache)}条数据")
    
    def _fix_merged_columns(self, df, symbol):
        """
        修复合并的列名
        
        Args:
            df: 包含合并列名的DataFrame
            symbol: 股票代码(用于日志)
            
        Returns:
            DataFrame: 修复后的DataFrame
        """
        try:
            # 检查是否有合并的列名
            merged_columns = [col for col in df.columns if isinstance(col, str) and ',' in col]
            if not merged_columns:
                return df
            
            self.log.warning(f"检测到 {symbol} 的历史数据有合并列名: {merged_columns}")
            
            # 尝试修复合并的列名
            for merged_col in merged_columns:
                # 分割合并的列名
                split_cols = [c.strip() for c in merged_col.split(',')]
                
                # 如果数据是一列，但列名包含多个字段，尝试分割数据
                if len(df.columns) == 1:
                    # 检查第一行数据是否为字符串且包含逗号
                    first_row = df.iloc[0, 0]
                    if isinstance(first_row, str) and ',' in first_row:
                        # 数据也是逗号分隔的，需要完全重新解析
                        self.log.info(f"尝试重新解析 {symbol} 的历史数据")
                        
                        # 将每行数据分割成多列
                        new_data = []
                        for i in range(len(df)):
                            row_values = df.iloc[i, 0].split(',')
                            if len(row_values) == len(split_cols):
                                new_data.append([v.strip() for v in row_values])
                        
                        # 创建新的DataFrame
                        df = pd.DataFrame(new_data, columns=split_cols)
                        self.log.info(f"成功重新解析 {symbol} 的历史数据")
                    else:
                        # 只有列名有问题，数据本身格式正确
                        # 重命名列
                        df.columns = split_cols
                        self.log.info(f"成功修复 {symbol} 的历史数据列名")
                else:
                    # 如果有多列，但其中一列名包含逗号，需要重命名该列
                    new_columns = list(df.columns)
                    col_index = new_columns.index(merged_col)
                    
                    # 如果合并列名中的字段数量与DataFrame的列数相同，可能是所有列名被合并了
                    if len(split_cols) == len(df.columns):
                        df.columns = split_cols
                        self.log.info(f"成功重命名所有列: {split_cols}")
                    else:
                        # 否则只重命名当前列
                        new_columns[col_index] = split_cols[0]  # 使用第一个拆分后的名称
                        df.columns = new_columns
                        self.log.info(f"重命名列 {merged_col} 为 {split_cols[0]}")
            
            return df
        except Exception as e:
            self.log.error(f"修复 {symbol} 的历史数据格式失败: {str(e)}")
            import traceback
            self.log.error(f"异常堆栈: {traceback.format_exc()}")
            return df 

    def get_unified_history_data(self, symbol, frequency, days, fields, use_realtime_price=True):
        """
        获取统一的历史数据，实盘和回测使用相同的数据处理逻辑
        
        参数:
        - symbol: 股票代码
        - frequency: 数据频率，如'1d'
        - days: 获取的历史天数
        - fields: 需要获取的字段
        - use_realtime_price: 是否使用当前实时价格替代最后一个交易日的收盘价
        
        返回:
        - 统一处理后的历史数据DataFrame
        """
        try:
            # 检查是否启用实时价格替代 - 直接使用参数值
            use_realtime = use_realtime_price
            
            # 只有当context不是字符串时，才尝试从配置获取
            if not isinstance(self.context, str):
                use_realtime = use_realtime_price and self._get_config_value('USE_REALTIME_PRICE', True)
            
            # 确保fields是列表格式
            if isinstance(fields, str):
                fields = fields.split(',')
            fields = [f.strip() for f in fields]
            
            # 获取历史数据，不包括当天
            end_date = self.context.now.date() - datetime.timedelta(days=1)
            start_date = end_date - datetime.timedelta(days=days)
            
            # 使用现有的缓存机制获取历史数据
            hist_data = self.get_history_data(
                symbol=symbol, 
                frequency=frequency,
                count=days,
                fields=fields
            )
            
            # 如果需要使用实时价格且在交易时间内
            if use_realtime and not isinstance(self.context, str) and self._is_trading_hour(self.context.now.time()):
                # 获取当前实时价格
                try:
                    current_data = current(symbols=symbol)
                    if current_data and len(current_data) > 0:
                        # 创建当天的数据行
                        today_data = pd.DataFrame(index=[self.context.now.strftime('%Y-%m-%d')])
                        
                        # 填充所有需要的字段
                        for field in fields:
                            field = field.strip()
                            if field == 'close' or field == 'price':
                                today_data[field] = current_data[0]['price']
                            elif field in current_data[0]:
                                today_data[field] = current_data[0][field]
                            else:
                                today_data[field] = current_data[0]['price']  # 默认使用当前价格
                        
                        # 将当天数据添加到历史数据中
                        hist_data = pd.concat([hist_data, today_data])
                        
                        # 记录日志
                        self.log.debug(f"已为{symbol}添加实时价格数据，当前价格: {current_data[0]['price']}")
                except Exception as e:
                    self.log.warning(f"获取{symbol}实时价格异常: {str(e)}")
            
            return hist_data
        except Exception as e:
            self.log.error(f"获取{symbol}统一历史数据异常: {str(e)}")
            return None
    
    def _is_trading_hour(self, current_time=None):
        """
        判断当前是否在交易时间段内
        
        Args:
            current_time: 指定时间，如果为None则使用当前时间
            
        Returns:
            bool: 是否在交易时间段内
        """
        if current_time is None:
            current_time = datetime.datetime.now().time()
        elif isinstance(current_time, datetime.datetime):
            current_time = current_time.time()
            
        # 上午交易时段: 9:30 - 11:30
        morning_session = (
            datetime.time(9, 30) <= current_time <= datetime.time(11, 30)
        )
        
        # 下午交易时段: 13:00 - 15:00
        afternoon_session = (
            datetime.time(13, 0) <= current_time <= datetime.time(15, 0)
        )
        
        # 如果context是字符串，直接返回交易时段判断结果
        if isinstance(self.context, str):
            return morning_session or afternoon_session
        
        # 回测模式下，根据配置决定返回值
        if hasattr(self.context, 'run_mode') and self.context.run_mode == 'backtest':
            # 安全地获取配置值
            try:
                simulate_live_data = self._get_config_value('SIMULATE_LIVE_DATA_IN_BACKTEST', True)
                if simulate_live_data:
                    return morning_session or afternoon_session
                else:
                    return True
            except Exception as e:
                # 如果获取配置值失败，默认返回True
                self.log.warning(f"获取SIMULATE_LIVE_DATA_IN_BACKTEST配置值失败: {str(e)}")
                return True
        
        return morning_session or afternoon_session 