import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import os
import streamlit as st
from datetime import datetime
import json

# 配置matplotlib支持中文显示
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def load_config():
    """加载配置文件"""
    config_file = "analysis_system/streamlit_app/config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except:
            return {"visualization": {"theme": "default", "dpi": 100, "save_format": "png"}}
    else:
        return {"visualization": {"theme": "default", "dpi": 100, "save_format": "png"}}

def setup_plot_style():
    """设置绘图样式"""
    config = load_config()
    vis_config = config.get("visualization", {})
    
    theme = vis_config.get("theme", "default")
    
    # 设置seaborn样式
    if theme == "dark":
        plt.style.use("dark_background")
        sns.set_style("darkgrid")
    elif theme == "light":
        plt.style.use("classic")
        sns.set_style("whitegrid")
    elif theme == "colorblind":
        sns.set_style("whitegrid")
        sns.set_palette("colorblind")
    else:  # default
        sns.set_style("whitegrid")
        sns.set_palette("deep")
    
    # 设置字体大小
    plt.rcParams.update({
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 12,
    })

def save_figure(fig, filename, folder=None):
    """保存图表"""
    config = load_config()
    vis_config = config.get("visualization", {})
    reports_path = config.get("reports_path", "reports")
    
    dpi = vis_config.get("dpi", 100)
    save_format = vis_config.get("save_format", "png")
    
    # 创建保存目录
    if folder:
        save_dir = os.path.join(reports_path, folder)
    else:
        save_dir = reports_path
        
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成文件名
    if not filename.endswith(f".{save_format}"):
        filename = f"{filename}.{save_format}"
        
    file_path = os.path.join(save_dir, filename)
    
    # 保存图表
    fig.savefig(file_path, dpi=dpi, bbox_inches='tight')
    return file_path

def create_profit_distribution_chart(df, save=True):
    """创建收益分布图"""
    setup_plot_style()
    
    fig, ax = plt.subplots(figsize=(10, 6))
    sns.histplot(df['Actual_Profit_Pct'], bins=30, kde=True, ax=ax)
    ax.axvline(x=0, color='red', linestyle='--')
    ax.set_title('交易收益率分布')
    ax.set_xlabel('收益率 (%)')
    ax.set_ylabel('频率')
    
    if save:
        save_figure(fig, "profit_distribution")
    
    return fig

def create_holding_time_chart(df, save=True):
    """创建持仓时间与收益关系图"""
    if 'Holding_Hours' not in df.columns:
        return None
        
    setup_plot_style()
    
    # 创建持仓时间分组
    df['Holding_Days'] = df['Holding_Hours'] / 24
    df['Holding_Group'] = pd.cut(
        df['Holding_Days'], 
        bins=[0, 1, 2, 3, 5, 10, float('inf')],
        labels=['0-1天', '1-2天', '2-3天', '3-5天', '5-10天', '10天以上']
    )
    
    # 绘制持仓时间与收益关系图
    fig, ax = plt.subplots(figsize=(10, 6))
    sns.boxplot(x='Holding_Group', y='Actual_Profit_Pct', data=df, ax=ax)
    ax.set_title('持仓时间与收益率关系')
    ax.set_xlabel('持仓时间')
    ax.set_ylabel('收益率 (%)')
    ax.axhline(y=0, color='red', linestyle='--')
    plt.xticks(rotation=45)
    
    if save:
        save_figure(fig, "holding_time_profit")
    
    return fig

def create_correlation_heatmap(df, features=None, save=True):
    """创建特征相关性热力图"""
    setup_plot_style()
    
    if not features:
        features = [col for col in df.columns if col.endswith('_Buy') or col in ['Actual_Profit_Pct', 'Holding_Hours', 'ATR_Pct']]
    
    if not features:
        return None
    
    # 计算相关性矩阵
    corr_matrix = df[features].corr()
    
    # 为特征名称创建中文映射字典
    feature_names_cn = {
        'Actual_Profit_Pct': '收益率',
        'Holding_Hours': '持仓时间',
        'ATR_Pct': 'ATR百分比',
        'TRIX_Buy': 'TRIX指标',
        'Volatility_Buy': '波动率',
        'ATR_Pct_Buy': '买入时ATR',
        'Volatility_Score_Buy': '波动分数',
        'Allocation_Factor_Buy': '资金分配系数'
    }
    
    # 为其他可能的特征添加默认中文名称
    for col in features:
        if col not in feature_names_cn:
            if col.endswith('_Buy'):
                base_name = col[:-4]  # 移除 _Buy 后缀
                feature_names_cn[col] = f'买入时{base_name}'
            else:
                feature_names_cn[col] = col
    
    # 创建带有中文标签的相关性矩阵
    corr_matrix_cn = corr_matrix.copy()
    corr_matrix_cn.index = [feature_names_cn.get(col, col) for col in corr_matrix.index]
    corr_matrix_cn.columns = [feature_names_cn.get(col, col) for col in corr_matrix.columns]
    
    # 绘制热力图
    fig, ax = plt.subplots(figsize=(12, 10))
    sns.heatmap(corr_matrix_cn, annot=True, cmap='coolwarm', ax=ax, fmt=".2f")
    ax.set_title('特征相关性热力图')
    
    if save:
        save_figure(fig, "feature_correlation")
    
    return fig

def create_feature_importance_chart(feature_importance_df, save=True):
    """创建特征重要性图"""
    setup_plot_style()
    
    # 为特征名称创建中文映射字典
    feature_names_cn = {
        'Actual_Profit_Pct': '收益率',
        'Holding_Hours': '持仓时间',
        'ATR_Pct': 'ATR百分比',
        'TRIX_Buy': 'TRIX指标',
        'Volatility_Buy': '波动率',
        'ATR_Pct_Buy': '买入时ATR',
        'Volatility_Score_Buy': '波动分数',
        'Allocation_Factor_Buy': '资金分配系数'
    }
    
    # 创建带有中文特征名的数据框
    df_cn = feature_importance_df.copy()
    df_cn['Feature_CN'] = df_cn['Feature'].map(lambda x: feature_names_cn.get(x, x))
    
    fig, ax = plt.subplots(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature_CN', data=df_cn, ax=ax)
    ax.set_title('特征重要性')
    ax.set_xlabel('重要性')
    ax.set_ylabel('特征')
    
    if save:
        save_figure(fig, "feature_importance")
    
    return fig

def create_performance_metrics_chart(metrics_df, save=True):
    """创建性能指标对比图"""
    setup_plot_style()
    
    # 绘制性能对比图
    fig, ax = plt.subplots(figsize=(10, 6))
    bar_width = 0.35
    x = np.arange(len(metrics_df["指标"]))
    
    ax.bar(x - bar_width/2, metrics_df["训练集"], bar_width, label="训练集")
    ax.bar(x + bar_width/2, metrics_df["测试集"], bar_width, label="测试集")
    
    ax.set_ylabel("得分")
    ax.set_title("模型性能评估")
    ax.set_xticks(x)
    ax.set_xticklabels(metrics_df["指标"])
    ax.legend()
    
    if save:
        save_figure(fig, "model_performance")
    
    return fig

def create_confusion_matrix_chart(conf_matrix, save=True):
    """创建混淆矩阵图"""
    setup_plot_style()
    
    fig, ax = plt.subplots(figsize=(8, 6))
    sns.heatmap(
        conf_matrix, 
        annot=True, 
        fmt="d", 
        cmap="Blues", 
        xticklabels=["预测负例", "预测正例"],
        yticklabels=["实际负例", "实际正例"], 
        ax=ax
    )
    ax.set_title("混淆矩阵")
    
    if save:
        save_figure(fig, "confusion_matrix")
    
    return fig

def create_profit_calendar_chart(df, save=True):
    """创建收益日历热力图"""
    if 'Buy_Time' not in df.columns:
        return None
        
    setup_plot_style()
    
    try:
        # 转换日期并提取月份和星期
        df['Date'] = pd.to_datetime(df['Buy_Time'])
        df['Month'] = df['Date'].dt.month
        df['Weekday'] = df['Date'].dt.dayofweek
        
        # 按月份和星期聚合
        profit_calendar = df.groupby(['Month', 'Weekday'])['Actual_Profit_Pct'].mean().unstack()
        
        # 如果数据为空，返回None
        if profit_calendar.empty:
            print("收益日历数据为空")
            return None
            
        # 准备星期标签 (确保与数据中的列匹配)
        weekday_labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        # 获取实际存在的星期列
        available_weekdays = sorted(profit_calendar.columns.tolist())
        # 只使用存在的星期对应的标签
        available_labels = [weekday_labels[day] for day in available_weekdays if 0 <= day < len(weekday_labels)]
        
        # 月份的中文标签
        month_labels = {
            1: '一月', 2: '二月', 3: '三月', 4: '四月', 
            5: '五月', 6: '六月', 7: '七月', 8: '八月',
            9: '九月', 10: '十月', 11: '十一月', 12: '十二月'
        }
        
        # 创建带有中文月份标签的数据框
        profit_calendar_cn = profit_calendar.copy()
        profit_calendar_cn.index = [month_labels.get(month, month) for month in profit_calendar.index]
        
        # 绘制热力图
        fig, ax = plt.subplots(figsize=(12, 8))
        sns.heatmap(
            profit_calendar_cn, 
            cmap='RdYlGn', 
            center=0, 
            annot=True, 
            fmt=".2f",
            linewidths=.5, 
            ax=ax
        )
        ax.set_title('各月份各星期平均收益率 (%)')
        ax.set_xlabel('星期')
        ax.set_ylabel('月份')
        # 使用实际存在的标签
        ax.set_xticklabels(available_labels)
        
        if save:
            save_figure(fig, "profit_calendar")
        
        return fig
    except Exception as e:
        print(f"创建收益日历图表时出错: {e}")
        return None 