# coding=utf-8
"""
修复analysis表结构
添加缺失的分析字段，确保能正确保存所有分析数据
"""

import sqlite3

def check_current_analysis_table():
    """检查当前analysis表结构"""
    print('🔍 检查当前analysis表结构')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取当前字段
        cursor.execute("PRAGMA table_info(analysis)")
        current_columns = cursor.fetchall()
        
        current_fields = [col[1].lower() for col in current_columns]
        
        print(f'📊 当前analysis表字段数: {len(current_fields)}')
        print('📋 当前字段列表:')
        for i, field in enumerate(current_fields, 1):
            print(f'  {i:2d}. {field}')
        
        conn.close()
        return current_fields
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return []

def get_required_analysis_fields():
    """获取需要的分析字段"""
    print('\n📋 需要的分析字段')
    print('=' * 50)
    
    required_fields = [
        'timestamp', 'symbol', 'current_price', 'ma3', 'ma7', 'ma20',
        'rsi', 'macd', 'kdj_k', 'kdj_d', 'boll_middle',
        'volume_ratio', 'trend_strength', 'ma_cross_buy_signal',
        'rebound_buy_signal', 'final_buy_signal',
        # TRIX买入信号相关字段
        'trix_buy_signal', 'trix_buy_current', 'trix_buy_prev', 'trix_buy_prev2',
        # TRIX拐点信号相关字段
        'trix_reversal_signal', 'trix_reversal_current', 'trix_reversal_prev', 
        'trix_reversal_prev2', 'trix_reversal_prev3',
        # 反弹买入信号相关字段
        'rebound_period_low', 'rebound_ratio'
    ]
    
    print(f'📊 需要的字段数: {len(required_fields)}')
    print('📋 字段列表:')
    for i, field in enumerate(required_fields, 1):
        print(f'  {i:2d}. {field}')
    
    return required_fields

def add_missing_fields(current_fields, required_fields):
    """添加缺失的字段"""
    print('\n🔧 添加缺失的字段')
    print('=' * 50)
    
    missing_fields = [field for field in required_fields if field not in current_fields]
    
    if not missing_fields:
        print('✅ 所有字段都已存在，无需添加')
        return True
    
    print(f'📊 需要添加的字段数: {len(missing_fields)}')
    print('📋 缺失字段列表:')
    for i, field in enumerate(missing_fields, 1):
        print(f'  {i:2d}. {field}')
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 为每个缺失字段添加列
        for field in missing_fields:
            # 根据字段类型确定数据类型
            if field in ['timestamp', 'symbol']:
                data_type = 'TEXT'
            elif 'signal' in field:
                data_type = 'INTEGER'
            else:
                data_type = 'REAL'
            
            sql = f"ALTER TABLE analysis ADD COLUMN {field} {data_type}"
            print(f'  执行: {sql}')
            
            try:
                cursor.execute(sql)
                print(f'  ✅ 成功添加字段: {field}')
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print(f'  ⚠️ 字段已存在: {field}')
                else:
                    print(f'  ❌ 添加字段失败: {field} - {e}')
        
        conn.commit()
        conn.close()
        
        print(f'\n✅ 字段添加完成')
        return True
        
    except Exception as e:
        print(f'❌ 添加字段失败: {e}')
        return False

def verify_table_structure():
    """验证表结构"""
    print('\n✅ 验证表结构')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取更新后的字段
        cursor.execute("PRAGMA table_info(analysis)")
        updated_columns = cursor.fetchall()
        
        updated_fields = [col[1].lower() for col in updated_columns]
        
        print(f'📊 更新后analysis表字段数: {len(updated_fields)}')
        
        # 检查关键字段
        required_fields = get_required_analysis_fields()
        
        missing_count = 0
        for field in required_fields:
            if field in updated_fields:
                print(f'  ✅ {field}')
            else:
                print(f'  ❌ {field} (仍然缺失)')
                missing_count += 1
        
        if missing_count == 0:
            print(f'\n🎉 所有字段都已正确添加！')
        else:
            print(f'\n⚠️ 仍有{missing_count}个字段缺失')
        
        conn.close()
        return missing_count == 0
        
    except Exception as e:
        print(f'❌ 验证失败: {e}')
        return False

def test_analysis_data_save_after_fix():
    """修复后测试分析数据保存"""
    print('\n🧪 修复后测试分析数据保存')
    print('=' * 50)
    
    # 模拟完整的分析数据
    test_analysis_data = {
        'Timestamp': '2025-07-20 21:00:00',
        'Symbol': 'TEST.000002',
        'Current_Price': 12.50,
        'MA3': 12.45,
        'MA7': 12.40,
        'MA20': 12.35,
        'RSI': 70.5,
        'MACD': 0.25,
        'KDJ_K': 75.0,
        'KDJ_D': 72.0,
        'Final_Buy_Signal': 1,
        'TRIX_Buy_Signal': 1,
        'TRIX_Buy_Current': 0.15,
        'TRIX_Reversal_Signal': 1,
        'TRIX_Reversal_Current': 0.12,
        'Rebound_Buy_Signal': 1,
        'Rebound_Ratio': 5.5
    }
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取表字段
        cursor.execute("PRAGMA table_info(analysis)")
        columns = cursor.fetchall()
        db_fields = [col[1].lower() for col in columns]
        
        # 准备插入数据
        fields = []
        placeholders = []
        values = []
        
        for field, value in test_analysis_data.items():
            field_lower = field.lower()
            if field_lower in db_fields:
                fields.append(field_lower)
                placeholders.append('?')
                values.append(value)
        
        if fields:
            sql = f"INSERT INTO analysis ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
            print(f'📝 SQL: {sql}')
            print(f'📊 保存字段数: {len(fields)}')
            
            cursor.execute(sql, values)
            conn.commit()
            
            print('✅ 完整分析数据保存成功')
            
            # 验证保存
            cursor.execute("SELECT COUNT(*) FROM analysis WHERE symbol = ?", ('TEST.000002',))
            count = cursor.fetchone()[0]
            print(f'✅ 验证: 找到{count}条测试记录')
        else:
            print('❌ 没有匹配的字段可以保存')
        
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return False

def create_analysis_table_backup():
    """创建analysis表备份"""
    print('\n💾 创建analysis表备份')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 创建备份表
        cursor.execute("DROP TABLE IF EXISTS analysis_backup")
        cursor.execute("CREATE TABLE analysis_backup AS SELECT * FROM analysis")
        
        # 检查备份
        cursor.execute("SELECT COUNT(*) FROM analysis_backup")
        backup_count = cursor.fetchone()[0]
        
        print(f'✅ 备份完成，备份了{backup_count}条记录')
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ 备份失败: {e}')
        return False

def main():
    """主函数"""
    print('🔧 analysis表结构修复工具')
    print('=' * 60)
    
    # 创建备份
    print('📋 步骤1: 创建备份')
    create_analysis_table_backup()
    
    # 检查当前表结构
    print('\n📋 步骤2: 检查当前表结构')
    current_fields = check_current_analysis_table()
    
    # 获取需要的字段
    print('\n📋 步骤3: 获取需要的字段')
    required_fields = get_required_analysis_fields()
    
    # 添加缺失字段
    print('\n📋 步骤4: 添加缺失字段')
    add_success = add_missing_fields(current_fields, required_fields)
    
    # 验证表结构
    print('\n📋 步骤5: 验证表结构')
    verify_success = verify_table_structure()
    
    # 测试数据保存
    print('\n📋 步骤6: 测试数据保存')
    test_success = test_analysis_data_save_after_fix()
    
    print(f'\n🎯 修复结果总结')
    print('=' * 40)
    
    if add_success and verify_success and test_success:
        print('✅ analysis表结构修复成功！')
        print('✅ 所有分析字段都已正确添加')
        print('✅ 分析数据保存测试通过')
        print('🎉 现在分析数据应该能正确保存到analysis表')
    else:
        print('❌ 修复过程中遇到问题')
        print('🔧 请检查错误信息并手动修复')
    
    print(f'\n📋 下一步建议:')
    print('1. 🔄 重新运行策略测试')
    print('2. 👀 观察analysis表是否有新记录')
    print('3. 📊 检查trades表的买入记录是否减少')
    print('4. 🎯 验证分析数据和交易数据的正确分离')

if __name__ == '__main__':
    main()
