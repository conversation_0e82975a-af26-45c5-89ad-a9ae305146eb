# coding=utf-8
"""
深度分析胜率未提升的根本原因
检查买入时间分布、因子有效性、策略根本问题
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_current_real_performance():
    """分析当前真实表现"""
    print('📊 当前真实表现深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'SELL' 
        AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 1000
        """
        
        sell_df = pd.read_sql_query(query, conn)
        
        # 获取买入数据
        buy_query = """
        SELECT * FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 1500
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        conn.close()
        
        print(f'📈 最新卖出记录: {len(sell_df)} 条')
        print(f'📈 最新买入记录: {len(buy_df)} 条')
        
        # 转换时间戳
        sell_df['timestamp'] = pd.to_datetime(sell_df['timestamp'])
        buy_df['timestamp'] = pd.to_datetime(buy_df['timestamp'])
        
        # 分析最新表现
        segments = [50, 100, 200, 500, 1000]
        
        print(f'\n📊 最新胜率分析:')
        print(f'样本数    胜率%    平均收益%   盈利交易数   亏损交易数')
        print(f'-' * 60)
        
        for segment_size in segments:
            if segment_size > len(sell_df):
                segment_size = len(sell_df)
            
            segment_data = sell_df.head(segment_size)
            profitable = segment_data[segment_data['net_profit_pct_sell'] > 0]
            losing = segment_data[segment_data['net_profit_pct_sell'] <= 0]
            
            win_rate = len(profitable) / len(segment_data) * 100
            avg_profit = segment_data['net_profit_pct_sell'].mean()
            
            print(f'{segment_size:6d}   {win_rate:6.1f}   {avg_profit:8.2f}   {len(profitable):8d}   {len(losing):8d}')
        
        return sell_df, buy_df
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None

def analyze_buy_time_distribution(buy_df):
    """分析买入时间分布"""
    print(f'\n🕐 买入时间分布分析')
    print('=' * 60)
    
    # 提取小时信息
    buy_df['hour'] = buy_df['timestamp'].dt.hour
    buy_df['minute'] = buy_df['timestamp'].dt.minute
    buy_df['time_period'] = buy_df['hour'] + buy_df['minute'] / 60.0
    
    # 分析最新1000条买入记录的时间分布
    latest_1000 = buy_df.head(1000)
    
    print(f'📊 分析最新 {len(latest_1000)} 条买入记录的时间分布')
    
    # 按小时统计
    hour_distribution = latest_1000['hour'].value_counts().sort_index()
    
    print(f'\n⏰ 按小时分布:')
    print(f'时间    买入数量   占比%')
    print(f'-' * 30)
    
    total_buys = len(latest_1000)
    opening_buys = 0  # 9:30-10:30的买入
    
    for hour in range(9, 16):  # 交易时间9:00-15:00
        if hour in hour_distribution.index:
            count = hour_distribution[hour]
            percentage = count / total_buys * 100
            
            if hour == 9:  # 9:30-10:30算开盘时段
                opening_buys += count
            elif hour == 10:
                # 检查10:30之前的
                morning_10_30 = latest_1000[(latest_1000['hour'] == 10) & (latest_1000['minute'] <= 30)]
                opening_buys += len(morning_10_30)
                count_before_1030 = len(morning_10_30)
                count_after_1030 = count - count_before_1030
                print(f'{hour:02d}:00  {count_before_1030:6d}     {count_before_1030/total_buys*100:5.1f}  (开盘时段)')
                if count_after_1030 > 0:
                    print(f'{hour:02d}:30  {count_after_1030:6d}     {count_after_1030/total_buys*100:5.1f}')
                continue
            
            time_label = "开盘时段" if hour == 9 else ""
            print(f'{hour:02d}:00  {count:6d}     {percentage:5.1f}  {time_label}')
        else:
            print(f'{hour:02d}:00  {0:6d}     {0:5.1f}')
    
    opening_percentage = opening_buys / total_buys * 100
    
    print(f'\n🚨 开盘时段分析 (9:30-10:30):')
    print(f'   开盘时段买入: {opening_buys} 条')
    print(f'   开盘时段占比: {opening_percentage:.1f}%')
    
    if opening_percentage > 50:
        print(f'   ⚠️ 问题: 开盘时段买入过多，可能是问题根源')
    elif opening_percentage > 30:
        print(f'   🟡 注意: 开盘时段买入较多，需要关注')
    else:
        print(f'   ✅ 正常: 开盘时段买入比例合理')
    
    return opening_percentage, hour_distribution

def analyze_opening_period_performance(sell_df, buy_df):
    """分析开盘时段表现"""
    print(f'\n📊 开盘时段表现分析')
    print('=' * 60)
    
    # 匹配买入卖出记录
    matched_trades = []
    
    for _, sell_row in sell_df.head(500).iterrows():
        symbol = sell_row['symbol']
        sell_time = sell_row['timestamp']
        
        # 找对应的买入记录
        symbol_buys = buy_df[buy_df['symbol'] == symbol].copy()
        if len(symbol_buys) > 0:
            symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
            valid_buys = symbol_buys[symbol_buys['timestamp'] < sell_time]
            
            if len(valid_buys) > 0:
                recent_buy = valid_buys.loc[valid_buys['timestamp'].idxmax()]
                buy_hour = recent_buy['timestamp'].hour
                buy_minute = recent_buy['timestamp'].minute
                
                # 判断是否为开盘时段
                is_opening = (buy_hour == 9 and buy_minute >= 30) or (buy_hour == 10 and buy_minute <= 30)
                
                matched_trades.append({
                    'symbol': symbol,
                    'profit': sell_row['net_profit_pct_sell'],
                    'profitable': sell_row['net_profit_pct_sell'] > 0,
                    'buy_hour': buy_hour,
                    'buy_minute': buy_minute,
                    'is_opening': is_opening,
                    'buy_time_str': f'{buy_hour:02d}:{buy_minute:02d}'
                })
    
    matched_df = pd.DataFrame(matched_trades)
    print(f'📊 成功匹配: {len(matched_df)} 条交易')
    
    if len(matched_df) == 0:
        print('⚠️ 没有匹配的交易数据')
        return None
    
    # 分析开盘vs非开盘时段表现
    opening_trades = matched_df[matched_df['is_opening'] == True]
    non_opening_trades = matched_df[matched_df['is_opening'] == False]
    
    print(f'\n📈 开盘 vs 非开盘时段对比:')
    print(f'时段        交易数   胜率%   平均收益%   盈利数   亏损数')
    print(f'-' * 55)
    
    if len(opening_trades) > 0:
        opening_win_rate = opening_trades['profitable'].mean() * 100
        opening_avg_profit = opening_trades['profit'].mean()
        opening_profitable = opening_trades['profitable'].sum()
        opening_losing = len(opening_trades) - opening_profitable
        
        print(f'开盘时段    {len(opening_trades):6d}  {opening_win_rate:6.1f}  {opening_avg_profit:8.2f}  {opening_profitable:6d}  {opening_losing:6d}')
    
    if len(non_opening_trades) > 0:
        non_opening_win_rate = non_opening_trades['profitable'].mean() * 100
        non_opening_avg_profit = non_opening_trades['profit'].mean()
        non_opening_profitable = non_opening_trades['profitable'].sum()
        non_opening_losing = len(non_opening_trades) - non_opening_profitable
        
        print(f'非开盘时段  {len(non_opening_trades):6d}  {non_opening_win_rate:6.1f}  {non_opening_avg_profit:8.2f}  {non_opening_profitable:6d}  {non_opening_losing:6d}')
    
    # 分析差异
    if len(opening_trades) > 0 and len(non_opening_trades) > 0:
        win_rate_diff = opening_win_rate - non_opening_win_rate
        profit_diff = opening_avg_profit - non_opening_avg_profit
        
        print(f'\n🔍 差异分析:')
        print(f'   胜率差异: {win_rate_diff:+.1f}% (开盘 - 非开盘)')
        print(f'   收益差异: {profit_diff:+.2f}% (开盘 - 非开盘)')
        
        if win_rate_diff < -10:
            print(f'   🚨 严重问题: 开盘时段胜率显著低于非开盘时段')
            opening_issue = 'SEVERE'
        elif win_rate_diff < -5:
            print(f'   ⚠️ 明显问题: 开盘时段表现明显差于非开盘时段')
            opening_issue = 'MODERATE'
        elif win_rate_diff < 0:
            print(f'   🟡 轻微问题: 开盘时段表现略差于非开盘时段')
            opening_issue = 'MINOR'
        else:
            print(f'   ✅ 开盘时段表现正常或更好')
            opening_issue = 'NONE'
    else:
        opening_issue = 'UNKNOWN'
    
    return matched_df, opening_issue

def analyze_factor_effectiveness_reality(buy_df):
    """分析因子实际有效性"""
    print(f'\n🔍 因子实际有效性分析')
    print('=' * 60)
    
    latest_1000 = buy_df.head(1000)
    
    print(f'📊 分析最新 {len(latest_1000)} 条买入记录的因子分布')
    
    # 检查当前配置的实际生效情况
    factors_analysis = {}
    
    # CCI分析
    if 'cci' in latest_1000.columns:
        cci_data = latest_1000['cci'].dropna()
        cci_in_range = latest_1000[(latest_1000['cci'] >= 0) & (latest_1000['cci'] <= 100)]['cci'].count()
        cci_stats = {
            'total': len(cci_data),
            'in_range': cci_in_range,
            'in_range_pct': cci_in_range / len(cci_data) * 100 if len(cci_data) > 0 else 0,
            'mean': cci_data.mean(),
            'median': cci_data.median(),
            'std': cci_data.std(),
            'min': cci_data.min(),
            'max': cci_data.max()
        }
        factors_analysis['cci'] = cci_stats
    
    # ATR分析
    if 'atr_pct' in latest_1000.columns:
        atr_data = latest_1000['atr_pct'].dropna()
        atr_above_26 = latest_1000[latest_1000['atr_pct'] > 2.6]['atr_pct'].count()
        atr_above_15 = latest_1000[latest_1000['atr_pct'] > 1.5]['atr_pct'].count()
        atr_stats = {
            'total': len(atr_data),
            'above_26': atr_above_26,
            'above_26_pct': atr_above_26 / len(atr_data) * 100 if len(atr_data) > 0 else 0,
            'above_15': atr_above_15,
            'above_15_pct': atr_above_15 / len(atr_data) * 100 if len(atr_data) > 0 else 0,
            'mean': atr_data.mean(),
            'median': atr_data.median(),
            'std': atr_data.std(),
            'min': atr_data.min(),
            'max': atr_data.max()
        }
        factors_analysis['atr_pct'] = atr_stats
    
    # 其他因子分析
    other_factors = ['rsi', 'adx', 'macd_hist', 'bb_width', 'overall_score']
    
    for factor in other_factors:
        if factor in latest_1000.columns:
            factor_data = latest_1000[factor].dropna()
            if len(factor_data) > 0:
                factor_stats = {
                    'total': len(factor_data),
                    'mean': factor_data.mean(),
                    'median': factor_data.median(),
                    'std': factor_data.std(),
                    'min': factor_data.min(),
                    'max': factor_data.max()
                }
                factors_analysis[factor] = factor_stats
    
    # 输出分析结果
    print(f'\n📈 因子配置生效情况:')
    
    if 'cci' in factors_analysis:
        cci = factors_analysis['cci']
        print(f'   CCI [0,100]: {cci["in_range"]}条 ({cci["in_range_pct"]:.1f}%)')
        print(f'   CCI分布: 均值{cci["mean"]:.1f}, 中位{cci["median"]:.1f}, 范围[{cci["min"]:.1f}, {cci["max"]:.1f}]')
    
    if 'atr_pct' in factors_analysis:
        atr = factors_analysis['atr_pct']
        print(f'   ATR >2.6%: {atr["above_26"]}条 ({atr["above_26_pct"]:.1f}%)')
        print(f'   ATR >1.5%: {atr["above_15"]}条 ({atr["above_15_pct"]:.1f}%)')
        print(f'   ATR分布: 均值{atr["mean"]:.1f}%, 中位{atr["median"]:.1f}%, 范围[{atr["min"]:.1f}%, {atr["max"]:.1f}%]')
        
        if atr["above_26_pct"] < 20:
            print(f'   🚨 问题: ATR>2.6%的信号过少，可能过度筛选')
    
    for factor in other_factors:
        if factor in factors_analysis:
            stats = factors_analysis[factor]
            print(f'   {factor.upper()}: 均值{stats["mean"]:.2f}, 中位{stats["median"]:.2f}, 范围[{stats["min"]:.2f}, {stats["max"]:.2f}]')
    
    return factors_analysis

def identify_fundamental_issues():
    """识别根本问题"""
    print(f'\n🚨 根本问题识别')
    print('=' * 60)
    
    issues = '''
🔍 可能的根本问题:

1. 📊 开盘时段问题:
   - 如果开盘时段买入过多且表现差
   - 开盘时段市场波动大，技术指标失效
   - 需要特殊的开盘时段处理逻辑

2. ⚙️ 因子配置问题:
   - ATR>2.6%可能过于严格，信号过少
   - 因子组合可能不适合当前市场环境
   - 单一因子优化可能不够，需要组合优化

3. 📈 策略逻辑问题:
   - 技术指标在当前市场环境下可能失效
   - 买入卖出逻辑可能存在系统性偏差
   - 止损止盈设置可能不合理

4. 🕐 时间因子问题:
   - 不同时间段的市场特征差异很大
   - 需要针对不同时间段的差异化策略
   - 开盘、盘中、收盘的处理方式需要区别

5. 📊 数据质量问题:
   - 历史数据可能不代表当前市场
   - 因子计算可能存在误差
   - 数据匹配逻辑可能有问题

6. 🎯 优化方向问题:
   - 可能优化方向根本错误
   - 过度依赖技术指标而忽略其他因素
   - 需要重新审视策略的基本假设
'''
    
    print(issues)

def generate_diagnostic_recommendations(opening_issue, factors_analysis):
    """生成诊断建议"""
    print(f'\n🔧 诊断建议')
    print('=' * 60)
    
    recommendations = f'''
🚀 基于分析结果的诊断建议:

📊 开盘时段问题处理:
   问题等级: {opening_issue}
   
   如果开盘时段表现差:
   1. 🕐 实施开盘时段特殊处理
   2. ⚙️ 提高开盘时段的因子要求
   3. 🚫 考虑禁用开盘时段交易
   4. 📊 分析开盘时段的特殊模式

⚙️ 因子配置调整:
'''
    
    if 'atr_pct' in factors_analysis:
        atr_pct = factors_analysis['atr_pct']['above_26_pct']
        if atr_pct < 20:
            recommendations += f'''
   ATR配置问题:
   - 当前ATR>2.6%仅{atr_pct:.1f}%信号符合
   - 建议回退到ATR>2.0%或ATR>1.8%
   - 平衡信号数量与质量
'''
    
    recommendations += f'''
🔍 深度诊断方向:
   1. 📊 重新分析不同时间段的表现差异
   2. ⚙️ 测试更宽松的因子配置
   3. 🕐 实施时间段差异化策略
   4. 📈 分析止损止盈逻辑的合理性
   5. 🔄 考虑完全不同的策略方向

💡 立即行动建议:
   1. 如果开盘时段问题严重，立即调整开盘配置
   2. 如果ATR过严，回退到更宽松的配置
   3. 分析最近表现好的交易的共同特征
   4. 重新审视策略的基本假设
'''
    
    print(recommendations)

def main():
    """主函数"""
    print('🔍 深度分析胜率未提升的根本原因')
    print('=' * 60)
    
    print('🚨 问题: 胜率仍然是44%，没有有效提升')
    print('🎯 目标: 找出根本原因，制定解决方案')
    
    # 分析当前真实表现
    result = analyze_current_real_performance()
    
    if result[0] is not None:
        sell_df, buy_df = result
        
        # 分析买入时间分布
        opening_percentage, hour_distribution = analyze_buy_time_distribution(buy_df)
        
        # 分析开盘时段表现
        opening_result = analyze_opening_period_performance(sell_df, buy_df)
        
        if opening_result:
            matched_df, opening_issue = opening_result
        else:
            opening_issue = 'UNKNOWN'
        
        # 分析因子实际有效性
        factors_analysis = analyze_factor_effectiveness_reality(buy_df)
        
        # 识别根本问题
        identify_fundamental_issues()
        
        # 生成诊断建议
        generate_diagnostic_recommendations(opening_issue, factors_analysis)
        
        print(f'\n🎯 关键发现')
        print('=' * 40)
        print(f'📊 当前胜率确实未提升 (仍在44%左右)')
        print(f'🕐 开盘时段买入占比: {opening_percentage:.1f}%')
        print(f'📈 开盘时段问题等级: {opening_issue}')
        
        if 'atr_pct' in factors_analysis:
            atr_pct = factors_analysis['atr_pct']['above_26_pct']
            print(f'⚙️ ATR>2.6%信号占比: {atr_pct:.1f}%')
            
            if atr_pct < 20:
                print(f'🚨 ATR配置过严，需要调整')
        
        print(f'\n🔧 立即行动建议:')
        if opening_percentage > 50 and opening_issue in ['SEVERE', 'MODERATE']:
            print(f'   1. 优先解决开盘时段问题')
        if 'atr_pct' in factors_analysis and factors_analysis['atr_pct']['above_26_pct'] < 20:
            print(f'   2. 回退ATR配置到更宽松设置')
        print(f'   3. 重新审视策略基本假设')
        print(f'   4. 考虑时间段差异化策略')
    
    else:
        print('❌ 数据分析失败，请检查数据库')

if __name__ == '__main__':
    main()
