# coding=utf-8
"""
快速检查关键配置
"""

from config import get_config_value

def quick_check():
    print('🔍 快速配置检查')
    print('=' * 40)
    
    # 检查智能评分
    smart_config = get_config_value('SMART_SCORING_CONFIG', {})
    print(f'智能评分配置: {smart_config.get("enable_smart_scoring", "NOT_FOUND")}')
    
    # 检查时序分析
    timeseries_global = get_config_value('enable_timeseries_analysis', 'NOT_FOUND')
    timeseries_config = smart_config.get('enable_timeseries_analysis', 'NOT_FOUND')
    print(f'时序分析(全局): {timeseries_global}')
    print(f'时序分析(配置): {timeseries_config}')
    
    # 检查多因子确认
    confirmations = get_config_value('MULTIFACTOR_CONFIRMATIONS', {})
    print(f'确认条件数: {confirmations.get("min_score_count", "NOT_FOUND")}')
    print(f'动量确认: {confirmations.get("require_momentum_confirmation", "NOT_FOUND")}')
    
    # 检查跟踪止盈
    trailing_stop = get_config_value('TRAILING_STOP', 'NOT_FOUND')
    print(f'跟踪止盈: {trailing_stop}')

if __name__ == '__main__':
    quick_check()
