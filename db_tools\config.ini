[Database]
# 数据库文件路径，可以是绝对路径或相对于项目根目录的相对路径
# 默认使用项目根目录下的data/trades.db文件
db_file = data/trades.db

# 数据库备份目录，可以是绝对路径或相对于项目根目录的相对路径
backup_dir = backups

[Reports]
# 报告输出目录，可以是绝对路径或相对于项目根目录的相对路径
report_dir = reports

# 报告文件名格式，支持日期格式化
report_name_format = db_report_%Y%m%d_%H%M%S.html

[Analysis]
# 分析结果输出目录，可以是绝对路径或相对于项目根目录的相对路径
output_dir = analysis

# 是否在分析时自动生成图表
generate_plots = true

# 图表DPI设置（影响图表质量和文件大小）
plot_dpi = 100

[UI]
# 是否使用彩色终端输出
use_color = true

# 终端输出语言
language = zh_CN 