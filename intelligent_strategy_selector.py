# coding=utf-8
"""
智能策略选择器
基于完整因子数据的智能买入策略
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentStrategySelector:
    """智能策略选择器"""
    
    def __init__(self, db_path='data/enhanced_market_data.db'):
        self.db_path = db_path
        
        # 基于盈利分析的优化配置
        self.optimized_config = {
            'technical_factors': {
                'cci_14': {
                    'weight': 0.15,
                    'min_threshold': -30,    # 基于高盈利CCI均值-9.0优化
                    'max_threshold': 100,
                    'optimal_range': [-10, 20]
                },
                'rsi_14': {
                    'weight': 0.12,
                    'min_threshold': 30,
                    'max_threshold': 70,
                    'optimal_range': [40, 60]
                },
                'atr_pct': {
                    'weight': 0.15,
                    'min_threshold': 3.5,    # 基于高盈利ATR均值3.9%优化
                    'max_threshold': 6.0,
                    'optimal_range': [3.5, 5.0]
                },
                'adx_14': {
                    'weight': 0.10,
                    'min_threshold': 30,     # 基于高盈利ADX均值31.7优化
                    'optimal_range': [30, 50]
                },
                'bb_position': {
                    'weight': 0.08,
                    'min_threshold': 20,
                    'max_threshold': 80,
                    'optimal_range': [30, 70]
                }
            },
            'fundamental_factors': {
                'pe_relative': {
                    'weight': 0.08,
                    'max_threshold': 1.5,    # PE相对值不能过高
                },
                'roe_ttm': {
                    'weight': 0.06,
                    'min_threshold': 8,      # ROE最低要求
                },
                'revenue_growth': {
                    'weight': 0.05,
                    'min_threshold': -10,    # 营收增长最低要求
                }
            },
            'sentiment_factors': {
                'main_fund_persistence': {
                    'weight': 0.08,
                    'min_threshold': 0.4,    # 主力资金持续性
                },
                'market_attention': {
                    'weight': 0.06,
                    'min_threshold': 1.2,    # 市场关注度
                },
                'volume_ratio': {
                    'weight': 0.07,
                    'min_threshold': 1.5,    # 成交量放大
                }
            },
            'buy_conditions': {
                'min_overall_score': 0.65,      # 基于优化后的综合评分
                'min_technical_score': 0.6,     # 技术面最低要求
                'min_fundamental_score': 0.4,   # 基本面最低要求
                'min_sentiment_score': 0.5,     # 情绪面最低要求
                'max_signals_per_run': 10,      # 每次最多选择10只股票
            }
        }
    
    def get_all_calculated_factors(self, limit_days=5):
        """获取所有计算好的因子数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取最近计算的因子数据
            query = """
                SELECT cf.*, dm.close_price, dm.pct_change, dm.volume,
                       icm.industry_sw1, icm.concept_names
                FROM calculated_factors cf
                LEFT JOIN daily_market_data dm ON cf.symbol = dm.symbol 
                    AND cf.trade_date = dm.trade_date
                LEFT JOIN industry_concept_mapping icm ON cf.symbol = icm.symbol
                WHERE cf.trade_date >= date('now', '-{} days')
                ORDER BY cf.overall_score DESC
            """.format(limit_days)
            
            factors_df = pd.read_sql_query(query, conn)
            conn.close()
            
            logger.info(f"获取到 {len(factors_df)} 条因子数据")
            return factors_df
            
        except Exception as e:
            logger.error(f"获取因子数据失败: {e}")
            return pd.DataFrame()
    
    def apply_technical_filters(self, factors_df):
        """应用技术面筛选"""
        if factors_df.empty:
            return factors_df
        
        logger.info("应用技术面筛选...")
        
        filtered_df = factors_df.copy()
        initial_count = len(filtered_df)
        
        # CCI筛选 (基于盈利分析优化)
        cci_config = self.optimized_config['technical_factors']['cci_14']
        filtered_df = filtered_df[
            (filtered_df['cci_14'] >= cci_config['min_threshold']) &
            (filtered_df['cci_14'] <= cci_config['max_threshold'])
        ]
        logger.info(f"CCI筛选: {len(filtered_df)}/{initial_count} (阈值: {cci_config['min_threshold']} ~ {cci_config['max_threshold']})")
        
        # ATR筛选 (基于盈利分析大幅提升)
        atr_config = self.optimized_config['technical_factors']['atr_pct']
        filtered_df = filtered_df[
            (filtered_df['atr_pct'] >= atr_config['min_threshold']) &
            (filtered_df['atr_pct'] <= atr_config['max_threshold'])
        ]
        logger.info(f"ATR筛选: {len(filtered_df)}/{initial_count} (阈值: ≥{atr_config['min_threshold']}%)")
        
        # ADX筛选 (基于盈利分析提升)
        adx_config = self.optimized_config['technical_factors']['adx_14']
        filtered_df = filtered_df[filtered_df['adx_14'] >= adx_config['min_threshold']]
        logger.info(f"ADX筛选: {len(filtered_df)}/{initial_count} (阈值: ≥{adx_config['min_threshold']})")
        
        # RSI筛选
        rsi_config = self.optimized_config['technical_factors']['rsi_14']
        filtered_df = filtered_df[
            (filtered_df['rsi_14'] >= rsi_config['min_threshold']) &
            (filtered_df['rsi_14'] <= rsi_config['max_threshold'])
        ]
        logger.info(f"RSI筛选: {len(filtered_df)}/{initial_count} (阈值: {rsi_config['min_threshold']} ~ {rsi_config['max_threshold']})")
        
        # 布林带位置筛选
        bb_config = self.optimized_config['technical_factors']['bb_position']
        filtered_df = filtered_df[
            (filtered_df['bb_position'] >= bb_config['min_threshold']) &
            (filtered_df['bb_position'] <= bb_config['max_threshold'])
        ]
        logger.info(f"布林带筛选: {len(filtered_df)}/{initial_count}")
        
        return filtered_df
    
    def apply_fundamental_filters(self, factors_df):
        """应用基本面筛选"""
        if factors_df.empty:
            return factors_df
        
        logger.info("应用基本面筛选...")
        
        filtered_df = factors_df.copy()
        initial_count = len(filtered_df)
        
        # PE相对值筛选
        pe_config = self.optimized_config['fundamental_factors']['pe_relative']
        filtered_df = filtered_df[
            (filtered_df['pe_relative'].isna()) | 
            (filtered_df['pe_relative'] <= pe_config['max_threshold'])
        ]
        logger.info(f"PE相对值筛选: {len(filtered_df)}/{initial_count}")
        
        # ROE筛选
        roe_config = self.optimized_config['fundamental_factors']['roe_ttm']
        filtered_df = filtered_df[
            (filtered_df['roe_quality'].isna()) | 
            (filtered_df['roe_quality'] >= roe_config['min_threshold'])
        ]
        logger.info(f"ROE筛选: {len(filtered_df)}/{initial_count}")
        
        return filtered_df
    
    def apply_sentiment_filters(self, factors_df):
        """应用市场情绪筛选"""
        if factors_df.empty:
            return factors_df
        
        logger.info("应用市场情绪筛选...")
        
        filtered_df = factors_df.copy()
        initial_count = len(filtered_df)
        
        # 主力资金持续性筛选
        fund_config = self.optimized_config['sentiment_factors']['main_fund_persistence']
        filtered_df = filtered_df[
            (filtered_df['main_fund_persistence'].isna()) | 
            (filtered_df['main_fund_persistence'] >= fund_config['min_threshold'])
        ]
        logger.info(f"主力资金筛选: {len(filtered_df)}/{initial_count}")
        
        # 市场关注度筛选
        attention_config = self.optimized_config['sentiment_factors']['market_attention']
        filtered_df = filtered_df[
            (filtered_df['market_attention'].isna()) | 
            (filtered_df['market_attention'] >= attention_config['min_threshold'])
        ]
        logger.info(f"市场关注度筛选: {len(filtered_df)}/{initial_count}")
        
        return filtered_df
    
    def calculate_enhanced_score(self, factors_df):
        """计算增强评分"""
        if factors_df.empty:
            return factors_df
        
        logger.info("计算增强评分...")
        
        enhanced_df = factors_df.copy()
        
        # 计算技术面增强评分
        tech_factors = self.optimized_config['technical_factors']
        tech_score = 0
        
        for factor, config in tech_factors.items():
            if factor in enhanced_df.columns:
                weight = config['weight']
                
                if factor == 'cci_14':
                    # CCI评分：接近最优区间得分更高
                    optimal_range = config['optimal_range']
                    score = enhanced_df[factor].apply(lambda x: 
                        1.0 if optimal_range[0] <= x <= optimal_range[1] 
                        else max(0, 1 - abs(x - np.mean(optimal_range)) / 50)
                    )
                elif factor == 'rsi_14':
                    # RSI评分：接近50得分更高
                    score = enhanced_df[factor].apply(lambda x: 1 - abs(x - 50) / 50)
                elif factor == 'atr_pct':
                    # ATR评分：在最优区间内得分更高
                    optimal_range = config['optimal_range']
                    score = enhanced_df[factor].apply(lambda x: 
                        1.0 if optimal_range[0] <= x <= optimal_range[1] 
                        else max(0, 1 - abs(x - np.mean(optimal_range)) / 3)
                    )
                elif factor == 'adx_14':
                    # ADX评分：越高越好，但有上限
                    score = enhanced_df[factor].apply(lambda x: min(1, x / 50))
                elif factor == 'bb_position':
                    # 布林带位置评分
                    score = enhanced_df[factor].apply(lambda x: 
                        max(0, min(1, (x - 20) / 60)) if not np.isnan(x) else 0.5
                    )
                else:
                    score = 0.5  # 默认中性评分
                
                tech_score += score * weight
        
        enhanced_df['enhanced_technical_score'] = tech_score
        
        # 计算综合增强评分
        enhanced_df['enhanced_overall_score'] = (
            enhanced_df['enhanced_technical_score'] * 0.6 +
            enhanced_df['fundamental_score'].fillna(0.5) * 0.2 +
            enhanced_df['sentiment_score'].fillna(0.5) * 0.2
        )
        
        return enhanced_df
    
    def apply_final_selection(self, factors_df):
        """应用最终选择条件"""
        if factors_df.empty:
            return factors_df
        
        logger.info("应用最终选择条件...")
        
        buy_conditions = self.optimized_config['buy_conditions']
        
        # 综合评分筛选
        filtered_df = factors_df[
            factors_df['enhanced_overall_score'] >= buy_conditions['min_overall_score']
        ]
        
        logger.info(f"综合评分筛选: {len(filtered_df)}/{len(factors_df)} (阈值: ≥{buy_conditions['min_overall_score']})")
        
        # 按综合评分排序，选择前N只
        max_signals = buy_conditions['max_signals_per_run']
        top_stocks = filtered_df.nlargest(max_signals, 'enhanced_overall_score')
        
        logger.info(f"最终选择: {len(top_stocks)} 只股票")
        
        return top_stocks
    
    def generate_buy_signals(self):
        """生成买入信号"""
        logger.info("🚀 开始生成智能买入信号...")
        
        # 1. 获取因子数据
        factors_df = self.get_all_calculated_factors()
        if factors_df.empty:
            logger.warning("没有可用的因子数据")
            return pd.DataFrame()
        
        # 2. 应用技术面筛选
        filtered_df = self.apply_technical_filters(factors_df)
        
        # 3. 应用基本面筛选
        filtered_df = self.apply_fundamental_filters(filtered_df)
        
        # 4. 应用市场情绪筛选
        filtered_df = self.apply_sentiment_filters(filtered_df)
        
        # 5. 计算增强评分
        enhanced_df = self.calculate_enhanced_score(filtered_df)
        
        # 6. 最终选择
        final_selection = self.apply_final_selection(enhanced_df)
        
        return final_selection
    
    def format_buy_signals(self, signals_df):
        """格式化买入信号"""
        if signals_df.empty:
            return "❌ 没有找到符合条件的买入信号"
        
        result = f"🎯 智能策略选择结果 ({len(signals_df)} 只股票)\n"
        result += "=" * 80 + "\n"
        
        for i, (_, row) in enumerate(signals_df.iterrows(), 1):
            result += f"\n📈 {i}. {row['symbol']}\n"
            result += f"   综合评分: {row['enhanced_overall_score']:.4f}\n"
            result += f"   技术评分: {row['enhanced_technical_score']:.4f}\n"
            result += f"   基本面评分: {row['fundamental_score']:.4f}\n"
            result += f"   情绪评分: {row['sentiment_score']:.4f}\n"
            
            # 关键因子
            result += f"   关键因子:\n"
            result += f"     CCI: {row['cci_14']:.2f}\n"
            result += f"     RSI: {row['rsi_14']:.2f}\n"
            result += f"     ATR: {row['atr_pct']:.2f}%\n"
            result += f"     ADX: {row['adx_14']:.2f}\n"
            
            # 行业概念
            if pd.notna(row['industry_sw1']):
                result += f"   行业: {row['industry_sw1']}\n"
            if pd.notna(row['concept_names']):
                concepts = row['concept_names'].split(',')[:3]  # 显示前3个概念
                result += f"   概念: {', '.join(concepts)}\n"
        
        return result
    
    def save_signals_to_file(self, signals_df, filename=None):
        """保存信号到文件"""
        if filename is None:
            filename = f"buy_signals_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        try:
            signals_df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"✅ 买入信号已保存到 {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存信号失败: {e}")
            return None

def main():
    """主函数"""
    print("🧠 智能策略选择器启动")
    print("=" * 60)
    
    selector = IntelligentStrategySelector()
    
    # 生成买入信号
    signals = selector.generate_buy_signals()
    
    # 格式化并显示结果
    formatted_result = selector.format_buy_signals(signals)
    print(formatted_result)
    
    # 保存到文件
    if not signals.empty:
        filename = selector.save_signals_to_file(signals)
        print(f"\n💾 详细数据已保存到: {filename}")
    
    # 显示配置信息
    print(f"\n⚙️ 当前配置 (基于盈利分析优化):")
    print(f"   CCI阈值: [-30, 100] (基于高盈利均值-9.0)")
    print(f"   ATR阈值: ≥3.5% (基于高盈利均值3.9%)")
    print(f"   ADX阈值: ≥30 (基于高盈利均值31.7)")
    print(f"   综合评分: ≥0.65")
    
    return signals

if __name__ == '__main__':
    main()
