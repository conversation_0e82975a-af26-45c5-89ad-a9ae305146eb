# coding=utf-8
"""
CCI优化后效果分析
深度分析哪些指标组合带来高胜率和高收益
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_cci_optimization_effect():
    """分析CCI优化效果"""
    print('📊 CCI优化效果分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'SELL' 
        AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 获取对应的买入数据
        buy_query = """
        SELECT * FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 4000
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        conn.close()
        
        print(f'📈 卖出记录: {len(df)} 条')
        print(f'📈 买入记录: {len(buy_df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        buy_df['timestamp'] = pd.to_datetime(buy_df['timestamp'])
        
        # 分析最近的表现 (最新500条交易)
        recent_trades = df.head(500)
        older_trades = df.iloc[500:1500] if len(df) > 1500 else df.iloc[500:]
        
        recent_win_rate = (recent_trades['net_profit_pct_sell'] > 0).mean() * 100
        recent_avg_profit = recent_trades['net_profit_pct_sell'].mean()
        
        older_win_rate = (older_trades['net_profit_pct_sell'] > 0).mean() * 100 if len(older_trades) > 0 else 0
        older_avg_profit = older_trades['net_profit_pct_sell'].mean() if len(older_trades) > 0 else 0
        
        print(f'\n📊 CCI优化效果对比:')
        print(f'   最新500条交易 (优化后): 胜率{recent_win_rate:.1f}%, 平均收益{recent_avg_profit:.2f}%')
        print(f'   之前1000条交易 (优化前): 胜率{older_win_rate:.1f}%, 平均收益{older_avg_profit:.2f}%')
        
        win_rate_change = recent_win_rate - older_win_rate
        profit_change = recent_avg_profit - older_avg_profit
        
        print(f'   胜率变化: {win_rate_change:+.1f}%')
        print(f'   收益变化: {profit_change:+.2f}%')
        
        # 评估CCI优化效果
        if win_rate_change >= 3:
            cci_effect = 'EXCELLENT'
            print(f'   🔥 CCI优化效果: 优秀 (胜率提升{win_rate_change:.1f}%)')
        elif win_rate_change >= 1:
            cci_effect = 'GOOD'
            print(f'   ✅ CCI优化效果: 良好 (胜率提升{win_rate_change:.1f}%)')
        elif win_rate_change >= -1:
            cci_effect = 'NEUTRAL'
            print(f'   📊 CCI优化效果: 中性 (胜率变化{win_rate_change:.1f}%)')
        else:
            cci_effect = 'POOR'
            print(f'   ⚠️ CCI优化效果: 不佳 (胜率下降{abs(win_rate_change):.1f}%)')
        
        return recent_trades, buy_df, cci_effect, win_rate_change, profit_change
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None, None, None

def analyze_high_performance_indicators(recent_trades, buy_df):
    """分析高胜率高收益指标"""
    print(f'\n🎯 高胜率高收益指标分析')
    print('=' * 60)
    
    # 匹配买入数据获取因子信息
    matched_data = []
    
    for _, sell_row in recent_trades.iterrows():
        symbol = sell_row['symbol']
        sell_time = sell_row['timestamp']
        
        # 找到对应的买入记录
        symbol_buys = buy_df[buy_df['symbol'] == symbol]
        if len(symbol_buys) > 0:
            symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
            recent_buy = symbol_buys[symbol_buys['timestamp'] < sell_time]
            
            if len(recent_buy) > 0:
                recent_buy = recent_buy.iloc[-1]  # 最近的买入记录
                
                combined_row = {
                    'symbol': symbol,
                    'net_profit_pct_sell': sell_row['net_profit_pct_sell'],
                    'sell_reason': sell_row.get('sell_reason', ''),
                    'cci': recent_buy.get('cci'),
                    'rsi': recent_buy.get('rsi'),
                    'adx': recent_buy.get('adx'),
                    'macd_hist': recent_buy.get('macd_hist'),
                    'atr_pct': recent_buy.get('atr_pct'),
                    'bb_width': recent_buy.get('bb_width'),
                    'bb_position': recent_buy.get('bb_position'),
                    'macd': recent_buy.get('macd'),
                    'overall_score': recent_buy.get('overall_score'),
                }
                matched_data.append(combined_row)
    
    matched_df = pd.DataFrame(matched_data)
    print(f'📊 成功匹配: {len(matched_df)} 条最新交易')
    
    if len(matched_df) == 0:
        print('⚠️ 没有匹配的数据')
        return None
    
    # 分析高胜率交易的特征
    profitable_trades = matched_df[matched_df['net_profit_pct_sell'] > 0]
    losing_trades = matched_df[matched_df['net_profit_pct_sell'] <= 0]
    
    print(f'\n📈 盈利交易 vs 亏损交易对比:')
    print(f'   盈利交易: {len(profitable_trades)} 条')
    print(f'   亏损交易: {len(losing_trades)} 条')
    print(f'   当前胜率: {len(profitable_trades)/len(matched_df)*100:.1f}%')
    
    # 分析各指标在盈利交易中的分布
    indicators = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct', 'bb_width', 'overall_score']
    
    print(f'\n🔍 盈利交易指标特征:')
    print(f'指标        盈利均值    亏损均值    差异      影响力')
    print(f'-' * 60)
    
    indicator_analysis = {}
    
    for indicator in indicators:
        if indicator in matched_df.columns:
            profit_data = profitable_trades.dropna(subset=[indicator])
            loss_data = losing_trades.dropna(subset=[indicator])
            
            if len(profit_data) > 5 and len(loss_data) > 5:
                profit_mean = profit_data[indicator].mean()
                loss_mean = loss_data[indicator].mean()
                difference = profit_mean - loss_mean
                
                # 计算影响力 (标准化差异)
                combined_std = matched_df[indicator].std()
                impact = abs(difference) / combined_std if combined_std > 0 else 0
                
                indicator_analysis[indicator] = {
                    'profit_mean': profit_mean,
                    'loss_mean': loss_mean,
                    'difference': difference,
                    'impact': impact,
                    'profit_samples': len(profit_data),
                    'loss_samples': len(loss_data)
                }
                
                print(f'{indicator:<10} {profit_mean:8.2f} {loss_mean:8.2f} {difference:8.2f} {impact:8.2f}')
    
    return matched_df, indicator_analysis

def identify_optimal_ranges(matched_df, indicator_analysis):
    """识别最优指标范围"""
    print(f'\n🎯 最优指标范围识别')
    print('=' * 60)
    
    # 按影响力排序指标
    sorted_indicators = sorted(
        indicator_analysis.items(),
        key=lambda x: x[1]['impact'],
        reverse=True
    )
    
    optimal_ranges = {}
    
    print(f'指标        最优范围        胜率%   样本数  建议配置')
    print(f'-' * 65)
    
    for indicator, analysis in sorted_indicators[:5]:  # 分析前5个最有影响力的指标
        if indicator not in matched_df.columns:
            continue
        
        data = matched_df.dropna(subset=[indicator])
        if len(data) < 20:
            continue
        
        # 分析不同区间的胜率
        indicator_values = data[indicator]
        percentiles = [0, 25, 50, 75, 100]
        thresholds = [indicator_values.quantile(p/100) for p in percentiles]
        
        best_range = None
        best_win_rate = 0
        
        for i in range(len(thresholds)-1):
            lower = thresholds[i]
            upper = thresholds[i+1]
            
            range_data = data[(data[indicator] >= lower) & (data[indicator] <= upper)]
            
            if len(range_data) >= 10:
                win_rate = (range_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = range_data['net_profit_pct_sell'].mean()
                
                if win_rate > best_win_rate:
                    best_win_rate = win_rate
                    best_range = {
                        'lower': lower,
                        'upper': upper,
                        'win_rate': win_rate,
                        'avg_profit': avg_profit,
                        'sample_count': len(range_data),
                        'percentile_range': f'P{percentiles[i]}-P{percentiles[i+1]}'
                    }
        
        if best_range:
            optimal_ranges[indicator] = best_range
            
            # 生成配置建议
            if indicator == 'cci':
                config_suggestion = f'CCI [{best_range["lower"]:.0f}, {best_range["upper"]:.0f}]'
            elif indicator == 'rsi':
                config_suggestion = f'RSI [{best_range["lower"]:.0f}, {best_range["upper"]:.0f}]'
            elif indicator == 'adx':
                config_suggestion = f'ADX [{best_range["lower"]:.0f}, {best_range["upper"]:.0f}]'
            elif indicator == 'macd_hist':
                config_suggestion = f'MACD > {best_range["lower"]:.3f}'
            elif indicator == 'atr_pct':
                config_suggestion = f'ATR [{best_range["lower"]:.1f}%, {best_range["upper"]:.1f}%]'
            else:
                config_suggestion = f'[{best_range["lower"]:.2f}, {best_range["upper"]:.2f}]'
            
            print(f'{indicator:<10} {best_range["percentile_range"]:<12} {best_range["win_rate"]:6.1f} {best_range["sample_count"]:6d}  {config_suggestion}')
    
    return optimal_ranges

def generate_next_optimization_plan(cci_effect, optimal_ranges, win_rate_change):
    """生成下一步优化计划"""
    print(f'\n🚀 下一步优化计划')
    print('=' * 60)
    
    if cci_effect == 'EXCELLENT':
        print(f'🔥 CCI优化非常成功! 立即执行第二阶段优化')
        next_action = 'PROCEED_MACD'
    elif cci_effect == 'GOOD':
        print(f'✅ CCI优化效果良好! 可以继续下一步优化')
        next_action = 'PROCEED_MACD'
    elif cci_effect == 'NEUTRAL':
        print(f'📊 CCI优化效果中性，谨慎推进')
        next_action = 'CAREFUL_PROCEED'
    else:
        print(f'⚠️ CCI优化效果不佳，需要调整策略')
        next_action = 'REASSESS'
    
    # 基于最优范围分析生成具体建议
    if optimal_ranges:
        print(f'\n📊 基于最新数据的优化建议:')
        
        # 按影响力排序
        sorted_ranges = sorted(
            optimal_ranges.items(),
            key=lambda x: x[1]['win_rate'],
            reverse=True
        )
        
        for i, (indicator, range_info) in enumerate(sorted_ranges[:3], 1):
            print(f'   {i}. {indicator.upper()}优化:')
            print(f'      最优范围: {range_info["percentile_range"]} (胜率{range_info["win_rate"]:.1f}%)')
            print(f'      样本数: {range_info["sample_count"]}')
            
            if indicator == 'macd_hist' and next_action in ['PROCEED_MACD', 'CAREFUL_PROCEED']:
                print(f'      🎯 建议立即执行: MACD > {range_info["lower"]:.3f}')
    
    return next_action, optimal_ranges

def execute_next_optimization(next_action, optimal_ranges):
    """执行下一步优化"""
    print(f'\n⚙️ 执行下一步优化')
    print('=' * 50)
    
    if next_action == 'PROCEED_MACD':
        # 查找MACD的最优配置
        if 'macd_hist' in optimal_ranges:
            macd_range = optimal_ranges['macd_hist']
            suggested_threshold = macd_range['lower']
            
            print(f'🔧 执行MACD柱优化:')
            print(f'   当前配置: MACD > 0')
            print(f'   建议配置: MACD > {suggested_threshold:.3f}')
            print(f'   数据支撑: 胜率{macd_range["win_rate"]:.1f}%, 样本{macd_range["sample_count"]}条')
            
            return 'macd_hist', suggested_threshold
        else:
            print(f'📊 MACD数据不足，考虑其他指标优化')
            return None, None
    
    elif next_action == 'CAREFUL_PROCEED':
        print(f'📊 谨慎推进策略:')
        print(f'   1. 延长CCI优化验证时间')
        print(f'   2. 小幅度调整其他指标')
        print(f'   3. 密切监控效果')
        return None, None
    
    else:  # REASSESS
        print(f'🔄 重新评估策略:')
        print(f'   1. 分析CCI优化失败原因')
        print(f'   2. 考虑回退CCI配置')
        print(f'   3. 重新制定优化方案')
        return None, None

def main():
    """主函数"""
    print('🚀 CCI优化后深度分析')
    print('=' * 60)
    
    print('🎯 目标: 分析CCI优化效果，识别高胜率指标，制定下一步计划')
    
    # 分析CCI优化效果
    result = analyze_cci_optimization_effect()
    
    if result[0] is not None:
        recent_trades, buy_df, cci_effect, win_rate_change, profit_change = result
        
        # 分析高胜率指标
        analysis_result = analyze_high_performance_indicators(recent_trades, buy_df)
        
        if analysis_result and analysis_result[0] is not None:
            matched_df, indicator_analysis = analysis_result
            
            # 识别最优范围
            optimal_ranges = identify_optimal_ranges(matched_df, indicator_analysis)
            
            # 生成下一步计划
            next_action, optimal_ranges = generate_next_optimization_plan(cci_effect, optimal_ranges, win_rate_change)
            
            # 执行下一步优化
            optimization_result = execute_next_optimization(next_action, optimal_ranges)
            
            print(f'\n🎯 分析总结')
            print('=' * 40)
            print(f'📊 CCI优化效果: {cci_effect}')
            print(f'📈 胜率变化: {win_rate_change:+.1f}%')
            print(f'💰 收益变化: {profit_change:+.2f}%')
            print(f'🔍 发现 {len(optimal_ranges)} 个优化机会')
            
            if optimization_result[0]:
                print(f'🚀 下一步: 优化{optimization_result[0]}因子')
                return optimization_result
            else:
                print(f'📊 建议: 根据分析结果调整策略')
                return None, None
        
        else:
            print('❌ 指标分析失败')
            return None, None
    
    else:
        print('❌ 数据分析失败，请检查数据库')
        return None, None

if __name__ == '__main__':
    result = main()
    if result and result[0]:
        print(f'\n💡 建议立即执行: {result[0]}优化，阈值{result[1]:.3f}')
