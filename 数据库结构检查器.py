# coding=utf-8
"""
数据库结构检查器
检查数据库中的表结构和数据内容
"""

import sqlite3
import pandas as pd

def check_database_structure():
    """检查数据库结构"""
    print('🔍 数据库结构检查器')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 1. 检查所有表
        tables_query = "SELECT name FROM sqlite_master WHERE type='table';"
        tables = pd.read_sql_query(tables_query, conn)
        print(f'📊 数据库中的表: {list(tables["name"])}')
        
        # 2. 检查trades表结构
        if 'trades' in tables['name'].values:
            print(f'\n📋 trades表结构:')
            
            # 获取表结构
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            
            for _, row in structure.iterrows():
                print(f'  {row["name"]:20} {row["type"]:10} {"NOT NULL" if row["notnull"] else "NULL":8}')
            
            # 3. 检查数据样本
            print(f'\n📈 trades表数据样本 (前5条):')
            sample_query = "SELECT * FROM trades LIMIT 5;"
            sample = pd.read_sql_query(sample_query, conn)
            
            print(sample.to_string())
            
            # 4. 检查数据统计
            print(f'\n📊 trades表统计信息:')
            count_query = "SELECT COUNT(*) as total_records FROM trades;"
            count = pd.read_sql_query(count_query, conn)
            print(f'  总记录数: {count["total_records"].iloc[0]}')
            
            # 检查买入记录
            buy_query = "SELECT COUNT(*) as buy_records FROM trades WHERE action = 'BUY';"
            buy_count = pd.read_sql_query(buy_query, conn)
            print(f'  买入记录: {buy_count["buy_records"].iloc[0]}')
            
            # 检查卖出记录
            sell_query = "SELECT COUNT(*) as sell_records FROM trades WHERE action = 'SELL';"
            sell_count = pd.read_sql_query(sell_query, conn)
            print(f'  卖出记录: {sell_count["sell_records"].iloc[0]}')
            
            # 5. 检查关键字段
            print(f'\n🔍 关键字段检查:')
            
            key_fields = ['profit_pct', 'is_win', 'enhanced_overall_score', 'buy_confidence_score', 
                         'rsi', 'macd', 'ma_3', 'ma_7', 'ma_20']
            
            for field in key_fields:
                try:
                    field_query = f"SELECT COUNT({field}) as count FROM trades WHERE {field} IS NOT NULL;"
                    field_count = pd.read_sql_query(field_query, conn)
                    print(f'  {field:25} 有效数据: {field_count["count"].iloc[0]}')
                except:
                    print(f'  {field:25} 字段不存在')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

if __name__ == "__main__":
    check_database_structure()
