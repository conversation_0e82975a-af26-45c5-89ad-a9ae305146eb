# coding=utf-8
"""
分析现有数据库数据
基于实际可用字段优化策略
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def check_database_structure():
    """检查数据库结构"""
    print('🔍 检查数据库结构')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute('PRAGMA table_info(trades)')
        columns = cursor.fetchall()
        
        print(f'📊 数据库字段:')
        available_columns = []
        for col in columns:
            print(f'   {col[1]} ({col[2]})')
            available_columns.append(col[1])
        
        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
        buy_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY' AND net_profit_pct_sell IS NOT NULL")
        completed_count = cursor.fetchone()[0]
        
        print(f'\n📈 数据统计:')
        print(f'   买入记录总数: {buy_count}')
        print(f'   已完成交易: {completed_count}')
        
        conn.close()
        return available_columns, buy_count, completed_count
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return [], 0, 0

def analyze_available_data(available_columns):
    """分析可用数据"""
    print(f'\n📊 基于可用字段的数据分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 构建查询，只使用存在的字段
        base_fields = ['timestamp', 'symbol', 'action']
        score_fields = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                       'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
        tech_fields = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
        result_fields = ['net_profit_pct_sell', 'holding_hours']
        
        # 筛选存在的字段
        query_fields = []
        for field in base_fields + score_fields + tech_fields + result_fields:
            if field in available_columns:
                query_fields.append(field)
        
        print(f'📋 可用于分析的字段: {len(query_fields)} 个')
        for field in query_fields:
            print(f'   ✅ {field}')
        
        # 构建查询
        query = f"""
        SELECT {', '.join(query_fields)}
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'\n📈 数据加载完成: {len(df)} 条买入记录')
        
        return df, query_fields
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, []

def analyze_performance_by_factors(df, available_fields):
    """基于可用因子分析表现"""
    print(f'\n🎯 基于可用因子的表现分析')
    print('=' * 50)
    
    if 'net_profit_pct_sell' not in available_fields:
        print('⚠️ 缺少收益数据，无法分析表现')
        return
    
    # 过滤已完成的交易
    completed_trades = df.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    print(f'📊 已完成交易: {len(completed_trades)} 条')
    
    # 计算总体胜率
    wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    total = len(completed_trades)
    win_rate = wins / total * 100
    
    avg_profit = completed_trades['net_profit_pct_sell'].mean()
    avg_win = completed_trades[completed_trades['net_profit_pct_sell'] > 0]['net_profit_pct_sell'].mean()
    avg_loss = abs(completed_trades[completed_trades['net_profit_pct_sell'] <= 0]['net_profit_pct_sell'].mean())
    
    print(f'📈 总体表现:')
    print(f'   胜率: {win_rate:.1f}% ({wins}/{total})')
    print(f'   平均收益: {avg_profit:.2f}%')
    print(f'   平均盈利: {avg_win:.2f}%')
    print(f'   平均亏损: {avg_loss:.2f}%')
    print(f'   盈亏比: {avg_win/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
    
    return completed_trades

def analyze_factor_effectiveness(completed_trades, available_fields):
    """分析因子有效性"""
    print(f'\n🔬 因子有效性分析')
    print('=' * 40)
    
    # 分析技术指标与收益的关系
    tech_fields = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    score_fields = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                   'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    # 分离盈利和亏损交易
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
    
    print(f'📊 盈利 vs 亏损交易对比:')
    print(f'   盈利交易: {len(winning_trades)} 条')
    print(f'   亏损交易: {len(losing_trades)} 条')
    
    # 分析技术指标
    print(f'\n📋 技术指标对比:')
    for field in tech_fields:
        if field in available_fields and field in completed_trades.columns:
            win_values = winning_trades[field].dropna()
            loss_values = losing_trades[field].dropna()
            
            if len(win_values) > 0 and len(loss_values) > 0:
                win_avg = win_values.mean()
                loss_avg = loss_values.mean()
                diff = win_avg - loss_avg
                
                # 计算显著性（简单的t检验概念）
                significance = "显著" if abs(diff) > win_values.std() * 0.5 else "一般"
                
                print(f'   {field}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} (差值: {diff:+.3f}) [{significance}]')
    
    # 分析评分指标
    print(f'\n📊 评分指标对比:')
    for field in score_fields:
        if field in available_fields and field in completed_trades.columns:
            win_values = winning_trades[field].dropna()
            loss_values = losing_trades[field].dropna()
            
            if len(win_values) > 0 and len(loss_values) > 0:
                win_avg = win_values.mean()
                loss_avg = loss_values.mean()
                diff = win_avg - loss_avg
                
                significance = "显著" if abs(diff) > win_values.std() * 0.3 else "一般"
                
                print(f'   {field}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} (差值: {diff:+.3f}) [{significance}]')

def find_optimal_ranges(completed_trades, available_fields):
    """寻找最优范围"""
    print(f'\n🎯 最优范围分析')
    print('=' * 40)
    
    if len(completed_trades) < 20:
        print('⚠️ 样本数量不足，无法进行可靠的范围分析')
        return
    
    # 分析关键指标的最优范围
    key_fields = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    
    optimal_ranges = {}
    
    for field in key_fields:
        if field in available_fields and field in completed_trades.columns:
            values = completed_trades[field].dropna()
            profits = completed_trades.loc[values.index, 'net_profit_pct_sell']
            
            if len(values) < 10:
                continue
            
            # 分析不同范围的胜率
            print(f'\n📊 {field} 范围分析:')
            
            # 按四分位数分组
            q25 = values.quantile(0.25)
            q50 = values.quantile(0.50)
            q75 = values.quantile(0.75)
            
            ranges = [
                (f'低值 (<{q25:.2f})', values < q25),
                (f'中低值 ({q25:.2f}-{q50:.2f})', (values >= q25) & (values < q50)),
                (f'中高值 ({q50:.2f}-{q75:.2f})', (values >= q50) & (values < q75)),
                (f'高值 (>{q75:.2f})', values >= q75)
            ]
            
            best_range = None
            best_win_rate = 0
            
            for range_name, condition in ranges:
                if condition.sum() >= 3:  # 至少3个样本
                    range_profits = profits[condition]
                    win_rate = (range_profits > 0).mean() * 100
                    avg_profit = range_profits.mean()
                    sample_size = len(range_profits)
                    
                    print(f'   {range_name}: 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%, 样本{sample_size}')
                    
                    if win_rate > best_win_rate and sample_size >= 5:
                        best_win_rate = win_rate
                        best_range = range_name
            
            if best_range:
                optimal_ranges[field] = {
                    'range': best_range,
                    'win_rate': best_win_rate
                }
                print(f'   ✅ 最优范围: {best_range} (胜率: {best_win_rate:.1f}%)')
    
    return optimal_ranges

def generate_optimization_suggestions(completed_trades, available_fields, optimal_ranges):
    """生成优化建议"""
    print(f'\n💡 基于实际数据的优化建议')
    print('=' * 50)
    
    # 计算当前胜率
    current_win_rate = (completed_trades['net_profit_pct_sell'] > 0).mean() * 100
    
    print(f'📊 当前表现:')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    print(f'   样本数量: {len(completed_trades)}')
    
    # 基于最优范围生成过滤建议
    if optimal_ranges:
        print(f'\n🎯 过滤条件建议:')
        for field, data in optimal_ranges.items():
            print(f'   {field}: 优先选择 {data["range"]} (胜率: {data["win_rate"]:.1f}%)')
    
    # 分析盈利交易的共同特征
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    
    if len(winning_trades) >= 5:
        print(f'\n🏆 盈利交易特征 (基于{len(winning_trades)}个样本):')
        
        key_fields = ['atr_pct', 'bb_width', 'macd_hist', 'rsi']
        for field in key_fields:
            if field in available_fields and field in winning_trades.columns:
                values = winning_trades[field].dropna()
                if len(values) >= 3:
                    avg_val = values.mean()
                    median_val = values.median()
                    std_val = values.std()
                    
                    # 建议范围：均值 ± 0.5个标准差
                    lower_bound = max(0, avg_val - 0.5 * std_val)
                    upper_bound = avg_val + 0.5 * std_val
                    
                    print(f'   {field}: 平均{avg_val:.3f}, 建议范围 {lower_bound:.3f}-{upper_bound:.3f}')
    
    # 生成配置建议
    print(f'\n⚙️ 配置优化建议:')
    
    if current_win_rate < 30:
        print(f'   📉 当前胜率较低，建议:')
        print(f'     1. 放宽多因子阈值，增加买入机会')
        print(f'     2. 重点关注表现最好的技术指标')
        print(f'     3. 考虑增加止损和止盈条件')
    elif current_win_rate < 40:
        print(f'   📊 当前胜率中等，建议:')
        print(f'     1. 微调阈值，平衡信号数量和质量')
        print(f'     2. 优化持仓时间管理')
        print(f'     3. 加强风险控制')
    else:
        print(f'   📈 当前胜率较高，建议:')
        print(f'     1. 保持当前策略稳定性')
        print(f'     2. 适当提高阈值，追求更高质量信号')
        print(f'     3. 考虑增加仓位或扩大策略覆盖')

def main():
    """主函数"""
    print('🚀 基于现有数据的策略优化分析')
    print('=' * 60)
    
    # 检查数据库结构
    available_columns, buy_count, completed_count = check_database_structure()
    
    if buy_count == 0:
        print('❌ 没有买入记录，无法进行分析')
        return
    
    # 分析可用数据
    df, available_fields = analyze_available_data(available_columns)
    
    if df is not None and len(df) > 0:
        # 分析表现
        completed_trades = analyze_performance_by_factors(df, available_fields)
        
        if completed_trades is not None and len(completed_trades) > 0:
            # 分析因子有效性
            analyze_factor_effectiveness(completed_trades, available_fields)
            
            # 寻找最优范围
            optimal_ranges = find_optimal_ranges(completed_trades, available_fields)
            
            # 生成优化建议
            generate_optimization_suggestions(completed_trades, available_fields, optimal_ranges)
            
            print(f'\n🎯 总结')
            print('=' * 30)
            print('✅ 基于实际数据的分析完成')
            print('📊 已识别关键因子的有效性')
            print('🎯 已生成针对性的优化建议')
            print('')
            print('🚀 建议根据分析结果调整策略参数')
        else:
            print('⚠️ 没有已完成的交易数据')
    else:
        print('❌ 数据加载失败')

if __name__ == '__main__':
    main()
