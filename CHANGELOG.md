# 通达信买入信号增强 - CHANGELOG

## 2025-07-23

### 简化策略，只保留TRIX预筛选和反转买入逻辑

- 禁用智能评分系统和多因子系统
  - 修改`init`函数，禁用`INTELLIGENT_SYSTEM_AVAILABLE`全局变量
  - 修改`buy_strategy`函数，移除智能评分系统相关代码
  - 添加新的简化版`analyze_single_symbol_simple`函数，只检查TRIX反转信号
  - 简化`buy_strategy_main_logic`函数，只保留TRIX预筛选和反转逻辑

- 策略逻辑简化为两步：
  1. TRIX预筛选：筛选昨日TRIX < 前日TRIX的股票（寻找下降趋势）
  2. TRIX反转确认：检查当日TRIX > 昨日TRIX（确认反转向上）
  
- 优化原因：
  - 专注于TRIX指标的核心买入逻辑，提高策略清晰度
  - 减少复杂度，便于理解和维护
  - 降低计算资源消耗，提高策略运行效率

### 禁用周末和月末买入逻辑

- 移除了每周和每月最后交易日的买入检查功能
  - 禁用了`weekly_buy_strategy`和`monthly_buy_strategy`函数
  - 修改`setup_buy_check_schedules`函数，强制设置`enable_weekly_check`和`enable_monthly_check`为`False`
  - 保留但禁用了`weekly_last_day_check`和`monthly_last_day_check`函数
  - 简化了策略逻辑，专注于日内交易信号

- 优化原因：
  - 减少策略复杂度，提高系统稳定性
  - 避免周期性买入与日常买入策略的潜在冲突
  - 专注于优化核心日内交易逻辑

## 2025-07-16

### 修复历史数据获取和处理问题

- 修复了`signal_generator.py`中`history`函数调用的参数错误
  - 将`count`参数改为使用`start_time`和`end_time`参数
  - 解决了"history() got an unexpected keyword argument 'count'"错误
  - 确保在获取周线和月线数据时能够正确获取历史数据

- 修复了`_convert_to_weekly`和`_convert_to_monthly`函数的数据类型处理问题
  - 增加了对列表类型历史数据的支持，自动转换为DataFrame
  - 解决了"'list' object has no attribute 'empty'"错误
  - 增强了日志输出，提供更详细的转换过程信息
  - 完善了错误处理机制，确保数据转换的稳定性

- 修复了掘金量化API返回的数据缺少'bob'列的问题
  - 增加了自动创建时间列的逻辑，支持从索引或当前时间创建
  - 增加了使用'eob'列替代'bob'列的功能
  - 改进了数据排序逻辑，支持多种情况下的数据排序
  - 解决了"KeyError: 'bob'"错误，提高了代码的鲁棒性

### 增加每周和每月最后交易日买入检查功能

- 在`main.py`中添加了每周和每月最后交易日的买入检查功能
  - 新增`is_last_trading_day_of_period`函数用于判断当前日期是否为每周或每月的最后一个交易日
  - 新增`weekly_last_day_check`和`monthly_last_day_check`函数用于设置每周和每月最后交易日的买入检查
  - 新增`weekly_buy_strategy`和`monthly_buy_strategy`函数用于执行周线和月线买入策略
  - 在每周和每月的最后交易日，会根据配置的时间点进行额外的买入检查

- 在`signal_generator.py`中增加了对强制周期设置的支持
  - 修改`calculate_tdx_buy_signal`函数，支持从context中获取强制周期设置
  - 改进了周期数据的获取和转换逻辑，提供更详细的日志信息

- 在`config.py`中增加了相关配置参数
  - `ENABLE_WEEKLY_LAST_DAY_CHECK`: 是否在每周最后一个交易日进行周线买入检查
  - `ENABLE_MONTHLY_LAST_DAY_CHECK`: 是否在每月最后一个交易日进行月线买入检查
  - `PERIODIC_CHECK_TIMES`: 周期性买入检查的时间点

### 使用说明

1. 周期性买入检查功能：
   - 设置`ENABLE_WEEKLY_LAST_DAY_CHECK = True`启用每周最后交易日买入检查
   - 设置`ENABLE_MONTHLY_LAST_DAY_CHECK = True`启用每月最后交易日买入检查
   - 通过`PERIODIC_CHECK_TIMES`配置在最后交易日的具体检查时间点

2. 周期性买入检查的优势：
   - 可以在周期结束时进行更准确的周线和月线信号判断
   - 避免在周期中间使用不完整的K线数据做出错误判断
   - 能够捕捉到周期转换点的重要买入机会

3. 注意事项：
   - 周期性买入检查与常规的日内买入检查可以同时启用
   - 系统会自动识别每周和每月的最后交易日，排除周末和节假日
   - 在最后交易日的检查中，会强制使用对应的周期（周线或月线）

## 2025-07-15

### 增加多周期通达信买入信号

- 在`signal_generator.py`中扩展了通达信买入信号计算逻辑，支持日线、周线和月线周期
  - 修改`calculate_tdx_buy_signal`函数，支持根据配置使用不同周期的历史数据
  - 增加对不同周期历史数据的获取和处理逻辑
  - 支持多周期信号组合策略，可设置为任一周期触发、全部周期触发或指定周期触发

- 在`config.py`中增加了多周期通达信买入信号的详细配置参数
  - `TDX_BUY_FREQUENCY`: 控制使用的周期，可选值为'day', 'week', 'month'
  - `TDX_CHECK_MULTIPLE_FREQUENCIES`: 是否检查多个周期的信号
  - `TDX_SIGNAL_COMBINE_MODE`: 多周期信号组合模式，可选'any', 'all', 'specific'
  - `TDX_SPECIFIC_FREQUENCY`: 在'specific'模式下使用的特定周期
  - `HISTORY_DATA_DAYS_WEEKLY`: 获取周线数据的历史天数，默认400天
  - `HISTORY_DATA_DAYS_MONTHLY`: 获取月线数据的历史天数，默认1200天

- 修复了周线和月线数据获取的问题
  - 改进了周期数据处理方法，使用日线数据自行转换为周线和月线
  - 增加了_convert_to_weekly和_convert_to_monthly函数实现周期转换
  - 增加了对不同版本pandas的兼容性处理
  - 增强了错误处理机制，提供更详细的错误日志
  - 解决了"无效 frequency 值"的错误问题
  - 确保不同周期的历史数据能够正确获取和处理

- 优化了日志输出，显示不同周期的计算结果和触发信号
  - 在调试模式下，输出每个周期的中间计算结果
  - 记录信号触发的具体周期信息
  - 增强了异常处理，提供更详细的错误日志

### 使用说明

1. 基本使用：
   - 设置`TDX_BUY_FREQUENCY = 'day'/'week'/'month'`选择使用单一周期
   - 单一周期模式下，系统只会计算指定周期的买入信号

2. 多周期组合使用：
   - 设置`TDX_CHECK_MULTIPLE_FREQUENCIES = True`启用多周期检查
   - 通过`TDX_SIGNAL_COMBINE_MODE`设置信号组合策略：
     * `'any'`: 任一周期出现买入信号即触发(最宽松)
     * `'all'`: 所有周期都出现买入信号才触发(最严格)
     * `'specific'`: 只使用`TDX_SPECIFIC_FREQUENCY`指定的特定周期

3. 周期选择建议：
   - 日线周期：适合短期交易，信号较为频繁
   - 周线周期：适合中期趋势跟踪，信号稳定性较好
   - 月线周期：适合长期趋势跟踪，信号最稳定但次数最少
   - 多周期结合：可以减少假信号，提高交易成功率

4. 调试方法：
   - 设置`TDX_DEBUG_MODE = True`查看详细计算过程
   - 多周期模式下，日志会显示每个周期的计算结果和最终的触发周期

## 2025-07-11

### 添加功能

- 在`signal_generator.py`中实现了完整的通达信买入信号计算逻辑
  - 增加了`calculate_tdx_buy_signal`函数，完整实现通达信公式买入信号计算
  - 信号计算基于通达信公式：`BUY(ZJ=REF(ZJ,1) AND REF(ZJ,1)<REF(ZJ,2) AND R1>45, C)`
  - 信号判断逻辑更为精确，使用小阈值处理浮点数比较
  - 增加了数据缺失和有效性检查，确保计算稳定性

- 在`config.py`中增加了通达信买入信号的详细配置参数
  - `ENABLE_TDX_BUY_SIGNAL`: 控制是否启用通达信买入信号
  - `TDX_RSI_THRESHOLD`: 控制RSI阈值，默认为45
  - `TDX_L1_PERIOD`: 控制L1均线周期，默认为16
  - `TDX_MA1_PERIOD`: 控制MA1周期，默认为3
  - `TDX_J1_PERIOD`: 控制J1周期，默认为16
  - `TDX_ZJ_PERIOD`: 控制ZJ周期，默认为15
  - `TDX_RSI_PERIOD`: 控制RSI计算周期，默认为14
  - `TDX_DEBUG_MODE`: 控制是否启用调试模式，默认为True

- 增加了调试模式功能
  - 在调试模式下，输出每个计算步骤的中间结果
  - 详细记录ZJ值和条件判断结果，便于排查问题
  - 可通过配置文件开关控制是否启用

### 改进

- 完善了买入信号计算流程
  - 信号计算集成到了`analyze_signals`函数中
  - 将通达信买入信号结果包含在综合买入信号判断中
  - 添加了详细的信号计算日志

- 增强了信号数据存储
  - 在信号信息中包含了详细的通达信买入信号数据
  - 在分析数据中添加了通达信买入信号相关字段
  - 便于后续对交易历史进行分析

### 使用说明

1. 通过设置`config.py`中的`ENABLE_TDX_BUY_SIGNAL = True`启用通达信买入信号
2. 可通过调整`TDX_RSI_THRESHOLD`参数控制买入信号的严格程度
3. 在调试期间，建议将`TDX_DEBUG_MODE`设为`True`，以便查看详细计算过程

### 技术细节

通达信买入信号是基于价格触底企稳形态和RSI指标的组合判断：
- 价格连续下跌后开始企稳(ZJ=REF(ZJ,1)且前一日ZJ小于前前日ZJ)
- 同时RSI指标已经回升到有一定上涨动能的区域(RSI>45)
- 这种判断方式能有效捕捉到下跌趋势结束后的反转信号 