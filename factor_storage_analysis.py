# coding=utf-8
"""
因子存储和分析能力检查
检查策略是否存储了190+因子信息，以及如何利用这些因子进行盈利分析
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def check_database_structure():
    """检查数据库结构和存储的因子信息"""
    print('🔍 数据库结构和因子存储检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        
        print(f'📊 trades表结构分析:')
        print(f'   总列数: {len(columns)}')
        
        # 分类统计列
        basic_columns = []
        factor_columns = []
        technical_columns = []
        score_columns = []
        other_columns = []
        
        for col in columns:
            col_name = col[1]  # 列名
            col_type = col[2]  # 数据类型
            
            if col_name in ['id', 'timestamp', 'symbol', 'action', 'price', 'quantity', 'sell_reason', 'net_profit_pct_sell', 'holding_hours']:
                basic_columns.append(col_name)
            elif 'score' in col_name.lower():
                score_columns.append(col_name)
            elif col_name in ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy', 'adx', 'cci', 'williams_r', 'stoch_k', 'stoch_d']:
                technical_columns.append(col_name)
            elif col_name.startswith('factor_') or 'factor' in col_name.lower():
                factor_columns.append(col_name)
            else:
                other_columns.append(col_name)
        
        print(f'\n📋 列分类统计:')
        print(f'   基础信息列: {len(basic_columns)}个')
        print(f'     {basic_columns}')
        print(f'   评分列: {len(score_columns)}个')
        print(f'     {score_columns}')
        print(f'   技术指标列: {len(technical_columns)}个')
        print(f'     {technical_columns}')
        print(f'   因子列: {len(factor_columns)}个')
        print(f'     {factor_columns}')
        print(f'   其他列: {len(other_columns)}个')
        print(f'     {other_columns}')
        
        # 检查是否有扩展因子表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f'\n📊 数据库表结构:')
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA table_info({table_name})")
            table_columns = cursor.fetchall()
            print(f'   {table_name}: {len(table_columns)}列')
        
        conn.close()
        
        return columns, len(columns)
        
    except Exception as e:
        print(f'❌ 数据库检查失败: {e}')
        return None, 0

def check_factor_data_availability():
    """检查因子数据的可用性"""
    print(f'\n📊 因子数据可用性检查')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最近的买入记录样本
        query = """
        SELECT * FROM trades 
        WHERE action = 'BUY' 
        ORDER BY timestamp DESC 
        LIMIT 100
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print('⚠️ 没有买入记录')
            return None
        
        print(f'📈 分析样本: {len(df)} 条最新买入记录')
        
        # 分析各列的数据完整性
        print(f'\n🔍 因子数据完整性分析:')
        
        factor_completeness = {}
        
        for col in df.columns:
            if col not in ['id', 'timestamp', 'symbol', 'action']:
                non_null_count = df[col].notna().sum()
                completeness = non_null_count / len(df) * 100
                factor_completeness[col] = {
                    'non_null_count': non_null_count,
                    'completeness': completeness,
                    'unique_values': df[col].nunique(),
                    'data_type': str(df[col].dtype)
                }
        
        # 按完整性排序
        sorted_factors = sorted(factor_completeness.items(), 
                               key=lambda x: x[1]['completeness'], 
                               reverse=True)
        
        print(f'   因子完整性排序 (前20个):')
        for i, (factor, stats) in enumerate(sorted_factors[:20], 1):
            completeness = stats['completeness']
            unique_vals = stats['unique_values']
            status = "✅" if completeness > 90 else "⚠️" if completeness > 50 else "❌"
            
            print(f'   {i:2d}. {factor}: {completeness:.1f}% ({unique_vals}个唯一值) {status}')
        
        # 统计高质量因子
        high_quality_factors = [f for f, s in factor_completeness.items() if s['completeness'] > 90]
        medium_quality_factors = [f for f, s in factor_completeness.items() if 50 < s['completeness'] <= 90]
        low_quality_factors = [f for f, s in factor_completeness.items() if s['completeness'] <= 50]
        
        print(f'\n📊 因子质量统计:')
        print(f'   高质量因子 (>90%): {len(high_quality_factors)}个')
        print(f'   中等质量因子 (50-90%): {len(medium_quality_factors)}个')
        print(f'   低质量因子 (<50%): {len(low_quality_factors)}个')
        
        return df, factor_completeness, high_quality_factors
        
    except Exception as e:
        print(f'❌ 因子数据检查失败: {e}')
        return None, None, None

def analyze_factor_predictive_power(df, factor_completeness):
    """分析因子的预测能力"""
    print(f'\n🎯 因子预测能力分析')
    print('=' * 50)
    
    if df is None:
        print('⚠️ 无数据可分析')
        return
    
    try:
        # 获取对应的卖出记录进行匹配
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配数据
        query = """
        SELECT 
            b.*,
            s.net_profit_pct_sell,
            s.sell_reason,
            s.holding_hours
        FROM trades b
        LEFT JOIN trades s ON b.symbol = s.symbol 
        WHERE b.action = 'BUY' 
        AND s.action = 'SELL'
        AND s.net_profit_pct_sell IS NOT NULL
        AND b.timestamp < s.timestamp
        ORDER BY b.timestamp DESC
        LIMIT 1000
        """
        
        matched_df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(matched_df) == 0:
            print('⚠️ 没有匹配的买入-卖出记录')
            return
        
        print(f'📈 成功匹配: {len(matched_df)} 条买入-卖出记录')
        
        # 创建盈利标识
        matched_df['is_profitable'] = matched_df['net_profit_pct_sell'] > 0
        
        profitable_count = matched_df['is_profitable'].sum()
        total_count = len(matched_df)
        win_rate = profitable_count / total_count * 100
        
        print(f'   盈利交易: {profitable_count}/{total_count} ({win_rate:.1f}%)')
        
        # 分析高质量因子的预测能力
        high_quality_factors = [f for f, s in factor_completeness.items() 
                               if s['completeness'] > 90 and f in matched_df.columns]
        
        print(f'\n🔍 高质量因子预测能力分析 (前15个):')
        
        factor_ic_results = []
        
        for factor in high_quality_factors:
            if factor in matched_df.columns and factor not in ['id', 'timestamp', 'symbol', 'action', 'price', 'quantity']:
                factor_values = matched_df[factor].dropna()
                profit_values = matched_df.loc[matched_df[factor].notna(), 'net_profit_pct_sell']
                
                if len(factor_values) > 20:
                    # 计算信息系数 (IC)
                    correlation = np.corrcoef(factor_values, profit_values)[0, 1]
                    
                    # 计算分组胜率差异
                    high_factor = matched_df[matched_df[factor] >= factor_values.quantile(0.75)]
                    low_factor = matched_df[matched_df[factor] <= factor_values.quantile(0.25)]
                    
                    if len(high_factor) > 5 and len(low_factor) > 5:
                        high_win_rate = (high_factor['is_profitable']).mean() * 100
                        low_win_rate = (low_factor['is_profitable']).mean() * 100
                        win_rate_diff = high_win_rate - low_win_rate
                        
                        factor_ic_results.append({
                            'factor': factor,
                            'ic': correlation,
                            'ic_abs': abs(correlation),
                            'win_rate_diff': win_rate_diff,
                            'high_win_rate': high_win_rate,
                            'low_win_rate': low_win_rate,
                            'sample_size': len(factor_values)
                        })
        
        # 按IC绝对值排序
        factor_ic_results.sort(key=lambda x: x['ic_abs'], reverse=True)
        
        print(f'   排名  因子名称                    IC值     胜率差异  高分组胜率  低分组胜率')
        print(f'   ' + '-' * 75)
        
        for i, result in enumerate(factor_ic_results[:15], 1):
            ic_str = f"{result['ic']:+.3f}"
            diff_str = f"{result['win_rate_diff']:+.1f}%"
            high_str = f"{result['high_win_rate']:.1f}%"
            low_str = f"{result['low_win_rate']:.1f}%"
            
            effectiveness = "🔥" if result['ic_abs'] > 0.05 else "📊" if result['ic_abs'] > 0.02 else "🔹"
            
            print(f'   {i:2d}.  {result["factor"]:<25} {ic_str:>8} {diff_str:>9} {high_str:>10} {low_str:>10} {effectiveness}')
        
        # 识别最有效的因子
        top_factors = factor_ic_results[:10]
        
        print(f'\n🏆 最有效的10个因子:')
        for i, result in enumerate(top_factors, 1):
            print(f'   {i}. {result["factor"]}: IC={result["ic"]:+.3f}, 胜率差异={result["win_rate_diff"]:+.1f}%')
        
        return factor_ic_results, top_factors
        
    except Exception as e:
        print(f'❌ 预测能力分析失败: {e}')
        return None, None

def check_enhanced_factor_engine():
    """检查增强因子引擎的能力"""
    print(f'\n🔧 增强因子引擎能力检查')
    print('=' * 50)
    
    try:
        # 检查增强因子引擎文件
        import os
        
        factor_files = [
            'enhanced_factor_engine.py',
            'factor_engine.py', 
            'technical_indicators.py',
            'fundamental_factors.py'
        ]
        
        existing_files = []
        for file in factor_files:
            if os.path.exists(file):
                existing_files.append(file)
        
        print(f'📁 因子引擎文件检查:')
        for file in existing_files:
            file_size = os.path.getsize(file)
            print(f'   ✅ {file}: {file_size:,} 字节')
        
        # 尝试导入增强因子引擎
        try:
            from enhanced_factor_engine import EnhancedFactorEngine
            print(f'\n🚀 增强因子引擎导入成功')
            
            # 检查因子引擎的方法
            engine = EnhancedFactorEngine()
            methods = [method for method in dir(engine) if not method.startswith('_')]
            
            print(f'   可用方法: {len(methods)}个')
            
            # 分类方法
            calculation_methods = [m for m in methods if 'calculate' in m.lower()]
            analysis_methods = [m for m in methods if 'analyze' in m.lower() or 'score' in m.lower()]
            
            print(f'   计算方法: {len(calculation_methods)}个')
            for method in calculation_methods[:10]:  # 显示前10个
                print(f'     - {method}')
            
            print(f'   分析方法: {len(analysis_methods)}个')
            for method in analysis_methods[:10]:  # 显示前10个
                print(f'     - {method}')
                
        except ImportError as e:
            print(f'⚠️ 增强因子引擎导入失败: {e}')
        
    except Exception as e:
        print(f'❌ 因子引擎检查失败: {e}')

def generate_factor_enhancement_plan():
    """生成因子增强计划"""
    print(f'\n💡 因子增强计划')
    print('=' * 50)
    
    plan = '''
🎯 当前状况评估:
   - 数据库存储的因子数量有限 (约20-30个)
   - 缺乏190+因子的完整存储
   - 现有因子预测能力较弱 (IC值普遍<0.05)
   - 需要建立更强大的因子体系

🚀 因子增强建议:

1. 📊 扩展因子存储:
   - 在数据库中添加更多因子列
   - 建立专门的因子表 (factors table)
   - 存储技术、基本面、市场微观结构因子
   - 实现因子的历史回溯计算

2. 🔧 增强因子计算:
   - 扩展技术指标因子 (50+个)
   - 添加价量关系因子 (30+个)
   - 引入时间序列因子 (40+个)
   - 计算交叉验证因子 (30+个)
   - 开发自定义组合因子 (40+个)

3. 🎯 因子有效性分析:
   - 实施滚动IC分析
   - 计算因子衰减率
   - 分析因子稳定性
   - 建立因子评级体系

4. 🏆 因子选择优化:
   - 基于IC值筛选有效因子
   - 实施因子正交化处理
   - 建立因子组合模型
   - 动态调整因子权重

5. 📈 实时因子监控:
   - 建立因子有效性监控
   - 实施因子漂移检测
   - 自动因子更新机制
   - 因子表现报告系统

🔧 技术实现方案:

阶段1 - 数据库扩展 (1-2天):
   - 设计因子存储表结构
   - 实现因子批量计算
   - 建立因子数据管道

阶段2 - 因子计算增强 (3-5天):
   - 实现190+因子计算
   - 优化计算性能
   - 建立因子缓存机制

阶段3 - 分析系统建立 (5-7天):
   - 实现因子IC分析
   - 建立因子选择算法
   - 开发因子监控系统

阶段4 - 策略集成 (2-3天):
   - 将有效因子集成到策略
   - 实现动态因子权重
   - 建立因子反馈机制
'''
    
    print(plan)

def main():
    """主函数"""
    print('🚀 因子存储和分析能力检查')
    print('=' * 60)
    
    # 检查数据库结构
    columns, column_count = check_database_structure()
    
    # 检查因子数据可用性
    df, factor_completeness, high_quality_factors = check_factor_data_availability()
    
    # 分析因子预测能力
    if df is not None and factor_completeness is not None:
        factor_ic_results, top_factors = analyze_factor_predictive_power(df, factor_completeness)
    
    # 检查增强因子引擎
    check_enhanced_factor_engine()
    
    # 生成因子增强计划
    generate_factor_enhancement_plan()
    
    print(f'\n🎯 检查总结')
    print('=' * 40)
    
    if column_count > 0:
        print(f'✅ 数据库结构检查完成')
        print(f'📊 当前存储因子数量: 约{column_count-9}个 (排除基础列)')
        
        if column_count < 50:
            print(f'⚠️ 因子数量不足，远少于190+的目标')
            print(f'💡 建议: 实施因子扩展计划')
        else:
            print(f'✅ 因子数量充足')
        
        if high_quality_factors and len(high_quality_factors) > 0:
            print(f'📈 高质量因子: {len(high_quality_factors)}个')
        else:
            print(f'⚠️ 高质量因子不足')
    else:
        print(f'❌ 数据库检查失败')
    
    print(f'\n🚀 下一步建议:')
    print(f'   1. 扩展因子存储到190+个')
    print(f'   2. 实施因子有效性分析')
    print(f'   3. 建立因子选择机制')
    print(f'   4. 集成有效因子到策略')

if __name__ == '__main__':
    main()
