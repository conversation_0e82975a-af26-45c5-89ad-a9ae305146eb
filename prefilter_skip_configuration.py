# coding=utf-8
"""
预筛选跳过配置说明
详细说明新增的预筛选跳过开关功能
"""

def show_configuration_details():
    """显示配置详情"""
    print('🔧 预筛选跳过配置说明')
    print('=' * 60)
    
    print('📋 新增配置项:')
    print('   配置文件: config.py')
    print('   配置项: skip_when_no_prefilter_results')
    print('   位置: PERFORMANCE_CONFIG 字典中')
    print('   默认值: False')
    
    print(f'\n⚙️ 配置选项:')
    options = [
        {
            'value': 'False',
            'behavior': '使用原始信号生成器',
            'description': '当预筛选没有找到符合条件的股票时，回退到使用原始信号生成器分析所有股票',
            '适用场景': '希望确保每天都有交易机会，不错过任何可能的买入信号',
            '优点': '保证策略的连续性，不会因为预筛选失败而错过交易',
            '缺点': '可能增加计算量，降低策略执行效率'
        },
        {
            'value': 'True',
            'behavior': '直接跳过',
            'description': '当预筛选没有找到符合条件的股票时，直接跳过当天的买入操作',
            '适用场景': '严格按照预筛选条件执行，只在预筛选有结果时才进行交易',
            '优点': '严格遵循预筛选逻辑，提高策略执行效率',
            '缺点': '可能错过一些交易机会，特别是在市场条件不符合预筛选条件时'
        }
    ]
    
    for option in options:
        print(f'\n📊 {option["value"]} - {option["behavior"]}:')
        print(f'   描述: {option["description"]}')
        print(f'   适用场景: {option["适用场景"]}')
        print(f'   优点: {option["优点"]}')
        print(f'   缺点: {option["缺点"]}')

def show_implementation_details():
    """显示实现细节"""
    print(f'\n💻 实现细节')
    print('=' * 50)
    
    print('📋 修改的文件:')
    files_modified = [
        {
            'file': 'config.py',
            'changes': [
                '在PERFORMANCE_CONFIG中添加skip_when_no_prefilter_results配置项',
                '默认值设为False，保持向后兼容性'
            ]
        },
        {
            'file': 'main.py',
            'changes': [
                '在buy_strategy_main_logic函数中添加配置检查',
                '当预筛选无结果且开关为True时，返回空的signal_analysis_results',
                '添加相应的日志输出，明确说明跳过原因'
            ]
        }
    ]
    
    for file_info in files_modified:
        print(f'\n📄 {file_info["file"]}:')
        for change in file_info['changes']:
            print(f'   • {change}')

def show_usage_examples():
    """显示使用示例"""
    print(f'\n📖 使用示例')
    print('=' * 50)
    
    examples = [
        {
            'scenario': '保守策略（严格预筛选）',
            'config': 'skip_when_no_prefilter_results = True',
            'behavior': '只在预筛选有结果时才交易',
            'log_output': '📊 每日预筛选未找到符合条件的股票，根据配置跳过（不使用原始信号生成器）'
        },
        {
            'scenario': '积极策略（确保交易）',
            'config': 'skip_when_no_prefilter_results = False',
            'behavior': '预筛选无结果时使用原始信号生成器',
            'log_output': '📊 每日预筛选未找到符合条件的股票，使用原始信号生成器'
        }
    ]
    
    for example in examples:
        print(f'\n🎯 {example["scenario"]}:')
        print(f'   配置: {example["config"]}')
        print(f'   行为: {example["behavior"]}')
        print(f'   日志: {example["log_output"]}')

def show_impact_analysis():
    """显示影响分析"""
    print(f'\n📊 影响分析')
    print('=' * 50)
    
    impacts = [
        {
            'aspect': '策略执行效率',
            'skip_true': '提高（减少不必要的分析）',
            'skip_false': '可能降低（需要分析所有股票）',
            'recommendation': '如果预筛选效果好，建议设为True'
        },
        {
            'aspect': '交易机会',
            'skip_true': '可能减少（严格筛选）',
            'skip_false': '保持完整（不错过机会）',
            'recommendation': '如果希望最大化交易机会，设为False'
        },
        {
            'aspect': '策略一致性',
            'skip_true': '更一致（严格按预筛选逻辑）',
            'skip_false': '有回退机制（混合逻辑）',
            'recommendation': '根据策略设计理念选择'
        },
        {
            'aspect': '计算资源',
            'skip_true': '节省（跳过大量计算）',
            'skip_false': '消耗更多（需要全量分析）',
            'recommendation': '资源有限时建议设为True'
        }
    ]
    
    print(f'{"方面":<12} | {"跳过=True":<15} | {"跳过=False":<15} | {"建议"}')
    print('-' * 70)
    
    for impact in impacts:
        print(f'{impact["aspect"]:<12} | {impact["skip_true"]:<15} | {impact["skip_false"]:<15} | {impact["recommendation"]}')

def show_monitoring_suggestions():
    """显示监控建议"""
    print(f'\n📈 监控建议')
    print('=' * 50)
    
    monitoring_points = [
        {
            'metric': '预筛选成功率',
            'description': '监控预筛选找到股票的频率',
            'threshold': '如果经常为0，考虑调整预筛选条件',
            'action': '优化TRIX预筛选参数或条件'
        },
        {
            'metric': '跳过天数统计',
            'description': '统计因预筛选无结果而跳过的交易日',
            'threshold': '如果跳过天数过多，影响策略效果',
            'action': '考虑设置为False或优化预筛选逻辑'
        },
        {
            'metric': '交易频率变化',
            'description': '对比开关前后的交易频率',
            'threshold': '交易频率大幅下降可能影响收益',
            'action': '评估是否需要调整配置'
        },
        {
            'metric': '策略收益影响',
            'description': '分析开关对整体策略收益的影响',
            'threshold': '如果收益明显下降，需要重新评估',
            'action': '可能需要回退到False或优化策略'
        }
    ]
    
    for point in monitoring_points:
        print(f'\n📊 {point["metric"]}:')
        print(f'   描述: {point["description"]}')
        print(f'   阈值: {point["threshold"]}')
        print(f'   行动: {point["action"]}')

def show_configuration_code():
    """显示配置代码"""
    print(f'\n💻 配置代码示例')
    print('=' * 50)
    
    print('📄 在config.py中的配置:')
    print('''
PERFORMANCE_CONFIG = {
    # ... 其他配置项 ...
    'skip_when_no_prefilter_results': False,   # 预筛选无结果时是否跳过（True=跳过，False=使用原始信号生成器）
    # ... 其他配置项 ...
}
''')
    
    print('📄 在main.py中的实现:')
    print('''
if len(daily_prefiltered_symbols) == 0:
    # 检查是否启用了预筛选无结果时跳过的开关
    skip_when_no_prefilter = get_config_value('skip_when_no_prefilter_results', False)
    
    if skip_when_no_prefilter:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 每日预筛选未找到符合条件的股票，根据配置跳过（不使用原始信号生成器）")
        signal_analysis_results = []
    else:
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 📊 每日预筛选未找到符合条件的股票，使用原始信号生成器")
        signal_analysis_results = context.signal_generator.analyze_signals(available_symbols)
''')

def main():
    """主函数"""
    print('🔧 预筛选跳过配置完整说明')
    print('=' * 60)
    
    # 显示配置详情
    show_configuration_details()
    
    # 显示实现细节
    show_implementation_details()
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示影响分析
    show_impact_analysis()
    
    # 显示监控建议
    show_monitoring_suggestions()
    
    # 显示配置代码
    show_configuration_code()
    
    print(f'\n🎯 总结')
    print('=' * 40)
    print('✅ 已成功添加预筛选跳过开关')
    print('✅ 配置项: skip_when_no_prefilter_results')
    print('✅ 默认值: False (保持向后兼容)')
    print('✅ 支持两种模式: 跳过 vs 使用原始信号生成器')
    print('')
    print('🚀 使用建议:')
    print('   • 如果预筛选效果好且希望提高效率，设为True')
    print('   • 如果希望确保交易连续性，保持False')
    print('   • 建议先测试True模式的效果再决定')
    print('')
    print('📊 现在您可以通过修改config.py中的配置来控制行为')

if __name__ == '__main__':
    main()
