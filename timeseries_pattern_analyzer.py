# coding=utf-8
"""
时序模式分析器
识别因子序列中的特定模式，实现动态阈值计算
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sqlite3
from typing import Dict, List, Tuple, Optional

class TimeSeriesPatternAnalyzer:
    """时序模式分析器"""
    
    def __init__(self, db_path='data/timeseries_factors.db'):
        self.db_path = db_path
    
    def get_timeseries_data(self, symbol: str, lookback_hours: int = 24) -> pd.DataFrame:
        """获取时序数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=lookback_hours)
            
            query = """
                SELECT * FROM timeseries_factors 
                WHERE symbol = ? AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp ASC
            """
            
            df = pd.read_sql_query(query, conn, params=[symbol, start_time, end_time])
            conn.close()
            
            if len(df) > 0:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            print(f'❌ 获取{symbol}时序数据失败: {e}')
            return pd.DataFrame()
    
    def detect_macd_golden_cross_trend(self, symbol: str, lookback_periods: int = 20) -> Tuple[bool, str, Dict]:
        """检测MACD金叉趋势"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=lookback_periods)
            
            if len(data) < 3:
                return False, "数据不足", {}
            
            # 获取MACD相关数据
            macd_hist = data['macd_hist'].dropna()
            macd = data['macd'].dropna()
            macd_signal = data['macd_signal'].dropna()
            
            if len(macd_hist) < 3:
                return False, "MACD数据不足", {}
            
            # 检测金叉模式
            recent_hist = macd_hist.tail(3).values
            recent_macd = macd.tail(3).values if len(macd) >= 3 else None
            recent_signal = macd_signal.tail(3).values if len(macd_signal) >= 3 else None
            
            # 金叉条件：MACD_hist从负转正
            golden_cross = False
            cross_strength = 0
            
            if recent_hist[-1] > 0 and recent_hist[-2] <= 0:
                golden_cross = True
                cross_strength = recent_hist[-1]  # 金叉强度
            
            # 计算趋势强度
            if len(recent_hist) >= 3:
                trend_strength = (recent_hist[-1] - recent_hist[-3]) / (abs(recent_hist[-3]) + 1e-6)
            else:
                trend_strength = 0
            
            # 验证MACD主线和信号线的关系
            macd_above_signal = False
            if recent_macd is not None and recent_signal is not None:
                macd_above_signal = recent_macd[-1] > recent_signal[-1]
            
            # 综合判断
            signal_strength = 0
            if golden_cross:
                signal_strength += 0.4
            if macd_above_signal:
                signal_strength += 0.3
            if trend_strength > 0:
                signal_strength += 0.3
            
            pattern_info = {
                'golden_cross': golden_cross,
                'cross_strength': cross_strength,
                'trend_strength': trend_strength,
                'macd_above_signal': macd_above_signal,
                'signal_strength': signal_strength,
                'recent_hist': recent_hist.tolist(),
                'latest_macd': recent_macd[-1] if recent_macd is not None else None,
                'latest_signal': recent_signal[-1] if recent_signal is not None else None
            }
            
            # 判断是否为强金叉信号
            is_strong_signal = signal_strength >= 0.6 and golden_cross
            
            reason = f"MACD金叉分析: 信号强度{signal_strength:.2f}"
            if golden_cross:
                reason += f", 金叉强度{cross_strength:.4f}"
            if macd_above_signal:
                reason += ", MACD上穿信号线"
            
            return is_strong_signal, reason, pattern_info
            
        except Exception as e:
            return False, f"MACD分析失败: {str(e)}", {}
    
    def calculate_dynamic_atr_threshold(self, symbol: str, lookback_days: int = 30) -> Tuple[float, Dict]:
        """计算动态ATR阈值"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=lookback_days * 24)
            
            if len(data) < 10:
                return 2.5, {"reason": "数据不足，使用默认阈值"}
            
            atr_values = data['atr_pct'].dropna()
            
            if len(atr_values) < 10:
                return 2.5, {"reason": "ATR数据不足，使用默认阈值"}
            
            # 计算统计信息
            atr_mean = atr_values.mean()
            atr_std = atr_values.std()
            atr_median = atr_values.median()
            
            # 计算分位数
            q25 = atr_values.quantile(0.25)
            q50 = atr_values.quantile(0.50)
            q75 = atr_values.quantile(0.75)
            q90 = atr_values.quantile(0.90)
            
            # 动态阈值计算逻辑
            # 基于历史分布选择合适的阈值
            if atr_std < atr_mean * 0.3:  # 低波动环境
                threshold = q75
                reason = "低波动环境，使用75分位数"
            elif atr_std > atr_mean * 0.8:  # 高波动环境
                threshold = q90
                reason = "高波动环境，使用90分位数"
            else:  # 正常波动环境
                threshold = q75 + (q90 - q75) * 0.5
                reason = "正常波动环境，使用75-90分位数中值"
            
            # 确保阈值在合理范围内
            threshold = max(1.5, min(threshold, 8.0))
            
            threshold_info = {
                'threshold': threshold,
                'reason': reason,
                'atr_mean': atr_mean,
                'atr_std': atr_std,
                'atr_median': atr_median,
                'q25': q25,
                'q50': q50,
                'q75': q75,
                'q90': q90,
                'data_points': len(atr_values),
                'latest_atr': atr_values.iloc[-1] if len(atr_values) > 0 else None
            }
            
            return threshold, threshold_info
            
        except Exception as e:
            return 2.5, {"reason": f"计算失败: {str(e)}，使用默认阈值"}
    
    def detect_bb_expansion_pattern(self, symbol: str, lookback_periods: int = 15) -> Tuple[bool, str, Dict]:
        """检测布林带扩张模式"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=lookback_periods)
            
            if len(data) < 5:
                return False, "数据不足", {}
            
            bb_width = data['bb_width'].dropna()
            bb_position = data['bb_position'].dropna()
            
            if len(bb_width) < 5:
                return False, "布林带数据不足", {}
            
            # 检测布林带宽度变化趋势
            recent_width = bb_width.tail(5).values
            width_trend = np.polyfit(range(len(recent_width)), recent_width, 1)[0]
            
            # 检测是否从收窄转向扩张
            is_expanding = width_trend > 0
            current_width = recent_width[-1]
            
            # 计算宽度相对水平
            width_percentile = (current_width - bb_width.min()) / (bb_width.max() - bb_width.min() + 1e-6)
            
            # 检测布林带位置
            current_position = bb_position.iloc[-1] if len(bb_position) > 0 else 50
            
            # 综合判断
            expansion_strength = 0
            if is_expanding:
                expansion_strength += 0.4
            if width_percentile > 0.6:  # 宽度在较高水平
                expansion_strength += 0.3
            if 20 < current_position < 80:  # 价格在布林带中部
                expansion_strength += 0.3
            
            pattern_info = {
                'is_expanding': is_expanding,
                'width_trend': width_trend,
                'current_width': current_width,
                'width_percentile': width_percentile,
                'current_position': current_position,
                'expansion_strength': expansion_strength,
                'recent_width': recent_width.tolist()
            }
            
            is_strong_pattern = expansion_strength >= 0.6
            reason = f"布林带扩张分析: 强度{expansion_strength:.2f}, 宽度{current_width:.2f}"
            
            return is_strong_pattern, reason, pattern_info
            
        except Exception as e:
            return False, f"布林带分析失败: {str(e)}", {}
    
    def analyze_volume_pattern(self, symbol: str, lookback_periods: int = 10) -> Tuple[bool, str, Dict]:
        """分析成交量模式"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=lookback_periods)
            
            if len(data) < 3:
                return False, "数据不足", {}
            
            relative_volume = data['relative_volume'].dropna()
            volume_change_rate = data['volume_change_rate'].dropna()
            
            if len(relative_volume) < 3:
                return False, "成交量数据不足", {}
            
            # 当前成交量水平
            current_rel_volume = relative_volume.iloc[-1]
            current_change_rate = volume_change_rate.iloc[-1] if len(volume_change_rate) > 0 else 0
            
            # 成交量趋势
            recent_volume = relative_volume.tail(3).values
            volume_trend = np.mean(np.diff(recent_volume))
            
            # 判断成交量放大
            volume_strength = 0
            if current_rel_volume > 1.5:  # 相对成交量大于1.5
                volume_strength += 0.4
            if current_change_rate > 50:  # 成交量增长超过50%
                volume_strength += 0.3
            if volume_trend > 0:  # 成交量呈上升趋势
                volume_strength += 0.3
            
            pattern_info = {
                'current_rel_volume': current_rel_volume,
                'current_change_rate': current_change_rate,
                'volume_trend': volume_trend,
                'volume_strength': volume_strength,
                'recent_volume': recent_volume.tolist()
            }
            
            is_high_volume = volume_strength >= 0.6
            reason = f"成交量分析: 强度{volume_strength:.2f}, 相对量{current_rel_volume:.2f}"
            
            return is_high_volume, reason, pattern_info
            
        except Exception as e:
            return False, f"成交量分析失败: {str(e)}", {}
    
    def comprehensive_pattern_analysis(self, symbol: str) -> Dict:
        """综合模式分析"""
        try:
            print(f'🔍 开始对{symbol}进行综合时序模式分析')
            
            results = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'patterns': {},
                'overall_score': 0,
                'recommendation': 'HOLD'
            }
            
            # MACD金叉分析
            macd_signal, macd_reason, macd_info = self.detect_macd_golden_cross_trend(symbol)
            results['patterns']['macd_golden_cross'] = {
                'signal': macd_signal,
                'reason': macd_reason,
                'info': macd_info
            }
            
            # 动态ATR阈值
            atr_threshold, atr_info = self.calculate_dynamic_atr_threshold(symbol)
            results['patterns']['dynamic_atr'] = {
                'threshold': atr_threshold,
                'info': atr_info
            }
            
            # 布林带扩张分析
            bb_signal, bb_reason, bb_info = self.detect_bb_expansion_pattern(symbol)
            results['patterns']['bb_expansion'] = {
                'signal': bb_signal,
                'reason': bb_reason,
                'info': bb_info
            }
            
            # 成交量分析
            volume_signal, volume_reason, volume_info = self.analyze_volume_pattern(symbol)
            results['patterns']['volume_pattern'] = {
                'signal': volume_signal,
                'reason': volume_reason,
                'info': volume_info
            }
            
            # 计算综合得分
            score = 0
            if macd_signal:
                score += 0.3
            if bb_signal:
                score += 0.3
            if volume_signal:
                score += 0.2
            
            # 获取当前ATR值进行比较
            current_data = self.get_timeseries_data(symbol, lookback_hours=1)
            if len(current_data) > 0 and 'atr_pct' in current_data.columns:
                current_atr = current_data['atr_pct'].iloc[-1]
                if pd.notna(current_atr) and current_atr > atr_threshold:
                    score += 0.2
                    results['patterns']['atr_check'] = {
                        'current_atr': current_atr,
                        'threshold': atr_threshold,
                        'above_threshold': True
                    }
                else:
                    results['patterns']['atr_check'] = {
                        'current_atr': current_atr if pd.notna(current_atr) else None,
                        'threshold': atr_threshold,
                        'above_threshold': False
                    }
            
            results['overall_score'] = score
            
            # 生成建议
            if score >= 0.7:
                results['recommendation'] = 'STRONG_BUY'
            elif score >= 0.5:
                results['recommendation'] = 'BUY'
            elif score >= 0.3:
                results['recommendation'] = 'WEAK_BUY'
            else:
                results['recommendation'] = 'HOLD'
            
            print(f'✅ {symbol} 综合分析完成，得分: {score:.2f}, 建议: {results["recommendation"]}')
            
            return results
            
        except Exception as e:
            print(f'❌ {symbol} 综合分析失败: {e}')
            return {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'error': str(e),
                'overall_score': 0,
                'recommendation': 'HOLD'
            }

def main():
    """测试函数"""
    print('🧪 时序模式分析器测试')
    print('=' * 50)
    
    analyzer = TimeSeriesPatternAnalyzer()
    
    # 获取测试股票
    try:
        conn = sqlite3.connect('data/trades.db')
        query = "SELECT DISTINCT symbol FROM trades WHERE action = 'BUY' LIMIT 3"
        df = pd.read_sql_query(query, conn)
        conn.close()
        test_symbols = df['symbol'].tolist()
    except:
        test_symbols = ['SHSE.600036', 'SZSE.000001', 'SHSE.600519']
    
    print(f'📊 测试股票: {test_symbols}')
    
    for symbol in test_symbols:
        print(f'\n🔍 分析 {symbol}:')
        result = analyzer.comprehensive_pattern_analysis(symbol)
        
        if 'error' not in result:
            print(f'   综合得分: {result["overall_score"]:.2f}')
            print(f'   投资建议: {result["recommendation"]}')
            
            # 显示各个模式的结果
            patterns = result['patterns']
            if 'macd_golden_cross' in patterns:
                macd = patterns['macd_golden_cross']
                print(f'   MACD金叉: {"✅" if macd["signal"] else "❌"} - {macd["reason"]}')
            
            if 'dynamic_atr' in patterns:
                atr = patterns['dynamic_atr']
                print(f'   动态ATR阈值: {atr["threshold"]:.2f}')
            
            if 'bb_expansion' in patterns:
                bb = patterns['bb_expansion']
                print(f'   布林带扩张: {"✅" if bb["signal"] else "❌"} - {bb["reason"]}')
            
            if 'volume_pattern' in patterns:
                vol = patterns['volume_pattern']
                print(f'   成交量模式: {"✅" if vol["signal"] else "❌"} - {vol["reason"]}')
        else:
            print(f'   ❌ 分析失败: {result["error"]}')

if __name__ == '__main__':
    main()
