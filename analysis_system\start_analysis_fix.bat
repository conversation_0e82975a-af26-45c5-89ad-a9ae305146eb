@echo off
rem 设置代码页为UTF-8
chcp 65001 >nul

rem 清屏
cls

echo ===============================================================================
echo                           万和策略分析系统 - 启动器                            
echo ===============================================================================
echo.

rem 设置Python路径
set PYTHON_PATH=python

rem 检查Python是否存在
%PYTHON_PATH% --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

echo 正在检查必要的Python包...

rem 检查pandas
%PYTHON_PATH% -m pip show pandas >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装pandas包...
    %PYTHON_PATH% -m pip install pandas
)

rem 检查numpy
%PYTHON_PATH% -m pip show numpy >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装numpy包...
    %PYTHON_PATH% -m pip install numpy
)

rem 检查matplotlib
%PYTHON_PATH% -m pip show matplotlib >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装matplotlib包...
    %PYTHON_PATH% -m pip install matplotlib
)

rem 检查seaborn
%PYTHON_PATH% -m pip show seaborn >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装seaborn包...
    %PYTHON_PATH% -m pip install seaborn
)

rem 检查scikit-learn
%PYTHON_PATH% -m pip show scikit-learn >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装scikit-learn包...
    %PYTHON_PATH% -m pip install scikit-learn
)

rem 检查jinja2
%PYTHON_PATH% -m pip show jinja2 >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装jinja2包...
    %PYTHON_PATH% -m pip install jinja2
)

cls
echo ===============================================================================
echo                           万和策略分析系统 - 启动器                            
echo ===============================================================================
echo.
echo 请选择要执行的操作:
echo.
echo 1. 运行完整分析流程
echo 2. 仅运行交易分析
echo 3. 仅运行策略优化
echo 4. 仅生成HTML报告
echo 0. 退出
echo.

set /p choice=请输入选项 (0-4): 

if "%choice%"=="1" (
    echo.
    echo 正在运行完整分析流程...
    %PYTHON_PATH% analysis_manager.py --all
) else if "%choice%"=="2" (
    echo.
    echo 正在运行交易分析...
    %PYTHON_PATH% analysis_manager.py --analyze
) else if "%choice%"=="3" (
    echo.
    echo 正在运行策略优化...
    %PYTHON_PATH% analysis_manager.py --optimize
) else if "%choice%"=="4" (
    echo.
    echo 正在生成HTML报告...
    %PYTHON_PATH% analysis_manager.py --html
) else if "%choice%"=="0" (
    echo.
    echo 退出系统...
    exit /b 0
) else (
    echo.
    echo 无效的选项，请重新运行并选择正确的选项
)

echo.
echo 操作完成
pause 