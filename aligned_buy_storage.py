
# 对齐卖出逻辑的买入记录保存代码
def save_buy_record_aligned(context, symbol, price, volume, signal_data):
    """保存买入记录（对齐卖出逻辑）"""
    try:
        # 创建买入交易记录（使用与卖出相同的字段格式）
        buy_trade_data = {
            'Timestamp': context.now.strftime('%Y-%m-%d %H:%M:%S%z'),
            'Symbol': symbol,
            'Action': 'BUY',  # 使用大写，与卖出一致
            'Price': price,
            'Volume': volume,
            'Signal_Type': signal_data.get('buy_signal_type', 'trix_reversal'),
            'Signal_Reason': signal_data.get('signal_reason', 'TRIX反转信号'),
        }

        # 直接使用全局data_manager保存（与卖出逻辑一致）
        from scripts.data_manager import get_data_manager
        data_manager = get_data_manager()
        data_manager.save_trade(buy_trade_data)
        
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已保存买入交易记录: {symbol}")

    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 保存买入交易记录异常: {str(e)}")
