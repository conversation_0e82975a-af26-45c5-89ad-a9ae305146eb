
# 时间段优化配置
# 目标: 将胜率从44.54%提升到55%+

# ==================== 时间过滤配置 ====================

# 启用时间段过滤
ENABLE_TIME_FILTER = True

# 交易时间段配置
TRADING_TIME_CONFIG = {
    # 开盘时段限制 (胜率37.7%，需要限制)
    'morning_open': {
        'start_time': '09:30',
        'end_time': '09:45',
        'max_trades_ratio': 0.3,        # 最多30%的交易在此时段
        'min_score_multiplier': 1.5,    # 评分要求提高50%
        'enable': True
    },
    
    # 优质时段1 (胜率64.8%，重点时段)
    'morning_prime': {
        'start_time': '10:00',
        'end_time': '11:30',
        'max_trades_ratio': 0.4,        # 最多40%的交易
        'min_score_multiplier': 0.8,    # 评分要求降低20%
        'enable': True
    },
    
    # 优质时段2 (胜率64.4%，重点时段)
    'afternoon_prime': {
        'start_time': '13:00',
        'end_time': '14:30',
        'max_trades_ratio': 0.3,        # 最多30%的交易
        'min_score_multiplier': 0.8,    # 评分要求降低20%
        'enable': True
    },
    
    # 尾盘时段
    'afternoon_close': {
        'start_time': '14:30',
        'end_time': '15:00',
        'max_trades_ratio': 0.1,        # 最多10%的交易
        'min_score_multiplier': 1.2,    # 评分要求提高20%
        'enable': True
    }
}

# 星期过滤配置
WEEKDAY_FILTER_CONFIG = {
    'enable': True,
    'monday_multiplier': 1.3,           # 周一评分要求提高30%
    'tuesday_multiplier': 1.0,          # 周二正常
    'wednesday_multiplier': 0.9,        # 周三评分要求降低10%
    'thursday_multiplier': 0.9,         # 周四评分要求降低10%
    'friday_multiplier': 1.1,           # 周五评分要求提高10%
}

# ==================== 动态评分调整 ====================

# 基于时间段的动态多因子阈值
DYNAMIC_MULTIFACTOR_THRESHOLDS = {
    'base_thresholds': {
        'min_overall_score': 0.12,
        'min_technical_score': 0.08,
        'min_momentum_score': 0.06,
        'min_trend_score': 0.35,
        'min_risk_adjusted_score': 0.03,
    },
    
    # 时间段调整系数
    'time_adjustments': {
        '09:30-09:45': 1.5,              # 开盘时段要求更高
        '10:00-11:30': 0.8,              # 优质时段要求更低
        '13:00-14:30': 0.8,              # 优质时段要求更低
        '14:30-15:00': 1.2,              # 尾盘时段要求更高
    }
}

# ==================== 买入信号增强 ====================

# 时间段权重配置
TIME_WEIGHT_CONFIG = {
    'enable': True,
    'morning_open_weight': 0.5,         # 开盘时段权重降低
    'morning_prime_weight': 1.5,        # 优质时段权重提高
    'afternoon_prime_weight': 1.5,      # 优质时段权重提高
    'afternoon_close_weight': 0.8,      # 尾盘时段权重略降
}

# ==================== 风险控制增强 ====================

# 时间段风险控制
TIME_RISK_CONFIG = {
    'enable': True,
    'max_morning_open_positions': 5,    # 开盘时段最多5个仓位
    'max_prime_time_positions': 15,     # 优质时段最多15个仓位
    'position_size_by_time': {
        '09:30-09:45': 0.03,            # 开盘时段仓位更小
        '10:00-11:30': 0.06,            # 优质时段仓位更大
        '13:00-14:30': 0.06,            # 优质时段仓位更大
        '14:30-15:00': 0.04,            # 尾盘时段仓位适中
    }
}
