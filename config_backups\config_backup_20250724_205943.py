#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和交易策略配置文件
--------------------

此文件包含策略的所有可配置参数，支持动态加载，修改后自动生效。

基础配置:
- INDEX_SYMBOL: 参考指数代码
- SUBSCRIBE_ALL_MARKET: 是否订阅全市场股票
- CUSTOM_SUBSCRIBE_MODE: 是否启用自定义订阅模式
- CUSTOM_SUBSCRIBE_SYMBOLS: 自定义订阅的股票列表

性能优化相关配置:
- ENABLE_PARALLEL_COMPUTING: 是否启用并行计算
- PARALLEL_WORKERS: 并行计算的线程数
- CACHE_EXPIRES_SECONDS: 普通缓存的有效期(秒)
- LONG_CACHE_EXPIRES_SECONDS: 长期缓存的有效期(秒)
- MAX_ANALYSIS_STOCKS: 每次分析的最大股票数量

买入信号配置:
- ENABLE_BUY_SIGNALS: 是否启用买入信号
- ENABLE_TRIX_BUY_SIGNAL: 是否启用TRIX买入信号
- ENABLE_MA_CROSS_BUY_SIGNAL: 是否启用均线交叉买入信号
- ENABLE_REBOUND_BUY: 是否启用最低价反弹买入信号

卖出信号配置:
- ENABLE_SELL_SIGNALS: 是否启用卖出信号
- ENABLE_TRIX_SELL_SIGNAL: 是否启用TRIX卖出信号
- ENABLE_TRAILING_STOP: 是否启用跟踪止盈
- ENABLE_DYNAMIC_STOP_LOSS: 是否启用动态止损
"""

# =============================================================================
# 配置管理函数
# =============================================================================

def get_config_value(key, default=None):
    """
    获取配置值

    参数:
    - key: 配置键名
    - default: 默认值

    返回:
    - 配置值或默认值
    """
    import sys
    current_module = sys.modules[__name__]
    return getattr(current_module, key, default)

def set_config_value(key, value):
    """
    设置配置值

    参数:
    - key: 配置键名
    - value: 配置值
    """
    import sys
    current_module = sys.modules[__name__]
    setattr(current_module, key, value)

def get_all_config():
    """获取所有配置"""
    import sys
    current_module = sys.modules[__name__]
    config_dict = {}

    for attr_name in dir(current_module):
        if not attr_name.startswith('_') and not callable(getattr(current_module, attr_name)):
            config_dict[attr_name] = getattr(current_module, attr_name)

    return config_dict

# =============================================================================
# 交易核心参数
# =============================================================================

# 基础参数
INDEX_SYMBOL = 'SHSE.000300'  # 参考指数
SUBSCRIBE_ALL_MARKET = False  # 是否订阅全市场股票，True表示订阅全市场，False表示只订阅参考指数成分股

# =============================================================================
# 数据完整性和未来函数保护配置
# =============================================================================

# 数据完整性保护配置
DATA_INTEGRITY_CONFIG = {
    'enable_future_function_protection': True,  # 启用未来函数保护
    'intraday_volume_cutoff_time': '14:30',    # 允许使用当日成交量数据的时间点
    'enable_data_validation': True,            # 启用数据验证
    'enable_backup_on_repair': True,           # 修复时自动备份
    'log_data_integrity_issues': True,         # 记录数据完整性问题
}

# =============================================================================
# 智能评分系统配置
# =============================================================================

# 评分系统总开关 (性能优化：禁用高耗时功能)
SMART_SCORING_CONFIG = {
    'enable_smart_scoring': False,              # 禁用智能评分系统 (性能优化)
    'enable_legacy_strategy': False,           # 启用传统策略作为备选
    'enable_adaptive_threshold': True,         # 启用自适应阈值
    'enable_dynamic_position': True,           # 启用动态仓位管理
    'log_scoring_details': False,              # 记录评分详情（关闭以提升性能）
}

# 评分系统权重配置
SCORING_WEIGHTS = {
    'ma20_distance_score': 0.35,               # 20日均线偏离权重 (最重要)
    'volatility_score': 0.25,                  # 波动率确认权重
    'price_momentum_score': 0.20,              # 价格动量权重
    'technical_confirmation': 0.20             # 技术确认权重
}

# 评分阈值配置
SCORING_THRESHOLDS = {
    'min_buy_score': 75,                       # 提高最低买入评分到75分
    'high_confidence_score': 90,               # 高信心评分
    'medium_confidence_score': 80,             # 中等信心评分
    'low_confidence_score': 75,                # 提高低信心评分到75分
    'adaptive_threshold_enabled': False,       # 禁用自适应阈值，使用固定70分
    'threshold_update_frequency': 50,          # 阈值更新频率(交易次数)
}

# 动态仓位配置
DYNAMIC_POSITION_CONFIG = {
    'max_position_ratio': 1.0,                 # 最大仓位比例
    'score_90_position': 1.0,                  # 90分以上仓位
    'score_80_position': 0.8,                  # 80-89分仓位
    'score_70_position': 0.6,                  # 70-79分仓位
    'score_60_position': 0.4,                  # 60-69分仓位
    'below_60_position': 0.0,                  # 60分以下仓位
}

# =============================================================================
# 回测性能优化配置
# =============================================================================

# 性能优化开关
PERFORMANCE_OPTIMIZATION = {
    'enable_batch_data_fetch': True,           # 启用批量数据获取
    'enable_trix_cache': True,                 # 启用TRIX计算缓存
    'enable_early_exit': False,                # 启用早期退出（可能影响策略完整性）
    'enable_parallel_processing': False,       # 启用并行处理（实验性）
    'batch_size': 1000,                        # 批量获取数据的批次大小
    'max_prefilter_candidates': None,          # 预筛选最大候选数量（None=不限制）
    'cache_cleanup_interval': 300,             # 缓存清理间隔（秒）
    'skip_when_no_prefilter_results': False,   # 预筛选无结果时是否跳过（True=跳过，False=使用原始信号生成器）
    'enable_timeseries_analysis': False,       # 禁用时序因子分析 (性能优化)
    'timeseries_lookback_hours': 24,           # 时序分析回看小时数
    'timeseries_weight': 0.3,                  # 时序分析在决策中的权重

    # 🚀 新增高级性能优化
    'enable_data_preload': True,               # 启用数据预加载
    'enable_symbol_cache': True,               # 启用股票列表缓存
    'enable_calculation_cache': True,          # 启用计算结果缓存
    'enable_memory_pool': True,                # 启用内存池
    'enable_vectorized_calculation': True,     # 启用向量化计算
    'preload_days': 100,                       # 预加载历史数据天数
    'symbol_cache_duration': 3600,             # 股票列表缓存时长（秒）
    'calculation_cache_size': 10000,           # 计算缓存大小
    'memory_pool_size': 50,                    # 内存池大小（MB）
}

# 日志输出模式配置
LOGGING_CONFIG = {
    'mode': 1,                                 # 日志模式：1=极简模式, 2=详细模式, 3=调试模式
    'show_progress': True,                     # 显示进度信息
    'show_debug_details': False,               # 显示调试详情（仅在详细模式下有效）
    'show_data_format_check': False,           # 显示数据格式检查（仅前几个股票）
    'show_trix_calculation': False,            # 显示TRIX计算详情
    'show_individual_results': False,          # 显示每只股票的筛选结果
    'progress_interval': 1000,                  # 进度报告间隔（处理多少只股票后报告一次）
    'max_individual_logs': 5,                  # 最多显示多少只股票的详细日志
}

# 预设日志模式（数字控制）
LOGGING_PRESETS = {
    1: {  # 极简模式 - 最高性能
        'show_progress': True,
        'show_debug_details': False,
        'show_data_format_check': False,
        'show_trix_calculation': False,
        'show_individual_results': False,
        'progress_interval': 100,
        'max_individual_logs': 0,
    },
    2: {  # 详细模式 - 平衡性能和信息
        'show_progress': True,
        'show_debug_details': True,
        'show_data_format_check': True,
        'show_trix_calculation': True,
        'show_individual_results': True,
        'progress_interval': 50,
        'max_individual_logs': 10,
    },
    3: {  # 调试模式 - 完整调试信息
        'show_progress': True,
        'show_debug_details': True,
        'show_data_format_check': True,
        'show_trix_calculation': True,
        'show_individual_results': True,
        'progress_interval': 25,
        'max_individual_logs': 20,
    }
}

# 未来函数风险字段配置
FUTURE_FUNCTION_RISK_FIELDS = [
    'volume_ma5_ratio', 'volume_ma10_ratio', 'volume_ma20_ratio',
    'relative_volume', 'volume_change_pct', 'money_flow_5d',
    'money_flow_10d', 'intraday_volume_ratio', 'volume_spike_indicator',
    'daily_volume_profile', 'volume_weighted_price', 'vwap_ratio'
]

# 安全指标配置（确认无未来函数风险）
SAFE_INDICATORS_CONFIG = {
    'time_factors': [
        'day_of_month', 'day_of_week', 'month_of_year', 'quarter_of_year',
        'hour_of_day', 'trading_session', 'days_to_month_end'
    ],
    'price_momentum': [
        'price_change_pct', 'price_momentum_3d', 'price_momentum_5d',
        'price_momentum_10d', 'price_momentum_20d'
    ],
    'technical_indicators': [
        'ma5_distance_pct', 'ma10_distance_pct', 'ma20_distance_pct',
        'rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d',
        'macd_12_26', 'macd_signal_9', 'macd_histogram',
        'bb_position_20', 'bb_width_20', 'adx_14', 'cci_14'
    ],
    'volatility_indicators': [
        'volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d',
        'atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized'
    ]
}

# 自定义订阅股票配置
"""
自定义订阅股票配置说明:

- CUSTOM_SUBSCRIBE_MODE: 是否启用自定义订阅模式
  - 设为True时，系统将【只】订阅CUSTOM_SUBSCRIBE_SYMBOLS中指定的股票，完全不会订阅沪深300或其他指数成分股
  - 设为False时，系统将按照默认逻辑订阅指数成分股或全市场股票
  - 默认为False，即使用默认订阅逻辑
  - 注意：启用此模式会完全覆盖INDEX_SYMBOL和SUBSCRIBE_ALL_MARKET的设置

- CUSTOM_SUBSCRIBE_SYMBOLS: 自定义订阅的股票列表
  - 仅在CUSTOM_SUBSCRIBE_MODE为True时生效
  - 格式为股票代码列表，例如['SHSE.600519', 'SZSE.300750']
  - 股票代码前缀说明:
    * SHSE: 上海证券交易所
    * SZSE: 深圳证券交易所
  - 可以添加任意数量的股票，但建议控制在合理范围内
  - 如果列表为空，则不会订阅任何股票
"""
CUSTOM_SUBSCRIBE_MODE = False  # 是否启用自定义订阅模式
CUSTOM_SUBSCRIBE_SYMBOLS = [
    #'SHSE.600099',  # 贵州茅台
    'SZSE.000007',  # 宁德时代
    #'SHSE.601318',  # 中国平安
    # 可以根据需要添加更多股票
]  # 自定义订阅的股票列表

# =============================================================================
# 股票过滤参数
# =============================================================================

# 股票类型过滤参数
FILTER_ST_STOCKS = True           # 是否过滤ST股票
FILTER_STARTUP_BOARD = True      # 是否过滤创业板股票(3开头)
FILTER_SCIENCE_BOARD = True       # 是否过滤科创板股票(688开头)
FILTER_BEIJING_BOARD = True      # 是否过滤北交所股票(8开头)

# 价格过滤参数
PRICE_FILTER_ENABLED = True       # 是否启用价格过滤
MIN_PRICE_FILTER = 1.0            # 最低价格过滤(元)
MAX_PRICE_FILTER = 10000.0          # 最高价格过滤(元)

# 注释：已删除振幅过滤器 - 当前策略已有其他活跃度筛选机制
MAX_AMPLITUDE_PCT = 9.5           # 新增：最大振幅限制，避免过度投机股票

# 注释：已删除均线过滤器 - TRIX指标已包含趋势判断
MA_FILTER_DAYS = 3                # 判断均线方向的天数增加到3天

# 持仓和资金管理参数（优化：降低持仓数量，提高选股质量）
MAX_POSITIONS = 500            # 最大持仓数量从60降低到50
POSITION_RATIO = 0.95         # 仓位比例系数从0.99降低到0.95，保留更多现金
SINGLE_POSITION_LIMIT = 120000 # 单只股票最大持仓金额从150000降低到120000
SINGLE_POSITION_RATIO = 0.15  # 单只股票最大持仓比例从0.2降低到0.15
DYNAMIC_POSITION_MAX_RATIO = 0.15 # 动态调整后单只股票最大持仓比例也相应调整
MIN_POSITION_LIMIT = 12000    # 单只股票最小持仓金额从11000提升到12000
MIN_POSITION_RATIO = 0.012    # 单只股票最小持仓比例从0.01提升到0.012

T_PLUS_1 = True               # 是否遵循A股T+1交易规则

# =============================================================================
# 增强因子系统参数
# =============================================================================

# 因子筛选控制
ENABLE_FACTOR_FILTERING = False    # 是否启用因子筛选（False=只收集数据，True=进行筛选）
FACTOR_DATA_COLLECTION = True      # 是否收集因子数据（建议始终为True）

# 因子分析参数
FACTOR_ANALYSIS_MIN_SAMPLES = 50   # 进行因子分析的最小样本数
FACTOR_EFFECTIVENESS_THRESHOLD = 10.0  # 因子有效性阈值

# 数据收集模式说明：
# - ENABLE_FACTOR_FILTERING = False: 收集所有因子数据但不筛选，用于数据积累和分析
# - ENABLE_FACTOR_FILTERING = True:  基于因子进行筛选，提升买入精度

# =============================================================================
# 回测参数
# =============================================================================

# 回测参数
BACKTEST_START_TIME = '2025-02-01 09:30:00'
BACKTEST_END_TIME = '2025-06-30 15:00:00'
BACKTEST_INITIAL_CASH = 10000000
BACKTEST_COMMISSION_RATIO = 0.0003
BACKTEST_SLIPPAGE_RATIO = 0.0002

# 买入检查间隔模式设置
BUY_CHECK_INTERVAL_MODE = False  # 是否启用间隔模式进行买入检查 (改为False使用固定时间点)
BUY_CHECK_INTERVAL_MINUTES = 15  # 买入检查的时间间隔(分钟)



# ==============================================
# 买入检查时间点配置
# ==============================================

# 可以根据需要增加、减少或修改这些时间点
BUY_CHECK_TIMES = [
    '10:00:00',  # 延迟到10点，避开开盘30分钟的因子失效期
    '10:15:00',  # 增加15分钟检查点，提高实时性
    '10:30:00',  # 上午10点半，早盘中段
    '10:45:00',  # 增加15分钟检查点
    '11:00:00',  # 上午11点，接近午盘前
    '11:15:00',  # 增加15分钟检查点
    '13:30:00',  # 下午开盘后30分钟
    '13:45:00',  # 增加15分钟检查点
    '14:00:00',  # 下午2点，盘中时段
    '14:15:00',  # 增加15分钟检查点
    '14:30:00',  # 下午2点半，尾盘前
    '14:45:00',  # 增加15分钟检查点，尾盘机会
   # '13:30:00',  # 午盘开始后，可以捕捉午盘开盘行情
   # '14:00:00',  # 下午2点，下午盘趋势形成
   # '14:30:00'   # 下午2点半，尾盘前最后机会
]

# 信号检查和持仓更新参数

SIGNAL_CHECK_INTERVAL = 5     # 卖出信号计算间隔(分钟)
POSITION_CHECK_INTERVAL = 30  # 持仓更新间隔(分钟)

# 当日买入股票卖出检查设置
"""
当日买入股票卖出检查设置说明:

- SKIP_TODAY_BOUGHT_SELL_CHECK: 是否跳过当日买入股票的卖出检查
  - 设为True时，系统将不对当日买入的股票进行卖出信号检查，直到下一个交易日
  - 设为False时，系统将对所有持仓股票进行卖出信号检查，包括当日买入的股票
  - 默认为True，推荐开启以避免不必要的计算和日志输出
  - 由于A股T+1交易规则，当日买入的股票无法当日卖出，跳过检查可以提高系统效率
  - 此设置不会影响持仓高点的更新，系统仍会记录所有股票的价格高点
"""
SKIP_TODAY_BOUGHT_SELL_CHECK = True  # 是否跳过当日买入股票的卖出检查，避免不必要的计算

# =============================================================================
# 交易执行参数
# =============================================================================

# 订单类型参
USE_MARKET_ORDER = True      # 是否使用市价单（True使用市价单，False使用限价单）
PRICE_ADJUST_RATIO = 0.002    # 价格调整比例（限价单时使用）

# =============================================================================
# 信号生成参数
# =============================================================================

# 历史数据获取参数

HISTORY_DATA_DAYS = 80        # 获取历史数据的天数（用于一般技术指标）
HISTORY_DATA_DAYS_VOLATILITY = 90  # 获取历史数据的天数（用于波动性计算）

# 历史数据处理参数
HISTORY_DATA_FORMAT = 'dataframe'  # 历史数据返回格式，可选 'dataframe' 或 'dict'
HISTORY_COLUMN_MAPPING = {         # 历史数据列名映射
    'symbol': 'symbol',
    'open': 'open',
    'high': 'high',
    'low': 'low',
    'close': 'close',
    'volume': 'volume',
    'amount': 'amount',
    'time': 'time'
}
ENABLE_COLUMN_RENAME = True        # 是否启用列名重命名
FIX_COLUMN_NAMES = True            # 是否修复错误的列名格式（如包含分隔符的列名）
USE_REALTIME_PRICE = True          # 是否使用实时价格替代当日收盘价计算TRIX等指标，解决回测与实盘效果不一致问题

# 因子实时更新配置 (解决昨日因子问题)
FACTOR_REALTIME_CONFIG = {
    'enable_intraday_update': True,        # 启用盘中因子更新
    'update_interval_minutes': 30,         # 30分钟更新一次因子
    'use_current_price_for_calculation': True,  # 使用当前价格计算因子
    'enable_opening_delay': True,          # 启用开盘延迟机制
    'opening_delay_minutes': 30,           # 开盘后30分钟才开始交易
    'use_shorter_periods': True,           # 使用更短的计算周期
    'cci_period': 7,                       # CCI周期: 14天→7天
    'rsi_period': 7,                       # RSI周期: 14天→7天
    'adx_period': 7,                       # ADX周期: 14天→7天
    'enable_realtime_factors': True,       # 启用专门的实时因子
}

# 信号开关参数
ENABLE_BUY_SIGNALS = True     # 是否启用买入信号

"""
买入信号参数说明:

- ENABLE_BUY_SIGNALS: 总开关，控制是否启用所有买入信号检测
  - 设为True时启用买入信号检测，False时完全禁用买入

- ENABLE_TRIX_BUY_SIGNAL: 是否启用TRIX买入信号
  - 当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX时触发

- ENABLE_MA_CROSS_BUY_SIGNAL: 是否启用均线交叉买入信号
  - 短期均线上穿中期均线时触发
  
- ENABLE_TRIX_REVERSAL_SIGNAL: 是否启用TRIX拐点买入信号
  - 昨日TRIX>前日TRIX且前日TRIX<大前日TRIX时触发
  

"""

# 买入信号参数
# 注释：已删除增强买入信号和增强选股过滤器 - 与智能评分系统功能重叠
ENABLE_TRIX_BUY_SIGNAL = False # 关闭TRIX买入信号，改用多因子综合策略
# 注释：已删除均线交叉买入信号 - TRIX信号已足够
# 注释：已删除TRIX拐点信号 - 当前TRIX反转信号已足够




# 注释：已删除反弹买入策略 - 增加复杂度，与主策略逻辑不符


# TRIX指标参数（统一控制所有TRIX计算）
TRIX_EMA_PERIOD =  3         # TRIX的EMA周期（统一用于买入信号、预筛选、拐点信号）
USE_TALIB_TRIX = False         # 是否使用talib直接计算TRIX（统一控制计算方法）
TRIX_REVERSAL_PERIOD = 3       # TRIX拐点信号的周期设置（已废弃，使用TRIX_EMA_PERIOD）

# 均线参数
MA_SHORT_PERIOD = 5          # 短期均线周期
MA_MID_PERIOD = 8            # 中期均线周期
MA_LONG_PERIOD = 20           # 长期均线周期

# 技术指标参数
RSI_PERIOD = 14               # RSI计算周期
BOLL_PERIOD = 20              # 布林带计算周期

# =============================================================================
# 波动性相关参数
# =============================================================================

# 波动性筛选参数
VOLATILITY_PERIOD = 7        # 计算波动性的周期
VOLATILITY_THRESHOLD = 1.0    # 波动性阈值(相对市场波动率的倍数)
ATR_THRESHOLD = 1.5           # ATR阈值(占收盘价的百分比)
VOLATILITY_WEIGHT = 0.7       # 波动率在综合得分中的权重
ATR_WEIGHT = 0.3              # ATR在综合得分中的权重
MIN_ABSOLUTE_VOLATILITY = 0.1 # 最小绝对波动率阈值(%)

# 波动率买入过滤参数
"""
波动率买入过滤参数说明:

- ENABLE_VOLATILITY_BUY_FILTER: 是否启用波动率买入过滤
  - 设为True时，系统将根据股票波动率决定是否买入
  - 设为False时，系统不考虑波动率因素进行买入决策
  - 默认为False，即不使用波动率过滤买入

- VOLATILITY_BUY_FILTER_MODE: 波动率买入过滤模式
  - 'range': 波动率在指定范围内才买入
  - 'min': 波动率大于最小阈值才买入
  - 'max': 波动率小于最大阈值才买入
  - 默认为'range'，即波动率需在最小和最大阈值之间

- MIN_BUY_VOLATILITY_RATIO: 最小买入波动率比例
  - 相对于市场平均波动率的倍数
  - 小于此值的股票被视为波动性不足，不考虑买入
  - 默认为0.8，即波动率至少为市场平均的80%

- MAX_BUY_VOLATILITY_RATIO: 最大买入波动率比例
  - 相对于市场平均波动率的倍数
  - 大于此值的股票被视为波动性过高，风险较大，不考虑买入
  - 默认为2.5，即波动率最高为市场平均的2.5倍

- VOLATILITY_CALCULATION_DAYS: 波动率计算天数
  - 计算波动率时使用的历史数据天数
  - 默认为20天，可根据交易周期调整

- ENABLE_ATR_BUY_FILTER: 是否启用ATR买入过滤
  - 设为True时，系统将根据股票ATR决定是否买入
  - 设为False时，系统不考虑ATR因素进行买入决策
  - 默认为False，即不使用ATR过滤买入

- MIN_BUY_ATR_PERCENT: 最小买入ATR百分比
  - ATR占股价的最小百分比要求
  - 小于此值的股票被视为波动性不足，不考虑买入
  - 默认为1.0，即ATR至少为股价的1%

- MAX_BUY_ATR_PERCENT: 最大买入ATR百分比
  - ATR占股价的最大百分比限制
  - 大于此值的股票被视为波动性过高，风险较大，不考虑买入
  - 默认为5.0，即ATR最高为股价的5%

- VOLATILITY_BUY_PRIORITY_BOOST: 波动率适中股票优先级提升
  - 当股票波动率在理想范围内时，买入优先级提升的系数
  - 默认为1.2，即优先级提高20%
"""
ENABLE_VOLATILITY_BUY_FILTER = True   # 启用波动率买入过滤，提高选股质量
VOLATILITY_BUY_FILTER_MODE = 'range'  # 波动率买入过滤模式: 'range', 'min', 'max'
MIN_BUY_VOLATILITY_RATIO = 0.6        # 最小买入波动率比例从0.8提升到0.6（更严格）
MAX_BUY_VOLATILITY_RATIO = 2.0        # 最大买入波动率比例从2.5降低到2.0（避免过度投机）
VOLATILITY_CALCULATION_DAYS = 20      # 波动率计算天数

ENABLE_ATR_BUY_FILTER = True          # 启用ATR买入过滤
MIN_BUY_ATR_PERCENT = 1.5             # 最小买入ATR百分比从1.0提升到1.5
MAX_BUY_ATR_PERCENT = 4.0             # 最大买入ATR百分比从5.0降低到4.0

VOLATILITY_BUY_PRIORITY_BOOST = 1.2   # 波动率适中股票优先级提升系数

# 初始市场波动率(后续会动态更新)
DEFAULT_MARKET_VOLATILITY = 2.0

# 波动性资金调整参数
MAX_VOLATILITY_FACTOR = 3.0   # 最大波动性资金调整因子
MIN_VOLATILITY_FACTOR = 1.0   # 最小波动性资金调整因子
VOLATILITY_FACTOR_SCALE = 1.5 # 波动性因子缩放系数

# 波动性计算错误处理
SKIP_VOLATILITY_ERROR_STOCKS = True  # 是否跳过波动性计算出错的股票
DEFAULT_VOLATILITY_VALUE = 2.5       # 当计算出错时使用的默认波动性值
MAX_VOLATILITY_ERRORS = 50           # 每日最大允许的波动性计算错误数量
LOG_VOLATILITY_ERRORS = True         # 是否记录波动性计算错误

# =============================================================================
# 性能优化参数
# =============================================================================

# 性能优化总开关
"""
性能优化总开关说明:
- ENABLE_PERFORMANCE_OPTIMIZATION: 是否启用性能优化设置
  - 设为True时，将应用以下性能优化设置:
    * 禁用额外的风险检查(RISK_CHECK_ENABLED=False)
    * 限制每次买入检查的最大买入股票数量(MAX_BUY_BATCH)
  - 设为False时，将使用更频繁的检查，可能提高交易及时性但增加系统负担
  - 默认为False，在系统负载较高时可考虑设为True
"""
ENABLE_PERFORMANCE_OPTIMIZATION = True  # 是否启用性能优化设置

# ==============================================
# 性能分析参数
# ==============================================
"""
性能分析参数说明:
- ENABLE_PERFORMANCE_PROFILING: 是否启用性能分析
  - 设为True时，系统将记录关键函数的执行时间
  - 设为False时，不会进行性能分析，无额外开销
  - 仅在需要排查性能瓶颈时启用
  
- PROFILING_OUTPUT_FILE: 性能分析结果输出文件
  - 设置为None时输出到日志
  - 设置为文件路径时，将结果写入文件

- PROFILING_THRESHOLD_MS: 性能分析阈值(毫秒)
  - 仅记录执行时间超过此阈值的函数调用
  - 设置较低值可捕获更多信息，但会增加输出量
  - 建议值: 回测模式10ms，实盘模式50ms

- PROFILING_TOP_FUNCTIONS: 性能分析结果展示的函数数量
  - 每次汇总仅展示最耗时的N个函数
  - 设为0则展示所有记录的函数
"""
ENABLE_PERFORMANCE_PROFILING = False  # 是否启用性能分析
PROFILING_OUTPUT_FILE = "data/performance_profile.log"  # 性能分析结果输出文件，指定输出到data目录下
PROFILING_THRESHOLD_MS = 10  # 性能分析阈值(毫秒)，仅记录执行时间超过此阈值的函数调用
PROFILING_TOP_FUNCTIONS = 20  # 性能分析结果展示的函数数量
PROFILING_SUMMARY_INTERVAL = 30  # 性能分析结果汇总间隔(秒)



# ==============================================
# 持仓摘要输出时间点配置
# ==============================================

# 可以根据需要增加、减少或修改这些时间点
POSITION_SUMMARY_TIMES = [
    #'10:30:00',  # 上午10点半，早盘中段
    #'14:30:00'   # 下午2点半，尾盘前
]

# ==============================================
# 其他性能优化参数
# ==============================================

MAX_BUY_BATCH = 500            # 每次最多买入的股票数量（优化：200→500）
MAX_ANALYSIS_STOCKS = 1000     # 每次分析的最大股票数量（优化：300→1000）
RISK_CHECK_ENABLED = False    # 是否启用风险检查（性能优化：减少检查）
CACHE_EXPIRE_SECONDS = 3600   # 缓存过期时间(秒)

# ==============================================
# 新增性能优化参数
# ==============================================

# 数据库优化参数
ENABLE_BATCH_DATABASE_WRITE = True  # 启用批量数据库写入
DATABASE_BATCH_SIZE = 1000  # 数据库批量写入大小
DATABASE_CONNECTION_POOL_SIZE = 5  # 数据库连接池大小

# 指标计算优化参数
ENABLE_INDICATOR_CACHE = True  # 启用指标计算缓存
INDICATOR_CACHE_SIZE = 1000  # 指标缓存大小
INDICATOR_CACHE_EXPIRE_SECONDS = 1800  # 指标缓存过期时间（30分钟）

# 并行处理参数
ENABLE_PARALLEL_PROCESSING = True  # 启用并行处理
MAX_WORKER_THREADS = 24  # 最大工作线程数
PARALLEL_BATCH_SIZE = 500  # 并行处理批次大小

# 内存优化参数
ENABLE_MEMORY_OPTIMIZATION = True  # 启用内存优化
AUTO_CLEANUP_INTERVAL = 300  # 自动清理间隔（秒）
MAX_MEMORY_USAGE_MB = 2048  # 最大内存使用（MB）





# =============================================================================
# 日志和数据管理参数
# =============================================================================

# 日志参数
LOG_LEVEL = 'INFO'            # 日志级别
# 注释：已删除CSV日志记录 - 已有数据库记录，CSV冗余

# CSV数据解析参数
CSV_DELIMITER = ','           # CSV文件分隔符
CSV_PARSE_DATES = True        # 是否将日期列解析为日期时间对象
CSV_HEADER = 0                # CSV文件头行索引，0表示第一行是列名
CSV_INDEX_COL = None          # 用作索引的列，None表示使用默认索引

# CSV列名修复参数
FIX_CSV_COLUMN_NAMES = True   # 是否修复CSV列名问题
CSV_COLUMN_SPLIT_CHAR = ','   # CSV列名中可能存在的错误分隔符
EXPECTED_COLUMNS = ['symbol', 'open', 'high', 'low', 'close', 'volume', 'amount', 'time']  # 预期的列名列表
HANDLE_MISSING_COLUMNS = True # 是否处理缺失列（用默认值填充）

# =============================================================================
# API连接参数
# =============================================================================

# API凭证(默认值，可被命令行参数覆盖)
DEFAULT_STRATEGY_ID = '39da9282-3bbd-11f0-8755-d4e98a5e8c02'
DEFAULT_TOKEN = '927022466b9f6476ef82fe30991f521c61feac74'


# ==============================================
# 模式切换与订阅管理参数
# ==============================================
"""
模式切换与订阅管理参数说明:

- ENABLE_MODE_ADAPTIVE: 模式自适应功能设置
  - 0: 启用自动模式自适应（系统自动判断当前运行环境）
  - 1: 强制使用回测模式订阅策略（一次性订阅所有股票）
  - 2: 强制使用模拟盘模式订阅策略（使用批次订阅，批次大小较大）
  - 3: 强制使用实盘模式订阅策略（使用批次订阅，批次大小较小，更频繁轮换）
  - 推荐使用0，让系统自动判断运行环境并优化订阅策略

- SUBSCRIPTION_ROTATION_ENABLED: 是否启用订阅轮换机制
  - 仅在实盘/模拟盘模式下生效，回测模式会忽略此设置
  - 如果设为True，系统将采用轮换订阅机制，每隔一定时间轮换订阅一批股票
  - 如果设为False，系统将尝试一次性订阅所有股票(可能受平台限制)
  - 默认为True，推荐开启以避免超过平台订阅限制

- SUBSCRIPTION_ROTATION_INTERVAL: 订阅轮换间隔(分钟)
  - 仅在SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制系统轮换订阅批次的时间间隔
  - 默认为1分钟，表示每分钟轮换一批股票
  - 降低此值可以更快地轮换完所有股票，但会增加API调用频率
  - 提高此值可以减少API调用，但会延长轮换完所有股票的时间

- LIVE_MODE_BATCH_SIZE: 实盘/模拟盘模式下的批次大小
  - 仅在实盘/模拟盘模式且SUBSCRIPTION_ROTATION_ENABLED为True时生效
  - 控制每批次订阅的股票数量
  - 默认为100，表示每批次订阅100只股票
  - 需要根据平台的订阅限制进行设置，通常掘金量化平台限制为500个订阅
  - 设置过大可能导致超过平台限制，设置过小会增加轮换次数

- MAX_SUBSCRIPTION_LIMIT: 平台最大订阅限制
  - 平台允许的最大订阅数量
  - 默认为50，表示平台最多允许同时订阅50个标的
  - 此参数用于系统自动计算批次数量，确保不超过平台限制
  - 如果平台限制有变化，请相应调整此参数

- SYNC_BUY_CHECK_WITH_ROTATION: 是否将买入检查与订阅轮换同步
  - 仅在实盘/模拟盘模式、SUBSCRIPTION_ROTATION_ENABLED为True且BUY_CHECK_INTERVAL_MODE为True时生效
  - 如果设为True，系统将在每次轮换订阅时同步检查当前批次的买入信号
  - 如果设为False，买入检查将按照BUY_CHECK_INTERVAL_MINUTES设置的间隔独立执行
  - 默认为True，推荐开启以优化性能，避免重复计算
  - 此参数主要用于高频交易场景，如果您使用的是中低频策略，可以设为False

- BACKTEST_FULL_SUBSCRIPTION: 回测模式是否一次性订阅所有股票
  - 仅在回测模式下生效
  - 如果设为True，回测模式将一次性订阅所有股票池中的股票
  - 如果设为False，回测模式也将使用轮换订阅机制(通常不推荐)
  - 默认为True，推荐开启以充分利用回测模式的无限制特性
  - 只有在特殊情况下(如模拟实盘环境进行回测)才需要设为False
"""
ENABLE_MODE_ADAPTIVE = 1           # 模式自适应功能(0:自动 1:回测 2:模拟盘 3:实盘)
SUBSCRIPTION_ROTATION_ENABLED = True  # 启用订阅轮换机制
SUBSCRIPTION_ROTATION_INTERVAL = 1    # 订阅轮换间隔(分钟)
LIVE_MODE_BATCH_SIZE = 50             # 实盘/模拟盘模式下的批次大小（根据账户限制设置为10）
MAX_SUBSCRIPTION_LIMIT = 50           # 平台最大订阅限制（根据账户实际限制调整为50）
MAX_SUBSCRIPTION_PER_BATCH = 50       # 每次订阅操作最多的股票数量
SYNC_BUY_CHECK_WITH_ROTATION = True   # 将买入检查与订阅轮换同步
BACKTEST_FULL_SUBSCRIPTION = True     # 回测模式一次性订阅所有股票

# 控制回测模式是否只在开始时订阅一次，后续跳过订阅刷新
# 设置为True时，回测模式下只在第一次订阅股票，后续的订阅刷新操作将被跳过
# 这可以减少回测过程中的重复订阅操作，提高回测效率
# 设置为False时，回测模式下会按照常规逻辑进行订阅刷新
BACKTEST_SUBSCRIBE_ONCE = True        # 回测模式只在开始时订阅一次

# 您可以根据需要添加更多参数... 

# 启用并行计算功能
ENABLE_PARALLEL_COMPUTING = True

# 并行计算的线程数
PARALLEL_WORKERS = 16

# 常规缓存有效期（秒）
CACHE_EXPIRES_SECONDS = 1800  # 30分钟

# 长期缓存有效期（秒）
LONG_CACHE_EXPIRES_SECONDS = 86400  # 24小时

# 历史数据管理配置
HISTORY_CACHE_SIZE = 500  # 历史数据缓存大小（优化：50→500，10倍提升）
ENABLE_HISTORY_PREFETCH = True  # 是否启用历史数据预获取
HISTORY_PREFETCH_DAYS = 60  # 预获取的历史数据天数
HISTORY_PREFETCH_BATCH_SIZE = 100  # 每批预获取的股票数量（优化：50→100）

# 批量数据获取配置
ENABLE_BATCH_DATA_FETCH = True  # 启用批量数据获取
BATCH_FETCH_SIZE = 50  # 批量获取的股票数量

# =============================================================================
# 异常处理参数
# =============================================================================

# 通用异常处理
MAX_RETRY_COUNT = 3           # 操作失败时的最大重试次数
RETRY_DELAY_SECONDS = 1       # 重试前的延迟时间(秒)
ENABLE_GRACEFUL_ERROR_HANDLING = True  # 启用优雅的错误处理

# 数据解析异常处理
HANDLE_PARSE_ERRORS = True    # 是否处理数据解析异常
SKIP_PROBLEMATIC_STOCKS = True  # 遇到解析问题时是否跳过该股票
MAX_PARSE_ERRORS_PER_DAY = 100  # 每日最大允许的解析错误数量
LOG_PARSE_ERRORS = True       # 是否记录解析错误 

# 特定错误处理
FIX_COLUMN_INDEX_ERROR = True  # 修复"None of [Index...] are in the [columns]"类型的错误
COLUMN_ERROR_ACTION = 'rename'  # 列错误处理方式: 'rename'(重命名), 'skip'(跳过), 'default'(使用默认值)

# =============================================================================
# 卖出信号配置
# =============================================================================

# 卖出信号总开关
ENABLE_SELL_SIGNALS = True    # 是否启用卖出信号

# 跟踪止盈卖出信号（交易结构优化：进一步优化参数）
ENABLE_TRAILING_STOP = True   # 是否启用跟踪止盈卖出信号
TRAILING_STOP = 0.008         # 跟踪止盈阈值从1.0%收紧到0.8% (提升胜率)

# 固定止盈卖出信号（交易结构优化：降低门槛增加使用频率）
ENABLE_FIXED_PROFIT_STOP = True    # 启用固定止盈卖出信号
FIXED_PROFIT_RATIO = 0.03         # 固定止盈比例从5%降低到3% (增加100%胜率使用)
FIXED_PROFIT_PRIORITY = 1.5       # 固定止盈优先级(介于跟踪止盈和动态止损之间)

"""
固定止盈与移动止盈的区别与优势说明:

1. 固定止盈(Fixed Profit Stop):
   - 原理: 当股票价格上涨达到预设的固定盈利比例时(如10%)触发卖出
   - 特点: 目标价格固定，一旦达到即触发卖出
   - 优势: 确保在达到特定盈利目标时锁定利润，不受后续价格波动影响
   - 适用场景: 适合短期交易、波动较大的市场或有明确盈利目标的交易

2. 移动止盈(Trailing Stop):
   - 原理: 根据股票价格的上涨动态调整止盈点，当价格从最高点回落特定比例(如1.5%)时触发卖出
   - 特点: 目标价格随市场价格变动而移动，会随着股价上涨而抬高
   - 优势: 允许利润持续增长，同时保护已实现的部分利润，最大化趋势行情收益
   - 适用场景: 适合中长期趋势交易、持续上涨行情

3. 两者结合使用的优势:
   - 双重保障: 既能在达到固定盈利目标时锁定利润，又能在趋势行情中追踪最大收益
   - 灵活性: 根据不同市场环境和个股特性，可能先触发固定止盈或移动止盈
   - 风险控制: 提供更全面的盈利管理策略，避免因单一止盈方式带来的局限性

4. 参数设置建议:
   - 固定止盈比例(FIXED_PROFIT_RATIO): 一般设置为10%-30%，根据个人风险偏好调整
   - 移动止盈阈值(TRAILING_STOP): 一般设置为1%-5%，波动性大的股票可适当放宽
   - 优先级设置: 通常移动止盈优先级高于固定止盈，以便在趋势行情中充分获利
"""

# TRIX死叉卖出信号
ENABLE_TRIX_SELL_SIGNAL = False # 是否启用TRIX死叉卖出信号
TRIX_SELL_EMA_PERIOD = 7      # TRIX卖出信号的EMA周期

# 动态止损卖出信号（最终优化：大幅放宽止损比例）
ENABLE_DYNAMIC_STOP_LOSS = True # 是否启用动态止损卖出信号
DYNAMIC_STOP_LOSS_RATIO = 0.04  # 动态止损比例大幅放宽到4.0% (解决0%胜率问题)

# 固定止损卖出信号（最终优化：大幅放宽止损比例）
ENABLE_FIXED_STOP_LOSS = True   # 是否启用固定止损卖出信号
FIXED_STOP_LOSS_RATIO = 0.04    # 固定止损比例大幅放宽到4.0% (解决0%胜率问题)

# 卖出信号检查间隔(分钟)  ``
SELL_SIGNAL_CHECK_INTERVAL = 5  # 卖出信号检查的时间间隔

# 最小持仓时间(天)
MIN_HOLDING_DAYS = 1          # 最小持仓天数，用于T+1规则

# 最大持仓时间(天)（跟踪止盈优化：大幅延长持仓时间）
MAX_HOLDING_DAYS = 30         # 延长到30天最大持仓 (让更多交易达到82.9%胜率)

# 时间止损（基于实际数据优化：禁用时间止损）
ENABLE_TIME_STOP_LOSS = False  # 禁用时间止损 (实际数据显示时间止损胜率0%)
TIME_STOP_LOSS_DAYS = 7       # 如果启用，延长到7天 (原3天过短)

# 卖出信号优先级（交易结构优化：让100%胜率的固定止盈绝对优先）
SELL_SIGNAL_PRIORITY = {
    'fixed_profit_stop': 1.0, # 最高优先级 (实际胜率100%!)
    'max_holding_days': 1.1,  # 第二优先级 (高胜率)
    'trailing_stop': 2.0,     # 大幅降低优先级 (实际胜率40.6%)
    'fixed_stop_loss': 3.0,   # 最低优先级
    'dynamic_stop_loss': 3.1, # 最低优先级
    'time_stop_loss': 3.5,    # 时间止损优先级最低 (已禁用)
    'trix_death_cross': 4     # TRIX死叉优先级最低
}

# 卖出信号组合模式
SELL_SIGNAL_MODE = 'any'      # 'any': 任一信号触发即卖出, 'all': 所有信号都触发才卖出

# 卖出信号日志记录
ENABLE_SELL_SIGNAL_LOG = True # 是否记录卖出信号日志
SELL_SIGNAL_LOG_LEVEL = 'INFO' # 卖出信号日志级别

# =============================================================================
# 持仓摘要配置
# =============================================================================

# 注释：已删除持仓摘要 - 仅用于监控，不影响交易逻辑

# 性能优化配置 

# =============================================================================
# 反弹买入策略配置说明
# =============================================================================
"""
反弹买入策略配置说明:

这是一个与移动止盈策略相反的买入策略，通过监控股票从最低价反弹的程度来产生买入信号。

- ENABLE_REBOUND_BUY: 是否启用反弹买入策略
  - 设为True时启用反弹买入信号检测
  - 设为False时禁用此类信号

- REBOUND_PERIOD: 监控最低价的天数
  - 在这个周期内寻找最低价点
  - 建议值为5-20天，视市场状况调整
  - 较短周期(5-10天)适合快速反应的交易
  - 较长周期(10-20天)适合中期趋势反转捕捉

- REBOUND_THRESHOLD: 反弹买入阈值
  - 股价从最低点反弹超过此比例时触发信号
  - 默认为0.025(2.5%)
  - 设置过小会产生过多信号，过大可能错过机会
  - 建议根据个股波动特性调整，高波动股票可设置更高值

- REBOUND_MAX_THRESHOLD: 反弹买入上限阈值
  - 股价从最低点反弹超过此比例时不再触发买入信号
  - 默认为0.08(8%)
  - 设置此上限可避免追高买入，控制风险
  - 建议根据市场环境和个股特性调整，震荡市可设置较低值(如5%)，强势市场可适当放宽(如10%)

- REBOUND_VOLUME_FILTER: 是否启用成交量确认
  - 设为True时，要求成交量配合价格反弹
  - 设为False时，仅考虑价格因素
  - 通常建议开启以避免无量反弹陷阱

- REBOUND_VOLUME_RATIO: 买入时成交量应大于均值的倍数
  - 仅在REBOUND_VOLUME_FILTER为True时生效
  - 当日成交量需大于N日平均成交量的倍数
  - 默认为1.5倍，表示比近期平均成交量放大50%

- REBOUND_MA_FILTER: 是否使用均线过滤
  - 设为True时，需要股价站上指定均线
  - 设为False时，不考虑均线位置
  - 建议开启以避免在下跌趋势中过早买入

- REBOUND_MA_PERIOD: 均线过滤周期
  - 仅在REBOUND_MA_FILTER为True时生效
  - 默认使用5日均线作为参考
  - 建议短期交易使用5日或10日，中期交易使用20日

- REBOUND_WITH_MARKET_FILTER: 是否考虑大盘环境
  - 设为True时，会参考大盘指数走势
  - 设为False时，只考虑个股情况
  - 建议开启以避免在大盘下跌过程中买入

- REBOUND_MIN_ATR_RATIO: 最小ATR比例
  - 要求股票的波动性(ATR)达到价格的一定比例
  - 过低的波动率可能导致反弹信号不明显
  - 默认为0.015(1.5%)，表示ATR至少达到价格的1.5%
"""

# =============================================================================
# 时序分析配置
# =============================================================================
enable_timeseries_analysis = False  # 性能优化：禁用时序分析

# ==================== 市场自适应策略 (基于数据驱动，尊重市场规律) ====================

# 启用市场自适应策略 (替代强制时间分散)
ENABLE_MARKET_ADAPTIVE_STRATEGY = True

# 基于CCI因子特征的自适应调整 (CCI在开盘时段高35.9%)
CCI_ADAPTIVE_CONFIG = {
    'enable': True,
    'opening_cci_avg': 23.7,               # 开盘时段CCI平均值
    'other_cci_avg': 17.4,                 # 其他时段CCI平均值

    # 基于CCI值动态调整阈值 (基于优化后的CCI[20,30]范围)
    'dynamic_thresholds': {
        'optimal_cci_zone': {              # CCI 20-30 (最优区间)
            'cci_min': 20,
            'cci_max': 30,
            'quality_multiplier': 0.8,     # 最优区间降低其他要求20%
        },
        'high_cci_zone': {                 # CCI > 30 (超出最优区间)
            'cci_min': 30,
            'quality_multiplier': 1.2,     # 质量要求提高20%
        },
        'low_cci_zone': {                  # CCI < 20 (低于最优区间)
            'cci_max': 20,
            'quality_multiplier': 1.3,     # 质量要求提高30%
        }
    }
}

# ==================== 紧急修复：时间分散配置 ====================

# 启用时间分散机制 (解决96.7%开盘信号集中问题)
ENABLE_TIME_DISTRIBUTION = True

# 时间分散配置
TIME_DISTRIBUTION_CONFIG = {
    'enable': True,
    'max_signals_per_hour': {
        '09:30-10:00': 10,                  # 开盘时段严格限制
        '10:00-11:30': 20,                  # 上午时段
        '13:00-14:30': 20,                  # 下午时段
        '14:30-15:00': 5,                   # 尾盘时段
    },
    'signal_cooldown': 300,                 # 信号冷却期5分钟
    'max_daily_signals': 50,                # 每日最大信号数
}

# 开盘时段特殊处理 (解决开盘信号过多问题)
OPENING_SPECIAL_CONFIG = {
    'enable': True,
    'opening_period': '09:30-10:00',
    'special_requirements': {
        'min_atr_pct': 2.0,                 # 开盘时段ATR要求适度降低
        'min_bb_width': 10.0,               # 开盘时段BB宽度要求适度降低
        'max_gap_ratio': 0.05,              # 最大跳空比例放宽到5%
        'factor_multiplier': 1.2,           # 因子要求适度提高20%
    }
}

# 启用高效因子策略 (基于8个最有效因子) - 修复配置键名
ENABLE_MULTIFACTOR_STRATEGY = True  # 保持原有键名，但使用新逻辑
ENABLE_EFFECTIVE_FACTORS_STRATEGY = True  # 备用键名

# 高效因子策略配置 (基于IC分析结果)
EFFECTIVE_FACTORS_CONFIG = {
    'enable': True,
    'factors': {
        'cci': {
            'weight': 0.10,             # 降低权重 0.170 → 0.10
            'direction': 'positive',
            'ic': 0.1107,
            'min_threshold': -40,       # CCI > -40 (基于高盈利CCI均值-9.0精准优化)
            'max_threshold': 120,       # CCI < 120 (基于回测结果精准调整)
            'optimization_note': '基于新系统回测优化：高盈利交易CCI均值-9.0，调整为[-40,120]提升信号质量',
        },
        'adx': {
            'weight': 0.08,             # 调整权重 0.162 → 0.08 (平衡权重分配)
            'direction': 'positive',
            'ic': 0.1056,
            'min_threshold': 28,        # ADX > 28 (基于高盈利ADX均值31.7渐进优化)
            'optimization_note': '基于新系统回测优化：高盈利交易ADX均值31.7，渐进提升到28增强趋势筛选',
        },
        'bb_position': {
            'weight': 0.141,
            'direction': 'positive',
            'ic': 0.0917,
            'min_threshold': 0.3,       # BB位置 > 0.3
            'max_threshold': 0.8,       # BB位置 < 0.8
        },
        'rsi': {
            'weight': 0.128,
            'direction': 'positive',
            'ic': 0.0832,
            'min_threshold': 35,        # RSI > 35 (回退到适中)
            'max_threshold': 75,        # RSI < 75 (回退到适中)
            'optimization_note': '回退过严配置，使用更平衡的RSI范围',
        },
        'macd_hist': {
            'weight': 0.125,
            'direction': 'positive',
            'ic': 0.0813,
            'min_threshold': -2.089,    # MACD柱 > -2.089 (基于最新数据优化)
            'optimization_note': '基于500条最新交易，P0-P25区间胜率55.2%，提升9.4%',
        },
        'macd': {
            'weight': 0.102,
            'direction': 'positive',
            'ic': 0.0665,
            'min_threshold': 0,         # MACD > 0
        },
        'bb_width': {
            'weight': 0.099,
            'direction': 'positive',
            'ic': 0.0644,
            'min_threshold': 10.0,      # BB宽度 > 10
        },
        'atr_pct': {
            'weight': 0.10,             # 提升权重 0.074 → 0.10
            'direction': 'positive',
            'ic': 0.0484,
            'min_threshold': 3.0,       # ATR > 3.0% (基于高盈利ATR均值3.9%重大优化)
            'max_threshold': 6.0,       # ATR < 6%
            'optimization_note': '基于新系统回测重大优化：高盈利交易ATR均值3.9%，提升到3.0%大幅改善信号质量',
        },
        'opening_momentum': {
            'weight': 0.08,             # 开盘动量因子权重
            'direction': 'positive',
            'min_threshold': 0.005,     # 开盘动量 > 0.5% (正向动量)
            'max_threshold': 0.05,      # 开盘动量 < 5% (避免过度跳空)
            'optimization_note': '新增实时因子：(当前价格-开盘价)/开盘价，捕捉开盘后价格动量',
        },
        # 基本面因子 (基于新系统集成)
        'pe_relative': {
            'weight': 0.06,             # PE相对值因子权重
            'direction': 'negative',    # PE相对值越低越好
            'max_threshold': 1.5,       # PE相对值不超过1.5
            'optimization_note': '基本面因子：PE相对市场平均值，低估值优选',
        },
        'roe_quality': {
            'weight': 0.05,             # ROE质量因子权重
            'direction': 'positive',    # ROE越高越好
            'min_threshold': 8,         # ROE最低8%
            'optimization_note': '基本面因子：ROE盈利能力评估，高质量企业优选',
        },
        'revenue_growth': {
            'weight': 0.04,             # 营收增长因子权重
            'direction': 'positive',    # 增长率越高越好
            'min_threshold': -10,       # 营收增长最低-10%
            'optimization_note': '基本面因子：营收同比增长率，成长性评估',
        },
        # 市场情绪因子 (基于新系统集成)
        'main_fund_persistence': {
            'weight': 0.07,             # 主力资金持续性权重
            'direction': 'positive',    # 持续性越高越好
            'min_threshold': 0.4,       # 主力资金持续性40%+
            'optimization_note': '情绪因子：主力资金流入持续性，资金面确认',
        },
        'market_attention': {
            'weight': 0.05,             # 市场关注度权重
            'direction': 'positive',    # 关注度越高越好
            'min_threshold': 1.2,       # 市场关注度1.2倍+
            'optimization_note': '情绪因子：相对成交量关注度，市场热度评估',
        },
        'volume_breakthrough': {
            'weight': 0.06,             # 成交量突破权重
            'direction': 'positive',    # 突破越明显越好
            'min_threshold': 1.5,       # 成交量突破1.5倍+
            'optimization_note': '情绪因子：成交量放大突破，资金介入确认',
        },
        # 跨市场因子 (基于新系统集成)
        'industry_relative_strength': {
            'weight': 0.04,             # 行业相对强度权重
            'direction': 'positive',    # 相对强度越高越好
            'min_threshold': 0,         # 行业相对强度为正
            'optimization_note': '跨市场因子：相对行业指数强度，板块轮动捕捉',
        },
        'market_beta': {
            'weight': 0.03,             # 市场Beta权重
            'direction': 'positive',    # 适中Beta为好
            'min_threshold': 0.8,       # 市场Beta适中下限
            'max_threshold': 1.5,       # 市场Beta适中上限
            'optimization_note': '跨市场因子：市场Beta系数，系统性风险评估',
        },
        'concept_heat': {
            'weight': 0.03,             # 概念热度权重
            'direction': 'positive',    # 热度越高越好
            'min_threshold': 0.3,       # 概念热度30%+
            'optimization_note': '跨市场因子：概念板块热度，题材轮动捕捉',
        },
    },
    'buy_conditions': {
        'min_combined_score': 0.52,            # 进一步提高阈值只保留最高质量 (从0.50到0.52)
        'min_factors_count': 4,                # 保持4个因子要求
        'require_top3_factors': False,         # 保持关闭避免过严
        'max_signals_per_stock': 1,            # 每只股票最多1个信号
        'optimization_note': '精英筛选：只保留最高质量信号，确保胜率显著提升',
    },

    # 多维度评分权重配置 (基于实际表现优化)
    'scoring_weights': {
        'technical_score': 0.35,               # 技术面权重35% (表现稳定，平均0.60)
        'fundamental_score': 0.30,             # 基本面权重30% (表现良好，平均0.55)
        'sentiment_score': 0.20,               # 情绪面权重20% (降低，表现不佳，平均0.34)
        'cross_market_score': 0.15,            # 跨市场权重15% (提高，相对稳定，平均0.53)
        'optimization_note': '基于实际表现重新平衡：降低sentiment_score权重，提高稳定因子权重',
    }
}

# 市场环境自适应配置 (动态优化系统)
MARKET_ENVIRONMENT_CONFIG = {
    'bull_market': {                           # 牛市配置
        'cci_weight_multiplier': 0.8,          # CCI权重降低
        'atr_threshold_multiplier': 0.8,       # ATR阈值降低 (2.4%)
        'adx_threshold_multiplier': 0.9,       # ADX阈值降低 (25.2)
        'min_combined_score': 0.50,            # 综合评分降低
        'sentiment_weight_boost': 1.2,         # 情绪面权重提升
        'optimization_note': '牛市配置：放宽筛选条件，增强情绪面权重，捕捉更多机会',
    },
    'bear_market': {                           # 熊市配置
        'cci_weight_multiplier': 1.2,          # CCI权重提升
        'atr_threshold_multiplier': 1.3,       # ATR阈值提升 (3.9%)
        'adx_threshold_multiplier': 1.25,      # ADX阈值提升 (35)
        'min_combined_score': 0.65,            # 综合评分提升
        'fundamental_weight_boost': 1.3,       # 基本面权重提升
        'optimization_note': '熊市配置：严格筛选条件，增强基本面权重，注重防御性',
    },
    'sideways_market': {                       # 震荡市配置
        'cci_weight_multiplier': 1.0,          # CCI权重保持
        'atr_threshold_multiplier': 1.1,       # ATR阈值略提升 (3.3%)
        'adx_threshold_multiplier': 1.1,       # ADX阈值略提升 (30.8)
        'min_combined_score': 0.58,            # 综合评分适中
        'cross_market_weight_boost': 1.5,      # 跨市场权重提升
        'optimization_note': '震荡市配置：适中筛选条件，增强跨市场权重，捕捉轮动机会',
    }
}

# 时间段差异化配置
TIME_BASED_CONFIG = {
    'morning_session': {                       # 上午时段 (9:30-11:30)
        'volume_weight_boost': 1.2,            # 成交量权重提升20%
        'momentum_weight_boost': 1.3,          # 动量权重提升30%
        'opening_momentum_boost': 1.5,         # 开盘动量权重提升50%
        'min_gap_threshold': 0.005,            # 跳空阈值降低到0.5%
        'optimization_note': '上午时段：增强动量和成交量权重，捕捉开盘活跃机会',
    },
    'afternoon_session': {                     # 下午时段 (13:00-15:00)
        'volume_weight_boost': 1.0,            # 成交量权重保持
        'momentum_weight_boost': 1.0,          # 动量权重保持
        'stability_weight_boost': 1.2,         # 稳定性权重提升20%
        'fundamental_weight_boost': 1.1,       # 基本面权重略提升
        'optimization_note': '下午时段：增强稳定性和基本面权重，注重持续性',
    }
}

# 动态权重调整配置
DYNAMIC_WEIGHT_CONFIG = {
    'enable': True,                            # 启用动态权重调整
    'adjustment_frequency': 'weekly',          # 每周调整一次
    'performance_window': 20,                  # 基于最近20笔交易表现
    'adjustment_rate': 0.1,                    # 每次调整幅度10%
    'min_weight': 0.02,                        # 最小权重2%
    'max_weight': 0.20,                        # 最大权重20%
    'performance_threshold': 0.6,              # 表现阈值60%胜率
    'optimization_note': '动态权重：基于实际表现自动调整因子权重，持续优化',
}
timeseries_lookback_hours = 24
timeseries_weight = 0.3

# =============================================================================
# 多因子综合策略配置 (新增)
# =============================================================================

# 多因子策略总开关 (已在上方定义，此处注释掉避免重复)
# ENABLE_MULTIFACTOR_STRATEGY = True          # 启用多因子综合策略

# 多因子评分阈值 (基于市场自适应策略)
MULTIFACTOR_THRESHOLDS = {
    'min_overall_score': 0.02,              # 适中阈值，配合技术指标筛选
    'min_technical_score': 0.02,            # 适中阈值，配合技术指标筛选
    'min_momentum_score': 0.02,             # 适中阈值，配合技术指标筛选
    'min_volume_score': 0.00,               # 保持0.00 (成交量因子效果一般)
    'min_volatility_score': 0.02,           # 适中阈值，配合技术指标筛选
    'min_trend_score': 0.02,                # 适中阈值，配合技术指标筛选
    'min_buy_signal_strength': 0.02,        # 适中阈值，配合技术指标筛选
    'min_risk_adjusted_score': 0.02,        # 适中阈值，配合技术指标筛选
}

# 多因子确认条件 (紧急修复：大幅简化确认条件)
MULTIFACTOR_CONFIRMATIONS = {
    'require_multiple_scores': True,         # 要求多个评分同时满足
    'min_score_count': 1,                   # 从2降低到1 (因为大部分因子失效)
    'require_technical_confirmation': False, # 暂时禁用 (技术因子可能有问题)
    'require_momentum_confirmation': False,  # 保持禁用 (动量因子可能有问题)
    'require_volume_confirmation': False,    # 保持禁用 (成交量因子可能无效)
}

# 多因子权重配置 (用于综合评分计算)
MULTIFACTOR_WEIGHTS = {
    'overall_score_weight': 0.30,           # 综合评分权重
    'technical_score_weight': 0.25,         # 技术评分权重
    'momentum_score_weight': 0.20,          # 动量评分权重
    'volume_score_weight': 0.15,            # 成交量评分权重
    'trend_score_weight': 0.10,             # 趋势评分权重
}

# 多因子风险控制
MULTIFACTOR_RISK_CONTROL = {
    'max_position_size': 0.02,              # 最大单笔仓位 2%
    'enable_dynamic_position': True,        # 启用动态仓位调整
    'position_size_factor': 0.8,            # 仓位调整因子
    'max_daily_signals': 5,                 # 每日最大信号数
    'min_signal_interval_hours': 4,         # 信号间隔4小时
}