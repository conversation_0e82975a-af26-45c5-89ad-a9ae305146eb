# coding=utf-8
"""
测试数据完整性修复功能
"""

import sys
import os
import datetime
import sqlite3

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主策略模块中的数据完整性函数
try:
    from main import clean_existing_future_function_data, get_config_value
    print("✅ 成功导入数据完整性函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_data_integrity_repair():
    """测试数据完整性修复功能"""
    print('🔧 测试数据完整性修复功能')
    print('=' * 60)
    
    try:
        # 1. 检查数据库状态
        print('\n📊 1. 检查数据库状态')
        db_path = 'data/trades.db'
        
        if not os.path.exists(db_path):
            print(f'❌ 数据库文件不存在: {db_path}')
            return False
        
        conn = sqlite3.connect(db_path)
        
        # 检查总记录数
        total_query = "SELECT COUNT(*) FROM trades"
        total_count = conn.execute(total_query).fetchone()[0]
        print(f'📊 总记录数: {total_count}')
        
        # 检查收盘前记录数
        before_close_query = '''
            SELECT COUNT(*) FROM trades 
            WHERE strftime('%H:%M', timestamp) < '14:30'
            AND action = 'BUY'
        '''
        before_close_count = conn.execute(before_close_query).fetchone()[0]
        print(f'📊 收盘前买入记录数: {before_close_count}')
        
        # 检查可能存在未来函数的字段
        risk_fields = [
            'volume_ma5_ratio', 'volume_ma10_ratio', 'relative_volume',
            'money_flow_5d', 'money_flow_10d'
        ]
        
        print('\n🔍 2. 检查风险字段使用情况')
        for field in risk_fields:
            try:
                field_query = f'''
                    SELECT COUNT(*) FROM trades 
                    WHERE strftime('%H:%M', timestamp) < '14:30'
                    AND action = 'BUY'
                    AND {field} IS NOT NULL
                '''
                field_count = conn.execute(field_query).fetchone()[0]
                print(f'  {field}: {field_count} 条记录')
            except Exception as e:
                print(f'  {field}: 字段不存在或查询失败')
        
        conn.close()
        
        # 2. 执行数据修复
        print('\n🔧 3. 执行数据修复')
        repair_success = clean_existing_future_function_data()
        
        if repair_success:
            print('✅ 数据修复完成')
        else:
            print('❌ 数据修复失败')
            return False
        
        # 3. 验证修复结果
        print('\n✅ 4. 验证修复结果')
        conn = sqlite3.connect(db_path)
        
        for field in risk_fields:
            try:
                field_query = f'''
                    SELECT COUNT(*) FROM trades 
                    WHERE strftime('%H:%M', timestamp) < '14:30'
                    AND action = 'BUY'
                    AND {field} IS NOT NULL
                '''
                field_count = conn.execute(field_query).fetchone()[0]
                print(f'  修复后 {field}: {field_count} 条记录')
            except Exception as e:
                print(f'  {field}: 字段不存在或查询失败')
        
        # 检查是否添加了修复标记
        try:
            marker_query = '''
                SELECT COUNT(*) FROM trades 
                WHERE future_function_cleaned = 1
            '''
            marker_count = conn.execute(marker_query).fetchone()[0]
            print(f'📊 已标记为清理的记录数: {marker_count}')
        except Exception as e:
            print(f'⚠️ 清理标记字段不存在: {e}')
        
        conn.close()
        
        print('\n🎉 数据完整性修复测试完成')
        return True
        
    except Exception as e:
        print(f'❌ 测试过程出现异常: {e}')
        return False

def test_config_loading():
    """测试配置加载"""
    print('\n📋 测试配置加载')
    print('-' * 40)
    
    try:
        # 测试数据完整性配置
        data_integrity_config = get_config_value('DATA_INTEGRITY_CONFIG', {})
        print(f'数据完整性配置: {data_integrity_config}')
        
        # 测试风险字段配置
        risk_fields = get_config_value('FUTURE_FUNCTION_RISK_FIELDS', [])
        print(f'风险字段数量: {len(risk_fields)}')
        print(f'风险字段: {risk_fields[:5]}...' if len(risk_fields) > 5 else f'风险字段: {risk_fields}')
        
        # 测试安全指标配置
        safe_indicators = get_config_value('SAFE_INDICATORS_CONFIG', {})
        print(f'安全指标配置类别数: {len(safe_indicators)}')
        
        return True
        
    except Exception as e:
        print(f'❌ 配置加载失败: {e}')
        return False

def main():
    """主函数"""
    print('🔍 数据完整性修复测试')
    print('=' * 80)
    
    # 测试配置加载
    config_success = test_config_loading()
    
    if not config_success:
        print('❌ 配置加载测试失败，退出')
        return
    
    # 测试数据修复
    repair_success = test_data_integrity_repair()
    
    if repair_success:
        print('\n🎉 所有测试通过！')
        print('\n📋 下一步建议:')
        print('1. 重新运行胜率分析验证修复效果')
        print('2. 检查策略在修复后的表现')
        print('3. 监控未来的数据记录是否正确应用保护机制')
    else:
        print('\n❌ 测试失败，请检查错误信息')

if __name__ == "__main__":
    main()
