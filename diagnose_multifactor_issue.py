# coding=utf-8
"""
诊断多因子策略买入问题
分析日志和数据库记录，找出多因子策略不买入的原因
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime, timedelta

def check_recent_trades():
    """检查最近的交易记录"""
    print('📊 最近交易记录检查')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 检查最近24小时的买入记录
        yesterday = datetime.now() - timedelta(hours=24)
        query = """
        SELECT timestamp, symbol, action, buy_signal_type, signal_reason, overall_score
        FROM trades 
        WHERE action = 'BUY' AND timestamp >= ?
        ORDER BY timestamp DESC
        LIMIT 20
        """
        
        df = pd.read_sql_query(query, conn, params=[yesterday.strftime('%Y-%m-%d %H:%M:%S')])
        
        print(f'📈 最近24小时买入记录: {len(df)} 条')
        
        if len(df) > 0:
            print(f'\n📋 最近买入记录:')
            for i, row in df.iterrows():
                signal_type = row.get('buy_signal_type', 'unknown')
                score = row.get('overall_score', 'N/A')
                print(f'   {row["timestamp"][:16]} {row["symbol"]} 类型:{signal_type} 评分:{score}')
            
            # 统计信号类型
            signal_types = df['buy_signal_type'].value_counts()
            print(f'\n📊 信号类型统计:')
            for signal_type, count in signal_types.items():
                print(f'   {signal_type}: {count}条')
        else:
            print('⚠️ 最近24小时没有买入记录')
        
        # 检查总的买入记录数
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
        total_buys = cursor.fetchone()[0]
        print(f'\n📊 历史总买入记录: {total_buys} 条')
        
        # 检查多因子信号
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY' AND buy_signal_type = 'multifactor_comprehensive'")
        multifactor_buys = cursor.fetchone()[0]
        print(f'🎯 多因子策略买入: {multifactor_buys} 条')
        
        conn.close()
        return df
        
    except Exception as e:
        print(f'❌ 检查交易记录失败: {e}')
        return None

def check_log_files():
    """检查日志文件"""
    print(f'\n📝 日志文件检查')
    print('=' * 40)
    
    # 查找可能的日志文件
    log_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.log') or 'log' in file.lower():
                log_files.append(os.path.join(root, file))
    
    print(f'📁 找到日志文件: {len(log_files)} 个')
    for log_file in log_files:
        print(f'   {log_file}')
    
    # 检查最近的日志内容
    if log_files:
        latest_log = max(log_files, key=os.path.getmtime)
        print(f'\n📖 最新日志文件: {latest_log}')
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f'📊 日志行数: {len(lines)}')
            
            # 查找多因子相关的日志
            multifactor_lines = []
            for i, line in enumerate(lines[-200:], len(lines)-200):  # 检查最后200行
                if '多因子' in line or 'multifactor' in line.lower():
                    multifactor_lines.append((i+1, line.strip()))
            
            if multifactor_lines:
                print(f'\n🎯 多因子相关日志 (最近{len(multifactor_lines)}条):')
                for line_num, content in multifactor_lines[-10:]:  # 显示最后10条
                    print(f'   {line_num}: {content}')
            else:
                print(f'⚠️ 未找到多因子相关日志')
            
            # 查找错误信息
            error_lines = []
            for i, line in enumerate(lines[-100:], len(lines)-100):  # 检查最后100行
                if '错误' in line or 'error' in line.lower() or '异常' in line or 'exception' in line.lower():
                    error_lines.append((i+1, line.strip()))
            
            if error_lines:
                print(f'\n❌ 错误信息 (最近{len(error_lines)}条):')
                for line_num, content in error_lines[-5:]:  # 显示最后5条错误
                    print(f'   {line_num}: {content}')
            
        except Exception as e:
            print(f'❌ 读取日志文件失败: {e}')
    else:
        print('⚠️ 未找到日志文件')

def check_factor_data():
    """检查因子数据"""
    print(f'\n🔧 因子数据检查')
    print('=' * 40)
    
    try:
        from enhanced_factor_engine import EnhancedFactorEngine
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=60, freq='D')
        np.random.seed(42)
        
        test_data = pd.DataFrame({
            'open': 100 + np.random.randn(60).cumsum(),
            'high': 100 + np.random.randn(60).cumsum() + 2,
            'low': 100 + np.random.randn(60).cumsum() - 2,
            'close': 100 + np.random.randn(60).cumsum(),
            'volume': np.random.randint(1000, 10000, 60)
        }, index=dates)
        
        # 测试因子计算
        engine = EnhancedFactorEngine()
        factors = engine.calculate_all_factors(test_data, 'TEST.000001')
        
        print(f'✅ 因子引擎工作正常')
        print(f'📊 计算因子数: {len(factors)}')
        
        # 检查关键评分
        key_scores = ['overall_score', 'technical_score', 'momentum_score', 'volume_score', 'buy_signal_strength']
        print(f'\n📋 关键评分:')
        for score in key_scores:
            value = factors.get(score, 'N/A')
            print(f'   {score}: {value}')
        
        # 模拟多因子检查
        from config import get_config_value
        thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
        
        print(f'\n🎯 多因子阈值检查:')
        satisfied = 0
        for score in key_scores:
            if score in factors and score.replace('_score', '') in ['overall', 'technical', 'momentum', 'volume'] or score == 'buy_signal_strength':
                threshold_key = f'min_{score}'
                threshold = thresholds.get(threshold_key, 0.5)
                value = factors.get(score, 0)
                
                if value >= threshold:
                    satisfied += 1
                    print(f'   ✅ {score}: {value:.2f} >= {threshold:.2f}')
                else:
                    print(f'   ❌ {score}: {value:.2f} < {threshold:.2f}')
        
        min_count = get_config_value('MULTIFACTOR_CONFIRMATIONS', {}).get('min_score_count', 4)
        print(f'\n🎯 满足条件: {satisfied}/{len(key_scores)}, 要求: {min_count}')
        
        if satisfied >= min_count:
            print(f'✅ 测试数据满足多因子条件')
        else:
            print(f'❌ 测试数据不满足多因子条件')
        
        return factors
        
    except Exception as e:
        print(f'❌ 因子数据检查失败: {e}')
        import traceback
        print(f'详细错误: {traceback.format_exc()}')
        return None

def check_strategy_execution():
    """检查策略执行情况"""
    print(f'\n🚀 策略执行检查')
    print('=' * 40)
    
    from config import get_config_value
    
    # 检查关键配置
    configs_to_check = [
        ('ENABLE_MULTIFACTOR_STRATEGY', '多因子策略开关'),
        ('SMART_SCORING_CONFIG', '智能评分配置'),
        ('ENABLE_TRIX_BUY_SIGNAL', 'TRIX策略开关'),
        ('enable_timeseries_analysis', '时序分析开关')
    ]
    
    print(f'⚙️ 关键配置检查:')
    for config_key, description in configs_to_check:
        value = get_config_value(config_key, 'NOT_FOUND')
        print(f'   {description}: {value}')
    
    # 检查多因子阈值
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    print(f'\n📊 多因子阈值:')
    if thresholds:
        for key, value in thresholds.items():
            print(f'   {key}: {value}')
    else:
        print(f'   ❌ 多因子阈值配置缺失')
    
    # 检查确认条件
    confirmations = get_config_value('MULTIFACTOR_CONFIRMATIONS', {})
    print(f'\n✅ 确认条件:')
    if confirmations:
        for key, value in confirmations.items():
            print(f'   {key}: {value}')
    else:
        print(f'   ❌ 确认条件配置缺失')

def check_prefilter_results():
    """检查预筛选结果"""
    print(f'\n🔍 预筛选结果检查')
    print('=' * 40)
    
    try:
        # 检查是否有预筛选相关的数据
        conn = sqlite3.connect('data/trades.db')
        
        # 查看最近的分析记录
        query = """
        SELECT timestamp, symbol, trix_buy, overall_score, technical_score
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC
        LIMIT 10
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) > 0:
            print(f'📊 最近分析的股票:')
            for i, row in df.iterrows():
                print(f'   {row["symbol"]} - TRIX:{row.get("trix_buy", "N/A")} 综合评分:{row.get("overall_score", "N/A")}')
        else:
            print(f'⚠️ 没有找到最近的分析记录')
        
        # 检查时序数据库
        if os.path.exists('data/timeseries_factors.db'):
            ts_conn = sqlite3.connect('data/timeseries_factors.db')
            cursor = ts_conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM timeseries_factors")
            ts_count = cursor.fetchone()[0]
            print(f'📊 时序数据库记录: {ts_count} 条')
            
            if ts_count > 0:
                cursor.execute("SELECT symbol, COUNT(*) as count FROM timeseries_factors GROUP BY symbol ORDER BY count DESC LIMIT 5")
                top_symbols = cursor.fetchall()
                print(f'📈 时序数据最多的股票:')
                for symbol, count in top_symbols:
                    print(f'   {symbol}: {count}条记录')
            
            ts_conn.close()
        else:
            print(f'⚠️ 时序数据库不存在')
        
    except Exception as e:
        print(f'❌ 预筛选结果检查失败: {e}')

def suggest_solutions():
    """提出解决方案"""
    print(f'\n💡 问题诊断与解决方案')
    print('=' * 50)
    
    solutions = [
        {
            'problem': '多因子策略阈值过高',
            'symptoms': ['没有股票满足多因子条件', '所有评分都低于阈值'],
            'solution': [
                '降低MULTIFACTOR_THRESHOLDS中的阈值',
                '将min_overall_score从0.75降到0.65',
                '将min_technical_score从0.70降到0.60',
                '将min_score_count从4降到3'
            ]
        },
        {
            'problem': '因子计算异常',
            'symptoms': ['因子引擎报错', '评分为0或NaN'],
            'solution': [
                '检查数据质量和完整性',
                '确认enhanced_factor_engine.py正常工作',
                '检查股票数据是否足够(至少60天)'
            ]
        },
        {
            'problem': '策略未正确启用',
            'symptoms': ['配置显示未启用', '日志无多因子信息'],
            'solution': [
                '确认ENABLE_MULTIFACTOR_STRATEGY = True',
                '重启策略程序',
                '检查main.py中的多因子逻辑'
            ]
        },
        {
            'problem': '预筛选过滤太严格',
            'symptoms': ['没有股票进入分析阶段', '预筛选结果为空'],
            'solution': [
                '多因子策略不依赖TRIX预筛选',
                '修改预筛选逻辑，为多因子策略提供更多候选',
                '或者跳过预筛选，直接分析所有股票'
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f'\n{i}. {solution["problem"]}:')
        print(f'   症状: {", ".join(solution["symptoms"])}')
        print(f'   解决方案:')
        for step in solution['solution']:
            print(f'     • {step}')

def main():
    """主函数"""
    print('🔍 多因子策略买入问题诊断')
    print('=' * 60)
    
    # 检查最近交易记录
    recent_trades = check_recent_trades()
    
    # 检查日志文件
    check_log_files()
    
    # 检查因子数据
    factors = check_factor_data()
    
    # 检查策略执行
    check_strategy_execution()
    
    # 检查预筛选结果
    check_prefilter_results()
    
    # 提出解决方案
    suggest_solutions()
    
    print(f'\n🎯 诊断总结')
    print('=' * 40)
    
    if recent_trades is not None and len(recent_trades) > 0:
        multifactor_count = len(recent_trades[recent_trades['buy_signal_type'] == 'multifactor_comprehensive'])
        if multifactor_count > 0:
            print(f'✅ 多因子策略正在工作: {multifactor_count}条记录')
        else:
            print(f'❌ 多因子策略未产生买入信号')
            print(f'💡 主要可能原因:')
            print(f'   1. 阈值设置过高，没有股票满足条件')
            print(f'   2. 预筛选过滤太严格，候选股票太少')
            print(f'   3. 市场环境不佳，整体评分偏低')
    else:
        print(f'❌ 最近没有任何买入记录')
        print(f'💡 可能原因:')
        print(f'   1. 策略未正常运行')
        print(f'   2. 所有策略的条件都不满足')
        print(f'   3. 数据或配置问题')
    
    print(f'\n🚀 立即行动建议:')
    print(f'   1. 检查策略是否正在运行')
    print(f'   2. 适当降低多因子阈值')
    print(f'   3. 观察实时日志输出')
    print(f'   4. 验证因子计算是否正常')

if __name__ == '__main__':
    main()
