# coding=utf-8
"""
时序因子分析MVP版本启动脚本
启动和测试时序因子分析系统
"""

import sys
import time
from datetime import datetime
import pandas as pd
import sqlite3
from timeseries_factor_collector import TimeSeriesFactorCollector
from timeseries_pattern_analyzer import TimeSeriesPatternAnalyzer
from timeseries_strategy_integrator import TimeSeriesStrategyIntegrator

def test_database_setup():
    """测试数据库设置"""
    print('🔧 测试数据库设置')
    print('=' * 50)
    
    try:
        # 测试时序数据库
        collector = TimeSeriesFactorCollector()
        print('✅ 时序数据库初始化成功')
        
        # 检查表结构
        conn = sqlite3.connect('data/timeseries_factors.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f'📊 数据库表: {[table[0] for table in tables]}')
        
        # 检查字段
        cursor.execute("PRAGMA table_info(timeseries_factors)")
        columns = cursor.fetchall()
        print(f'📋 字段数量: {len(columns)}')
        
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ 数据库设置失败: {e}')
        return False

def test_factor_collection():
    """测试因子采集"""
    print('\n📊 测试因子采集')
    print('=' * 50)
    
    try:
        collector = TimeSeriesFactorCollector()
        
        # 执行一次采集
        print('🔄 执行一次因子采集测试...')
        success, total = collector.collect_all_factors()
        
        print(f'📈 采集结果: {success}/{total} 成功')
        
        if success > 0:
            # 检查数据
            conn = sqlite3.connect('data/timeseries_factors.db')
            query = "SELECT COUNT(*) FROM timeseries_factors"
            count = pd.read_sql_query(query, conn).iloc[0, 0]
            conn.close()
            
            print(f'💾 数据库记录数: {count}')
            return True
        else:
            print('⚠️ 没有成功采集到数据')
            return False
            
    except Exception as e:
        print(f'❌ 因子采集测试失败: {e}')
        return False

def test_pattern_analysis():
    """测试模式分析"""
    print('\n🔍 测试模式分析')
    print('=' * 50)
    
    try:
        analyzer = TimeSeriesPatternAnalyzer()
        
        # 获取测试股票
        try:
            conn = sqlite3.connect('data/trades.db')
            query = "SELECT DISTINCT symbol FROM trades WHERE action = 'BUY' LIMIT 3"
            df = pd.read_sql_query(query, conn)
            conn.close()
            test_symbols = df['symbol'].tolist()
        except:
            test_symbols = ['SHSE.600036', 'SZSE.000001', 'SHSE.600519']
        
        print(f'📊 测试股票: {test_symbols[:2]}')  # 只测试前2只
        
        success_count = 0
        for symbol in test_symbols[:2]:
            try:
                print(f'\n🔍 分析 {symbol}:')
                
                # 综合模式分析
                result = analyzer.comprehensive_pattern_analysis(symbol)
                
                if 'error' not in result:
                    print(f'   ✅ 分析成功')
                    print(f'   📊 综合得分: {result["overall_score"]:.2f}')
                    print(f'   💡 投资建议: {result["recommendation"]}')
                    success_count += 1
                else:
                    print(f'   ❌ 分析失败: {result["error"]}')
                    
            except Exception as e:
                print(f'   ❌ 分析{symbol}时出错: {e}')
        
        print(f'\n📈 模式分析结果: {success_count}/{len(test_symbols[:2])} 成功')
        return success_count > 0
        
    except Exception as e:
        print(f'❌ 模式分析测试失败: {e}')
        return False

def test_strategy_integration():
    """测试策略集成"""
    print('\n🔗 测试策略集成')
    print('=' * 50)
    
    try:
        integrator = TimeSeriesStrategyIntegrator()
        
        # 模拟信号数据
        test_signal_data = {
            'atr_pct': 3.2,
            'bb_width': 12.5,
            'macd_hist': 0.15,
            'macd': -0.05,
            'rsi': 45.0,
            'volume': 50000
        }
        
        # 获取测试股票
        try:
            conn = sqlite3.connect('data/trades.db')
            query = "SELECT DISTINCT symbol FROM trades WHERE action = 'BUY' LIMIT 2"
            df = pd.read_sql_query(query, conn)
            conn.close()
            test_symbols = df['symbol'].tolist()
        except:
            test_symbols = ['SHSE.600036', 'SZSE.000001']
        
        print(f'📊 测试股票: {test_symbols}')
        
        success_count = 0
        for symbol in test_symbols:
            try:
                print(f'\n🔍 测试 {symbol}:')
                
                # 测试买入决策
                should_buy, reason = integrator.should_buy_with_timeseries(symbol, test_signal_data)
                print(f'   💡 买入决策: {"✅ 买入" if should_buy else "❌ 观察"}')
                print(f'   📝 决策理由: {reason[:100]}...' if len(reason) > 100 else f'   📝 决策理由: {reason}')
                
                # 测试动态阈值
                thresholds = integrator.get_dynamic_thresholds(symbol)
                print(f'   🎯 动态ATR阈值: {thresholds.get("atr_threshold", "N/A")}')
                
                success_count += 1
                
            except Exception as e:
                print(f'   ❌ 测试{symbol}时出错: {e}')
        
        print(f'\n📈 策略集成结果: {success_count}/{len(test_symbols)} 成功')
        return success_count > 0
        
    except Exception as e:
        print(f'❌ 策略集成测试失败: {e}')
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print('🧪 时序因子分析MVP版本综合测试')
    print('=' * 60)
    
    test_results = []
    
    # 1. 数据库设置测试
    print('1️⃣ 数据库设置测试')
    db_result = test_database_setup()
    test_results.append(('数据库设置', db_result))
    
    if not db_result:
        print('❌ 数据库设置失败，无法继续测试')
        return False
    
    # 2. 因子采集测试
    print('\n2️⃣ 因子采集测试')
    collection_result = test_factor_collection()
    test_results.append(('因子采集', collection_result))
    
    # 3. 模式分析测试
    print('\n3️⃣ 模式分析测试')
    analysis_result = test_pattern_analysis()
    test_results.append(('模式分析', analysis_result))
    
    # 4. 策略集成测试
    print('\n4️⃣ 策略集成测试')
    integration_result = test_strategy_integration()
    test_results.append(('策略集成', integration_result))
    
    # 显示测试结果
    print('\n📊 测试结果总结')
    print('=' * 50)
    
    success_count = 0
    for test_name, result in test_results:
        status = '✅ 通过' if result else '❌ 失败'
        print(f'{test_name}: {status}')
        if result:
            success_count += 1
    
    overall_success = success_count == len(test_results)
    print(f'\n🎯 总体结果: {success_count}/{len(test_results)} 测试通过')
    
    if overall_success:
        print('🎉 MVP版本测试完全成功！')
        print('✅ 时序因子分析系统已准备就绪')
    else:
        print('⚠️ 部分测试失败，请检查相关组件')
    
    return overall_success

def start_production_mode():
    """启动生产模式"""
    print('\n🚀 启动生产模式')
    print('=' * 50)
    
    try:
        # 创建集成器
        integrator = TimeSeriesStrategyIntegrator()
        
        # 启动时序数据采集
        print('📊 启动时序因子数据采集...')
        if integrator.start_timeseries_collection():
            print('✅ 时序数据采集已启动')
            print('⏰ 系统将每15分钟采集一次因子数据')
            print('🔍 时序模式分析已就绪')
            print('🎯 动态阈值计算已激活')
            
            print('\n💡 使用说明:')
            print('   1. 系统现在会自动采集时序因子数据')
            print('   2. 在买入决策时会自动进行时序模式分析')
            print('   3. 动态ATR阈值会根据历史数据自动调整')
            print('   4. 可以通过TimeSeriesStrategyIntegrator集成到现有策略')
            
            print('\n🛑 停止方法:')
            print('   调用 integrator.stop_timeseries_collection()')
            
            return integrator
        else:
            print('❌ 启动时序数据采集失败')
            return None
            
    except Exception as e:
        print(f'❌ 启动生产模式失败: {e}')
        return None

def show_usage_examples():
    """显示使用示例"""
    print('\n📖 使用示例')
    print('=' * 50)
    
    examples = [
        {
            'title': '在现有策略中集成时序分析',
            'code': '''
# 在main.py的买入逻辑中添加
from timeseries_strategy_integrator import TimeSeriesStrategyIntegrator

# 初始化集成器
timeseries_integrator = TimeSeriesStrategyIntegrator(context)

# 在买入决策时使用
def enhanced_buy_decision(symbol, signal_data):
    # 原始买入逻辑
    original_should_buy = original_buy_logic(symbol, signal_data)
    
    if original_should_buy:
        # 使用时序分析增强决策
        enhanced_should_buy, reason = timeseries_integrator.should_buy_with_timeseries(
            symbol, signal_data
        )
        return enhanced_should_buy, reason
    
    return False, "原始信号不满足"
'''
        },
        {
            'title': '获取动态阈值',
            'code': '''
# 获取动态ATR阈值
integrator = TimeSeriesStrategyIntegrator()
thresholds = integrator.get_dynamic_thresholds(symbol)
dynamic_atr_threshold = thresholds['atr_threshold']

# 在买入条件中使用动态阈值
if signal_data['atr_pct'] > dynamic_atr_threshold:
    # 满足动态ATR条件
    pass
'''
        },
        {
            'title': '手动执行模式分析',
            'code': '''
# 手动分析特定股票
from timeseries_pattern_analyzer import TimeSeriesPatternAnalyzer

analyzer = TimeSeriesPatternAnalyzer()
result = analyzer.comprehensive_pattern_analysis('SHSE.600036')

print(f"综合得分: {result['overall_score']}")
print(f"投资建议: {result['recommendation']}")
'''
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f'\n{i}. {example["title"]}:')
        print(example['code'])

def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'test':
            # 测试模式
            run_comprehensive_test()
        elif mode == 'start':
            # 启动生产模式
            integrator = start_production_mode()
            if integrator:
                try:
                    print('\n⌨️ 按 Ctrl+C 停止系统')
                    while True:
                        time.sleep(60)
                except KeyboardInterrupt:
                    print('\n🛑 收到停止信号')
                    integrator.stop_timeseries_collection()
                    print('✅ 系统已停止')
        elif mode == 'examples':
            # 显示使用示例
            show_usage_examples()
        else:
            print('❌ 未知模式，请使用: test, start, 或 examples')
    else:
        # 默认运行测试
        print('🎯 默认运行测试模式')
        print('💡 可用模式: test, start, examples')
        print('   python start_timeseries_mvp.py test     # 运行测试')
        print('   python start_timeseries_mvp.py start    # 启动生产模式')
        print('   python start_timeseries_mvp.py examples # 显示使用示例')
        print('')
        
        # 运行测试
        run_comprehensive_test()

if __name__ == '__main__':
    main()
