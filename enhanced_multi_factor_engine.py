# coding=utf-8
"""
增强多因子计算引擎
集成46个多维度因子的完整计算引擎
"""

import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
import logging
from config import EFFECTIVE_FACTORS_CONFIG, MARKET_ENVIRONMENT_CONFIG, TIME_BASED_CONFIG

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMultiFactorEngine:
    """增强多因子计算引擎"""
    
    def __init__(self, context=None):
        self.context = context
        self.config = EFFECTIVE_FACTORS_CONFIG
        
    def calculate_all_enhanced_factors(self, hist_data, symbol):
        """计算所有增强因子"""
        logger.info(f"开始计算 {symbol} 的46个多维度因子")
        
        if len(hist_data) < 30:
            logger.warning(f"{symbol} 历史数据不足30天，跳过")
            return {}
        
        all_factors = {}
        
        try:
            # 1. 技术面因子 (原有+增强)
            technical_factors = self._calculate_enhanced_technical_factors(hist_data)
            all_factors.update(technical_factors)
            
            # 2. 基本面因子 (新增)
            fundamental_factors = self._calculate_fundamental_factors(hist_data, symbol)
            all_factors.update(fundamental_factors)
            
            # 3. 市场情绪因子 (新增)
            sentiment_factors = self._calculate_sentiment_factors(hist_data, symbol)
            all_factors.update(sentiment_factors)
            
            # 4. 跨市场因子 (新增)
            cross_market_factors = self._calculate_cross_market_factors(hist_data, symbol)
            all_factors.update(cross_market_factors)
            
            # 5. 实时因子 (增强)
            realtime_factors = self._calculate_enhanced_realtime_factors(hist_data, symbol)
            all_factors.update(realtime_factors)
            
            # 6. 多维度综合评分
            composite_scores = self._calculate_multi_dimensional_scores(all_factors)
            all_factors.update(composite_scores)
            
            logger.info(f"✅ {symbol} 因子计算完成，共 {len(all_factors)} 个因子")
            
            return all_factors
            
        except Exception as e:
            logger.error(f"❌ {symbol} 因子计算失败: {e}")
            return {}
    
    def _calculate_enhanced_technical_factors(self, hist_data):
        """计算增强技术因子"""
        factors = {}
        
        try:
            close_prices = hist_data['close'].values
            high_prices = hist_data['high'].values
            low_prices = hist_data['low'].values
            volume_data = hist_data['volume'].values
            
            if len(close_prices) < 20:
                return factors
            
            # 1. 多周期RSI
            factors['rsi_6'] = talib.RSI(close_prices, timeperiod=6)[-1] if len(close_prices) >= 6 else np.nan
            factors['rsi_14'] = talib.RSI(close_prices, timeperiod=14)[-1] if len(close_prices) >= 14 else np.nan
            factors['rsi_21'] = talib.RSI(close_prices, timeperiod=21)[-1] if len(close_prices) >= 21 else np.nan
            
            # 2. 自适应RSI
            volatility = np.std(np.diff(close_prices[-20:])) / np.mean(close_prices[-20:])
            adaptive_period = max(6, min(30, int(14 * (1 + volatility))))
            factors['rsi_adaptive'] = talib.RSI(close_prices, timeperiod=adaptive_period)[-1]
            
            # 3. CCI多周期
            factors['cci_14'] = talib.CCI(high_prices, low_prices, close_prices, timeperiod=14)[-1]
            factors['cci_20'] = talib.CCI(high_prices, low_prices, close_prices, timeperiod=20)[-1]
            
            # 4. ADX趋势强度
            factors['adx_14'] = talib.ADX(high_prices, low_prices, close_prices, timeperiod=14)[-1]
            
            # 5. ATR波动率
            atr_14 = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)[-1]
            factors['atr_14'] = atr_14
            factors['atr_pct'] = (atr_14 / close_prices[-1] * 100) if atr_14 and close_prices[-1] else np.nan
            
            # 6. 布林带增强
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close_prices, timeperiod=20)
            if len(bb_upper) > 0:
                factors['bb_upper'] = bb_upper[-1]
                factors['bb_middle'] = bb_middle[-1]
                factors['bb_lower'] = bb_lower[-1]
                factors['bb_width'] = (bb_upper[-1] - bb_lower[-1]) / bb_middle[-1] * 100
                factors['bb_position'] = (close_prices[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) * 100
                factors['bb_squeeze'] = 1 if factors['bb_width'] < 10 else 0  # 布林带收窄
            
            # 7. MACD增强
            macd, macd_signal, macd_hist = talib.MACD(close_prices)
            if len(macd) > 0:
                factors['macd'] = macd[-1]
                factors['macd_signal'] = macd_signal[-1]
                factors['macd_hist'] = macd_hist[-1]
                factors['macd_cross'] = 1 if macd[-1] > macd_signal[-1] and macd[-2] <= macd_signal[-2] else 0
            
            # 8. 多时间框架动量
            if len(close_prices) >= 20:
                factors['momentum_5d'] = (close_prices[-1] - close_prices[-6]) / close_prices[-6] * 100
                factors['momentum_10d'] = (close_prices[-1] - close_prices[-11]) / close_prices[-11] * 100
                factors['momentum_20d'] = (close_prices[-1] - close_prices[-21]) / close_prices[-21] * 100
                
                # 动量一致性
                momentum_signals = [factors['momentum_5d'] > 0, factors['momentum_10d'] > 0, factors['momentum_20d'] > 0]
                factors['momentum_consistency'] = sum(momentum_signals) / len(momentum_signals)
            
            # 9. 成交量技术因子
            if len(volume_data) >= 20:
                volume_ma_20 = np.mean(volume_data[-20:])
                factors['volume_ratio'] = volume_data[-1] / volume_ma_20 if volume_ma_20 > 0 else np.nan
                
                # 成交量趋势
                volume_trend = np.polyfit(range(10), volume_data[-10:], 1)[0] if len(volume_data) >= 10 else 0
                factors['volume_trend'] = volume_trend
                
                # 量价协同
                price_changes = np.diff(close_prices[-10:])
                volume_changes = np.diff(volume_data[-10:])
                if len(price_changes) > 0 and len(volume_changes) > 0:
                    factors['volume_price_correlation'] = np.corrcoef(price_changes, volume_changes)[0, 1]
            
            # 10. 支撑阻力因子
            if len(close_prices) >= 20:
                high_20 = np.max(high_prices[-20:])
                low_20 = np.min(low_prices[-20:])
                factors['price_position_20d'] = (close_prices[-1] - low_20) / (high_20 - low_20) * 100
                factors['near_resistance'] = 1 if close_prices[-1] > high_20 * 0.98 else 0
                factors['near_support'] = 1 if close_prices[-1] < low_20 * 1.02 else 0
            
        except Exception as e:
            logger.error(f"技术因子计算失败: {e}")
        
        return factors
    
    def _calculate_fundamental_factors(self, hist_data, symbol):
        """计算基本面因子"""
        factors = {}
        
        try:
            # 模拟基本面数据 (实际应该从数据库获取)
            # 这里使用随机数模拟，实际使用时应该替换为真实数据
            np.random.seed(hash(symbol) % 2**32)
            
            # 1. PE相对值因子
            pe_ttm = np.random.uniform(10, 50)
            market_avg_pe = 20  # 市场平均PE
            factors['pe_relative'] = pe_ttm / market_avg_pe
            
            # 2. ROE质量因子
            roe_ttm = np.random.uniform(5, 25)
            factors['roe_quality'] = roe_ttm
            
            # ROE稳定性 (模拟4个季度数据)
            roe_history = [roe_ttm + np.random.normal(0, 2) for _ in range(4)]
            factors['roe_stability'] = 1 / (1 + np.std(roe_history))
            
            # 3. 营收增长因子
            revenue_growth = np.random.normal(10, 15)  # 平均10%增长，标准差15%
            factors['revenue_growth'] = revenue_growth
            
            # 4. 盈利增长因子
            profit_growth = np.random.normal(8, 20)  # 平均8%增长，标准差20%
            factors['profit_growth'] = profit_growth
            
            # 5. 财务健康因子
            debt_ratio = np.random.uniform(0.2, 0.8)  # 资产负债率
            factors['financial_health'] = 1 - debt_ratio  # 负债率越低，健康度越高
            
            # 6. 估值吸引力
            pb_ratio = np.random.uniform(1, 8)
            factors['valuation_attractiveness'] = 1 / pb_ratio  # PB越低，吸引力越高
            
        except Exception as e:
            logger.error(f"基本面因子计算失败: {e}")
        
        return factors
    
    def _calculate_sentiment_factors(self, hist_data, symbol):
        """计算市场情绪因子"""
        factors = {}
        
        try:
            # 模拟情绪数据 (实际应该从资金流向数据获取)
            np.random.seed(hash(symbol) % 2**32)
            
            # 1. 主力资金持续性
            main_fund_flows = [np.random.normal(0, 1000000) for _ in range(5)]  # 5日主力资金流向
            positive_days = sum(1 for flow in main_fund_flows if flow > 0)
            factors['main_fund_persistence'] = positive_days / len(main_fund_flows)
            
            # 2. 市场关注度
            if len(hist_data) >= 20:
                volumes = hist_data['volume'].values[-20:]
                avg_volume = np.mean(volumes)
                current_volume = volumes[-1]
                factors['market_attention'] = current_volume / avg_volume if avg_volume > 0 else 1
            
            # 3. 成交量突破
            if len(hist_data) >= 5:
                volumes = hist_data['volume'].values[-5:]
                avg_volume_5d = np.mean(volumes[:-1])
                factors['volume_breakthrough'] = volumes[-1] / avg_volume_5d if avg_volume_5d > 0 else 1
            
            # 4. 资金流向强度
            factors['fund_flow_strength'] = np.random.uniform(-1, 1)  # -1到1的资金流向强度
            
            # 5. 投资者情绪指标
            factors['investor_sentiment'] = np.random.uniform(0, 1)  # 0到1的投资者情绪
            
            # 6. 热点概念关联度
            hot_concepts = ['人工智能', '新能源', '芯片', '医药']
            concept_score = np.random.uniform(0, 1)  # 模拟概念关联度
            factors['concept_relevance'] = concept_score
            
        except Exception as e:
            logger.error(f"情绪因子计算失败: {e}")
        
        return factors
    
    def _calculate_cross_market_factors(self, hist_data, symbol):
        """计算跨市场因子"""
        factors = {}
        
        try:
            # 模拟跨市场数据
            np.random.seed(hash(symbol) % 2**32)
            
            # 1. 行业相对强度
            if len(hist_data) >= 20:
                stock_returns = hist_data['close'].pct_change().dropna()[-20:]
                market_returns = np.random.normal(0.001, 0.02, 20)  # 模拟市场收益率
                
                stock_avg_return = stock_returns.mean()
                market_avg_return = np.mean(market_returns)
                factors['industry_relative_strength'] = stock_avg_return - market_avg_return
            
            # 2. 市场Beta
            if len(hist_data) >= 60:
                stock_returns = hist_data['close'].pct_change().dropna()[-60:]
                market_returns = np.random.normal(0.001, 0.02, len(stock_returns))
                
                if len(stock_returns) > 30:
                    covariance = np.cov(stock_returns, market_returns)[0, 1]
                    market_variance = np.var(market_returns)
                    factors['market_beta'] = covariance / market_variance if market_variance > 0 else 1
            
            # 3. 概念热度
            concept_heat_score = np.random.uniform(0, 1)
            factors['concept_heat'] = concept_heat_score
            
            # 4. 板块轮动因子
            factors['sector_rotation'] = np.random.uniform(-0.5, 0.5)  # 板块轮动强度
            
            # 5. 风格因子 (大小盘、价值成长)
            factors['size_factor'] = np.random.uniform(-1, 1)  # 大小盘因子
            factors['value_growth_factor'] = np.random.uniform(-1, 1)  # 价值成长因子
            
        except Exception as e:
            logger.error(f"跨市场因子计算失败: {e}")
        
        return factors
    
    def _calculate_enhanced_realtime_factors(self, hist_data, symbol):
        """计算增强实时因子"""
        factors = {}
        
        try:
            if len(hist_data) == 0:
                return factors
            
            # 获取当前价格和开盘价
            current_price = hist_data['close'].iloc[-1]
            open_price = hist_data['open'].iloc[-1]
            
            # 1. 开盘动量因子 (增强版)
            if open_price > 0:
                opening_momentum = (current_price - open_price) / open_price
                factors['opening_momentum'] = float(opening_momentum)
                
                # 开盘动量强度分类
                factors['opening_momentum_strong'] = 1 if abs(opening_momentum) > 0.02 else 0
                factors['opening_momentum_positive'] = 1 if opening_momentum > 0 else 0
                
                # 开盘跳空分析
                if len(hist_data) >= 2:
                    prev_close = hist_data['close'].iloc[-2]
                    gap = (open_price - prev_close) / prev_close
                    factors['opening_gap'] = float(gap)
                    factors['gap_up'] = 1 if gap > 0.01 else 0
                    factors['gap_down'] = 1 if gap < -0.01 else 0
            
            # 2. 日内波动因子
            high_price = hist_data['high'].iloc[-1]
            low_price = hist_data['low'].iloc[-1]
            
            if open_price > 0:
                factors['intraday_high_ratio'] = (high_price - open_price) / open_price
                factors['intraday_low_ratio'] = (open_price - low_price) / open_price
                factors['intraday_volatility'] = (high_price - low_price) / open_price
            
            # 3. 价格位置因子
            if len(hist_data) >= 20:
                high_20d = hist_data['high'].iloc[-20:].max()
                low_20d = hist_data['low'].iloc[-20:].min()
                
                if high_20d > low_20d:
                    factors['price_position_pct'] = (current_price - low_20d) / (high_20d - low_20d) * 100
                    factors['near_high_20d'] = 1 if current_price > high_20d * 0.95 else 0
                    factors['near_low_20d'] = 1 if current_price < low_20d * 1.05 else 0
            
            # 4. 时间因子
            current_time = datetime.now()
            factors['hour_of_day'] = current_time.hour
            factors['is_morning_session'] = 1 if 9 <= current_time.hour <= 11 else 0
            factors['is_afternoon_session'] = 1 if 13 <= current_time.hour <= 15 else 0
            
        except Exception as e:
            logger.error(f"实时因子计算失败: {e}")
        
        return factors
    
    def _calculate_multi_dimensional_scores(self, all_factors):
        """计算多维度综合评分"""
        scores = {}
        
        try:
            # 1. 技术面评分
            technical_factors = [
                'rsi_14', 'cci_14', 'adx_14', 'atr_pct', 'bb_position', 
                'macd_hist', 'momentum_consistency', 'volume_ratio'
            ]
            
            technical_values = []
            for factor in technical_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    # 标准化评分
                    if factor == 'rsi_14':
                        score = 1 - abs(all_factors[factor] - 50) / 50
                    elif factor == 'cci_14':
                        score = max(0, min(1, (all_factors[factor] + 100) / 200))
                    elif factor == 'adx_14':
                        score = min(1, all_factors[factor] / 50)
                    elif factor == 'atr_pct':
                        score = min(1, max(0, (all_factors[factor] - 1) / 5))
                    elif factor == 'bb_position':
                        score = all_factors[factor] / 100
                    elif factor == 'momentum_consistency':
                        score = all_factors[factor]
                    elif factor == 'volume_ratio':
                        score = min(1, all_factors[factor] / 3)
                    else:
                        score = 0.5
                    
                    technical_values.append(score)
            
            scores['technical_score'] = np.mean(technical_values) if technical_values else 0.5
            
            # 2. 基本面评分
            fundamental_factors = ['pe_relative', 'roe_quality', 'revenue_growth', 'financial_health']
            fundamental_values = []
            
            for factor in fundamental_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    if factor == 'pe_relative':
                        score = max(0, min(1, 2 - all_factors[factor]))
                    elif factor == 'roe_quality':
                        score = min(1, all_factors[factor] / 30)
                    elif factor == 'revenue_growth':
                        score = max(0, min(1, (all_factors[factor] + 20) / 40))
                    elif factor == 'financial_health':
                        score = all_factors[factor]
                    else:
                        score = 0.5
                    
                    fundamental_values.append(score)
            
            scores['fundamental_score'] = np.mean(fundamental_values) if fundamental_values else 0.5
            
            # 3. 情绪面评分
            sentiment_factors = ['main_fund_persistence', 'market_attention', 'volume_breakthrough']
            sentiment_values = []
            
            for factor in sentiment_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    if factor == 'main_fund_persistence':
                        score = all_factors[factor]
                    elif factor == 'market_attention':
                        score = min(1, all_factors[factor] / 3)
                    elif factor == 'volume_breakthrough':
                        score = min(1, all_factors[factor] / 3)
                    else:
                        score = 0.5
                    
                    sentiment_values.append(score)
            
            scores['sentiment_score'] = np.mean(sentiment_values) if sentiment_values else 0.5
            
            # 4. 跨市场评分
            cross_market_factors = ['industry_relative_strength', 'market_beta', 'concept_heat']
            cross_market_values = []
            
            for factor in cross_market_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    if factor == 'industry_relative_strength':
                        score = max(0, min(1, (all_factors[factor] + 0.1) / 0.2))
                    elif factor == 'market_beta':
                        score = 1 - abs(all_factors[factor] - 1) / 2
                    elif factor == 'concept_heat':
                        score = all_factors[factor]
                    else:
                        score = 0.5
                    
                    cross_market_values.append(score)
            
            scores['cross_market_score'] = np.mean(cross_market_values) if cross_market_values else 0.5
            
            # 5. 综合评分 (基于新的权重配置)
            weights = self.config.get('scoring_weights', {
                'technical_score': 0.45,
                'fundamental_score': 0.25,
                'sentiment_score': 0.20,
                'cross_market_score': 0.10
            })
            
            overall_score = (
                scores['technical_score'] * weights['technical_score'] +
                scores['fundamental_score'] * weights['fundamental_score'] +
                scores['sentiment_score'] * weights['sentiment_score'] +
                scores['cross_market_score'] * weights['cross_market_score']
            )
            
            scores['overall_score'] = overall_score
            
        except Exception as e:
            logger.error(f"综合评分计算失败: {e}")
            scores = {
                'technical_score': 0.5,
                'fundamental_score': 0.5,
                'sentiment_score': 0.5,
                'cross_market_score': 0.5,
                'overall_score': 0.5
            }
        
        return scores

def main():
    """主函数"""
    print("🔬 增强多因子计算引擎测试")
    print("=" * 60)
    
    # 创建测试数据
    import pandas as pd
    dates = pd.date_range('2024-01-01', periods=60, freq='D')
    
    np.random.seed(42)
    test_data = pd.DataFrame({
        'open': np.random.uniform(95, 105, 60),
        'close': np.random.uniform(95, 105, 60),
        'high': np.random.uniform(100, 110, 60),
        'low': np.random.uniform(90, 100, 60),
        'volume': np.random.randint(1000000, 5000000, 60),
    }, index=dates)
    
    # 测试因子计算
    engine = EnhancedMultiFactorEngine()
    factors = engine.calculate_all_enhanced_factors(test_data, 'TEST.000001')
    
    print(f"✅ 计算完成，共 {len(factors)} 个因子")
    
    # 显示关键因子
    key_factors = [
        'technical_score', 'fundamental_score', 'sentiment_score', 
        'cross_market_score', 'overall_score'
    ]
    
    print(f"\n📊 多维度评分:")
    for factor in key_factors:
        if factor in factors:
            print(f"   {factor}: {factors[factor]:.4f}")
    
    print(f"\n🔍 部分技术因子:")
    tech_factors = ['rsi_14', 'cci_14', 'atr_pct', 'adx_14']
    for factor in tech_factors:
        if factor in factors:
            print(f"   {factor}: {factors[factor]:.4f}")
    
    return factors

if __name__ == '__main__':
    main()
