# coding=utf-8
"""
重复买入问题分析
分析策略是否在同一天对同一股票重复买入
"""

import sqlite3
import pandas as pd
from datetime import datetime
from collections import defaultdict

def analyze_duplicate_buys():
    """分析重复买入问题"""
    print('🔍 重复买入问题分析')
    print('=' * 60)
    print(f'分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有买入记录
        buy_df = pd.read_sql_query("""
            SELECT timestamp, symbol, price, volume 
            FROM trades 
            WHERE action = 'BUY' 
            ORDER BY timestamp, symbol
        """, conn)
        
        print(f'\n📊 买入记录基本统计:')
        print(f'  总买入记录数: {len(buy_df):,}条')
        print(f'  涉及股票数: {buy_df["symbol"].nunique():,}只')
        print(f'  时间跨度: {buy_df["timestamp"].min()} 到 {buy_df["timestamp"].max()}')
        
        # 转换时间戳
        buy_df['timestamp'] = pd.to_datetime(buy_df['timestamp'])
        buy_df['date'] = buy_df['timestamp'].dt.date
        buy_df['time'] = buy_df['timestamp'].dt.time
        
        conn.close()
        return buy_df
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def check_same_day_duplicate_buys(buy_df):
    """检查同一天重复买入"""
    print('\n🔍 同一天重复买入分析')
    print('=' * 50)
    
    if buy_df is None or len(buy_df) == 0:
        print('❌ 没有买入数据可分析')
        return
    
    # 按股票和日期分组统计
    daily_buys = buy_df.groupby(['symbol', 'date']).agg({
        'timestamp': 'count',
        'volume': 'sum',
        'price': ['min', 'max', 'mean']
    }).round(2)
    
    daily_buys.columns = ['buy_count', 'total_volume', 'min_price', 'max_price', 'avg_price']
    daily_buys = daily_buys.reset_index()
    
    # 找出同一天多次买入的情况
    duplicate_buys = daily_buys[daily_buys['buy_count'] > 1].sort_values('buy_count', ascending=False)
    
    print(f'📊 同一天重复买入统计:')
    print(f'  有重复买入的股票-日期组合: {len(duplicate_buys):,}个')
    print(f'  总股票-日期组合: {len(daily_buys):,}个')
    print(f'  重复买入比例: {len(duplicate_buys)/len(daily_buys)*100:.1f}%')
    
    if len(duplicate_buys) > 0:
        print(f'\n📋 重复买入最严重的前10个案例:')
        print(f'{"股票代码":<15} {"日期":<12} {"买入次数":<8} {"总股数":<10} {"价格范围"}')
        print('-' * 70)
        
        for _, row in duplicate_buys.head(10).iterrows():
            symbol = row['symbol']
            date = row['date']
            count = int(row['buy_count'])
            volume = int(row['total_volume'])
            price_range = f"¥{row['min_price']:.2f}-¥{row['max_price']:.2f}"
            
            print(f'{symbol:<15} {date} {count:<8} {volume:<10} {price_range}')
    
    return duplicate_buys

def check_same_time_duplicate_buys(buy_df):
    """检查同一时间重复买入"""
    print('\n🔍 同一时间重复买入分析')
    print('=' * 50)
    
    if buy_df is None or len(buy_df) == 0:
        print('❌ 没有买入数据可分析')
        return
    
    # 按股票和时间戳分组统计
    time_buys = buy_df.groupby(['symbol', 'timestamp']).agg({
        'volume': 'sum',
        'price': ['count', 'mean']
    }).round(2)
    
    time_buys.columns = ['total_volume', 'buy_count', 'avg_price']
    time_buys = time_buys.reset_index()
    
    # 找出同一时间多次买入的情况
    same_time_duplicates = time_buys[time_buys['buy_count'] > 1].sort_values('buy_count', ascending=False)
    
    print(f'📊 同一时间重复买入统计:')
    print(f'  同一时间重复买入的情况: {len(same_time_duplicates):,}个')
    
    if len(same_time_duplicates) > 0:
        print(f'\n📋 同一时间重复买入案例（前5个）:')
        for _, row in same_time_duplicates.head(5).iterrows():
            symbol = row['symbol']
            timestamp = row['timestamp']
            count = int(row['buy_count'])
            volume = int(row['total_volume'])
            
            print(f'  {symbol} 在 {timestamp} 买入{count}次，共{volume}股')

def analyze_position_management_logic():
    """分析持仓管理逻辑"""
    print('\n🔍 持仓管理逻辑分析')
    print('=' * 50)
    
    try:
        # 检查main.py中的持仓检查逻辑
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 持仓检查逻辑分析:')
        
        # 1. 检查是否有持仓检查
        import re
        position_checks = [
            (r'get_position\(', '获取持仓信息'),
            (r'position.*volume', '检查持仓数量'),
            (r'已持仓', '持仓状态检查'),
            (r'if.*position', '持仓条件判断'),
            (r'context\.portfolio\.positions', '组合持仓检查')
        ]
        
        for pattern, description in position_checks:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ✅ {description}: {len(matches)}处')
            else:
                print(f'  ❌ {description}: 未找到')
        
        # 2. 检查买入前的持仓检查
        buy_logic_pattern = r'def execute_backup_buy_logic\(.*?\):(.*?)(?=def|\Z)'
        buy_logic_match = re.search(buy_logic_pattern, content, re.DOTALL)
        
        if buy_logic_match:
            buy_logic_code = buy_logic_match.group(1)
            
            print(f'\n📝 买入逻辑中的持仓检查:')
            
            position_check_patterns = [
                (r'get_position\(.*symbol', '获取股票持仓'),
                (r'position.*volume.*>', '检查持仓数量'),
                (r'if.*position', '持仓条件判断'),
                (r'continue', '跳过已持仓股票'),
                (r'return', '提前返回')
            ]
            
            for pattern, description in position_check_patterns:
                if re.search(pattern, buy_logic_code):
                    print(f'    ✅ {description}')
                else:
                    print(f'    ❌ {description}: 未找到')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_portfolio_positions():
    """检查当前组合持仓"""
    print('\n🔍 当前组合持仓分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 计算每只股票的净持仓
        position_df = pd.read_sql_query("""
            SELECT 
                symbol,
                SUM(CASE WHEN action = 'BUY' THEN volume ELSE 0 END) as total_buy,
                SUM(CASE WHEN action = 'SELL' THEN volume ELSE 0 END) as total_sell,
                SUM(CASE WHEN action = 'BUY' THEN volume ELSE -volume END) as net_position,
                COUNT(CASE WHEN action = 'BUY' THEN 1 END) as buy_count,
                COUNT(CASE WHEN action = 'SELL' THEN 1 END) as sell_count
            FROM trades 
            GROUP BY symbol
            HAVING net_position > 0
            ORDER BY net_position DESC
        """, conn)
        
        print(f'📊 当前持仓统计:')
        print(f'  持仓股票数: {len(position_df)}只')
        
        if len(position_df) > 0:
            print(f'  总持仓股数: {position_df["net_position"].sum():,}股')
            print(f'  平均持仓: {position_df["net_position"].mean():.0f}股/只')
            print(f'  最大持仓: {position_df["net_position"].max():,}股')
            
            print(f'\n📋 持仓最多的前10只股票:')
            print(f'{"股票代码":<15} {"净持仓":<10} {"买入次数":<8} {"卖出次数"}')
            print('-' * 50)
            
            for _, row in position_df.head(10).iterrows():
                symbol = row['symbol']
                net_pos = int(row['net_position'])
                buy_count = int(row['buy_count'])
                sell_count = int(row['sell_count'])
                
                print(f'{symbol:<15} {net_pos:<10} {buy_count:<8} {sell_count}')
        
        conn.close()
        return position_df
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return None

def diagnose_root_cause():
    """诊断根本原因"""
    print('\n💡 根本原因诊断')
    print('=' * 50)
    
    print('📊 可能的原因分析:')
    
    causes = [
        {
            'cause': '缺少持仓检查逻辑',
            'description': '买入前没有检查是否已持仓该股票',
            'indicators': ['同一股票多次买入', '没有持仓检查代码'],
            'impact': '高',
            'solution': '在买入逻辑中添加持仓检查'
        },
        {
            'cause': '持仓检查逻辑失效',
            'description': '有持仓检查但逻辑错误或被绕过',
            'indicators': ['有持仓检查代码但仍重复买入'],
            'impact': '高',
            'solution': '修复持仓检查逻辑'
        },
        {
            'cause': '多时间段重复分析',
            'description': '同一天多个时间段都触发买入信号',
            'indicators': ['同一天同一股票多次买入'],
            'impact': '中',
            'solution': '添加当日买入记录检查'
        },
        {
            'cause': '信号重复触发',
            'description': '买入信号在短时间内重复触发',
            'indicators': ['同一时间多次买入'],
            'impact': '中',
            'solution': '添加信号去重逻辑'
        }
    ]
    
    for i, cause in enumerate(causes, 1):
        print(f'\n{i}. {cause["cause"]} (影响: {cause["impact"]})')
        print(f'   描述: {cause["description"]}')
        print(f'   指标: {", ".join(cause["indicators"])}')
        print(f'   解决: {cause["solution"]}')

def main():
    """主函数"""
    print('🔍 重复买入问题分析报告')
    print('=' * 60)
    
    # 分析重复买入
    buy_df = analyze_duplicate_buys()
    
    # 检查同一天重复买入
    duplicate_buys = check_same_day_duplicate_buys(buy_df)
    
    # 检查同一时间重复买入
    check_same_time_duplicate_buys(buy_df)
    
    # 分析持仓管理逻辑
    analyze_position_management_logic()
    
    # 检查当前组合持仓
    position_df = check_portfolio_positions()
    
    # 诊断根本原因
    diagnose_root_cause()
    
    print(f'\n🎯 分析结论')
    print('=' * 40)
    
    if buy_df is not None and len(buy_df) > 0:
        duplicate_ratio = len(duplicate_buys) / buy_df['symbol'].nunique() * 100 if duplicate_buys is not None else 0
        
        if duplicate_ratio > 50:
            print('❌ 发现严重的重复买入问题')
            print(f'   重复买入比例: {duplicate_ratio:.1f}%')
            print('🔧 建议立即修复持仓检查逻辑')
        elif duplicate_ratio > 10:
            print('⚠️ 发现中等程度的重复买入问题')
            print(f'   重复买入比例: {duplicate_ratio:.1f}%')
            print('🔧 建议优化持仓管理逻辑')
        else:
            print('✅ 重复买入问题较少')
            print(f'   重复买入比例: {duplicate_ratio:.1f}%')
    
    print('\n📋 建议修复步骤:')
    print('1. 🔍 检查买入逻辑中的持仓检查代码')
    print('2. 🔧 添加或修复持仓检查逻辑')
    print('3. 🧪 测试修复后的策略')
    print('4. 📊 验证重复买入问题是否解决')

if __name__ == '__main__':
    main()
