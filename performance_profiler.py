#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能分析模块 - 用于记录和分析函数执行时间
"""

import time
import functools
import logging
import os
from datetime import datetime
from collections import defaultdict
import threading

class PerformanceProfiler:
    """性能分析器类，用于记录和分析函数执行时间"""
    
    def __init__(self, context=None, enabled=False, threshold_ms=10, output_file=None, top_functions=10, summary_interval=300):
        """
        初始化性能分析器
        
        Args:
            context: 策略上下文对象
            enabled: 是否启用性能分析
            threshold_ms: 性能分析阈值(毫秒)，仅记录执行时间超过此阈值的函数调用
            output_file: 性能分析结果输出文件，None表示输出到日志
            top_functions: 性能分析结果展示的函数数量
            summary_interval: 性能分析结果汇总间隔(秒)
        """
        self.context = context
        self.enabled = enabled
        self.threshold_ms = threshold_ms
        self.output_file = output_file
        self.top_functions = top_functions
        self.summary_interval = summary_interval
        
        # 日志记录器
        self.logger = logging.getLogger('performance_profiler')
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.propagate = False
        
        # 性能统计数据
        self.stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'min_time': float('inf'), 'max_time': 0})
        self.stats_lock = threading.RLock()  # 线程安全锁
        
        # 上次汇总时间
        self.last_summary_time = time.time()
        
        if self.enabled:
            self.logger.info("性能分析器已启用")
            if self.output_file:
                # 确保输出目录存在
                os.makedirs(os.path.dirname(os.path.abspath(self.output_file)), exist_ok=True)
                # 写入文件头
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# 性能分析报告 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 阈值: {self.threshold_ms}ms, 展示函数数量: {self.top_functions}\n")
                    f.write("timestamp,function,calls,total_ms,avg_ms,min_ms,max_ms\n")
                self.logger.info(f"性能分析结果将输出到: {self.output_file}")
    
    def update_settings(self, enabled=None, threshold_ms=None, output_file=None, top_functions=None, summary_interval=None):
        """更新性能分析器设置"""
        if enabled is not None:
            self.enabled = enabled
        if threshold_ms is not None:
            self.threshold_ms = threshold_ms
        if output_file is not None:
            self.output_file = output_file
        if top_functions is not None:
            self.top_functions = top_functions
        if summary_interval is not None:
            self.summary_interval = summary_interval
        
        self.logger.info(f"性能分析器设置已更新: enabled={self.enabled}, threshold_ms={self.threshold_ms}, "
                         f"top_functions={self.top_functions}, summary_interval={self.summary_interval}")
    
    def profile(self, func):
        """性能分析装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not self.enabled:
                return func(*args, **kwargs)
            
            start_time = time.time()
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            elapsed_ms = elapsed_time * 1000
            
            # 仅记录超过阈值的函数调用
            if elapsed_ms >= self.threshold_ms:
                function_name = f"{func.__module__}.{func.__qualname__}"
                
                # 线程安全更新统计数据
                with self.stats_lock:
                    self.stats[function_name]['count'] += 1
                    self.stats[function_name]['total_time'] += elapsed_ms
                    self.stats[function_name]['min_time'] = min(self.stats[function_name]['min_time'], elapsed_ms)
                    self.stats[function_name]['max_time'] = max(self.stats[function_name]['max_time'], elapsed_ms)
                
                # 检查是否需要输出汇总
                if self.summary_interval > 0 and time.time() - self.last_summary_time >= self.summary_interval:
                    self.output_summary()
            
            return result
        return wrapper
    
    def output_summary(self, force_output=False):
        """输出性能分析汇总结果"""
        if not self.enabled and not force_output:
            return
        
        current_time = time.time()
        # 仅在距上次汇总已达到间隔时间时输出（或强制输出）
        if not force_output and current_time - self.last_summary_time < self.summary_interval:
            return
        
        self.last_summary_time = current_time
        
        # 线程安全获取统计数据
        with self.stats_lock:
            if not self.stats:
                return
            
            # 按总耗时排序
            sorted_stats = sorted(
                self.stats.items(), 
                key=lambda x: x[1]['total_time'], 
                reverse=True
            )
            
            # 限制显示的函数数量
            if self.top_functions > 0:
                sorted_stats = sorted_stats[:self.top_functions]
            
            # 准备汇总数据
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            summary_lines = [
                f"\n========== 性能分析汇总 ({timestamp}) ==========",
                f"{'函数名':<50} {'调用次数':>10} {'总耗时(ms)':>15} {'平均耗时(ms)':>15} {'最小耗时(ms)':>15} {'最大耗时(ms)':>15}"
            ]
            
            csv_lines = []
            
            for function_name, stats in sorted_stats:
                count = stats['count']
                total_time = stats['total_time']
                avg_time = total_time / count if count > 0 else 0
                min_time = stats['min_time'] if stats['min_time'] != float('inf') else 0
                max_time = stats['max_time']
                
                summary_lines.append(
                    f"{function_name:<50} {count:>10} {total_time:>15.2f} {avg_time:>15.2f} {min_time:>15.2f} {max_time:>15.2f}"
                )
                
                csv_lines.append(
                    f"{timestamp},{function_name},{count},{total_time:.2f},{avg_time:.2f},{min_time:.2f},{max_time:.2f}"
                )
            
            summary_lines.append("=" * 60)
            
            # 输出到日志
            if self.context and hasattr(self.context, 'log'):
                for line in summary_lines:
                    self.context.log.info(line)
            else:
                for line in summary_lines:
                    self.logger.info(line)
            
            # 输出到文件
            if self.output_file:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    for line in csv_lines:
                        f.write(line + "\n")
    
    def reset_stats(self):
        """重置统计数据"""
        with self.stats_lock:
            self.stats.clear()
        self.logger.info("性能分析统计数据已重置")
    
    def __del__(self):
        """析构函数，输出最终汇总"""
        try:
            self.output_summary(force_output=True)
        except:
            pass

# 全局性能分析器实例
_profiler = None

def init_profiler(context=None, enabled=False, threshold_ms=10, output_file=None, top_functions=10, summary_interval=300):
    """初始化全局性能分析器"""
    global _profiler
    _profiler = PerformanceProfiler(
        context=context, 
        enabled=enabled, 
        threshold_ms=threshold_ms,
        output_file=output_file,
        top_functions=top_functions,
        summary_interval=summary_interval
    )
    return _profiler

def get_profiler():
    """获取全局性能分析器实例"""
    global _profiler
    if _profiler is None:
        _profiler = PerformanceProfiler()
    return _profiler

def profile(func):
    """性能分析装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        profiler = get_profiler()
        if not profiler.enabled:
            return func(*args, **kwargs)
        
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed_time = time.time() - start_time
        elapsed_ms = elapsed_time * 1000
        
        # 仅记录超过阈值的函数调用
        if elapsed_ms >= profiler.threshold_ms:
            function_name = f"{func.__module__}.{func.__qualname__}"
            
            # 线程安全更新统计数据
            with profiler.stats_lock:
                profiler.stats[function_name]['count'] += 1
                profiler.stats[function_name]['total_time'] += elapsed_ms
                profiler.stats[function_name]['min_time'] = min(profiler.stats[function_name]['min_time'], elapsed_ms)
                profiler.stats[function_name]['max_time'] = max(profiler.stats[function_name]['max_time'], elapsed_ms)
            
            # 检查是否需要输出汇总
            if profiler.summary_interval > 0 and time.time() - profiler.last_summary_time >= profiler.summary_interval:
                profiler.output_summary()
        
        return result
    return wrapper 