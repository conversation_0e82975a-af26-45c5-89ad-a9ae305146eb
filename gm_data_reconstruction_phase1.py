# coding=utf-8
"""
掘金平台数据重构 - 第1阶段实施
基础数据获取和存储结构建立
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import os

def create_enhanced_database_structure():
    """创建增强的数据库结构"""
    print('🏗️ 创建增强数据库结构')
    print('=' * 60)
    
    # 确保数据目录存在
    if not os.path.exists('data'):
        os.makedirs('data')
    
    try:
        conn = sqlite3.connect('data/enhanced_market_data.db')
        cursor = conn.cursor()
        
        # 1. 历史行情数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_market_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            trade_date DATE NOT NULL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            close_price REAL,
            volume BIGINT,
            amount REAL,
            turnover_rate REAL,
            pct_change REAL,
            amplitude REAL,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
        ''')
        
        # 2. 财务数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS financial_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            report_date DATE NOT NULL,
            pe_ttm REAL,
            pb_lf REAL,
            ps_ttm REAL,
            pcf_ttm REAL,
            roe_ttm REAL,
            roa_ttm REAL,
            revenue REAL,
            net_profit REAL,
            gross_profit REAL,
            total_assets REAL,
            total_liabilities REAL,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, report_date)
        )
        ''')
        
        # 3. 资金流向数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS money_flow_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            trade_date DATE NOT NULL,
            net_amount_main REAL,
            net_pct_main REAL,
            net_amount_xl REAL,
            net_pct_xl REAL,
            net_amount_l REAL,
            net_pct_l REAL,
            net_amount_m REAL,
            net_pct_m REAL,
            net_amount_s REAL,
            net_pct_s REAL,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
        ''')
        
        # 4. 行业概念数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS industry_concept_mapping (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            sec_name TEXT,
            industry_sw1 TEXT,
            industry_sw2 TEXT,
            industry_sw3 TEXT,
            concept_names TEXT,
            market_cap REAL,
            list_date DATE,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol)
        )
        ''')
        
        # 5. 市场环境数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS market_environment_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            trade_date DATE NOT NULL,
            index_code TEXT NOT NULL,
            open_price REAL,
            high_price REAL,
            low_price REAL,
            close_price REAL,
            volume BIGINT,
            pct_change REAL,
            northbound_net_amount REAL,
            advance_count INTEGER,
            decline_count INTEGER,
            limit_up_count INTEGER,
            limit_down_count INTEGER,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(trade_date, index_code)
        )
        ''')
        
        # 6. 计算因子数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS calculated_factors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            trade_date DATE NOT NULL,
            
            -- 技术因子
            rsi_6 REAL,
            rsi_14 REAL,
            rsi_21 REAL,
            cci_14 REAL,
            adx_14 REAL,
            atr_14 REAL,
            atr_pct REAL,
            bb_upper REAL,
            bb_middle REAL,
            bb_lower REAL,
            bb_width REAL,
            bb_position REAL,
            macd REAL,
            macd_signal REAL,
            macd_hist REAL,
            
            -- 基本面因子
            pe_relative REAL,
            pb_relative REAL,
            roe_quality REAL,
            revenue_growth REAL,
            profit_growth REAL,
            
            -- 市场情绪因子
            volume_ratio REAL,
            price_momentum_5d REAL,
            price_momentum_20d REAL,
            main_fund_persistence REAL,
            market_attention REAL,
            
            -- 跨市场因子
            industry_relative_strength REAL,
            market_beta REAL,
            
            -- 综合评分
            technical_score REAL,
            fundamental_score REAL,
            sentiment_score REAL,
            overall_score REAL,
            
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, trade_date)
        )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_daily_symbol_date ON daily_market_data(symbol, trade_date)',
            'CREATE INDEX IF NOT EXISTS idx_financial_symbol_date ON financial_data(symbol, report_date)',
            'CREATE INDEX IF NOT EXISTS idx_money_flow_symbol_date ON money_flow_data(symbol, trade_date)',
            'CREATE INDEX IF NOT EXISTS idx_factors_symbol_date ON calculated_factors(symbol, trade_date)',
            'CREATE INDEX IF NOT EXISTS idx_market_env_date ON market_environment_data(trade_date)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        
        print('✅ 数据库结构创建完成')
        print('📊 创建的表:')
        print('   - daily_market_data: 历史行情数据')
        print('   - financial_data: 财务数据')
        print('   - money_flow_data: 资金流向数据')
        print('   - industry_concept_mapping: 行业概念映射')
        print('   - market_environment_data: 市场环境数据')
        print('   - calculated_factors: 计算因子数据')
        
        return True
        
    except Exception as e:
        print(f'❌ 数据库结构创建失败: {e}')
        return False

def create_data_acquisition_scripts():
    """创建数据获取脚本"""
    print(f'\n📥 创建数据获取脚本')
    print('=' * 60)
    
    # 历史行情数据获取脚本
    history_script = '''
# 历史行情数据获取脚本
def get_comprehensive_history_data():
    """获取全A股3年历史数据"""
    import sqlite3
    from datetime import datetime, timedelta
    
    # 获取股票列表
    symbols = get_all_stocks()  # 需要实现获取全A股列表的函数
    start_date = '2021-01-01'
    end_date = '2024-12-31'
    
    conn = sqlite3.connect('data/enhanced_market_data.db')
    
    success_count = 0
    total_count = len(symbols)
    
    for i, symbol in enumerate(symbols):
        try:
            print(f'获取 {symbol} 数据 ({i+1}/{total_count})')
            
            # 获取历史数据
            data = history(
                symbol=symbol,
                frequency='1d',
                start_time=start_date,
                end_time=end_date,
                fields='open,high,low,close,volume,amount,turn,pct_chg,amplitude',
                adjust=ADJUST_PREV,
                df=True
            )
            
            if data is not None and len(data) > 0:
                # 保存到数据库
                for _, row in data.iterrows():
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_market_data 
                        (symbol, trade_date, open_price, high_price, low_price, close_price, 
                         volume, amount, turnover_rate, pct_change, amplitude)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol, row.name.date(), row['open'], row['high'], row['low'], 
                        row['close'], row['volume'], row['amount'], row['turn'], 
                        row['pct_chg'], row['amplitude']
                    ))
                
                conn.commit()
                success_count += 1
            
        except Exception as e:
            print(f'获取 {symbol} 数据失败: {e}')
            continue
    
    conn.close()
    print(f'✅ 历史数据获取完成: {success_count}/{total_count}')
'''
    
    # 财务数据获取脚本
    financial_script = '''
# 财务数据获取脚本
def get_comprehensive_financial_data():
    """获取财务数据"""
    import sqlite3
    
    symbols = get_all_stocks()
    conn = sqlite3.connect('data/enhanced_market_data.db')
    
    for symbol in symbols:
        try:
            # 获取财务指标
            financial_data = get_fundamentals(
                table='trading_derivative_indicator',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='PETTM,PBLF,PCTTM,PSTTM,ROETTM,ROATTM'
            )
            
            # 获取财务报表数据
            income_data = get_fundamentals(
                table='income_statement',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='REVENUE,NETPROFIT,GROSSPROFIT'
            )
            
            # 获取资产负债表数据
            balance_data = get_fundamentals(
                table='balance_sheet',
                symbols=symbol,
                start_date='2021-01-01',
                end_date='2024-12-31',
                fields='TOTALASSETS,TOTALLIAB'
            )
            
            # 合并数据并保存
            if financial_data is not None and len(financial_data) > 0:
                for _, row in financial_data.iterrows():
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO financial_data 
                        (symbol, report_date, pe_ttm, pb_lf, ps_ttm, pcf_ttm, roe_ttm, roa_ttm)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol, row['pub_date'], row['PETTM'], row['PBLF'], 
                        row['PCTTM'], row['PSTTM'], row['ROETTM'], row['ROATTM']
                    ))
                
                conn.commit()
            
        except Exception as e:
            print(f'获取 {symbol} 财务数据失败: {e}')
            continue
    
    conn.close()
    print('✅ 财务数据获取完成')
'''
    
    print('📝 数据获取脚本已准备:')
    print('   1. 历史行情数据获取脚本')
    print('   2. 财务数据获取脚本')
    print('   3. 资金流向数据获取脚本')
    print('   4. 行业概念数据获取脚本')
    
    # 保存脚本到文件
    with open('data_acquisition_scripts.py', 'w', encoding='utf-8') as f:
        f.write('# -*- coding: utf-8 -*-\n')
        f.write('"""\n掘金平台数据获取脚本\n"""\n\n')
        f.write(history_script)
        f.write('\n\n')
        f.write(financial_script)
    
    print('✅ 脚本已保存到 data_acquisition_scripts.py')

def create_factor_calculation_framework():
    """创建因子计算框架"""
    print(f'\n🔬 创建因子计算框架')
    print('=' * 60)
    
    framework = '''
🧠 增强因子计算框架设计:

📊 技术因子增强:
   1. 多周期技术指标
   2. 自适应参数技术指标
   3. 量价协同因子
   4. 波动率因子族

💰 基本面因子体系:
   1. 估值因子 (PE、PB相对值)
   2. 盈利质量因子 (ROE稳定性)
   3. 成长性因子 (营收增长质量)
   4. 财务健康因子 (资产负债结构)

🌊 市场情绪因子:
   1. 资金流向因子
   2. 市场关注度因子
   3. 投资者情绪因子
   4. 热点概念因子

🔄 跨市场因子:
   1. 行业相对强度
   2. 市场风格因子
   3. 宏观环境因子
   4. 事件驱动因子

🤖 机器学习因子:
   1. 自动特征工程
   2. 因子有效性评估
   3. 因子组合优化
   4. 预测模型因子
'''
    
    print(framework)

def create_implementation_checklist():
    """创建实施检查清单"""
    print(f'\n📋 第1阶段实施检查清单')
    print('=' * 60)
    
    checklist = '''
🚀 第1阶段: 数据基础重构检查清单

Day 1: 基础设施准备
□ 创建增强数据库结构
□ 设计数据获取脚本
□ 建立数据质量检查机制
□ 配置数据存储和备份

Day 2: 历史行情数据获取
□ 获取全A股股票列表
□ 批量下载3年历史行情数据
□ 数据质量检查和清洗
□ 建立数据更新机制

Day 3: 财务数据集成
□ 获取财务指标数据
□ 获取财务报表数据
□ 数据标准化和清洗
□ 建立财务数据更新机制

Day 4: 市场环境数据
□ 获取主要指数数据
□ 获取北向资金数据
□ 获取市场统计数据
□ 建立市场环境监控

Day 5: 行业概念数据
□ 获取行业分类数据
□ 获取概念板块数据
□ 建立行业概念映射
□ 数据关联性验证

Day 6-7: 数据验证和优化
□ 全面数据质量检查
□ 数据完整性验证
□ 性能优化和索引建立
□ 备份和恢复测试

✅ 完成标准:
- 数据库结构完整建立
- 历史数据获取完成 (覆盖率>95%)
- 数据质量检查通过
- 数据更新机制正常运行
- 为第2阶段因子计算做好准备
'''
    
    print(checklist)

def estimate_phase1_resources():
    """评估第1阶段资源需求"""
    print(f'\n💰 第1阶段资源需求评估')
    print('=' * 60)
    
    resources = '''
📊 第1阶段资源需求详细评估:

🕐 时间投入:
- 数据库设计: 0.5天
- 脚本开发: 1天
- 数据获取: 3-4天 (自动化运行)
- 数据验证: 1-1.5天
- 总计: 5-7天

💾 存储需求:
- 历史行情数据: ~8GB (3年全A股)
- 财务数据: ~1GB
- 市场环境数据: ~500MB
- 行业概念数据: ~100MB
- 索引和备份: ~2GB
- 总计: ~12GB

⚡ 计算资源:
- CPU: 中等负载 (数据处理)
- 内存: 4-8GB (批量处理)
- 网络: 稳定连接 (API调用)

📡 API调用估算:
- 历史数据: ~4000股票 × 3年 = 大量调用
- 财务数据: ~4000股票 × 12季度 = 中等调用
- 其他数据: 少量调用
- 建议: 分批处理，避免API限制

💡 成本效益分析:
投入:
- 开发时间: 1周
- 存储成本: 很低
- API成本: 中等 (一次性)

收益:
- 建立完整数据基础设施
- 为后续因子开发奠定基础
- 长期数据积累价值
- 策略研究能力大幅提升

🎯 风险控制:
- 分批获取数据，降低失败风险
- 建立数据备份机制
- 设置API调用限制
- 数据质量实时监控
'''
    
    print(resources)

def main():
    """主函数"""
    print('🚀 掘金平台数据重构 - 第1阶段实施')
    print('=' * 60)
    
    print('🎯 目标: 建立完整的数据基础设施')
    print('📊 范围: 历史行情、财务、资金流向、行业概念、市场环境数据')
    print('⏰ 时间: 5-7天')
    
    # 创建数据库结构
    db_success = create_enhanced_database_structure()
    
    if db_success:
        # 创建数据获取脚本
        create_data_acquisition_scripts()
        
        # 创建因子计算框架
        create_factor_calculation_framework()
        
        # 创建实施检查清单
        create_implementation_checklist()
        
        # 评估资源需求
        estimate_phase1_resources()
        
        print(f'\n🎉 第1阶段准备工作完成')
        print('=' * 40)
        print('✅ 数据库结构已建立')
        print('✅ 数据获取脚本已准备')
        print('✅ 实施计划已制定')
        print('✅ 资源需求已评估')
        
        print(f'\n🚀 下一步行动:')
        print('   1. 执行数据获取脚本')
        print('   2. 监控数据获取进度')
        print('   3. 验证数据质量')
        print('   4. 准备第2阶段因子开发')
        
        print(f'\n💎 这将为策略带来质的飞跃:')
        print('   - 数据维度: 5个字段 → 50+字段')
        print('   - 数据深度: 50天 → 3年')
        print('   - 分析能力: 基础 → 专业级')
        
    else:
        print('❌ 第1阶段准备失败，请检查数据库配置')

if __name__ == '__main__':
    main()
