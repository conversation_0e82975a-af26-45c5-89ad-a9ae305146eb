# 万和交易策略系统 - 数据文件说明

本文档介绍了数据文件夹(`data/`)中各个文件的内容、结构和用途，以帮助您理解和使用交易系统产生的数据。

## 主要数据文件

### 1. `trades.db` - 核心数据库文件

SQLite数据库文件，存储所有交易记录和技术分析数据。

#### 数据库结构

数据库包含两个主要表：

##### 1.1 `trades` 表 - 交易记录表

存储所有交易操作记录，包括买入和卖出交易。

**主要字段**：
- `id`: 自增主键
- `timestamp`: 交易时间
- `symbol`: 股票代码
- `action`: 交易动作（BUY/SELL）
- `price`: 交易价格
- `volume`: 交易数量
- `trend_strength_buy`: 买入时的趋势强度
- `ma3_buy`: 买入时的3日均线
- `ma7_buy`: 买入时的7日均线
- `ma_cross_buy_signal_buy`: 买入时均线交叉信号
- `sell_reason`: 卖出原因
- `cost_price_sell`: 卖出时的成本价
- `net_profit_pct_sell`: 卖出时的净利润百分比
- `confirmed_high_sell`: 卖出时确认的最高价
- `confirmed_high_time`: 最高价的时间
- `holding_hours`: 持仓时间（小时）
- `max_profit_pct`: 最大盈利百分比
- `final_drawdown_pct`: 最终回撤百分比
- `trix_buy`: 买入时的TRIX指标值
- `volatility`: 波动率
- `atr_pct`: ATR百分比
- `volatility_score`: 波动性得分
- `allocation_factor`: 资金分配系数

**新增技术指标字段**：
- 趋势类指标：
  - `adx`: 平均趋向指数，衡量趋势强度
  - `dmi_plus`: DMI正向指标，衡量上升趋势强度
  - `dmi_minus`: DMI负向指标，衡量下降趋势强度
  - `bb_width`: 布林带宽度，反映市场波动性
  - `bb_position`: 价格在布林带中的相对位置（0-1之间）

- 动量类指标：
  - `rsi`: 相对强弱指标，判断超买超卖状态
  - `cci`: 顺势指标，判断价格偏离移动平均线的程度
  - `macd`: MACD指标值
  - `macd_signal`: MACD信号线值
  - `macd_hist`: MACD柱状图值（MACD与信号线的差值）

- 成交量指标：
  - `relative_volume`: 相对成交量（当日成交量/N日平均成交量）
  - `volume_change_rate`: 成交量变化率
  - `obv`: 能量潮指标，反映成交量与价格变化的关系

- 市场环境指标：
  - `industry_relative_strength`: 个股相对行业指数的强度
  - `market_correlation`: 与大盘的相关性系数

- 基本面快照：
  - `pe_ratio`: 市盈率
  - `pb_ratio`: 市净率
  - `roe`: 净资产收益率

- 买入时机指标：
  - `distance_from_high`: 距离前期高点的回撤百分比
  - `ma_system_status`: 均线系统形态（1=多头排列，-1=空头排列，0=未知）

**索引**：
- `idx_trades_symbol`: 基于股票代码的索引
- `idx_trades_timestamp`: 基于交易时间的索引

##### 1.2 `analysis` 表 - 技术分析数据表

存储对股票的技术分析结果，包括多种技术指标。

**主要字段**：
- `id`: 自增主键
- `timestamp`: 分析时间
- `symbol`: 股票代码
- `current_price`: 当前价格
- `ma3`: 3日均线
- `ma7`: 7日均线
- `ma20`: 20日均线
- `rsi`: 相对强弱指标
- `macd`: MACD指标
- `kdj_k`: KDJ指标的K值
- `kdj_d`: KDJ指标的D值
- `boll_middle`: 布林带中轨
- `volume_ratio`: 成交量比率
- `trend_strength`: 趋势强度
- `ma_cross_buy_signal`: 均线交叉买入信号

**索引**：
- `idx_analysis_symbol`: 基于股票代码的索引

### 2. `trade_log.csv` - 交易日志文件

CSV格式的交易记录，与数据库的`trades`表内容一致。

**用途**：
- 提供易读的交易记录格式
- 作为数据库的备份
- 支持使用电子表格软件直接查看和分析

### 3. `analysis_log.csv` - 分析日志文件

CSV格式的技术分析记录，与数据库的`analysis`表内容一致。

**用途**：
- 记录每次分析时计算的技术指标
- 支持技术指标趋势的跟踪和研究
- 作为数据库的备份

### 4. `exports/` - 数据导出目录

包含从数据库导出的各种数据文件。

**可能包含的文件**：
- 表导出的CSV文件
- 定制查询结果
- 数据分析输出

## 数据管理与维护

### 数据备份

系统定期将数据库文件备份到`backups/`目录，文件名格式为`db_backup_YYYYMMDD_HHMMSS/trades.db`。

您也可以使用数据库管理工具手动创建备份：
```
python scripts/db_manager.py --backup
```

或者通过启动脚本的菜单选项：
```
python start_db_tools.py
# 选择选项 3: 备份数据库
```

### 数据清空

如果需要清空数据库中的所有数据，可以使用以下方法：

1. **使用启动脚本**:
   ```
   python start_db_tools.py
   # 选择选项 2: 清空数据库数据
   ```

2. **使用命令行**:
   ```
   python scripts/db_manager.py --clear-all
   ```

3. **清空单个表**:
   ```
   python scripts/db_manager.py --clear trades
   # 或
   python scripts/db_manager.py --clear analysis
   ```

> **警告**: 清空操作是不可逆的，执行前请确保已经备份数据库。

### 数据同步

交易系统将数据同时保存到数据库和CSV文件，确保数据的冗余存储和可靠性。

### 数据优化

可以使用以下命令优化数据库：
```
sqlite3 data/trades.db "VACUUM;"
```

### 数据完整性检查

可以使用以下命令检查数据库完整性：
```
sqlite3 data/trades.db "PRAGMA integrity_check;"
```

## 使用数据进行分析

### 查询示例

1. **获取特定股票的所有交易记录**:
   ```sql
   SELECT * FROM trades WHERE symbol='SHSE.600000' ORDER BY timestamp;
   ```

2. **计算总体交易性能**:
   ```sql
   SELECT 
       COUNT(*) AS total_trades,
       SUM(CASE WHEN action='BUY' THEN 1 ELSE 0 END) AS buys,
       SUM(CASE WHEN action='SELL' THEN 1 ELSE 0 END) AS sells,
       AVG(CASE WHEN action='SELL' THEN net_profit_pct_sell ELSE NULL END) AS avg_profit_pct
   FROM trades;
   ```

3. **分析最近的技术指标**:
   ```sql
   SELECT * FROM analysis 
   WHERE timestamp > date('now', '-7 days') 
   ORDER BY timestamp DESC;
   ```

### 数据导出

可以使用数据管理工具将数据导出为CSV文件，方便进一步分析：
```
python scripts/db_manager.py --export trades
```

## 注意事项

1. 不要直接删除或修改数据文件，使用提供的脚本和工具进行操作
2. 定期备份数据文件夹，特别是在进行系统更新前
3. 如果CSV文件和数据库内容不一致，以数据库为准
4. 数据库文件可能会随着交易记录的增加而变大，定期使用VACUUM命令优化

## 工具与脚本

- `scripts/db_manager.py`: 基本数据库管理工具
- `scripts/db_advanced_tools.py`: 高级数据处理和分析工具
- `scripts/data_manager.py`: 数据访问和管理API

## 故障排除

如果遇到数据文件损坏或不一致的情况：

1. 检查最新的备份是否可用
2. 使用完整性检查工具验证数据库
3. 尝试从CSV文件重建数据库
4. 如果以上方法都不起作用，联系系统管理员或开发者 