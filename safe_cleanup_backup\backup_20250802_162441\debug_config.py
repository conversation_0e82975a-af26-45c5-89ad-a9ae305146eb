# coding=utf-8
"""
调试配置读取
"""

import config

def debug_config():
    print('🔍 调试配置读取')
    print('=' * 40)
    
    # 直接访问配置
    print(f'SMART_SCORING_CONFIG: {config.SMART_SCORING_CONFIG}')
    print(f'enable_timeseries_analysis: {config.enable_timeseries_analysis}')
    print(f'MULTIFACTOR_CONFIRMATIONS: {config.MULTIFACTOR_CONFIRMATIONS}')
    print(f'TRAILING_STOP: {config.TRAILING_STOP}')

if __name__ == '__main__':
    debug_config()
