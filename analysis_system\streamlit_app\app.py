import streamlit as st
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import sys
import time
import seaborn as sns
import sqlite3
import json
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置文件，用于获取买入指标参数
try:
    import config as strategy_config
except ImportError:
    strategy_config = None

# 导入分析模块
from analyze_trades import analyze_trades
from analyze_trades_db import analyze_trades_db
# 导入数据加载工具
from utils.data_loader import clear_database, clear_csv_data, rebuild_database, clear_analysis_results

def analyze_jq_data(jq_files, progress_callback=None):
    """
    分析掘金量化导出的数据
    
    参数:
    jq_files (dict): 包含掘金量化数据文件路径的字典
    progress_callback (function): 进度回调函数
    """
    # 定义总步骤数
    total_steps = 10
    current_step = 0
    
    # 更新进度
    if progress_callback:
        progress_callback(current_step, total_steps, "正在开始分析掘金量化数据...")
    
    # 确保输出目录存在
    output_file = 'reports/trade_analysis_results.csv'
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "已创建输出目录")
    
    try:
        # 读取掘金量化交易数据
        trades_file = jq_files["交易数据"]
        if progress_callback:
            progress_callback(current_step, total_steps, f"正在读取交易数据: {os.path.basename(trades_file)}")
        
        jq_trades = pd.read_csv(trades_file)
        print(f"成功读取交易数据，共{len(jq_trades)}条记录")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已读取交易数据，共{len(jq_trades)}条记录")
        
        # 读取掘金量化持仓数据
        positions_file = jq_files["持仓数据"]
        if progress_callback:
            progress_callback(current_step, total_steps, f"正在读取持仓数据: {os.path.basename(positions_file)}")
        
        jq_positions = pd.read_csv(positions_file)
        print(f"成功读取持仓数据，共{len(jq_positions)}条记录")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已读取持仓数据，共{len(jq_positions)}条记录")
        
        # 读取掘金量化净值数据
        nav_file = jq_files["净值数据"]
        if progress_callback:
            progress_callback(current_step, total_steps, f"正在读取净值数据: {os.path.basename(nav_file)}")
        
        jq_nav = pd.read_csv(nav_file)
        print(f"成功读取净值数据，共{len(jq_nav)}条记录")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已读取净值数据，共{len(jq_nav)}条记录")
        
        # 数据预处理
        if progress_callback:
            progress_callback(current_step, total_steps, "正在处理交易数据...")
        
        # 标准化列名
        jq_trades.columns = [col.lower() for col in jq_trades.columns]
        
        # 检查必要的列是否存在
        required_columns = ['symbol', 'side', 'trade_time', 'vwap', 'volume']
        missing_columns = [col for col in required_columns if col not in jq_trades.columns]
        if missing_columns:
            error_msg = f"交易数据缺少必要的列: {', '.join(missing_columns)}"
            print(error_msg)
            if progress_callback:
                progress_callback(total_steps, total_steps, f"错误: {error_msg}")
            return None
        
        # 确保时间列是datetime类型
        jq_trades['trade_time'] = pd.to_datetime(jq_trades['trade_time'])
        
        # 分离买入和卖出记录
        buy_records = jq_trades[jq_trades['side'] == '买入'].copy()
        sell_records = jq_trades[jq_trades['side'] == '卖出'].copy()
        
        # 计算交易金额
        buy_records['amount'] = buy_records['vwap'] * buy_records['volume']
        sell_records['amount'] = sell_records['vwap'] * sell_records['volume']
        
        print(f"买入记录: {len(buy_records)}条")
        print(f"卖出记录: {len(sell_records)}条")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已分离买入({len(buy_records)}条)和卖出({len(sell_records)}条)记录")
        
        # 创建完整交易记录
        complete_trades = []
        skipped_trades = []
        
        # 更新进度
        if progress_callback:
            progress_callback(current_step, total_steps, "正在匹配买卖记录...")
        
        # 按照掘金平台的方式处理交易记录
        # 1. 按照股票代码分组
        symbols = jq_trades['symbol'].unique()
        
        for symbol in symbols:
            symbol_buys = buy_records[buy_records['symbol'] == symbol].sort_values('trade_time')
            symbol_sells = sell_records[sell_records['symbol'] == symbol].sort_values('trade_time')
            
            if len(symbol_buys) == 0 or len(symbol_sells) == 0:
                continue
                
            # 使用先进先出(FIFO)方法匹配买卖记录
            for _, sell in symbol_sells.iterrows():
                sell_time = sell['trade_time']
                sell_volume = sell['volume']
                sell_price = sell['vwap']
                
                # 找出该卖出前的所有买入记录
                available_buys = symbol_buys[symbol_buys['trade_time'] < sell_time].copy()
                
                if len(available_buys) == 0:
                    skipped_trades.append({
                        'symbol': symbol,
                        'time': sell_time,
                        'reason': '没有找到匹配的买入记录'
                    })
                    continue
                
                # 计算持仓时间和收益
                buy = available_buys.iloc[0]  # 取最早的买入记录
                
                # 计算持仓时间（小时）
                holding_hours = (sell_time - buy['trade_time']).total_seconds() / 3600
                
                # 计算收益率和绝对收益
                profit_pct = (sell_price - buy['vwap']) / buy['vwap'] * 100
                profit_amount = (sell_price - buy['vwap']) * sell_volume
                
                # 合并买入和卖出数据
                trade_record = {
                    # 交易基本信息
                    'Symbol': symbol,
                    'Buy_Time': buy['trade_time'],
                    'Sell_Time': sell_time,
                    'Holding_Hours': holding_hours,
                    'Buy_Price': buy['vwap'],
                    'Sell_Price': sell_price,
                    'Volume': sell_volume,
                    'Buy_Amount': buy['vwap'] * sell_volume,
                    'Sell_Amount': sell_price * sell_volume,
                    'Profit_Amount': profit_amount,
                    'Profit_Pct': profit_pct,
                    
                    # 计算其他指标
                    'Trade_Result': 'Win' if profit_pct > 0 else 'Loss',
                }
                
                complete_trades.append(trade_record)
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, "完成买卖记录匹配")
        
        # 创建完整交易DataFrame
        if complete_trades:
            complete_df = pd.DataFrame(complete_trades)
            print(f"成功匹配{len(complete_df)}笔完整交易")
            
            if len(skipped_trades) > 0:
                print(f"警告: {len(skipped_trades)}笔交易因无法匹配而被跳过")
            
            current_step += 1
            if progress_callback:
                progress_callback(current_step, total_steps, f"成功匹配{len(complete_df)}笔完整交易，正在保存结果...")
            
            # 保存完整交易记录
            complete_df.to_csv(output_file, index=False)
            print(f"交易分析结果已保存到 {output_file}")
            
            # 保存跳过的交易记录（如果有）
            if skipped_trades:
                skipped_df = pd.DataFrame(skipped_trades)
                skipped_df.to_csv('reports/skipped_trades.csv', index=False)
                print(f"跳过的交易记录已保存到 reports/skipped_trades.csv")
            
            current_step += 1
            if progress_callback:
                progress_callback(current_step, total_steps, "已保存交易分析结果，开始分析交易表现...")
            
            # 分析交易表现
            analyze_jq_trade_performance(complete_df, jq_nav, progress_callback, current_step, total_steps)
            
            return complete_df
        else:
            print("没有找到匹配的完整交易记录")
            if progress_callback:
                progress_callback(total_steps, total_steps, "没有找到匹配的完整交易记录")
            return None
            
    except Exception as e:
        error_msg = f"分析掘金量化数据失败: {e}"
        print(error_msg)
        import traceback
        traceback.print_exc()  # 打印详细错误信息
        if progress_callback:
            progress_callback(total_steps, total_steps, f"错误: {error_msg}")
        return None

def analyze_jq_trade_performance(df, nav_df, progress_callback=None, current_step=0, total_steps=10):
    """
    分析交易表现
    
    参数:
    df (DataFrame): 交易数据
    nav_df (DataFrame): 净值数据
    progress_callback (function): 进度回调函数
    current_step (int): 当前进度步骤
    total_steps (int): 总步骤数
    """
    print("\n========== 交易表现分析 ==========")
    
    # 基本统计
    total_trades = len(df)
    winning_trades = len(df[df['Profit_Pct'] > 0])
    losing_trades = len(df[df['Profit_Pct'] <= 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    
    # 计算收益相关指标
    avg_profit_pct = df['Profit_Pct'].mean()
    avg_profit_amount = df['Profit_Amount'].mean()
    
    avg_win_pct = df[df['Profit_Pct'] > 0]['Profit_Pct'].mean() if winning_trades > 0 else 0
    avg_win_amount = df[df['Profit_Pct'] > 0]['Profit_Amount'].mean() if winning_trades > 0 else 0
    
    avg_loss_pct = df[df['Profit_Pct'] <= 0]['Profit_Pct'].mean() if losing_trades > 0 else 0
    avg_loss_amount = df[df['Profit_Pct'] <= 0]['Profit_Amount'].mean() if losing_trades > 0 else 0
    
    # 计算最大盈利和亏损
    max_profit_pct = df['Profit_Pct'].max() if not df.empty else 0
    max_profit_amount = df['Profit_Amount'].max() if not df.empty else 0
    
    min_profit_pct = df['Profit_Pct'].min() if not df.empty else 0
    min_profit_amount = df['Profit_Amount'].min() if not df.empty else 0
    
    # 计算利润因子
    total_profit = df[df['Profit_Amount'] > 0]['Profit_Amount'].sum() if not df.empty else 0
    total_loss = abs(df[df['Profit_Amount'] < 0]['Profit_Amount'].sum()) if not df.empty else 0
    profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
    
    # 计算盈亏比
    profit_loss_ratio = abs(avg_win_pct/avg_loss_pct) if avg_loss_pct != 0 else float('inf')
    
    # 保存交易统计指标
    stats = {
        '平均交易次数': total_trades,
        '盈利交易次数': winning_trades,
        '亏损交易次数': losing_trades,
        '胜率': win_rate,
        '平均收益率(%)': avg_profit_pct,
        '平均收益金额': avg_profit_amount,
        '平均盈利率(%)': avg_win_pct,
        '平均盈利金额': avg_win_amount,
        '平均亏损率(%)': avg_loss_pct,
        '平均亏损金额': avg_loss_amount,
        '最大单笔收益率(%)': max_profit_pct,
        '最大单笔收益金额': max_profit_amount,
        '最大单笔亏损率(%)': min_profit_pct,
        '最大单笔亏损金额': min_profit_amount,
        '盈亏比': profit_loss_ratio,
        '利润因子': profit_factor
    }
    
    # 将统计指标保存为CSV
    pd.DataFrame([stats]).to_csv('reports/trade_stats.csv', index=False)
    
    print(f"总交易次数: {total_trades}")
    print(f"盈利交易: {winning_trades} ({win_rate:.2f}%)")
    print(f"亏损交易: {losing_trades} ({100-win_rate:.2f}%)")
    print(f"平均收益率: {avg_profit_pct:.2f}%")
    print(f"平均收益金额: {avg_profit_amount:.2f}")
    print(f"平均盈利率: {avg_win_pct:.2f}%")
    print(f"平均盈利金额: {avg_win_amount:.2f}")
    print(f"平均亏损率: {avg_loss_pct:.2f}%")
    print(f"平均亏损金额: {avg_loss_amount:.2f}")
    print(f"盈亏比: {profit_loss_ratio:.2f}")
    print(f"利润因子: {profit_factor:.2f}")
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在分析交易统计数据...")
    
    # 按持仓时间分析
    df['Holding_Days'] = df['Holding_Hours'] / 24
    print("\n按持仓时间分析:")
    df['Holding_Group'] = pd.cut(df['Holding_Days'], 
                                bins=[0, 1, 2, 3, 5, 10, float('inf')],
                                labels=['0-1天', '1-2天', '2-3天', '3-5天', '5-10天', '10天以上'])
    holding_stats = df.groupby('Holding_Group').agg({
        'Profit_Pct': ['count', 'mean'],
        'Profit_Amount': ['sum', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    
    # 重命名列
    holding_stats.columns = ['持仓时间', '交易次数', '平均收益率(%)', '总收益金额', '平均收益金额', '胜率(%)']
    
    # 保存持仓时间分析结果
    holding_stats.to_csv('reports/holding_time_analysis.csv', index=False)
    
    print(holding_stats)
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在生成分析图表...")
    
    # 绘制图表
    generate_jq_charts(df, nav_df)
    
    # 最终进度
    if progress_callback:
        progress_callback(total_steps, total_steps, "交易分析完成!")
    
    print("交易分析完成!")
    return df

def generate_jq_charts(df, nav_df):
    """生成掘金量化数据分析图表"""
    # 确保输出目录存在
    charts_dir = 'reports/charts'
    os.makedirs(charts_dir, exist_ok=True)
    
    # 1. 收益分布图 (百分比)
    plt.figure(figsize=(10, 6))
    sns.histplot(df['Profit_Pct'], bins=30, kde=True)
    plt.title('交易收益率分布')
    plt.xlabel('收益率 (%)')
    plt.ylabel('频率')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(charts_dir, 'profit_distribution_pct.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 2. 收益分布图 (金额)
    plt.figure(figsize=(10, 6))
    sns.histplot(df['Profit_Amount'], bins=30, kde=True)
    plt.title('交易收益金额分布')
    plt.xlabel('收益金额')
    plt.ylabel('频率')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(charts_dir, 'profit_distribution_amount.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 3. 持仓时间与收益关系图
    plt.figure(figsize=(10, 6))
    plt.scatter(df['Holding_Hours'], df['Profit_Pct'], alpha=0.6)
    plt.title('持仓时间与收益率关系')
    plt.xlabel('持仓时间 (小时)')
    plt.ylabel('收益率 (%)')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(charts_dir, 'holding_profit_pct.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 4. 持仓时间与收益金额关系图
    plt.figure(figsize=(10, 6))
    plt.scatter(df['Holding_Hours'], df['Profit_Amount'], alpha=0.6)
    plt.title('持仓时间与收益金额关系')
    plt.xlabel('持仓时间 (小时)')
    plt.ylabel('收益金额')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(charts_dir, 'holding_profit_amount.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 5. 累计收益曲线
    if 'date' in nav_df.columns and 'nav' in nav_df.columns:
        plt.figure(figsize=(12, 6))
        nav_df['date'] = pd.to_datetime(nav_df['date'])
        nav_df = nav_df.sort_values('date')
        plt.plot(nav_df['date'], nav_df['nav'], linewidth=2)
        plt.title('策略净值曲线')
        plt.xlabel('日期')
        plt.ylabel('净值')
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(charts_dir, 'nav_curve.png'), dpi=150, bbox_inches='tight')
        plt.close()
        
    # 6. 交易结果饼图
    plt.figure(figsize=(8, 8))
    win_loss = df['Trade_Result'].value_counts()
    plt.pie(win_loss, labels=win_loss.index, autopct='%1.1f%%', startangle=90, colors=['#66b3ff', '#ff9999'])
    plt.title('交易结果分布')
    plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle
    plt.savefig(os.path.join(charts_dir, 'trade_result_pie.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # 7. 每月交易次数和收益
    if 'Buy_Time' in df.columns:
        df['Month'] = df['Buy_Time'].dt.to_period('M')
        monthly_stats = df.groupby('Month').agg({
            'Symbol': 'count',
            'Profit_Amount': 'sum',
            'Profit_Pct': 'mean'
        }).reset_index()
        monthly_stats.columns = ['Month', 'Trade_Count', 'Total_Profit', 'Avg_Profit_Pct']
        
        # 绘制每月交易次数和总收益
        fig, ax1 = plt.subplots(figsize=(12, 6))
        
        ax1.set_xlabel('月份')
        ax1.set_ylabel('交易次数', color='tab:blue')
        ax1.bar(monthly_stats['Month'].astype(str), monthly_stats['Trade_Count'], color='tab:blue', alpha=0.7)
        ax1.tick_params(axis='y', labelcolor='tab:blue')
        
        ax2 = ax1.twinx()
        ax2.set_ylabel('总收益', color='tab:red')
        ax2.plot(monthly_stats['Month'].astype(str), monthly_stats['Total_Profit'], color='tab:red', marker='o')
        ax2.tick_params(axis='y', labelcolor='tab:red')
        
        plt.title('每月交易次数和总收益')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(charts_dir, 'monthly_performance.png'), dpi=150, bbox_inches='tight')
        plt.close()

# 设置页面配置
st.set_page_config(
    page_title="万和策略分析系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 应用标题
st.title("万和策略分析系统")
st.markdown("---")

# 检查环境
if "initialized" not in st.session_state:
    st.session_state.initialized = True
    # 确保目录存在
    for dir_path in ["data", "reports", "reports/html"]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 清理所有刷新标记文件
    for file in os.listdir("data"):
        if file.startswith("refresh_token_"):
            try:
                os.remove(os.path.join("data", file))
            except:
                pass
    
    st.success("系统环境初始化完成")

# 主界面布局
tab1, tab2, tab3, tab4 = st.tabs(["交易分析", "策略优化", "指标优化", "报告生成"])

with tab1:
    st.header("交易数据分析")
    
    # 添加清空分析结果按钮
    col_header1, col_header2 = st.columns([3, 1])
    with col_header1:
        st.write("分析交易数据，生成交易统计和性能指标。")
    with col_header2:
        if os.path.exists("reports/trade_analysis_results.csv"):
            if st.button("清空分析结果", key="clear_results_tab1", help="清空所有分析结果文件"):
                success, message = clear_analysis_results()
                if success:
                    st.success(message)
                    # 清除Streamlit缓存
                    st.cache_data.clear()
                    # 刷新页面
                    st.experimental_rerun()
                else:
                    st.error(message)
    
    # 数据源选择
    data_source = st.radio(
        "选择数据源",
        ["CSV文件", "SQLite数据库", "掘金量化导出表"],
        index=1 if os.path.exists("data/trades.db") else 0,
        help="选择从CSV文件、SQLite数据库或掘金量化导出表加载交易数据"
    )
    
    # 数据检查
    trade_log_exists = os.path.exists("data/trade_log.csv")
    analysis_log_exists = os.path.exists("data/analysis_log.csv")
    trades_db_exists = os.path.exists("data/trades.db")
    
    # 掘金量化数据路径
    jq_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "回测数据")
    
    # 查找掘金量化数据文件（根据前缀查找）
    def find_latest_file(directory, prefix):
        """在指定目录中查找以特定前缀开头的最新CSV文件"""
        if not os.path.exists(directory):
            return None
        
        matching_files = [f for f in os.listdir(directory) 
                         if f.startswith(prefix) and f.endswith('.csv')]
        
        if not matching_files:
            return None
        
        # 如果有多个匹配的文件，返回最新的一个
        latest_file = max(matching_files, key=lambda f: os.path.getmtime(os.path.join(directory, f)))
        return os.path.join(directory, latest_file)
    
    # 根据数据源显示不同的提示
    if data_source == "CSV文件" and not trade_log_exists:
        st.warning("未找到交易日志文件(data/trade_log.csv)，请先上传数据")
        
        # 文件上传区
        uploaded_file = st.file_uploader("上传交易日志", type=["csv"])
        if uploaded_file is not None:
            with open(os.path.join("data", "trade_log.csv"), "wb") as f:
                f.write(uploaded_file.getvalue())
            st.success("交易日志上传成功！")
            trade_log_exists = True
    
    elif data_source == "SQLite数据库" and not trades_db_exists:
        st.warning("未找到交易数据库(data/trades.db)，请先上传数据")
        
        # 数据库文件上传区
        uploaded_db = st.file_uploader("上传交易数据库", type=["db"])
        if uploaded_db is not None:
            with open(os.path.join("data", "trades.db"), "wb") as f:
                f.write(uploaded_db.getvalue())
            st.success("交易数据库上传成功！")
            trades_db_exists = True
    
    elif data_source == "掘金量化导出表":
        # 允许用户指定掘金量化数据目录
        custom_jq_path = st.text_input("掘金量化数据目录路径", value=jq_data_path)
        jq_data_path = custom_jq_path if custom_jq_path else jq_data_path
        
        # 检查目录是否存在
        if not os.path.exists(jq_data_path):
            st.error(f"掘金量化回测数据目录不存在: {jq_data_path}")
            st.info(f"请创建目录 '{jq_data_path}' 并将掘金量化导出的CSV文件放入该目录。")
            
            # 创建目录选项
            if st.button("创建目录"):
                try:
                    os.makedirs(jq_data_path, exist_ok=True)
                    st.success(f"已成功创建目录: {jq_data_path}")
                    st.experimental_rerun()
                except Exception as e:
                    st.error(f"创建目录失败: {str(e)}")
        else:
            # 查找掘金量化数据文件
            jq_files = {
                "交易数据": find_latest_file(jq_data_path, "交易数据_"),
                "持仓数据": find_latest_file(jq_data_path, "持仓数据_"),
                "净值数据": find_latest_file(jq_data_path, "净值数据_")
            }
            
            # 检查是否找到了所有需要的文件
            missing_files = [name for name, path in jq_files.items() if path is None]
            if missing_files:
                st.error(f"以下掘金量化数据文件不存在: {', '.join(missing_files)}")
                st.info(f"请确保以下文件位于 {jq_data_path} 目录下：")
                for name in missing_files:
                    st.write(f"- {name}_*.csv (文件名以'{name}_'开头的CSV文件)")
                
                # 上传文件选项
                for name in missing_files:
                    uploaded_file = st.file_uploader(f"上传{name}", type=["csv"], key=f"upload_{name}")
                    if uploaded_file is not None:
                        file_path = os.path.join(jq_data_path, f"{name}_{datetime.now().strftime('%Y%m%d')}.csv")
                        with open(file_path, "wb") as f:
                            f.write(uploaded_file.getvalue())
                        st.success(f"{name}上传成功！")
                        # 刷新页面
                        st.experimental_rerun()
            else:
                # 显示找到的文件
                st.success("已找到所有必要的掘金量化数据文件")
                for name, path in jq_files.items():
                    if path:
                        st.info(f"{name}: {os.path.basename(path)}")
    
    # 如果选择了SQLite数据库且数据库文件存在，显示数据库查看器链接
    if data_source == "SQLite数据库" and trades_db_exists:
        st.info("您可以使用数据库查看器直接查看和操作数据库内容。")
        if st.button("打开数据库查看器", help="打开数据库查看器页面，可以直接查看表内容和执行SQL查询"):
            # 使用Streamlit的页面导航功能跳转到数据库查看器页面
            # 注意：Streamlit中页面跳转需要通过页面URL参数实现
            st.markdown(f'<meta http-equiv="refresh" content="0;url=/{os.path.basename(os.path.dirname(os.path.abspath(__file__)))}/数据库查看器">', unsafe_allow_html=True)
            st.markdown('[如果没有自动跳转，请点击这里](/数据库查看器)', unsafe_allow_html=True)
    
    # 显示数据状态和分析按钮
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # 根据选择的数据源决定运行哪个分析函数
        jq_files_ready = False
        if data_source == "掘金量化导出表" and os.path.exists(jq_data_path):
            # 检查掘金量化数据文件是否都存在
            jq_files = {
                "交易数据": find_latest_file(jq_data_path, "交易数据_"),
                "持仓数据": find_latest_file(jq_data_path, "持仓数据_"),
                "净值数据": find_latest_file(jq_data_path, "净值数据_")
            }
            jq_files_ready = all(path is not None for path in jq_files.values())
        
        if (data_source == "CSV文件" and trade_log_exists) or \
           (data_source == "SQLite数据库" and trades_db_exists) or \
           (data_source == "掘金量化导出表" and jq_files_ready):
            if st.button("运行交易分析", key="run_analysis"):
                # 创建进度条
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    # 定义进度回调函数
                    def progress_callback(step, total_steps, message):
                        progress = int(step / total_steps * 100)
                        progress_bar.progress(progress)
                        status_text.text(f"进度: {progress}% - {message}")
                    
                    # 显示初始状态
                    status_text.text("正在开始分析...")
                    
                    # 根据数据源调用不同的分析函数
                    if data_source == "CSV文件":
                        analyze_trades(progress_callback=progress_callback)
                    elif data_source == "SQLite数据库":
                        analyze_trades_db(progress_callback=progress_callback)
                    else:  # 掘金量化导出表
                        analyze_jq_data(jq_files, progress_callback=progress_callback)
                    
                    # 完成进度显示
                    progress_bar.progress(100)
                    status_text.text("分析完成！")
                    time.sleep(1)  # 显示完成状态一秒
                    
                    # 显示成功消息
                    st.success("交易分析完成！")
                    
                    # 重新加载数据
                    if os.path.exists("reports/trade_analysis_results.csv"):
                        df = pd.read_csv("reports/trade_analysis_results.csv")
                        st.write("### 分析结果摘要")
                        
                        # 计算买入金额和卖出金额以及实际收益率
                        if 'Buy_Amount' not in df.columns:
                            df['Buy_Amount'] = df['Buy_Price'] * df['Volume']
                        if 'Sell_Amount' not in df.columns:
                            df['Sell_Amount'] = df['Sell_Price'] * df['Volume']
                        if 'Profit_Amount' not in df.columns:
                            df['Profit_Amount'] = df['Sell_Amount'] - df['Buy_Amount']
                        if 'Actual_Profit_Pct' not in df.columns:
                            df['Actual_Profit_Pct'] = (df['Profit_Amount'] / df['Buy_Amount'] * 100)
                        
                        # 创建两行指标，每行3个
                        metrics_row1_col1, metrics_row1_col2, metrics_row1_col3 = st.columns(3)
                        metrics_row2_col1, metrics_row2_col2, metrics_row2_col3 = st.columns(3)
                        metrics_row3_col1, metrics_row3_col2, metrics_row3_col3 = st.columns(3)
                        
                        # 基本交易统计
                        with metrics_row1_col1:
                            st.metric("交易总数", f"{len(df)}")
                        
                        with metrics_row1_col2:
                            win_count = (df["Actual_Profit_Pct"] > 0).sum()
                            st.metric("盈利交易数", f"{win_count}")
                            
                        with metrics_row1_col3:
                            loss_count = (df["Actual_Profit_Pct"] <= 0).sum()
                            st.metric("亏损交易数", f"{loss_count}")
                        
                        # 胜率和盈亏比
                        with metrics_row2_col1:
                            win_rate = (df["Actual_Profit_Pct"] > 0).mean() * 100
                            st.metric("胜率", f"{win_rate:.2f}%")
                        
                        with metrics_row2_col2:
                            avg_win = df[df["Actual_Profit_Pct"] > 0]["Actual_Profit_Pct"].mean()
                            avg_loss = abs(df[df["Actual_Profit_Pct"] < 0]["Actual_Profit_Pct"].mean())
                            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf')
                            st.metric("盈亏比", f"{profit_loss_ratio:.2f}")
                            
                        with metrics_row2_col3:
                            total_profit = df[df["Profit_Amount"] > 0]["Profit_Amount"].sum()
                            total_loss = abs(df[df["Profit_Amount"] < 0]["Profit_Amount"].sum())
                            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
                            st.metric("利润因子", f"{profit_factor:.2f}")
                        
                        # 收益指标
                        with metrics_row3_col1:
                            avg_profit = df["Actual_Profit_Pct"].mean()
                            st.metric("平均收益率", f"{avg_profit:.2f}%")
                        
                        with metrics_row3_col2:
                            max_profit = df["Actual_Profit_Pct"].max()
                            st.metric("最大单笔收益", f"{max_profit:.2f}%")
                        
                        with metrics_row3_col3:
                            min_profit = df["Actual_Profit_Pct"].min()
                            st.metric("最大单笔亏损", f"{min_profit:.2f}%")
                        
                        # 添加图表展示
                        st.write("### 交易分析图表")
                        
                        # 检查图表是否存在
                        charts_dir = 'reports/charts'
                        available_charts = []
                        
                        chart_files = {
                            "收益分布图(百分比)": "profit_distribution_pct.png",
                            "收益分布图(金额)": "profit_distribution_amount.png",
                            "持仓时间与收益关系": "holding_profit_pct.png",
                            "持仓时间与收益金额关系": "holding_profit_amount.png",
                            "净值曲线": "nav_curve.png",
                            "交易结果分布": "trade_result_pie.png",
                            "每月交易表现": "monthly_performance.png"
                        }
                        
                        for chart_name, file_name in chart_files.items():
                            if os.path.exists(os.path.join(charts_dir, file_name)):
                                available_charts.append((chart_name, file_name))
                        
                        # 如果有图表，按2列布局显示
                        if available_charts:
                            # 创建选项卡以显示不同图表
                            chart_tabs = st.tabs([chart[0] for chart in available_charts])
                            
                            for i, (chart_name, file_name) in enumerate(available_charts):
                                with chart_tabs[i]:
                                    st.image(os.path.join(charts_dir, file_name), use_column_width=True)
                        
                        # 显示交易数据表格
                        with st.expander("查看详细交易记录"):
                            # 格式化日期时间列
                            if 'Buy_Time' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['Buy_Time']):
                                try:
                                    df['Buy_Time'] = pd.to_datetime(df['Buy_Time'])
                                except:
                                    pass
                            
                            if 'Sell_Time' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['Sell_Time']):
                                try:
                                    df['Sell_Time'] = pd.to_datetime(df['Sell_Time'])
                                except:
                                    pass
                                
                            # 选择要显示的列
                            display_columns = [
                                'Symbol', 'Buy_Time', 'Sell_Time', 'Buy_Price', 'Sell_Price', 
                                'Volume', 'Profit_Amount', 'Actual_Profit_Pct', 'Holding_Hours'
                            ]
                            
                            # 只显示存在的列
                            display_columns = [col for col in display_columns if col in df.columns]
                            
                            # 显示数据表
                            st.dataframe(df[display_columns], use_container_width=True)
                            
                            # 添加下载按钮
                            csv = df.to_csv(index=False).encode('utf-8')
                            st.download_button(
                                "下载完整交易数据",
                                csv,
                                "trade_analysis_results.csv",
                                "text/csv",
                                key='download-csv'
                            )
                except Exception as e:
                    status_text.text(f"错误: {str(e)}")
                    st.error(f"分析过程中出错: {str(e)}")
    
    with col2:
        # 根据选择的数据源显示相应的数据文件状态
        if data_source == "CSV文件":
            st.metric(
                label="可用数据文件", 
                value="交易日志" + ("，分析日志" if analysis_log_exists else "")
            )
            
            # 添加CSV数据管理选项
            if trade_log_exists:
                with st.expander("CSV数据管理"):
                    st.write("您可以在这里管理CSV交易数据。")
                    if st.button("清空CSV数据", help="清空所有CSV交易数据文件，但保留文件结构"):
                        # 显示确认对话框
                        confirm = st.warning("⚠️ 此操作将删除所有CSV交易数据且无法恢复。确定要继续吗？")
                        if st.button("确认清空CSV数据", key="confirm_clear_csv_main"):
                            success, message = clear_csv_data()
                            if success:
                                st.success(message)
                                # 清除Streamlit缓存
                                st.cache_data.clear()
                                # 刷新页面
                                st.experimental_rerun()
                            else:
                                st.error(message)
        else:  # SQLite数据库
            if trades_db_exists:
                # 尝试获取数据库中的记录数
                try:
                    conn = sqlite3.connect("data/trades.db")
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM trades")
                    count = cursor.fetchone()[0]
                    conn.close()
                    
                    # 使用st.empty()创建一个可以动态更新的容器
                    db_status_container = st.empty()
                    db_status_container.metric(label="数据库状态", value=f"可用 ({count}条记录)")
                    
                    # 添加数据库管理选项
                    with st.expander("数据库管理"):
                        st.write("您可以在这里管理数据库数据。")
                        
                        # 添加数据库查看器链接
                        if st.button("打开数据库查看器", key="open_db_viewer_in_expander", help="打开数据库查看器页面，可以直接查看表内容和执行SQL查询"):
                            # 使用Streamlit的页面导航功能跳转到数据库查看器页面
                            st.markdown(f'<meta http-equiv="refresh" content="0;url=/{os.path.basename(os.path.dirname(os.path.abspath(__file__)))}/数据库查看器">', unsafe_allow_html=True)
                            st.markdown('[如果没有自动跳转，请点击这里](/数据库查看器)', unsafe_allow_html=True)
                        
                        # 添加标准清空和彻底清空两个选项
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            if st.button("标准清空数据库", help="清空交易数据库中的所有记录，但保留表结构"):
                                # 显示确认对话框
                                confirm = st.warning("⚠️ 此操作将删除数据库中的所有交易记录且无法恢复。确定要继续吗？")
                                if st.button("确认标准清空", key="confirm_clear_db_main"):
                                    success, message = clear_database()
                                    if success:
                                        # 创建一个随机的刷新标记文件，强制Streamlit重新加载
                                        refresh_token = datetime.now().strftime("%Y%m%d%H%M%S")
                                        with open(f"data/refresh_token_{refresh_token}.tmp", "w") as f:
                                            f.write("refresh")
                                        
                                        # 清理旧的刷新标记文件
                                        for file in os.listdir("data"):
                                            if file.startswith("refresh_token_") and file != f"refresh_token_{refresh_token}.tmp":
                                                try:
                                                    os.remove(os.path.join("data", file))
                                                except:
                                                    pass
                                        
                                        st.success(message)
                                        # 清除Streamlit缓存
                                        st.cache_data.clear()
                                        # 刷新页面
                                        st.experimental_rerun()
                                    else:
                                        st.error(message)
                        
                        with col2:
                            if st.button("彻底重建数据库", help="删除并重新创建数据库文件，完全清空所有数据"):
                                # 显示确认对话框
                                confirm = st.warning("⚠️ 此操作将删除整个数据库并重新创建，所有数据将永久丢失。确定要继续吗？")
                                if st.button("确认彻底重建", key="confirm_rebuild_db_main"):
                                    success, message = rebuild_database()
                                    if success:
                                        # 创建一个随机的刷新标记文件，强制Streamlit重新加载
                                        refresh_token = datetime.now().strftime("%Y%m%d%H%M%S")
                                        with open(f"data/refresh_token_{refresh_token}.tmp", "w") as f:
                                            f.write("refresh")
                                        
                                        # 清理旧的刷新标记文件
                                        for file in os.listdir("data"):
                                            if file.startswith("refresh_token_") and file != f"refresh_token_{refresh_token}.tmp":
                                                try:
                                                    os.remove(os.path.join("data", file))
                                                except:
                                                    pass
                                        
                                        st.success(message)
                                        # 清除Streamlit缓存
                                        st.cache_data.clear()
                                        # 刷新页面
                                        st.experimental_rerun()
                                    else:
                                        st.error(message)
                except Exception as e:
                    st.error(f"获取数据库状态失败: {str(e)}")
                    st.metric(label="数据库状态", value="可用 (状态未知)")

with tab2:
    st.header("策略优化")
    
    # 添加清空分析结果按钮
    col_header1, col_header2 = st.columns([3, 1])
    with col_header1:
        st.write("使用机器学习方法优化交易策略，找出最佳参数组合。")
    with col_header2:
        if os.path.exists("reports/optimal_strategy_rules.txt"):
            if st.button("清空优化结果", key="clear_results_tab2", help="清空所有策略优化结果"):
                success, message = clear_analysis_results()
                if success:
                    st.success(message)
                    # 清除Streamlit缓存
                    st.cache_data.clear()
                    # 刷新页面
                    st.experimental_rerun()
                else:
                    st.error(message)
    
    # 检查分析结果是否存在
    analysis_results_exist = os.path.exists("reports/trade_analysis_results.csv")
    
    if not analysis_results_exist:
        st.warning("未找到交易分析结果，请先运行交易分析")
    else:
        # 显示优化参数选项
        st.subheader("优化参数设置")
        
        col1, col2 = st.columns(2)
        with col1:
            n_estimators = st.slider("决策树数量", 50, 500, 100, 50)
        with col2:
            test_size = st.slider("测试集比例", 0.1, 0.5, 0.3, 0.05)
        
        # 运行优化按钮
        if st.button("运行策略优化", key="run_optimization"):
            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # 导入优化函数
                from optimize_strategy import optimize_strategy
                
                # 定义进度回调函数
                def progress_callback(step, total_steps, message):
                    progress = int(step / total_steps * 100)
                    progress_bar.progress(progress)
                    status_text.text(f"进度: {progress}% - {message}")
                
                # 显示初始状态
                status_text.text("正在开始优化...")
                
                # 运行优化
                optimize_strategy(n_estimators=n_estimators, test_size=test_size, progress_callback=progress_callback)
                
                # 完成进度显示
                progress_bar.progress(100)
                status_text.text("优化完成！")
                time.sleep(1)  # 显示完成状态一秒
                
                # 显示成功消息
                st.success("策略优化完成！")
                
                # 显示优化结果
                if os.path.exists("reports/optimal_strategy_rules.txt"):
                    with open("reports/optimal_strategy_rules.txt", "r") as f:
                        rules_text = f.read()
                    
                    st.write("### 优化规则摘要")
                    st.text_area("策略规则", rules_text, height=300)
                    
                    if os.path.exists("reports/feature_importance.png"):
                        st.write("### 特征重要性")
                        st.image("reports/feature_importance.png")
                
            except Exception as e:
                status_text.text(f"错误: {str(e)}")
                st.error(f"优化过程中出错: {str(e)}")

with tab3:
    st.header("技术指标优化分析")
    
    # 添加清空分析结果按钮
    col_header1, col_header2 = st.columns([3, 1])
    with col_header1:
        st.write("通过选择技术指标及其范围，分析符合条件的交易胜率和收益率。")
    with col_header2:
        if os.path.exists("reports/indicator_optimization_results.csv"):
            if st.button("清空指标分析结果", key="clear_results_tab3", help="清空所有指标优化分析结果"):
                try:
                    os.remove("reports/indicator_optimization_results.csv")
                    if os.path.exists("reports/charts/indicator_optimization.png"):
                        os.remove("reports/charts/indicator_optimization.png")
                    st.success("指标分析结果已清空")
                    # 清除Streamlit缓存
                    st.cache_data.clear()
                    # 刷新页面
                    st.experimental_rerun()
                except Exception as e:
                    st.error(f"清空结果时出错: {str(e)}")
    
    # 检查是否有交易分析结果
    if not os.path.exists("reports/trade_analysis_results.csv"):
        st.warning("未找到交易分析结果，请先运行交易分析")
    else:
        # 读取交易分析结果
        df = pd.read_csv("reports/trade_analysis_results.csv")
        
        # 确保有Profit_Pct列或Actual_Profit_Pct列
        if 'Profit_Pct' not in df.columns and 'Actual_Profit_Pct' not in df.columns:
            st.error("交易数据缺少收益率信息，无法进行指标分析")
        else:
            # 使用可用的收益率列
            profit_col = 'Actual_Profit_Pct' if 'Actual_Profit_Pct' in df.columns else 'Profit_Pct'
            
            # 1. 获取可用的技术指标列
            # 排除一些基本列，只保留可能是技术指标的列
            basic_columns = ['Symbol', 'Buy_Time', 'Sell_Time', 'Buy_Price', 'Sell_Price', 
                            'Volume', 'Holding_Hours', 'Profit_Pct', 'Actual_Profit_Pct', 
                            'Buy_Amount', 'Sell_Amount', 'Profit_Amount', 'Trade_Result']
            
            indicator_columns = [col for col in df.columns if col not in basic_columns]
            
            # 从配置文件中获取买入指标信息
            strategy_indicators = {}
            if strategy_config:
                # 检查TRIX指标
                if hasattr(strategy_config, 'ENABLE_TRIX_BUY_SIGNAL') and strategy_config.ENABLE_TRIX_BUY_SIGNAL:
                    strategy_indicators['TRIX'] = f'启用 (TRIX买入信号)'
                    if hasattr(strategy_config, 'TRIX_EMA_PERIOD'):
                        strategy_indicators['TRIX'] += f', 周期={strategy_config.TRIX_EMA_PERIOD}'
                    strategy_indicators['TRIX_SIGNAL'] = '启用 (TRIX信号线)'
                    strategy_indicators['TRIX_HIST'] = '启用 (TRIX柱状图)'
                
                # 检查均线交叉指标
                if hasattr(strategy_config, 'ENABLE_MA_CROSS_BUY_SIGNAL') and strategy_config.ENABLE_MA_CROSS_BUY_SIGNAL:
                    ma_info = '启用 (均线交叉买入信号)'
                    if hasattr(strategy_config, 'MA_SHORT_PERIOD') and hasattr(strategy_config, 'MA_MID_PERIOD') and hasattr(strategy_config, 'MA_LONG_PERIOD'):
                        ma_info += f', 周期={strategy_config.MA_SHORT_PERIOD}/{strategy_config.MA_MID_PERIOD}/{strategy_config.MA_LONG_PERIOD}'
                    strategy_indicators['MA_Cross'] = ma_info
                    strategy_indicators[f'MA{strategy_config.MA_SHORT_PERIOD}'] = f'短期均线 (周期={strategy_config.MA_SHORT_PERIOD})'
                    strategy_indicators[f'MA{strategy_config.MA_MID_PERIOD}'] = f'中期均线 (周期={strategy_config.MA_MID_PERIOD})'
                    strategy_indicators[f'MA{strategy_config.MA_LONG_PERIOD}'] = f'长期均线 (周期={strategy_config.MA_LONG_PERIOD})'
                
                # 检查波动性指标
                if hasattr(strategy_config, 'VOLATILITY_PERIOD'):
                    vol_info = f'启用, 周期={strategy_config.VOLATILITY_PERIOD}'
                    if hasattr(strategy_config, 'VOLATILITY_THRESHOLD'):
                        vol_info += f', 阈值={strategy_config.VOLATILITY_THRESHOLD}'
                    strategy_indicators['Volatility'] = vol_info
                
                # 检查ATR指标
                if hasattr(strategy_config, 'ATR_THRESHOLD'):
                    strategy_indicators['ATR'] = f'启用, 阈值={strategy_config.ATR_THRESHOLD}%'
                    strategy_indicators['ATR_Pct'] = f'启用, 阈值={strategy_config.ATR_THRESHOLD}%'
                
                # 波动性评分
                if hasattr(strategy_config, 'VOLATILITY_WEIGHT') and hasattr(strategy_config, 'ATR_WEIGHT'):
                    strategy_indicators['Volatility_Score'] = f'启用, 权重={strategy_config.VOLATILITY_WEIGHT}/{strategy_config.ATR_WEIGHT}'
                
                # 布林带指标
                if hasattr(strategy_config, 'BOLL_PERIOD'):
                    strategy_indicators['BOLL_UPPER'] = f'布林带上轨 (周期={strategy_config.BOLL_PERIOD})'
                    strategy_indicators['BOLL_MIDDLE'] = f'布林带中轨 (周期={strategy_config.BOLL_PERIOD})'
                    strategy_indicators['BOLL_LOWER'] = f'布林带下轨 (周期={strategy_config.BOLL_PERIOD})'
                    strategy_indicators['BOLL_WIDTH'] = f'布林带宽度 (周期={strategy_config.BOLL_PERIOD})'
                
                # RSI指标
                if hasattr(strategy_config, 'RSI_PERIOD'):
                    strategy_indicators['RSI'] = f'相对强弱指标 (周期={strategy_config.RSI_PERIOD})'

            # 显示策略中使用的买入指标信息
            if strategy_indicators:
                with st.expander("策略中使用的买入指标", expanded=True):
                    st.write("以下是策略配置文件中定义的买入指标：")
                    for indicator, info in strategy_indicators.items():
                        # 检查指标名称是否在交易数据中存在，或者是否有类似的指标名称
                        found = False
                        matched_indicators = []
                        
                        # 精确匹配
                        if indicator in indicator_columns:
                            st.info(f"**{indicator}**: {info}")
                            found = True
                        else:
                            # 模糊匹配 - 检查是否有包含该指标名称的列
                            for col in indicator_columns:
                                if indicator.lower() in col.lower() or col.lower() in indicator.lower():
                                    matched_indicators.append(col)
                            
                            if matched_indicators:
                                st.info(f"**{indicator}**: {info} (可能对应的列: {', '.join(matched_indicators)})")
                                found = True
                        
                        if not found:
                            st.warning(f"**{indicator}**: {info} (在交易数据中未找到此指标或相似指标)")
            
            if not indicator_columns:
                st.warning("未找到技术指标列。如需添加技术指标，请在交易数据中包含相关指标值。")
            else:
                st.write("### 选择要分析的技术指标")
                
                # 2. 创建指标选择和范围设置界面
                # 最多允许选择3个指标进行组合分析
                col1, col2, col3 = st.columns(3)
                
                # 为指标名称添加策略指标标记
                def format_indicator_name(indicator):
                    if indicator in strategy_indicators:
                        return f"{indicator} 📊"  # 添加图标表示这是策略中使用的指标
                    return indicator
                
                # 创建带有格式化名称的指标列表
                formatted_indicators = ["无"] + [format_indicator_name(ind) for ind in indicator_columns]
                
                with col1:
                    indicator1_display = st.selectbox("指标1", formatted_indicators, key="indicator1_display")
                    # 提取实际的指标名称（去除可能的图标）
                    indicator1 = "无" if indicator1_display == "无" else indicator1_display.split(" ")[0]
                    
                    if indicator1 != "无":
                        min_val1 = df[indicator1].min()
                        max_val1 = df[indicator1].max()
                        indicator1_range = st.slider(
                            f"{indicator1}范围", 
                            float(min_val1), 
                            float(max_val1), 
                            (float(min_val1), float(max_val1)),
                            key="range1"
                        )
                        # 如果是策略指标，显示提示信息
                        if indicator1 in strategy_indicators:
                            st.info(f"📊 策略指标: {strategy_indicators[indicator1]}")
                
                with col2:
                    indicator2_display = st.selectbox("指标2", formatted_indicators, key="indicator2_display")
                    indicator2 = "无" if indicator2_display == "无" else indicator2_display.split(" ")[0]
                    
                    if indicator2 != "无":
                        min_val2 = df[indicator2].min()
                        max_val2 = df[indicator2].max()
                        indicator2_range = st.slider(
                            f"{indicator2}范围", 
                            float(min_val2), 
                            float(max_val2), 
                            (float(min_val2), float(max_val2)),
                            key="range2"
                        )
                        # 如果是策略指标，显示提示信息
                        if indicator2 in strategy_indicators:
                            st.info(f"📊 策略指标: {strategy_indicators[indicator2]}")
                
                with col3:
                    indicator3_display = st.selectbox("指标3", formatted_indicators, key="indicator3_display")
                    indicator3 = "无" if indicator3_display == "无" else indicator3_display.split(" ")[0]
                    
                    if indicator3 != "无":
                        min_val3 = df[indicator3].min()
                        max_val3 = df[indicator3].max()
                        indicator3_range = st.slider(
                            f"{indicator3}范围", 
                            float(min_val3), 
                            float(max_val3), 
                            (float(min_val3), float(max_val3)),
                            key="range3"
                        )
                        # 如果是策略指标，显示提示信息
                        if indicator3 in strategy_indicators:
                            st.info(f"📊 策略指标: {strategy_indicators[indicator3]}")

                # 3. 添加分析按钮
                if st.button("运行指标优化分析", key="run_indicator_analysis"):
                    # 检查是否至少选择了一个指标
                    if indicator1 == "无" and indicator2 == "无" and indicator3 == "无":
                        st.error("请至少选择一个技术指标进行分析")
                    else:
                        # 创建进度条
                        progress_bar = st.progress(0)
                        status_text = st.empty()
                        
                        try:
                            # 显示初始状态
                            status_text.text("正在开始分析...")
                            progress_bar.progress(10)
                            
                            # 构建过滤条件
                            filtered_df = df.copy()
                            filter_conditions = []
                            
                            if indicator1 != "无":
                                filtered_df = filtered_df[(filtered_df[indicator1] >= indicator1_range[0]) & 
                                                        (filtered_df[indicator1] <= indicator1_range[1])]
                                filter_conditions.append(f"{indicator1}: [{indicator1_range[0]:.2f}, {indicator1_range[1]:.2f}]")
                            
                            if indicator2 != "无":
                                filtered_df = filtered_df[(filtered_df[indicator2] >= indicator2_range[0]) & 
                                                        (filtered_df[indicator2] <= indicator2_range[1])]
                                filter_conditions.append(f"{indicator2}: [{indicator2_range[0]:.2f}, {indicator2_range[1]:.2f}]")
                            
                            if indicator3 != "无":
                                filtered_df = filtered_df[(filtered_df[indicator3] >= indicator3_range[0]) & 
                                                        (filtered_df[indicator3] <= indicator3_range[1])]
                                filter_conditions.append(f"{indicator3}: [{indicator3_range[0]:.2f}, {indicator3_range[1]:.2f}]")
                            
                            progress_bar.progress(30)
                            status_text.text("正在计算统计指标...")
                            
                            # 计算统计指标
                            total_trades = len(filtered_df)
                            
                            if total_trades == 0:
                                progress_bar.progress(100)
                                status_text.text("分析完成，但没有符合条件的交易")
                                st.warning("没有符合所选条件的交易记录")
                            else:
                                # 计算胜率
                                winning_trades = len(filtered_df[filtered_df[profit_col] > 0])
                                win_rate = winning_trades / total_trades * 100
                                
                                # 计算平均收益
                                avg_profit = filtered_df[profit_col].mean()
                                
                                # 计算最大收益和最大亏损
                                max_profit = filtered_df[profit_col].max()
                                min_profit = filtered_df[profit_col].min()
                                
                                # 计算收益标准差
                                profit_std = filtered_df[profit_col].std()
                                
                                # 计算盈亏比
                                avg_win = filtered_df[filtered_df[profit_col] > 0][profit_col].mean() if winning_trades > 0 else 0
                                losing_trades = len(filtered_df[filtered_df[profit_col] <= 0])
                                avg_loss = abs(filtered_df[filtered_df[profit_col] < 0][profit_col].mean()) if losing_trades > 0 else 0
                                profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf')
                                
                                progress_bar.progress(50)
                                status_text.text("正在生成分析图表...")
                                
                                # 生成分析图表
                                charts_dir = 'reports/charts'
                                os.makedirs(charts_dir, exist_ok=True)
                                
                                # 创建图表：收益分布直方图
                                plt.figure(figsize=(10, 6))
                                sns.histplot(filtered_df[profit_col], bins=30, kde=True)
                                plt.title(f'符合条件交易的收益率分布 (总计: {total_trades}笔)')
                                plt.xlabel('收益率 (%)')
                                plt.ylabel('频率')
                                plt.grid(True, alpha=0.3)
                                plt.savefig(os.path.join(charts_dir, 'indicator_optimization.png'), dpi=150, bbox_inches='tight')
                                plt.close()
                                
                                progress_bar.progress(70)
                                status_text.text("正在保存分析结果...")
                                
                                # 保存分析结果
                                results = {
                                    '分析时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                    '筛选条件': ', '.join(filter_conditions),
                                    '交易总数': total_trades,
                                    '盈利交易数': winning_trades,
                                    '亏损交易数': losing_trades,
                                    '胜率': win_rate,
                                    '平均收益率': avg_profit,
                                    '最大收益率': max_profit,
                                    '最大亏损率': min_profit,
                                    '收益标准差': profit_std,
                                    '盈亏比': profit_loss_ratio
                                }
                                
                                # 保存为CSV
                                pd.DataFrame([results]).to_csv('reports/indicator_optimization_results.csv', index=False)
                                
                                # 保存筛选后的交易记录
                                filtered_df.to_csv('reports/filtered_trades.csv', index=False)
                                
                                progress_bar.progress(100)
                                status_text.text("分析完成！")
                                time.sleep(1)
                                
                                # 显示分析结果
                                st.success("指标优化分析完成！")
                                
                                # 显示结果摘要
                                st.write("### 分析结果摘要")
                                st.write(f"**筛选条件**: {', '.join(filter_conditions)}")
                                
                                # 使用列布局显示指标
                                metrics_col1, metrics_col2, metrics_col3 = st.columns(3)
                                
                                with metrics_col1:
                                    st.metric("交易总数", f"{total_trades}")
                                
                                with metrics_col2:
                                    st.metric("胜率", f"{win_rate:.2f}%")
                                
                                with metrics_col3:
                                    st.metric("平均收益率", f"{avg_profit:.2f}%")
                                
                                metrics_col4, metrics_col5, metrics_col6 = st.columns(3)
                                
                                with metrics_col4:
                                    st.metric("最大收益", f"{max_profit:.2f}%")
                                
                                with metrics_col5:
                                    st.metric("最大亏损", f"{min_profit:.2f}%")
                                
                                with metrics_col6:
                                    st.metric("盈亏比", f"{profit_loss_ratio:.2f}")
                                
                                # 显示图表
                                st.write("### 收益分布图")
                                st.image(os.path.join(charts_dir, 'indicator_optimization.png'), use_column_width=True)
                                
                                # 显示筛选后的交易记录
                                with st.expander("查看符合条件的交易记录"):
                                    st.dataframe(filtered_df, use_container_width=True)
                                    
                                    # 添加下载按钮
                                    csv = filtered_df.to_csv(index=False).encode('utf-8')
                                    st.download_button(
                                        "下载筛选后的交易数据",
                                        csv,
                                        "filtered_trades.csv",
                                        "text/csv",
                                        key='download-filtered-csv'
                                    )
                        
                        except Exception as e:
                            status_text.text(f"错误: {str(e)}")
                            st.error(f"分析过程中出错: {str(e)}")
                
                # 4. 显示之前的分析结果（如果有）
                if os.path.exists("reports/indicator_optimization_results.csv"):
                    with st.expander("查看上次分析结果", expanded=False):
                        prev_results = pd.read_csv("reports/indicator_optimization_results.csv")
                        
                        st.write("### 上次分析结果")
                        st.write(f"**分析时间**: {prev_results['分析时间'].iloc[0]}")
                        st.write(f"**筛选条件**: {prev_results['筛选条件'].iloc[0]}")
                        
                        # 使用列布局显示指标
                        prev_col1, prev_col2, prev_col3 = st.columns(3)
                        
                        with prev_col1:
                            st.metric("交易总数", f"{prev_results['交易总数'].iloc[0]}")
                        
                        with prev_col2:
                            st.metric("胜率", f"{prev_results['胜率'].iloc[0]:.2f}%")
                        
                        with prev_col3:
                            st.metric("平均收益率", f"{prev_results['平均收益率'].iloc[0]:.2f}%")
                        
                        # 显示图表（如果存在）
                        if os.path.exists("reports/charts/indicator_optimization.png"):
                            st.image("reports/charts/indicator_optimization.png", use_column_width=True)
                        
                        # 显示筛选后的交易记录（如果存在）
                        if os.path.exists("reports/filtered_trades.csv"):
                            filtered_trades = pd.read_csv("reports/filtered_trades.csv")
                            st.dataframe(filtered_trades, use_container_width=True)
                
                # 5. 高级分析功能
                with st.expander("高级指标分析", expanded=False):
                    st.write("### 指标相关性分析")
                    st.write("分析技术指标与交易收益率之间的相关性，找出最有影响力的指标。")
                    
                    if st.button("运行相关性分析", key="run_correlation_analysis"):
                        try:
                            # 创建进度条
                            corr_progress = st.progress(0)
                            corr_status = st.empty()
                            
                            corr_status.text("正在计算指标相关性...")
                            corr_progress.progress(30)
                            
                            # 计算所有指标与收益率的相关性
                            numeric_df = df.select_dtypes(include=['float64', 'int64'])
                            
                            # 确保收益率列在数据框中
                            if profit_col in numeric_df.columns:
                                # 计算相关性
                                correlations = numeric_df.corrwith(numeric_df[profit_col])
                                # 移除自相关
                                correlations = correlations.drop(profit_col)
                                # 按绝对值排序
                                correlations = correlations.abs().sort_values(ascending=False)
                                
                                corr_progress.progress(60)
                                corr_status.text("正在生成相关性图表...")
                                
                                # 创建相关性图表
                                plt.figure(figsize=(10, 8))
                                
                                # 获取相关性数据
                                abs_correlations = correlations[:15]  # 只显示前15个相关性最强的指标
                                actual_correlations = numeric_df.corrwith(numeric_df[profit_col]).loc[abs_correlations.index]
                                
                                # 创建颜色映射，策略指标使用不同颜色
                                colors = ['#1f77b4' if ind not in strategy_indicators else '#ff7f0e' for ind in abs_correlations.index]
                                
                                # 绘制条形图
                                bars = plt.barh(abs_correlations.index, abs_correlations, color=colors)
                                
                                # 添加策略指标标记
                                for i, ind in enumerate(abs_correlations.index):
                                    if ind in strategy_indicators:
                                        plt.text(abs_correlations[ind] + 0.01, i, '📊', fontsize=12)
                                
                                plt.title(f'指标与{profit_col}的相关性(绝对值)')
                                plt.xlabel('相关性绝对值')
                                plt.tight_layout()
                                plt.savefig(os.path.join(charts_dir, 'indicator_correlation.png'), dpi=150, bbox_inches='tight')
                                plt.close()
                                
                                # 创建热力图
                                plt.figure(figsize=(12, 10))
                                
                                # 选择相关性最强的10个指标
                                top_indicators = correlations[:10].index.tolist()
                                if profit_col not in top_indicators:
                                    top_indicators.append(profit_col)
                                
                                # 计算这些指标之间的相关性矩阵
                                corr_matrix = numeric_df[top_indicators].corr()
                                
                                # 绘制热力图
                                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)
                                plt.title('指标相关性热力图')
                                plt.tight_layout()
                                plt.savefig(os.path.join(charts_dir, 'correlation_heatmap.png'), dpi=150, bbox_inches='tight')
                                plt.close()
                                
                                corr_progress.progress(100)
                                corr_status.text("相关性分析完成！")
                                
                                # 显示结果
                                st.write("#### 指标与收益率的相关性（按相关性强度排序）")
                                
                                # 创建一个包含相关性和相关性方向的数据框
                                corr_df = pd.DataFrame({
                                    '指标名称': numeric_df.corrwith(numeric_df[profit_col]).drop(profit_col).index,
                                    '相关性系数': numeric_df.corrwith(numeric_df[profit_col]).drop(profit_col).values,
                                    '相关性强度': correlations.values
                                })
                                corr_df = corr_df.sort_values('相关性强度', ascending=False)
                                
                                # 添加相关性方向解释和策略指标标记
                                corr_df['影响'] = corr_df['相关性系数'].apply(
                                    lambda x: "正相关 (指标值越大，收益率越高)" if x > 0 else "负相关 (指标值越小，收益率越高)"
                                )
                                
                                corr_df['策略指标'] = corr_df['指标名称'].apply(
                                    lambda x: "✓" if x in strategy_indicators else ""
                                )
                                
                                # 显示相关性表格
                                st.dataframe(corr_df, use_container_width=True)
                                
                                # 显示相关性图表
                                st.write("#### 指标相关性图表")
                                st.write("📊 标记表示策略中使用的指标")
                                st.image(os.path.join(charts_dir, 'indicator_correlation.png'), use_column_width=True)
                                
                                # 显示热力图
                                st.write("#### 指标间相关性热力图")
                                st.image(os.path.join(charts_dir, 'correlation_heatmap.png'), use_column_width=True)
                                
                                # 提供优化建议
                                st.write("### 指标优化建议")
                                
                                # 获取相关性最强的3个指标
                                top_3_indicators = corr_df['指标名称'][:3].tolist()
                                
                                st.write("基于相关性分析，以下指标对交易收益影响最大：")
                                
                                for idx, indicator in enumerate(top_3_indicators):
                                    corr_value = corr_df[corr_df['指标名称'] == indicator]['相关性系数'].values[0]
                                    direction = "增加" if corr_value > 0 else "减少"
                                    
                                    # 计算最佳范围
                                    # 将数据按指标值分成10个等份，计算每个等份的平均收益率
                                    bins = 10
                                    df['bin'] = pd.qcut(df[indicator], bins, labels=False, duplicates='drop')
                                    bin_stats = df.groupby('bin').agg({profit_col: ['mean', 'count']})
                                    bin_stats.columns = ['avg_profit', 'count']
                                    
                                    # 找出平均收益率最高的bin
                                    best_bin = bin_stats['avg_profit'].idxmax()
                                    
                                    # 计算该bin的指标值范围
                                    bin_edges = pd.qcut(df[indicator], bins, duplicates='drop', retbins=True)[1]
                                    if best_bin + 1 < len(bin_edges):
                                        best_range = (bin_edges[best_bin], bin_edges[best_bin + 1])
                                    else:
                                        best_range = (bin_edges[best_bin], bin_edges[-1])
                                    
                                    # 添加策略指标标记
                                    strategy_mark = " 📊" if indicator in strategy_indicators else ""
                                    strategy_info = f" (策略指标: {strategy_indicators[indicator]})" if indicator in strategy_indicators else ""
                                    
                                    st.write(f"{idx+1}. **{indicator}{strategy_mark}** (相关性: {corr_value:.4f}){strategy_info}")
                                    st.write(f"   - 建议: 交易时优先选择{indicator}值{direction}的情况")
                                    st.write(f"   - 最佳范围: [{best_range[0]:.4f}, {best_range[1]:.4f}]")
                                    st.write(f"   - 在此范围内的平均收益率: {bin_stats.loc[best_bin, 'avg_profit']:.4f}%")

                                # 绘制每个指标的收益率分布图
                                st.write("### 指标值与收益率关系")
                                
                                for indicator in top_3_indicators:
                                    plt.figure(figsize=(10, 6))
                                    plt.scatter(df[indicator], df[profit_col], alpha=0.5)
                                    plt.title(f'{indicator} vs {profit_col}')
                                    plt.xlabel(indicator)
                                    plt.ylabel(profit_col)
                                    plt.grid(True, alpha=0.3)
                                    
                                    # 添加趋势线
                                    z = np.polyfit(df[indicator], df[profit_col], 1)
                                    p = np.poly1d(z)
                                    plt.plot(df[indicator], p(df[indicator]), "r--", alpha=0.8)
                                    
                                    plt.savefig(os.path.join(charts_dir, f'indicator_{indicator}_scatter.png'), dpi=150, bbox_inches='tight')
                                    plt.close()
                                    
                                    st.image(os.path.join(charts_dir, f'indicator_{indicator}_scatter.png'), use_column_width=True)
                            else:
                                st.error(f"收益率列 {profit_col} 不是数值类型，无法计算相关性")
                        
                        except Exception as e:
                            st.error(f"相关性分析过程中出错: {str(e)}")
                            import traceback
                            st.code(traceback.format_exc())
                    
                    # 指标组合分析
                    st.write("### 指标组合分析")
                    st.write("分析不同指标组合对交易结果的影响，找出最佳组合。")
                    
                    if len(indicator_columns) >= 2:
                        # 选择要组合分析的两个指标
                        combo_col1, combo_col2 = st.columns(2)
                        
                        with combo_col1:
                            combo_indicator1 = st.selectbox("选择第一个指标", indicator_columns, key="combo_ind1")
                        
                        with combo_col2:
                            # 排除已选的第一个指标
                            remaining_indicators = [ind for ind in indicator_columns if ind != combo_indicator1]
                            combo_indicator2 = st.selectbox("选择第二个指标", remaining_indicators, key="combo_ind2")
                        
                        if st.button("运行组合分析", key="run_combo_analysis"):
                            try:
                                # 创建进度条
                                combo_progress = st.progress(0)
                                combo_status = st.empty()
                                
                                combo_status.text("正在分析指标组合...")
                                combo_progress.progress(30)
                                
                                # 创建热图数据
                                # 将两个指标分成5x5网格，计算每个格子的平均收益率
                                bins = 5
                                
                                # 创建分箱
                                df['bin1'] = pd.qcut(df[combo_indicator1], bins, labels=False, duplicates='drop')
                                df['bin2'] = pd.qcut(df[combo_indicator2], bins, labels=False, duplicates='drop')
                                
                                # 计算每个组合的平均收益率
                                heatmap_data = df.groupby(['bin1', 'bin2'])[profit_col].mean().unstack()
                                
                                # 计算每个组合的交易次数
                                count_data = df.groupby(['bin1', 'bin2'])[profit_col].count().unstack()
                                
                                # 计算每个组合的胜率
                                win_data = df.groupby(['bin1', 'bin2']).apply(
                                    lambda x: (x[profit_col] > 0).mean() * 100
                                ).unstack()
                                
                                combo_progress.progress(60)
                                combo_status.text("正在生成组合分析图表...")
                                
                                # 创建热图
                                fig, axes = plt.subplots(1, 3, figsize=(18, 6))
                                
                                # 平均收益率热图
                                sns.heatmap(heatmap_data, annot=True, fmt=".2f", cmap="RdYlGn", ax=axes[0])
                                axes[0].set_title(f'平均收益率 (%) - {combo_indicator1} vs {combo_indicator2}')
                                axes[0].set_xlabel(combo_indicator2)
                                axes[0].set_ylabel(combo_indicator1)
                                
                                # 交易次数热图
                                sns.heatmap(count_data, annot=True, fmt="d", cmap="Blues", ax=axes[1])
                                axes[1].set_title(f'交易次数 - {combo_indicator1} vs {combo_indicator2}')
                                axes[1].set_xlabel(combo_indicator2)
                                axes[1].set_ylabel(combo_indicator1)
                                
                                # 胜率热图
                                sns.heatmap(win_data, annot=True, fmt=".1f", cmap="RdYlGn", ax=axes[2])
                                axes[2].set_title(f'胜率 (%) - {combo_indicator1} vs {combo_indicator2}')
                                axes[2].set_xlabel(combo_indicator2)
                                axes[2].set_ylabel(combo_indicator1)
                                
                                plt.tight_layout()
                                plt.savefig(os.path.join(charts_dir, 'indicator_combo_analysis.png'), dpi=150, bbox_inches='tight')
                                plt.close()
                                
                                combo_progress.progress(100)
                                combo_status.text("组合分析完成！")
                                
                                # 显示热图
                                st.write("#### 指标组合分析结果")
                                st.image(os.path.join(charts_dir, 'indicator_combo_analysis.png'), use_column_width=True)
                                
                                # 找出最佳组合
                                # 过滤掉交易次数太少的组合（少于总交易量的2%）
                                min_trades = len(df) * 0.02
                                
                                # 找出平均收益率最高且交易次数足够的组合
                                valid_combos = []
                                
                                for bin1 in range(bins):
                                    for bin2 in range(bins):
                                        if bin1 in heatmap_data.index and bin2 in heatmap_data.columns:
                                            if count_data.loc[bin1, bin2] >= min_trades:
                                                valid_combos.append({
                                                    'bin1': bin1,
                                                    'bin2': bin2,
                                                    'avg_profit': heatmap_data.loc[bin1, bin2],
                                                    'count': count_data.loc[bin1, bin2],
                                                    'win_rate': win_data.loc[bin1, bin2]
                                                })
                                
                                if valid_combos:
                                    # 按平均收益率排序
                                    valid_combos.sort(key=lambda x: x['avg_profit'], reverse=True)
                                    best_combo = valid_combos[0]
                                    
                                    # 获取最佳组合的指标范围
                                    bin_edges1 = pd.qcut(df[combo_indicator1], bins, duplicates='drop', retbins=True)[1]
                                    bin_edges2 = pd.qcut(df[combo_indicator2], bins, duplicates='drop', retbins=True)[1]
                                    
                                    best_range1 = (bin_edges1[best_combo['bin1']], bin_edges1[best_combo['bin1'] + 1])
                                    best_range2 = (bin_edges2[best_combo['bin2']], bin_edges2[best_combo['bin2'] + 1])
                                    
                                    st.write("#### 最佳指标组合")
                                    st.write(f"**{combo_indicator1}** 范围: [{best_range1[0]:.4f}, {best_range1[1]:.4f}]")
                                    st.write(f"**{combo_indicator2}** 范围: [{best_range2[0]:.4f}, {best_range2[1]:.4f}]")
                                    st.write(f"平均收益率: {best_combo['avg_profit']:.2f}%")
                                    st.write(f"胜率: {best_combo['win_rate']:.2f}%")
                                    st.write(f"交易次数: {best_combo['count']}")
                                    
                                    # 创建应用此组合的按钮
                                    if st.button("应用此最佳组合", key="apply_best_combo"):
                                        # 将最佳组合的范围设置到主界面的滑块中
                                        st.session_state.indicator1 = combo_indicator1
                                        st.session_state.range1 = (float(best_range1[0]), float(best_range1[1]))
                                        
                                        st.session_state.indicator2 = combo_indicator2
                                        st.session_state.range2 = (float(best_range2[0]), float(best_range2[1]))
                                        
                                        st.success(f"已应用最佳组合！请返回主界面查看。")
                                        st.experimental_rerun()
                                else:
                                    st.warning("没有找到交易次数足够的有效组合")
                            
                            except Exception as e:
                                st.error(f"组合分析过程中出错: {str(e)}")
                                import traceback
                                st.code(traceback.format_exc())
                    else:
                        st.warning("需要至少两个指标才能进行组合分析")

with tab4:
    st.header("报告生成")
    
    # 添加清空分析结果按钮
    col_header1, col_header2 = st.columns([3, 1])
    with col_header1:
        st.write("生成HTML格式的分析报告，包含交易统计和策略优化结果。")
    with col_header2:
        if os.path.exists("reports/reports_guide.html"):
            if st.button("清空HTML报告", key="clear_results_tab3", help="清空所有HTML报告"):
                success, message = clear_analysis_results()
                if success:
                    st.success(message)
                    # 清除Streamlit缓存
                    st.cache_data.clear()
                    # 刷新页面
                    st.experimental_rerun()
                else:
                    st.error(message)
    
    # 检查必要文件
    if not os.path.exists("reports/trade_analysis_results.csv"):
        st.warning("未找到交易分析结果，请先运行交易分析")
    elif not os.path.exists("reports/optimal_strategy_rules.txt"):
        st.warning("未找到优化策略规则，请先运行策略优化")
    else:
        if st.button("生成HTML报告", key="generate_reports"):
            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # 显示初始状态
                status_text.text("正在准备数据...")
                progress_bar.progress(10)
                
                # 生成HTML报告
                import pandas as pd
                from jinja2 import Template
                from datetime import datetime
                
                # 读取交易分析结果
                df = pd.read_csv("reports/trade_analysis_results.csv")
                status_text.text("正在读取分析结果...")
                progress_bar.progress(30)
                
                # 读取模板
                with open("analysis_system/templates/reports_guide_template.html", "r", encoding="utf-8") as f:
                    template_text = f.read()
                
                status_text.text("正在处理报告模板...")
                progress_bar.progress(50)
                
                template = Template(template_text)
                
                # 准备模板数据
                template_data = {
                    "total_reports": len(os.listdir("reports")),
                    "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "win_rate": (df["Actual_Profit_Pct"] > 0).mean() * 100,
                    "avg_profit": df["Actual_Profit_Pct"].mean(),
                    "total_trades": len(df),
                    "data_files_count": len([f for f in os.listdir("reports") if f.endswith(".csv")]),
                    "image_files_count": len([f for f in os.listdir("reports") if f.endswith(".png")]),
                    "rule_files_count": len([f for f in os.listdir("reports") if f.endswith(".txt")]),
                    "model_files_count": len([f for f in os.listdir("reports") if f.endswith(".pkl")])
                }
                
                status_text.text("正在生成HTML报告...")
                progress_bar.progress(70)
                
                # 渲染模板
                output = template.render(**template_data)
                
                status_text.text("正在保存报告...")
                progress_bar.progress(90)
                
                # 保存HTML报告
                with open("reports/reports_guide.html", "w", encoding="utf-8") as f:
                    f.write(output)
                
                # 完成进度显示
                progress_bar.progress(100)
                status_text.text("报告生成完成！")
                time.sleep(1)  # 显示完成状态一秒
                
                st.success("HTML报告生成成功！")
                
                # 显示报告链接
                st.markdown("[查看报告指南](reports/reports_guide.html)")
                
            except Exception as e:
                status_text.text(f"错误: {str(e)}")
                st.error(f"生成报告时出错: {str(e)}")

# 显示系统信息
st.sidebar.title("系统信息")
st.sidebar.info(
    f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    f"交易日志: {'✅ 已存在' if os.path.exists('data/trade_log.csv') else '❌ 未找到'}\n\n"
    f"分析日志: {'✅ 已存在' if os.path.exists('data/analysis_log.csv') else '❌ 未找到'}\n\n"
    f"交易数据库: {'✅ 已存在' if os.path.exists('data/trades.db') else '❌ 未找到'}\n\n"
    f"分析结果: {'✅ 已生成' if os.path.exists('reports/trade_analysis_results.csv') else '❌ 未生成'}\n\n"
    f"策略规则: {'✅ 已生成' if os.path.exists('reports/optimal_strategy_rules.txt') else '❌ 未生成'}"
)

# 添加系统管理功能到侧边栏
st.sidebar.markdown("---")
st.sidebar.subheader("系统管理")

# 添加清空分析结果的功能
if os.path.exists("reports/trade_analysis_results.csv") or os.path.exists("reports/optimal_strategy_rules.txt"):
    if st.sidebar.button("清空分析结果", help="清空所有分析结果文件，但保留原始数据"):
        # 显示确认对话框
        confirm = st.sidebar.warning("⚠️ 此操作将删除所有分析结果且无法恢复。确定要继续吗？")
        if st.sidebar.button("确认清空分析结果", key="confirm_clear_results"):
            success, message = clear_analysis_results()
            if success:
                st.sidebar.success(message)
                # 清除Streamlit缓存
                st.cache_data.clear()
                # 显示一个临时消息
                st.sidebar.info("分析结果已清空，页面将在3秒后刷新...")
                
                # 使用JavaScript强制刷新页面
                html_refresh = """
                <script>
                    // 等待3秒后刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 3000);
                </script>
                """
                st.components.v1.html(html_refresh)
            else:
                st.sidebar.error(message)
else:
    st.sidebar.info("没有可清空的分析结果")

# 添加关于信息
st.sidebar.markdown("---")
st.sidebar.markdown("© 2025 万和策略分析系统") 