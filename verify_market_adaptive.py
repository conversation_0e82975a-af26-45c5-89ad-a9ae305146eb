# coding=utf-8
"""
验证市场自适应策略配置
确认已撤销强制时间分散，启用基于数据驱动的优化
"""

from config import get_config_value

def verify_market_adaptive_config():
    """验证市场自适应配置"""
    print('✅ 验证市场自适应策略配置')
    print('=' * 60)
    
    print('🎯 新的优化理念:')
    print('   ✅ 尊重市场特征和规律')
    print('   ✅ 基于CCI等因子的自然差异优化')
    print('   ✅ 自适应质量调整，而非强制分散')
    print('   ❌ 撤销强制时间分散 (违背市场规律)')
    
    # 验证市场自适应策略
    print(f'\n🔧 市场自适应策略验证:')
    
    enable_adaptive = get_config_value('ENABLE_MARKET_ADAPTIVE_STRATEGY', False)
    cci_config = get_config_value('CCI_ADAPTIVE_CONFIG', {})
    
    if enable_adaptive == True:
        print(f'   ✅ 市场自适应策略: 已启用')
    else:
        print(f'   ❌ 市场自适应策略: {enable_adaptive} (期望: True)')
    
    if cci_config.get('enable', False):
        opening_cci = cci_config.get('opening_cci_avg', 0)
        other_cci = cci_config.get('other_cci_avg', 0)
        dynamic_thresholds = cci_config.get('dynamic_thresholds', {})
        
        print(f'   ✅ CCI自适应配置: 已启用')
        print(f'     开盘CCI均值: {opening_cci}')
        print(f'     其他时段CCI均值: {other_cci}')
        print(f'     动态阈值区间: {len(dynamic_thresholds)}个')
        
        if 'high_cci_zone' in dynamic_thresholds:
            high_zone = dynamic_thresholds['high_cci_zone']
            print(f'     高CCI区间 (>={high_zone.get("cci_min", 0)}): 质量要求x{high_zone.get("quality_multiplier", 1.0)}')
        
        if 'low_cci_zone' in dynamic_thresholds:
            low_zone = dynamic_thresholds['low_cci_zone']
            print(f'     低CCI区间 (<={low_zone.get("cci_max", 0)}): 质量要求x{low_zone.get("quality_multiplier", 1.0)}')
    else:
        print(f'   ❌ CCI自适应配置: 未启用')
    
    # 验证强制时间分散已撤销
    print(f'\n🚫 强制时间分散撤销验证:')
    
    force_time_dist = get_config_value('FORCE_TIME_DISTRIBUTION', 'NOT_FOUND')
    opening_limit = get_config_value('OPENING_HOUR_LIMIT', 'NOT_FOUND')
    
    if force_time_dist == 'NOT_FOUND':
        print(f'   ✅ FORCE_TIME_DISTRIBUTION: 已撤销')
    else:
        print(f'   ⚠️ FORCE_TIME_DISTRIBUTION: {force_time_dist} (应该撤销)')
    
    if opening_limit == 'NOT_FOUND':
        print(f'   ✅ OPENING_HOUR_LIMIT: 已撤销')
    else:
        print(f'   ⚠️ OPENING_HOUR_LIMIT: 仍存在 (应该撤销)')
    
    # 验证多因子阈值调整
    print(f'\n📊 多因子阈值验证:')
    
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    expected_thresholds = {
        'min_overall_score': 0.02,
        'min_technical_score': 0.02,
        'min_momentum_score': 0.02,
        'min_volatility_score': 0.02,
        'min_trend_score': 0.02,
        'min_buy_signal_strength': 0.02,
        'min_risk_adjusted_score': 0.02
    }
    
    threshold_correct = True
    for key, expected in expected_thresholds.items():
        actual = thresholds.get(key, 'NOT_FOUND')
        if actual == expected:
            print(f'   ✅ {key}: {actual} (适中阈值，配合技术指标)')
        else:
            print(f'   ❌ {key}: {actual} (期望: {expected})')
            threshold_correct = False
    
    # 验证高效因子配置
    print(f'\n🎯 高效因子配置验证:')
    
    effective_config = get_config_value('EFFECTIVE_FACTORS_CONFIG', {})
    
    if effective_config.get('enable', False):
        factors = effective_config.get('factors', {})
        print(f'   ✅ 高效因子配置: 已启用')
        print(f'   ✅ 配置因子数量: {len(factors)}个')
        
        # 检查关键因子
        key_factors = ['cci', 'adx', 'bb_position', 'rsi']
        for factor in key_factors:
            if factor in factors:
                weight = factors[factor].get('weight', 0)
                ic = factors[factor].get('ic', 0)
                print(f'     ✅ {factor}: 权重{weight:.3f}, IC{ic:.4f}')
            else:
                print(f'     ❌ {factor}: 未配置')
    else:
        print(f'   ❌ 高效因子配置: 未启用')
    
    # 总体验证结果
    all_correct = (
        enable_adaptive == True and
        cci_config.get('enable', False) and
        force_time_dist == 'NOT_FOUND' and
        threshold_correct
    )
    
    print(f'\n🎯 验证总结:')
    if all_correct:
        print('✅ 市场自适应策略配置正确')
        print('✅ 已撤销强制时间分散')
        print('✅ 基于数据驱动的优化方向')
        print('🚀 策略现在尊重市场规律')
        return True
    else:
        print('❌ 部分配置需要调整')
        print('💡 请检查config.py文件')
        return False

def show_correct_strategy_summary():
    """显示正确策略总结"""
    print(f'\n📋 正确策略总结')
    print('=' * 50)
    
    summary = '''
🎯 策略思维转变:
   ❌ 错误做法: 强制分散信号到全天 (违背市场规律)
   ✅ 正确做法: 基于市场特征和因子有效性自动优化

🔍 关键洞察:
   1. ✅ 开盘信号集中可能是合理的 (CCI因子在开盘时段高35.9%)
   2. ✅ 策略应该自动检测最佳交易时机 (基于因子有效性)
   3. ✅ 不应该主观强制分散 (应该尊重市场特征)
   4. ✅ 基于数据驱动优化 (而非主观判断)

🔧 新的优化方向:
   - 基于CCI等因子的时段特征进行自适应调整
   - 高CCI时段 (类似开盘): 质量要求提高10%
   - 低CCI时段 (其他时段): 质量要求降低10%
   - 让策略自然适应市场特征

📊 预期效果:
   - 信号分布: 基于市场特征自然分布
   - 信号质量: 基于因子有效性自动优化
   - 策略稳定性: 顺应市场规律，更加稳定
   - 可解释性: 基于数据驱动，逻辑清晰

💡 核心理念:
   "让策略基于市场数据和因子有效性自动检测最佳交易时机"
   而不是"强制分散信号到全天"
'''
    
    print(summary)

def create_monitoring_plan():
    """创建监控计划"""
    print(f'\n📋 市场自适应策略监控计划')
    print('=' * 50)
    
    plan = '''
🔍 重启后观察重点 (前2小时):
   □ 策略是否基于CCI值自动调整质量要求
   □ 高CCI时段信号质量是否提升
   □ 低CCI时段信号数量是否适度增加
   □ 信号分布是否基于市场特征自然形成

📊 24小时内效果评估:
   □ 各时段信号质量vs数量的平衡
   □ CCI自适应调整是否正常工作
   □ 整体胜率是否保持或提升
   □ 策略是否更好地适应市场特征

📈 一周内综合评估:
   □ 市场自适应策略的稳定性
   □ 基于因子有效性的优化效果
   □ 与强制分散方案的对比
   □ 策略的可解释性和逻辑性

🎯 长期优化方向:
   □ 基于更多因子的自适应调整
   □ 动态因子有效性监控
   □ 市场状态识别和适应
   □ 持续的数据驱动优化

⚠️ 关键原则:
   □ 始终基于数据驱动决策
   □ 尊重市场特征和规律
   □ 避免主观强制分散
   □ 专注因子有效性优化
'''
    
    print(plan)

def main():
    """主函数"""
    print('🚀 市场自适应策略验证')
    print('=' * 60)
    
    # 验证市场自适应配置
    success = verify_market_adaptive_config()
    
    # 显示正确策略总结
    show_correct_strategy_summary()
    
    # 创建监控计划
    create_monitoring_plan()
    
    if success:
        print(f'\n🏆 市场自适应策略配置成功!')
        print('🚀 策略已转向正确的优化方向!')
        print('')
        print('🎯 下一步: python main.py')
        print('📈 目标: 基于市场特征自然优化')
        print('💎 从强制分散 → 市场自适应的重要转变!')
    else:
        print(f'\n⚠️ 配置验证失败!')
        print('💡 请检查并修正配置文件')

if __name__ == '__main__':
    main()
