@echo off
REM 设置UTF-8编码
chcp 65001 > nul

:MENU
cls
echo ===============================================================================
echo                          万和策略分析系统 - 功能菜单
echo ===============================================================================
echo.
echo 请选择要执行的操作:
echo.
echo 1. 启动分析系统Web界面
echo 2. 清空数据库
echo 3. 清空CSV数据
echo 4. 创建系统备份
echo 5. 检查环境和依赖项
echo 0. 退出
echo.
echo ===============================================================================

set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto START_APP
if "%choice%"=="2" goto CLEAR_DB
if "%choice%"=="3" goto CLEAR_CSV
if "%choice%"=="4" goto BACKUP
if "%choice%"=="5" goto CHECK_ENV
if "%choice%"=="0" goto EXIT
echo.
echo 无效选项，请重新输入！
timeout /t 2 >nul
goto MENU

:START_APP
echo.
echo 正在启动分析系统Web界面...
cd %~dp0
cd ../..
python -m streamlit run analysis_system/streamlit_app/app.py --config-file analysis_system/streamlit_app/config.toml
echo.
echo 应用已关闭。
pause
goto MENU

:CLEAR_DB
echo.
echo 警告: 此操作将清空数据库中的所有交易记录且无法恢复。
set /p confirm=确定要继续吗? (Y/N): 
if /i "%confirm%"=="Y" (
    echo 正在清空数据库...
    cd %~dp0
    cd ../..
    python -c "from analysis_system.streamlit_app.utils.data_loader import clear_database; success, message = clear_database(); print(message)"
    echo.
    echo 操作完成。
) else (
    echo 操作已取消。
)
pause
goto MENU

:CLEAR_CSV
echo.
echo 警告: 此操作将清空所有CSV交易数据且无法恢复。
set /p confirm=确定要继续吗? (Y/N): 
if /i "%confirm%"=="Y" (
    echo 正在清空CSV数据...
    cd %~dp0
    cd ../..
    python -c "from analysis_system.streamlit_app.utils.data_loader import clear_csv_data; success, message = clear_csv_data(); print(message)"
    echo.
    echo 操作完成。
) else (
    echo 操作已取消。
)
pause
goto MENU

:BACKUP
echo.
echo 正在创建系统备份...
cd %~dp0
cd ../..
python -c "import os, shutil, datetime; backup_time = datetime.datetime.now().strftime('%%Y%%m%%d_%%H%%M%%S'); backup_dir = f'backup_{backup_time}'; os.makedirs(backup_dir, exist_ok=True); shutil.copytree('data', os.path.join(backup_dir, 'data'), dirs_exist_ok=True) if os.path.exists('data') else print('数据目录不存在'); shutil.copytree('reports', os.path.join(backup_dir, 'reports'), dirs_exist_ok=True) if os.path.exists('reports') else print('报告目录不存在'); config_file = 'analysis_system/streamlit_app/config.json'; shutil.copy(config_file, os.path.join(backup_dir, 'config.json')) if os.path.exists(config_file) else print('配置文件不存在'); print(f'系统备份已创建: {backup_dir}')"
echo.
echo 操作完成。
pause
goto MENU

:CHECK_ENV
echo.
echo 正在检查环境和依赖项...
python --version
echo.
echo 检查必要的Python包:
echo.
python -c "packages = ['streamlit', 'pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly', 'scikit-learn', 'jinja2']; [print(f'- {pkg}: ' + ('已安装' if __import__(pkg, fromlist=['']) else '未安装')) for pkg in packages]"
echo.
echo 检查数据文件:
echo.
python -c "import os; files = {'交易日志': 'data/trade_log.csv', '分析日志': 'data/analysis_log.csv', '交易数据库': 'data/trades.db', '分析结果': 'reports/trade_analysis_results.csv'}; [print(f'- {name}: ' + ('存在' if os.path.exists(file) else '不存在')) for name, file in files.items()]"
echo.
echo 环境检查完成。
pause
goto MENU

:EXIT
echo.
echo 感谢使用万和策略分析系统，再见！
echo.
exit /b 0 