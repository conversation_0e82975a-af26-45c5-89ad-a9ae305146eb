import streamlit as st
import pandas as pd
import sqlite3
import os
import sys
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置页面配置
st.set_page_config(
    page_title="数据库查看器",
    page_icon="🗄️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 页面标题
st.title("数据库查看器")
st.markdown("---")

# 数据库连接函数
def get_connection():
    """连接到SQLite数据库"""
    db_path = os.path.join("data", "trades.db")
    if not os.path.exists(db_path):
        st.error(f"数据库文件不存在: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        return conn
    except Exception as e:
        st.error(f"连接数据库时出错: {str(e)}")
        return None

# 获取表列表
def get_tables(conn):
    """获取数据库中的所有表"""
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        return [table[0] for table in tables]
    except Exception as e:
        st.error(f"获取表列表时出错: {str(e)}")
        return []

# 获取表结构
def get_table_schema(conn, table_name):
    """获取指定表的结构"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name});")
        schema = cursor.fetchall()
        return schema
    except Exception as e:
        st.error(f"获取表结构时出错: {str(e)}")
        return []

# 执行SQL查询
def execute_query(conn, query):
    """执行SQL查询并返回结果"""
    try:
        df = pd.read_sql_query(query, conn)
        return df, None
    except Exception as e:
        return None, str(e)

# 导出数据到CSV
def export_to_csv(df, filename):
    """导出数据框到CSV文件"""
    try:
        export_path = os.path.join("data", "exports", filename)
        os.makedirs(os.path.dirname(export_path), exist_ok=True)
        df.to_csv(export_path, index=False, encoding='utf-8-sig')
        return True, export_path
    except Exception as e:
        return False, str(e)

# 主界面
def main():
    # 连接数据库
    conn = get_connection()
    if conn is None:
        st.warning("请先上传或创建数据库文件。")
        
        # 数据库文件上传区
        uploaded_db = st.file_uploader("上传交易数据库", type=["db"])
        if uploaded_db is not None:
            os.makedirs("data", exist_ok=True)
            with open(os.path.join("data", "trades.db"), "wb") as f:
                f.write(uploaded_db.getvalue())
            st.success("交易数据库上传成功！")
            st.experimental_rerun()
        return
    
    # 获取表列表
    tables = get_tables(conn)
    if not tables:
        st.warning("数据库中没有表。")
        return
    
    # 创建侧边栏
    st.sidebar.title("数据库操作")
    
    # 选择操作模式
    mode = st.sidebar.radio("选择操作模式", ["浏览表数据", "执行SQL查询", "数据可视化"])
    
    if mode == "浏览表数据":
        browse_tables(conn, tables)
    elif mode == "执行SQL查询":
        execute_sql(conn, tables)
    elif mode == "数据可视化":
        visualize_data(conn, tables)

# 浏览表数据
def browse_tables(conn, tables):
    st.header("浏览表数据")
    
    # 选择表
    selected_table = st.selectbox("选择要查看的表", tables)
    
    # 获取表结构
    schema = get_table_schema(conn, selected_table)
    if schema:
        st.subheader("表结构")
        schema_df = pd.DataFrame(schema, columns=["ID", "列名", "数据类型", "非空", "默认值", "主键"])
        st.dataframe(schema_df)
    
    # 表数据分页浏览
    st.subheader("表数据")
    
    # 获取记录总数
    count_query = f"SELECT COUNT(*) FROM {selected_table}"
    count_df, _ = execute_query(conn, count_query)
    total_records = count_df.iloc[0, 0]
    
    st.write(f"总记录数: {total_records}")
    
    # 分页控制
    records_per_page = st.slider("每页显示记录数", 10, 100, 50)
    max_page = (total_records - 1) // records_per_page + 1
    
    col1, col2 = st.columns([1, 3])
    with col1:
        page = st.number_input("页码", min_value=1, max_value=max_page, value=1)
    
    # 排序选项
    with col2:
        # 获取列名
        columns_query = f"PRAGMA table_info({selected_table})"
        columns_df, _ = execute_query(conn, columns_query)
        column_names = columns_df['name'].tolist()
        
        sort_col = st.selectbox("排序列", ["无"] + column_names)
    
    # 构建查询
    offset = (page - 1) * records_per_page
    if sort_col != "无":
        sort_order = st.radio("排序方式", ["升序", "降序"], horizontal=True)
        order_by = f"ORDER BY {sort_col} {'ASC' if sort_order == '升序' else 'DESC'}"
        query = f"SELECT * FROM {selected_table} {order_by} LIMIT {records_per_page} OFFSET {offset}"
    else:
        query = f"SELECT * FROM {selected_table} LIMIT {records_per_page} OFFSET {offset}"
    
    # 执行查询
    df, error = execute_query(conn, query)
    if error:
        st.error(f"查询出错: {error}")
    else:
        st.dataframe(df)
        
        # 导出当前页数据
        if st.button("导出当前页数据到CSV"):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            success, result = export_to_csv(df, f"{selected_table}_page{page}_{timestamp}.csv")
            if success:
                st.success(f"数据已导出到: {result}")
            else:
                st.error(f"导出失败: {result}")

# 执行SQL查询
def execute_sql(conn, tables):
    st.header("执行SQL查询")
    
    # 显示可用表
    with st.expander("可用表"):
        for table in tables:
            st.write(f"- {table}")
            schema = get_table_schema(conn, table)
            schema_df = pd.DataFrame(schema, columns=["ID", "列名", "数据类型", "非空", "默认值", "主键"])
            st.dataframe(schema_df)
    
    # 查询输入
    query = st.text_area("输入SQL查询", height=150)
    
    # 执行按钮
    if st.button("执行查询"):
        if not query:
            st.warning("请输入SQL查询")
        else:
            df, error = execute_query(conn, query)
            if error:
                st.error(f"查询出错: {error}")
            else:
                st.success("查询执行成功")
                st.subheader("查询结果")
                st.dataframe(df)
                
                # 导出查询结果
                if not df.empty:
                    if st.button("导出查询结果到CSV"):
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        success, result = export_to_csv(df, f"query_result_{timestamp}.csv")
                        if success:
                            st.success(f"数据已导出到: {result}")
                        else:
                            st.error(f"导出失败: {result}")

# 数据可视化
def visualize_data(conn, tables):
    st.header("数据可视化")
    
    # 选择表
    selected_table = st.selectbox("选择要分析的表", tables)
    
    # 获取表结构
    schema = get_table_schema(conn, selected_table)
    if not schema:
        st.warning("无法获取表结构")
        return
    
    # 获取列名
    columns = [col[1] for col in schema]
    
    # 加载数据
    query = f"SELECT * FROM {selected_table}"
    df, error = execute_query(conn, query)
    
    if error:
        st.error(f"加载数据出错: {error}")
        return
    
    if df.empty:
        st.warning("表中没有数据")
        return
    
    # 选择可视化类型
    viz_type = st.selectbox("选择可视化类型", ["柱状图", "折线图", "散点图", "饼图", "箱线图", "热力图", "相关性矩阵"])
    
    # 根据可视化类型选择不同的参数
    if viz_type in ["柱状图", "折线图", "散点图"]:
        col1, col2 = st.columns(2)
        
        with col1:
            x_col = st.selectbox("X轴", columns)
        
        with col2:
            y_col = st.selectbox("Y轴", [col for col in columns if col != x_col])
        
        # 检查数据类型
        try:
            df[x_col] = pd.to_numeric(df[x_col], errors='coerce')
            df[y_col] = pd.to_numeric(df[y_col], errors='coerce')
            
            # 过滤掉NaN值
            df_clean = df.dropna(subset=[x_col, y_col])
            
            if df_clean.empty:
                st.warning("选择的列包含无效数据，无法创建图表")
                return
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(10, 6))
            
            if viz_type == "柱状图":
                sns.barplot(x=x_col, y=y_col, data=df_clean, ax=ax)
            elif viz_type == "折线图":
                sns.lineplot(x=x_col, y=y_col, data=df_clean, ax=ax)
            elif viz_type == "散点图":
                sns.scatterplot(x=x_col, y=y_col, data=df_clean, ax=ax)
            
            plt.title(f"{viz_type}: {y_col} vs {x_col}")
            plt.xlabel(x_col)
            plt.ylabel(y_col)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"创建图表时出错: {str(e)}")
    
    elif viz_type == "饼图":
        pie_col = st.selectbox("选择分类列", columns)
        
        try:
            # 计算每个类别的计数
            value_counts = df[pie_col].value_counts()
            
            # 如果类别太多，只显示前10个
            if len(value_counts) > 10:
                st.warning(f"类别数量({len(value_counts)})太多，只显示前10个")
                value_counts = value_counts.head(10)
            
            # 创建饼图
            fig, ax = plt.subplots(figsize=(10, 10))
            ax.pie(value_counts, labels=value_counts.index, autopct='%1.1f%%', startangle=90)
            ax.axis('equal')  # 确保饼图是圆的
            plt.title(f"{pie_col}的分布")
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"创建饼图时出错: {str(e)}")
    
    elif viz_type == "箱线图":
        box_col = st.selectbox("选择数值列", columns)
        group_col = st.selectbox("选择分组列(可选)", ["无"] + [col for col in columns if col != box_col])
        
        try:
            # 转换为数值类型
            df[box_col] = pd.to_numeric(df[box_col], errors='coerce')
            
            # 过滤掉NaN值
            df_clean = df.dropna(subset=[box_col])
            
            if df_clean.empty:
                st.warning("选择的列包含无效数据，无法创建图表")
                return
            
            # 创建箱线图
            fig, ax = plt.subplots(figsize=(10, 6))
            
            if group_col != "无":
                sns.boxplot(x=group_col, y=box_col, data=df_clean, ax=ax)
                plt.xticks(rotation=45)
            else:
                sns.boxplot(y=box_col, data=df_clean, ax=ax)
            
            plt.title(f"{box_col}的箱线图")
            plt.tight_layout()
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"创建箱线图时出错: {str(e)}")
    
    elif viz_type == "热力图":
        # 选择数值列
        numeric_cols = []
        for col in columns:
            try:
                df[col] = pd.to_numeric(df[col], errors='raise')
                numeric_cols.append(col)
            except:
                pass
        
        if len(numeric_cols) < 2:
            st.warning("需要至少2个数值列来创建热力图")
            return
        
        selected_cols = st.multiselect("选择要包含的列", numeric_cols, default=numeric_cols[:min(5, len(numeric_cols))])
        
        if len(selected_cols) < 2:
            st.warning("请选择至少2个列")
            return
        
        try:
            # 计算相关性
            corr = df[selected_cols].corr()
            
            # 创建热力图
            fig, ax = plt.subplots(figsize=(10, 8))
            sns.heatmap(corr, annot=True, cmap='coolwarm', ax=ax)
            plt.title("相关性热力图")
            plt.tight_layout()
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"创建热力图时出错: {str(e)}")
    
    elif viz_type == "相关性矩阵":
        # 选择数值列
        numeric_cols = []
        for col in columns:
            try:
                df[col] = pd.to_numeric(df[col], errors='raise')
                numeric_cols.append(col)
            except:
                pass
        
        if len(numeric_cols) < 2:
            st.warning("需要至少2个数值列来创建相关性矩阵")
            return
        
        selected_cols = st.multiselect("选择要包含的列", numeric_cols, default=numeric_cols[:min(5, len(numeric_cols))])
        
        if len(selected_cols) < 2:
            st.warning("请选择至少2个列")
            return
        
        try:
            # 创建散点图矩阵
            sns.set(style="ticks")
            fig = sns.pairplot(df[selected_cols], diag_kind="kde")
            plt.suptitle("相关性散点图矩阵", y=1.02)
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"创建相关性矩阵时出错: {str(e)}")

# 运行主程序
if __name__ == "__main__":
    main() 