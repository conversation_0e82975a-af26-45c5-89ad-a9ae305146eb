#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from logging.handlers import RotatingFileHandler
import os

# 确保日志目录存在
LOG_DIR = 'logs'
os.makedirs(LOG_DIR, exist_ok=True)

# 日志文件路径
STRATEGY_LOG_FILE = 'strategy.log'
DATA_MANAGER_LOG_FILE = 'data_manager.log'
DB_TOOLS_LOG_FILE = 'db_advanced_tools.log'

def setup_logger(name='strategy_log', log_file=STRATEGY_LOG_FILE, level=logging.INFO):
    """设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别
        
    Returns:
        配置好的日志记录器实例
    """
    # 获取日志记录器
    logger = logging.getLogger(name)
    
    # 检查是否已经配置过这个logger
    if hasattr(logger, '_configured') and logger._configured:
        return logger
    
    # 如果是数据管理器日志，设置为CRITICAL级别以禁用大部分日志输出
    if name == 'data_manager_log':
        logger.setLevel(logging.CRITICAL)
    else:
        logger.setLevel(level)
    
    # 清除已有的处理器，避免重复添加
    if logger.handlers:
        for handler in logger.handlers[:]:  # 使用复制的列表进行遍历，避免在移除过程中修改原始列表
            logger.removeHandler(handler)
    
    # 添加文件处理器
    file_handler = RotatingFileHandler(
        os.path.join(LOG_DIR, log_file),
        maxBytes=20*1024*1024,  # 20MB
        backupCount=20,
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 禁用日志传播，避免消息传递到父logger（通常是root logger）
    logger.propagate = False
    
    # 标记此logger已被配置
    logger._configured = True
    
    return logger

def get_strategy_logger():
    """获取策略日志记录器"""
    logger = logging.getLogger('strategy_log')
    # 如果logger没有被配置，先配置它
    if not hasattr(logger, '_configured') or not logger._configured:
        return setup_logger('strategy_log', STRATEGY_LOG_FILE)
    return logger

def get_data_manager_logger():
    """获取数据管理器日志记录器"""
    logger = logging.getLogger('data_manager_log')
    # 如果logger没有被配置，先配置它
    if not hasattr(logger, '_configured') or not logger._configured:
        return setup_logger('data_manager_log', DATA_MANAGER_LOG_FILE)
    return logger

def get_db_tools_logger():
    """获取数据库工具日志记录器"""
    logger = logging.getLogger('db_tools_log')
    # 如果logger没有被配置，先配置它
    if not hasattr(logger, '_configured') or not logger._configured:
        return setup_logger('db_tools_log', DB_TOOLS_LOG_FILE)
    return logger

def setup_all_loggers():
    """设置所有日志记录器"""
    # 获取所有logger，如果已经配置过则直接返回
    strategy_logger = get_strategy_logger()
    data_manager_logger = get_data_manager_logger()
    db_tools_logger = get_db_tools_logger()
    
    return {
        'strategy': strategy_logger,
        'data_manager': data_manager_logger,
        'db_tools': db_tools_logger
    }

# 增强的日志功能
def log_with_category(logger, category, message, level=logging.INFO):
    """带分类的日志记录
    
    Args:
        logger: 日志记录器实例
        category: 日志分类
        message: 日志消息
        level: 日志级别
    """
    formatted_message = f"[{category}] {message}"
    
    if level == logging.DEBUG:
        logger.debug(formatted_message)
    elif level == logging.INFO:
        logger.info(formatted_message)
    elif level == logging.WARNING:
        logger.warning(formatted_message)
    elif level == logging.ERROR:
        logger.error(formatted_message)
    elif level == logging.CRITICAL:
        logger.critical(formatted_message)

# 便捷的分类日志函数
def log_trade(logger, message, level=logging.INFO):
    """记录交易相关日志"""
    log_with_category(logger, 'TRADE', message, level)

def log_data(logger, message, level=logging.INFO):
    """记录数据相关日志"""
    log_with_category(logger, 'DATA', message, level)

def log_signal(logger, message, level=logging.INFO):
    """记录信号相关日志"""
    log_with_category(logger, 'SIGNAL', message, level)

def log_risk(logger, message, level=logging.INFO):
    """记录风险相关日志"""
    log_with_category(logger, 'RISK', message, level)

def log_system(logger, message, level=logging.INFO):
    """记录系统相关日志"""
    log_with_category(logger, 'SYSTEM', message, level)

# 设置日志级别
def set_log_level(logger_name, level):
    """设置指定日志记录器的级别
    
    Args:
        logger_name: 日志记录器名称
        level: 日志级别，可以是 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'
    """
    logger = logging.getLogger(logger_name)
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    if level in level_map:
        logger.setLevel(level_map[level]) 