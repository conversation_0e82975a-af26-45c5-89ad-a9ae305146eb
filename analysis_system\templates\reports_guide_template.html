<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易策略分析报告指南</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --bg-color: #f5f8fa;
            --card-bg: #ffffff;
            --text-color: #333333;
            --border-color: #e1e4e8;
        }
        
        body {
            font-family: "Microsoft YaHei", "Segoe UI", Tahoma, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-title {
            font-size: 1.5rem;
            margin: 0;
            padding: 10px 0;
        }
        
        .sidebar-subtitle {
            font-size: 0.9rem;
            margin: 5px 0 0 0;
            opacity: 0.7;
        }
        
        .nav-section {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .nav-section-title {
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0 0 10px 0;
            opacity: 0.6;
        }
        
        .nav-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-links li {
            margin-bottom: 5px;
        }
        
        .nav-links a {
            display: block;
            padding: 8px 10px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .nav-links a:hover, .nav-links a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 30px;
        }
        
        .update-info {
            display: inline-block;
            padding: 5px 10px;
            background-color: var(--accent-color);
            color: white;
            border-radius: 4px;
            font-size: 0.85rem;
            margin-bottom: 20px;
        }
        
        .section {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .section-title {
            margin-top: 0;
            color: var(--primary-color);
            font-size: 1.8rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .subsection {
            margin-top: 30px;
        }
        
        .subsection-title {
            color: var(--primary-color);
            font-size: 1.4rem;
            margin-bottom: 15px;
            position: relative;
        }
        
        .subsection-title::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--secondary-color);
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            display: flex;
            flex-direction: column;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .card-title {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .card-title svg {
            margin-right: 10px;
            color: var(--secondary-color);
        }
        
        .card-count {
            margin-left: auto;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 20px;
            padding: 2px 10px;
            font-size: 0.9rem;
        }
        
        .file-list {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .file-list li:last-child {
            border-bottom: none;
        }
        
        .file-list a {
            color: var(--secondary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .file-list a:hover {
            text-decoration: underline;
        }
        
        .file-icon {
            margin-right: 10px;
            width: 16px;
            height: 16px;
            color: var(--text-color);
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .image-item {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            background-color: white;
            transition: transform 0.2s ease;
        }
        
        .image-item:hover {
            transform: scale(1.05);
        }
        
        .image-item a {
            display: block;
            color: inherit;
            text-decoration: none;
        }
        
        .thumbnail {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }
        
        .image-name {
            padding: 10px;
            font-size: 0.9rem;
            display: block;
            font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .image-info {
            padding: 0 10px 10px 10px;
            font-size: 0.8rem;
            color: #666;
            display: block;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .tag-data {
            background-color: rgba(52, 152, 219, 0.2);
            color: #2980b9;
        }
        
        .tag-visualization {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }
        
        .tag-model {
            background-color: rgba(155, 89, 182, 0.2);
            color: #8e44ad;
        }
        
        .tag-rules {
            background-color: rgba(243, 156, 18, 0.2);
            color: #d35400;
        }
        
        .info-box {
            background-color: rgba(52, 152, 219, 0.1);
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .info-box h4 {
            margin-top: 0;
            color: var(--secondary-color);
        }
        
        .info-box p:last-child {
            margin-bottom: 0;
        }
        
        .warning-box {
            background-color: rgba(243, 156, 18, 0.1);
            border-left: 4px solid var(--warning-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .warning-box h4 {
            margin-top: 0;
            color: var(--warning-color);
        }
        
        .steps-list {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }
        
        .steps-list li {
            position: relative;
            padding-left: 50px;
            margin-bottom: 25px;
            counter-increment: step-counter;
        }
        
        .steps-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 35px;
            height: 35px;
            background-color: var(--secondary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
        }
        
        .command-box {
            background-color: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            overflow-x: auto;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            color: #666;
            font-size: 0.9rem;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .image-gallery {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h1 class="sidebar-title">报告指南</h1>
                <p class="sidebar-subtitle">交易策略分析文档导航</p>
            </div>
            
            <div class="nav-section">
                <h2 class="nav-section-title">导航</h2>
                <ul class="nav-links">
                    <li><a href="#overview" class="active">概述</a></li>
                    <li><a href="#data-analysis">数据分析结果</a></li>
                    <li><a href="#strategy-optimization">策略优化结果</a></li>
                    <li><a href="#ml-models">机器学习模型</a></li>
                    <li><a href="#visualizations">可视化图表</a></li>
                    <li><a href="#how-to-use">如何使用这些报告</a></li>
                    <li><a href="#update-reports">更新报告</a></li>
                </ul>
            </div>
            
            <div class="nav-section">
                <h2 class="nav-section-title">关键报告</h2>
                <ul class="nav-links">
                    <li><a href="./high_performance_trades.html">高胜率高收益模式</a></li>
                    <li><a href="reports/trade_analysis_results.csv">交易分析结果</a></li>
                    <li><a href="reports/feature_importance.png">特征重要性图表</a></li>
                    <li><a href="reports/optimal_strategy_rules.txt">最优策略规则</a></li>
                </ul>
            </div>
        </div>
        
        <div class="main-content">
            <div class="update-info">最后更新: {{update_time}}</div>
            
            <div class="section" id="overview">
                <h2 class="section-title">报告概述</h2>
                <p>本目录包含了交易策略分析的所有报告文件，共计 <strong>{{total_reports}}</strong> 个文件，分为数据分析结果、策略优化结果、机器学习模型和可视化图表四大类。</p>
                
                <div class="card-grid">
                    <div class="card">
                        <h3 class="card-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                            数据分析结果
                            <span class="card-count">{{data_files_count}}</span>
                        </h3>
                        <p>包含交易记录、参数组合结果等CSV数据文件，这些文件提供了策略分析的原始数据。</p>
                        <div class="tags">
                            <span class="tag tag-data">CSV</span>
                            <span class="tag tag-data">交易记录</span>
                            <span class="tag tag-data">参数组合</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3 class="card-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                            策略优化结果
                            <span class="card-count">{{rule_files_count}}</span>
                        </h3>
                        <p>包含优化后的交易策略规则，这些规则文件提供了交易决策的具体指导。</p>
                        <div class="tags">
                            <span class="tag tag-rules">TXT</span>
                            <span class="tag tag-rules">策略规则</span>
                            <span class="tag tag-rules">阈值设置</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3 class="card-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>
                            机器学习模型
                            <span class="card-count">{{model_files_count}}</span>
                        </h3>
                        <p>包含训练好的机器学习模型，这些模型可以用于预测交易结果和优化交易决策。</p>
                        <div class="tags">
                            <span class="tag tag-model">PKL</span>
                            <span class="tag tag-model">模型</span>
                            <span class="tag tag-model">随机森林</span>
                            <span class="tag tag-model">梯度提升</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3 class="card-title">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>
                            可视化图表
                            <span class="card-count">{{image_files_count}}</span>
                        </h3>
                        <p>包含各种可视化图表，这些图表直观展示了交易策略的性能和特征重要性等信息。</p>
                        <div class="tags">
                            <span class="tag tag-visualization">PNG</span>
                            <span class="tag tag-visualization">图表</span>
                            <span class="tag tag-visualization">趋势</span>
                            <span class="tag tag-visualization">分布</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section" id="data-analysis">
                <h2 class="section-title">数据分析结果</h2>
                <p>数据分析结果文件包含了交易策略的原始数据和分析结果，这些文件是策略优化和模型训练的基础。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">交易记录文件</h3>
                    <ul class="file-list">
                        {{data_files_list}}
                    </ul>
                </div>
                
                <div class="info-box">
                    <h4>数据文件说明</h4>
                    <p>交易分析结果CSV文件包含所有交易的详细记录，包括买入时间、卖出时间、持仓时长、买入价格、卖出价格、成交量、盈亏比例等信息。这些数据是后续分析和策略优化的基础。</p>
                </div>
            </div>
            
            <div class="section" id="strategy-optimization">
                <h2 class="section-title">策略优化结果</h2>
                <p>策略优化结果文件包含了通过数据分析和机器学习得出的最优交易策略规则，这些规则可以直接应用于交易决策。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">策略规则文件</h3>
                    <ul class="file-list">
                        {{rule_files_list}}
                    </ul>
                </div>
                
                <div class="info-box">
                    <h4>规则文件说明</h4>
                    <p>策略规则文件包含了不同指标组合的最优阈值设置，以及对应的胜率和平均收益率等信息。这些规则可以直接应用于交易策略，提高交易成功率和收益率。</p>
                </div>
            </div>
            
            <div class="section" id="ml-models">
                <h2 class="section-title">机器学习模型</h2>
                <p>机器学习模型文件包含了通过历史交易数据训练的模型，这些模型可以用于预测交易结果和优化交易决策。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">模型文件</h3>
                    <ul class="file-list">
                        {{model_files_list}}
                    </ul>
                </div>
                
                <div class="info-box">
                    <h4>模型文件说明</h4>
                    <p>模型文件是通过随机森林、梯度提升等算法训练得到的机器学习模型，这些模型可以预测交易的胜率和收益率，辅助交易决策。</p>
                </div>
            </div>
            
            <div class="section" id="visualizations">
                <h2 class="section-title">可视化图表</h2>
                <p>可视化图表文件直观展示了交易策略的性能、特征重要性和各种指标之间的关系，有助于理解交易策略的特点和优化方向。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">图表库</h3>
                    <div class="image-gallery">
                        {{image_files_list}}
                    </div>
                </div>
            </div>
            
            <div class="section" id="how-to-use">
                <h2 class="section-title">如何使用这些报告</h2>
                <p>这些报告文件可以帮助您了解交易策略的性能、优化交易参数、发现交易模式，从而提高交易的成功率和收益率。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">推荐使用步骤</h3>
                    <ol class="steps-list">
                        <li>
                            <h4>查看交易分析结果</h4>
                            <p>首先查看 <code>trade_analysis_results.csv</code> 文件，了解所有交易的详细情况，包括买入时间、卖出时间、持仓时长、盈亏比例等信息。</p>
                        </li>
                        <li>
                            <h4>了解特征重要性</h4>
                            <p>查看 <code>feature_importance.png</code> 图表，了解各个因素对交易结果的影响程度，找出最重要的因素。</p>
                        </li>
                        <li>
                            <h4>研究最优策略规则</h4>
                            <p>查看 <code>optimal_strategy_rules.txt</code> 文件，了解最优交易策略的具体规则，包括各个指标的阈值设置和对应的胜率、收益率等信息。</p>
                        </li>
                        <li>
                            <h4>分析最佳参数组合</h4>
                            <p>查看 <code>best_parameter_combinations.csv</code> 文件，了解不同参数组合的性能表现，找出最适合的参数设置。</p>
                        </li>
                        <li>
                            <h4>查看模式分析报告</h4>
                            <p>查看 <a href="./high_performance_trades.html">高胜率高收益交易模式分析</a> 报告，了解高胜率高收益的交易模式特点和操作建议。</p>
                        </li>
                    </ol>
                </div>
                
                <div class="warning-box">
                    <h4>注意事项</h4>
                    <p>报告中的分析结果基于历史数据，不保证在未来市场中的表现。交易策略应根据市场变化不断调整和优化。样本量较小的分析结果可能存在偶然性，应谨慎参考。</p>
                </div>
            </div>
            
            <div class="section" id="update-reports">
                <h2 class="section-title">更新报告</h2>
                <p>随着新的交易数据不断积累，建议定期更新分析报告，以保持策略的有效性和适应性。</p>
                
                <div class="subsection">
                    <h3 class="subsection-title">更新命令</h3>
                    <div class="command-box">python scripts/generate_html_reports.py</div>
                    <p>执行上述命令可以重新生成HTML报告文件，包括高胜率高收益交易模式分析和报告指南。</p>
                </div>
            </div>
            
            <div class="footer">
                <p>© 2025 交易策略分析报告 | 最后更新: {{update_time}}</p>
                <p>⚠️ 免责声明：所有分析结果仅供参考，不构成投资建议。交易决策应结合市场情况和个人风险承受能力综合考虑。</p>
            </div>
        </div>
    </div>
    
    <script>
        // 导航高亮脚本
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.nav-links a');
            
            // 初始激活第一个链接
            navLinks[0].classList.add('active');
            
            // 监听滚动事件
            window.addEventListener('scroll', function() {
                let current = '';
                
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if(pageYOffset >= (sectionTop - 200)) {
                        current = section.getAttribute('id');
                    }
                });
                
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if(link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html> 