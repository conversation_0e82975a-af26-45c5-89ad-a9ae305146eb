# coding=utf-8
"""
分析已完成的1808条交易
基于实际卖出数据优化策略胜率
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_completed_trades():
    """分析已完成的交易"""
    print('📊 分析已完成的1808条交易')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有已完成的交易（有卖出记录的）
        query = """
        SELECT 
            b.timestamp as buy_time,
            b.symbol,
            b.price as buy_price,
            b.overall_score, b.technical_score, b.momentum_score, b.volume_score,
            b.volatility_score, b.trend_score, b.buy_signal_strength, b.risk_adjusted_score,
            b.atr_pct, b.bb_width, b.macd_hist, b.rsi, b.trix_buy,
            s.timestamp as sell_time,
            s.price as sell_price,
            s.sell_reason,
            s.net_profit_pct_sell,
            s.holding_hours
        FROM trades b
        JOIN trades s ON b.symbol = s.symbol 
        WHERE b.action = 'BUY' 
        AND s.action = 'SELL'
        AND s.net_profit_pct_sell IS NOT NULL
        AND ABS(julianday(s.timestamp) - julianday(b.timestamp)) * 24 < 720  -- 30天内的匹配
        ORDER BY s.timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 成功匹配的交易: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有找到匹配的买卖记录')
            return None
        
        # 计算总体胜率
        wins = len(df[df['net_profit_pct_sell'] > 0])
        total = len(df)
        win_rate = wins / total * 100
        
        avg_profit = df['net_profit_pct_sell'].mean()
        avg_win = df[df['net_profit_pct_sell'] > 0]['net_profit_pct_sell'].mean()
        avg_loss = abs(df[df['net_profit_pct_sell'] <= 0]['net_profit_pct_sell'].mean())
        
        print(f'\n🎯 总体表现:')
        print(f'   胜率: {win_rate:.1f}% ({wins}/{total})')
        print(f'   平均收益: {avg_profit:.2f}%')
        print(f'   平均盈利: {avg_win:.2f}%')
        print(f'   平均亏损: {avg_loss:.2f}%')
        print(f'   盈亏比: {avg_win/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
        
        return df
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def analyze_sell_reasons(df):
    """分析卖出原因"""
    print(f'\n📋 卖出原因分析')
    print('=' * 40)
    
    # 统计卖出原因
    sell_reason_stats = df.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', lambda x: (x > 0).mean() * 100],
        'holding_hours': 'mean'
    }).round(2)
    
    sell_reason_stats.columns = ['交易数', '平均收益%', '胜率%', '平均持仓h']
    
    print(f'📊 各卖出原因表现:')
    print(sell_reason_stats.to_string())
    
    # 找出最佳卖出原因
    best_reasons = sell_reason_stats[sell_reason_stats['胜率%'] > 50].sort_values('胜率%', ascending=False)
    
    if len(best_reasons) > 0:
        print(f'\n🏆 表现最好的卖出原因:')
        for reason in best_reasons.index:
            stats = best_reasons.loc[reason]
            print(f'   {reason}: 胜率{stats["胜率%"]:.1f}%, 平均收益{stats["平均收益%"]:.2f}%, 交易数{stats["交易数"]}')

def analyze_winning_patterns(df):
    """分析获胜模式"""
    print(f'\n🏆 获胜模式分析')
    print('=' * 40)
    
    # 分离盈利和亏损交易
    winning_trades = df[df['net_profit_pct_sell'] > 0]
    losing_trades = df[df['net_profit_pct_sell'] <= 0]
    
    print(f'📊 盈利 vs 亏损交易对比:')
    print(f'   盈利交易: {len(winning_trades)} 条')
    print(f'   亏损交易: {len(losing_trades)} 条')
    
    # 分析多因子评分差异
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    print(f'\n📊 多因子评分对比 (盈利 vs 亏损):')
    for col in score_columns:
        if col in df.columns:
            win_values = winning_trades[col].dropna()
            loss_values = losing_trades[col].dropna()
            
            if len(win_values) > 0 and len(loss_values) > 0:
                win_avg = win_values.mean()
                loss_avg = loss_values.mean()
                diff = win_avg - loss_avg
                
                # 判断差异显著性
                significance = "🔥显著" if abs(diff) > 0.05 else "一般"
                direction = "📈" if diff > 0 else "📉"
                
                print(f'   {col}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} ({direction}{diff:+.3f}) [{significance}]')
    
    # 分析技术指标差异
    tech_columns = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    
    print(f'\n📋 技术指标对比 (盈利 vs 亏损):')
    for col in tech_columns:
        if col in df.columns:
            win_values = winning_trades[col].dropna()
            loss_values = losing_trades[col].dropna()
            
            if len(win_values) > 0 and len(loss_values) > 0:
                win_avg = win_values.mean()
                loss_avg = loss_values.mean()
                diff = win_avg - loss_avg
                
                significance = "🔥显著" if abs(diff) > win_values.std() * 0.3 else "一般"
                direction = "📈" if diff > 0 else "📉"
                
                print(f'   {col}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} ({direction}{diff:+.3f}) [{significance}]')

def find_optimal_score_thresholds(df):
    """寻找最优评分阈值"""
    print(f'\n🎯 最优评分阈值分析')
    print('=' * 50)
    
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    optimal_thresholds = {}
    
    for col in score_columns:
        if col in df.columns:
            values = df[col].dropna()
            profits = df.loc[values.index, 'net_profit_pct_sell']
            
            if len(values) < 20:
                continue
            
            print(f'\n📊 {col} 阈值分析:')
            
            best_threshold = None
            best_win_rate = 0
            best_sample_size = 0
            
            # 测试不同百分位数作为阈值
            for percentile in [10, 20, 30, 40, 50, 60, 70, 80, 90]:
                threshold = values.quantile(percentile / 100)
                
                # 计算超过阈值的交易胜率
                above_threshold = values >= threshold
                if above_threshold.sum() >= 10:  # 至少10个样本
                    above_profits = profits[above_threshold]
                    win_rate = (above_profits > 0).mean() * 100
                    sample_size = len(above_profits)
                    avg_profit = above_profits.mean()
                    
                    print(f'   阈值{threshold:.3f} (>{percentile}%): 胜率{win_rate:.1f}%, 样本{sample_size}, 平均收益{avg_profit:.2f}%')
                    
                    # 选择胜率最高且样本足够的阈值
                    if win_rate > best_win_rate and sample_size >= 20:
                        best_threshold = threshold
                        best_win_rate = win_rate
                        best_sample_size = sample_size
            
            if best_threshold is not None:
                optimal_thresholds[col] = {
                    'threshold': best_threshold,
                    'win_rate': best_win_rate,
                    'sample_size': best_sample_size
                }
                print(f'   ✅ 最优阈值: {best_threshold:.3f} (胜率: {best_win_rate:.1f}%, 样本: {best_sample_size})')
    
    return optimal_thresholds

def analyze_holding_time_performance(df):
    """分析持仓时间与表现的关系"""
    print(f'\n⏰ 持仓时间表现分析')
    print('=' * 50)
    
    # 按持仓时间分组
    df['holding_days'] = df['holding_hours'] / 24
    
    # 定义持仓时间区间
    time_ranges = [
        ('1天内', df['holding_days'] <= 1),
        ('1-3天', (df['holding_days'] > 1) & (df['holding_days'] <= 3)),
        ('3-7天', (df['holding_days'] > 3) & (df['holding_days'] <= 7)),
        ('1-2周', (df['holding_days'] > 7) & (df['holding_days'] <= 14)),
        ('2周以上', df['holding_days'] > 14)
    ]
    
    print(f'📊 不同持仓时间的表现:')
    for range_name, condition in time_ranges:
        subset = df[condition]
        if len(subset) > 0:
            wins = len(subset[subset['net_profit_pct_sell'] > 0])
            win_rate = wins / len(subset) * 100
            avg_profit = subset['net_profit_pct_sell'].mean()
            
            print(f'   {range_name}: {len(subset)}笔, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')

def generate_optimized_config(optimal_thresholds, df):
    """生成优化配置"""
    print(f'\n⚙️ 基于实际数据的优化配置')
    print('=' * 50)
    
    current_win_rate = (df['net_profit_pct_sell'] > 0).mean() * 100
    
    print(f'📊 当前实际胜率: {current_win_rate:.1f}%')
    
    if optimal_thresholds:
        print(f'\n🎯 建议的多因子阈值 (基于实际胜率优化):')
        print(f'```python')
        print(f'# 基于{len(df)}条实际交易优化的阈值')
        print(f'MULTIFACTOR_THRESHOLDS = {{')
        
        score_mapping = {
            'overall_score': 'min_overall_score',
            'technical_score': 'min_technical_score',
            'momentum_score': 'min_momentum_score',
            'volume_score': 'min_volume_score',
            'volatility_score': 'min_volatility_score',
            'trend_score': 'min_trend_score',
            'buy_signal_strength': 'min_buy_signal_strength',
            'risk_adjusted_score': 'min_risk_adjusted_score'
        }
        
        for score_name, config_key in score_mapping.items():
            if score_name in optimal_thresholds:
                data = optimal_thresholds[score_name]
                # 适当降低阈值避免过拟合
                adjusted_threshold = max(0.05, data['threshold'] * 0.85)
                print(f"    '{config_key}': {adjusted_threshold:.3f},  # 实际胜率: {data['win_rate']:.1f}%")
            else:
                print(f"    '{config_key}': 0.20,  # 默认值")
        
        print(f'}}')
        print(f'```')
    
    # 分析最佳卖出策略
    sell_analysis = df.groupby('sell_reason').agg({
        'net_profit_pct_sell': lambda x: (x > 0).mean() * 100
    })['net_profit_pct_sell'].sort_values(ascending=False)
    
    print(f'\n📈 卖出策略优化建议:')
    print(f'   最佳卖出方式: {sell_analysis.index[0]} (胜率: {sell_analysis.iloc[0]:.1f}%)')
    
    if '跟踪止盈' in sell_analysis.index:
        print(f'   建议: 优化跟踪止盈参数，这是表现最好的卖出方式')
    
    if '时间止损' in sell_analysis.index:
        time_stop_rate = sell_analysis.get('时间止损', 0)
        if time_stop_rate < 40:
            print(f'   建议: 缩短最大持仓时间，时间止损胜率较低 ({time_stop_rate:.1f}%)')

def main():
    """主函数"""
    print('🚀 基于1808条已完成交易的策略优化')
    print('=' * 60)
    
    # 分析已完成交易
    df = analyze_completed_trades()
    
    if df is not None and len(df) > 0:
        # 分析卖出原因
        analyze_sell_reasons(df)
        
        # 分析获胜模式
        analyze_winning_patterns(df)
        
        # 寻找最优阈值
        optimal_thresholds = find_optimal_score_thresholds(df)
        
        # 分析持仓时间
        analyze_holding_time_performance(df)
        
        # 生成优化配置
        generate_optimized_config(optimal_thresholds, df)
        
        print(f'\n🎯 优化总结')
        print('=' * 40)
        print('✅ 基于实际交易数据的分析完成')
        print('📊 已识别真实的获胜模式')
        print('🎯 已生成数据驱动的优化配置')
        print('')
        print('🚀 这些建议基于真实交易结果，可靠性更高')
    else:
        print('❌ 无法获取已完成交易数据')

if __name__ == '__main__':
    main()
