# coding=utf-8
"""
调试多因子评分计算
"""

import pandas as pd
import numpy as np
from enhanced_factor_engine import EnhancedFactorEngine

def debug_score_calculation():
    """调试评分计算"""
    print('🔧 调试多因子评分计算')
    print('=' * 50)
    
    # 创建简单测试数据
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'open': [100 + i + np.random.randn() for i in range(30)],
        'high': [102 + i + np.random.randn() for i in range(30)],
        'low': [98 + i + np.random.randn() for i in range(30)],
        'close': [100 + i + np.random.randn() for i in range(30)],
        'volume': [5000 + np.random.randint(0, 5000) for i in range(30)]
    }, index=dates)
    
    print(f'📊 测试数据: {len(test_data)} 天')
    
    # 创建引擎并计算因子
    engine = EnhancedFactorEngine()
    
    print(f'🔍 开始计算因子...')
    factors = engine.calculate_all_factors(test_data, 'DEBUG.TEST')
    
    print(f'✅ 因子计算完成: {len(factors)} 个')
    
    # 检查是否有多因子评分
    multifactor_scores = [
        'overall_score', 'technical_score', 'momentum_score', 
        'volume_score', 'volatility_score', 'trend_score',
        'buy_signal_strength', 'risk_adjusted_score'
    ]
    
    print(f'\n📊 多因子评分检查:')
    for score in multifactor_scores:
        if score in factors:
            value = factors[score]
            print(f'   ✅ {score}: {value}')
        else:
            print(f'   ❌ {score}: 缺失')
    
    # 检查一些基础因子
    basic_factors = ['macd_hist', 'rsi', 'bb_position', 'atr_pct', 'trix_buy']
    print(f'\n📋 基础因子检查:')
    for factor in basic_factors:
        if factor in factors:
            value = factors[factor]
            print(f'   ✅ {factor}: {value}')
        else:
            print(f'   ❌ {factor}: 缺失')
    
    # 手动调用多因子评分计算
    print(f'\n🔧 手动调用多因子评分计算...')
    try:
        engine._calculate_multifactor_scores(factors, test_data)
        print(f'✅ 手动调用成功')
        
        print(f'\n📊 手动调用后的评分:')
        for score in multifactor_scores:
            if score in factors:
                value = factors[score]
                print(f'   ✅ {score}: {value}')
            else:
                print(f'   ❌ {score}: 仍然缺失')
                
    except Exception as e:
        print(f'❌ 手动调用失败: {e}')
        import traceback
        print(f'详细错误: {traceback.format_exc()}')

if __name__ == '__main__':
    debug_score_calculation()
