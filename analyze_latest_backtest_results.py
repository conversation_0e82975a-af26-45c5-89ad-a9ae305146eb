# coding=utf-8
"""
分析最新回测结果
验证优化效果和胜率改善
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def analyze_latest_database():
    """分析最新的数据库"""
    print("📊 分析最新回测数据库")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 检查数据库表
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 数据库表:")
        for table in tables:
            print(f"   - {table[0]}")
        
        if not tables:
            print("   ❌ 数据库为空，可能仍在回测模式")
            return False
        
        # 分析交易记录
        if ('trades',) in tables:
            print(f"\n📈 分析交易记录:")
            trades_query = '''
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN return_pct > 0 THEN 1 ELSE 0 END) as winning_trades,
                    AVG(return_pct) as avg_return,
                    MIN(return_pct) as min_return,
                    MAX(return_pct) as max_return,
                    AVG(hold_days) as avg_hold_days
                FROM trades 
                WHERE sell_date IS NOT NULL
            '''
            
            result = cursor.execute(trades_query).fetchone()
            
            if result and result[0] > 0:
                total_trades, winning_trades, avg_return, min_return, max_return, avg_hold_days = result
                win_rate = winning_trades / total_trades if total_trades > 0 else 0
                
                print(f"   总交易数: {total_trades}")
                print(f"   盈利交易: {winning_trades}")
                print(f"   胜率: {win_rate:.2%}")
                print(f"   平均收益率: {avg_return:.2%}")
                print(f"   收益范围: {min_return:.2%} ~ {max_return:.2%}")
                print(f"   平均持仓天数: {avg_hold_days:.1f}天")
                
                # 胜率改善分析
                if win_rate > 0.41:
                    improvement = (win_rate - 0.41) * 100
                    print(f"   ✅ 胜率改善: +{improvement:.1f}个百分点 (从41%提升)")
                elif win_rate == 0.41:
                    print(f"   ⚠️ 胜率维持: 41% (无变化)")
                else:
                    decline = (0.41 - win_rate) * 100
                    print(f"   ❌ 胜率下降: -{decline:.1f}个百分点 (从41%下降)")
                
                return True, win_rate, total_trades
            else:
                print("   ❌ 没有完成的交易记录")
                return False, 0, 0
        
        # 分析买入记录
        if ('buy_records',) in tables:
            print(f"\n🎯 分析买入记录:")
            buy_query = '''
                SELECT 
                    COUNT(*) as total_signals,
                    AVG(combined_score) as avg_score,
                    MIN(combined_score) as min_score,
                    MAX(combined_score) as max_score,
                    AVG(factors_count) as avg_factors
                FROM buy_records
            '''
            
            result = cursor.execute(buy_query).fetchone()
            
            if result and result[0] > 0:
                total_signals, avg_score, min_score, max_score, avg_factors = result
                
                print(f"   信号总数: {total_signals}")
                print(f"   平均评分: {avg_score:.4f}")
                print(f"   评分范围: {min_score:.4f} ~ {max_score:.4f}")
                print(f"   平均因子数: {avg_factors:.1f}")
                
                # 检查是否符合新标准
                if avg_score >= 0.52:
                    print(f"   ✅ 平均评分符合新标准 (≥0.52)")
                else:
                    print(f"   ⚠️ 平均评分低于新标准 (<0.52)")
                
                if min_score >= 0.52:
                    print(f"   ✅ 所有信号都符合新标准")
                else:
                    print(f"   ⚠️ 部分信号低于新标准")
            else:
                print("   ❌ 没有买入记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库分析失败: {e}")
        return False

def analyze_latest_logs():
    """分析最新的日志"""
    print("\n📋 分析最新回测日志")
    print("=" * 80)
    
    try:
        with open('logs/strategy.log', 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 获取日志大小和最新时间
        log_size = len(log_content)
        print(f"📄 日志文件大小: {log_size:,} 字符")
        
        # 检查最新的配置使用情况
        print(f"\n⚙️ 检查配置使用情况:")
        
        # 检查新的筛选标准
        if 'min_combined_score: 0.52' in log_content:
            print(f"   ✅ 使用新的评分阈值 (0.52)")
        elif 'min_combined_score: 0.5' in log_content:
            print(f"   ✅ 使用提高的评分阈值 (0.50)")
        elif 'min_combined_score: 0.38' in log_content:
            print(f"   ⚠️ 使用中等评分阈值 (0.38)")
        else:
            print(f"   ❓ 未检测到明确的评分阈值")
        
        # 检查智能化系统运行状态
        print(f"\n🤖 智能化系统状态:")
        
        intelligent_indicators = [
            ('智能化68个因子计算完成', '因子计算'),
            ('智能化筛选: 通过', '筛选通过'),
            ('智能化筛选: 未通过', '筛选未通过'),
            ('多因子综合策略', '综合策略'),
            ('EnhancedMultiFactorEngine', '增强引擎'),
            ('IntelligentStrategyExecutor', '智能执行器')
        ]
        
        for indicator, description in intelligent_indicators:
            count = log_content.count(indicator)
            if count > 0:
                print(f"   ✅ {description}: {count}次")
            else:
                print(f"   ❌ {description}: 未检测到")
        
        # 分析信号质量
        print(f"\n📊 信号质量分析:")
        
        # 提取综合评分
        import re
        score_matches = re.findall(r'综合评分([\d.]+)', log_content)
        
        if score_matches:
            scores = [float(score) for score in score_matches[-50:]]  # 最近50个
            print(f"   最近50个信号评分:")
            print(f"     平均评分: {np.mean(scores):.4f}")
            print(f"     评分范围: {np.min(scores):.4f} ~ {np.max(scores):.4f}")
            print(f"     标准差: {np.std(scores):.4f}")
            
            # 评分分布
            high_quality = len([s for s in scores if s >= 0.52])
            medium_quality = len([s for s in scores if 0.45 <= s < 0.52])
            low_quality = len([s for s in scores if s < 0.45])
            
            print(f"   📈 信号质量分布:")
            print(f"     高质量(≥0.52): {high_quality} ({high_quality/len(scores)*100:.1f}%)")
            print(f"     中等质量(0.45-0.52): {medium_quality} ({medium_quality/len(scores)*100:.1f}%)")
            print(f"     低质量(<0.45): {low_quality} ({low_quality/len(scores)*100:.1f}%)")
            
            # 优化效果评估
            if high_quality / len(scores) >= 0.8:
                print(f"   ✅ 优化效果显著: {high_quality/len(scores)*100:.1f}%高质量信号")
            elif high_quality / len(scores) >= 0.5:
                print(f"   ⚠️ 优化效果一般: {high_quality/len(scores)*100:.1f}%高质量信号")
            else:
                print(f"   ❌ 优化效果不佳: {high_quality/len(scores)*100:.1f}%高质量信号")
        
        # 检查错误和警告
        print(f"\n⚠️ 错误和警告统计:")
        error_count = log_content.count('ERROR') + log_content.count('Error')
        warning_count = log_content.count('WARNING') + log_content.count('Warning')
        
        print(f"   错误数量: {error_count}")
        print(f"   警告数量: {warning_count}")
        
        if error_count == 0:
            print(f"   ✅ 无错误，系统运行正常")
        else:
            print(f"   ⚠️ 发现错误，需要检查")
        
        # 检查回测模式
        backtest_count = log_content.count('回测模式')
        if backtest_count > 0:
            print(f"\n🔄 运行模式: 回测模式 ({backtest_count}次标识)")
        else:
            print(f"\n🔄 运行模式: 可能是实盘模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志分析失败: {e}")
        return False

def compare_optimization_results():
    """对比优化结果"""
    print("\n📊 优化效果对比")
    print("=" * 80)
    
    comparison = '''
🔍 优化前后对比:

优化前状态:
   - 胜率: 41% (低于42%基线)
   - 筛选阈值: 0.35 (过低)
   - 筛选逻辑: 不严格
   - 信号质量: 混杂，包含低质量信号
   - 权重配置: sentiment_score权重过高

优化后目标:
   - 胜率: 55%+ (显著提升)
   - 筛选阈值: 0.52 (严格)
   - 筛选逻辑: 综合评分必须通过
   - 信号质量: 只保留高质量信号
   - 权重配置: 基于实际表现优化

🎯 关键验证指标:
   1. 胜率是否从41%开始改善
   2. 信号平均评分是否≥0.52
   3. 低质量信号是否被有效过滤
   4. 系统是否稳定运行无错误
   5. 68个因子是否正常计算

📈 成功标准:
   - 立即效果: 信号质量明显提升
   - 短期效果: 胜率>45%
   - 中期效果: 胜率>50%
   - 长期目标: 胜率>55%
'''
    
    print(comparison)

def generate_optimization_assessment():
    """生成优化评估"""
    print("\n🏆 优化效果评估")
    print("=" * 80)
    
    # 执行分析
    db_success = analyze_latest_database()
    log_success = analyze_latest_logs()
    
    print(f"\n📋 评估总结:")
    print(f"   数据库分析: {'✅ 成功' if db_success else '❌ 失败'}")
    print(f"   日志分析: {'✅ 成功' if log_success else '❌ 失败'}")
    
    if db_success and log_success:
        print(f"\n✅ 分析完成，请查看上述详细结果")
        print(f"🎯 重点关注:")
        print(f"   1. 胜率是否有改善")
        print(f"   2. 信号质量是否提升")
        print(f"   3. 系统是否稳定运行")
        print(f"   4. 优化配置是否生效")
    else:
        print(f"\n⚠️ 分析不完整，可能需要:")
        print(f"   1. 检查回测是否完成")
        print(f"   2. 确认数据库是否生成")
        print(f"   3. 验证日志是否更新")

def main():
    """主函数"""
    print("🔍 最新回测结果分析")
    print("=" * 80)
    
    print("🎯 目标: 验证优化效果和胜率改善")
    
    # 分析最新数据库
    analyze_latest_database()
    
    # 分析最新日志
    analyze_latest_logs()
    
    # 对比优化结果
    compare_optimization_results()
    
    # 生成优化评估
    generate_optimization_assessment()
    
    print(f"\n🏆 分析完成")
    print("=" * 40)
    print("📊 请根据以上分析结果评估优化效果")
    print("🎯 如果胜率有改善，说明优化成功")
    print("🔧 如果效果不佳，可能需要进一步调整")

if __name__ == '__main__':
    main()
