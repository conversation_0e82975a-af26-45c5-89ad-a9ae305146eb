# coding=utf-8
"""
卖出记录分析器
分析数据库中的卖出记录，找出胜率高的买入条件组合
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class SellRecordsAnalyzer:
    """卖出记录分析器"""
    
    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        
    def analyze_winning_combinations(self):
        """分析胜率高的组合条件"""
        print('📊 卖出记录分析器 - 胜率高的组合条件分析')
        print('=' * 80)
        
        try:
            # 1. 加载卖出记录数据
            data = self._load_sell_records()
            if data is None or len(data) == 0:
                print('❌ 没有找到卖出记录数据')
                return None
            
            print(f'📈 加载卖出记录: {len(data)} 条')
            
            # 2. 数据预处理
            processed_data = self._preprocess_data(data)
            if processed_data is None or len(processed_data) == 0:
                print('❌ 数据预处理失败')
                return None
            
            print(f'✅ 数据预处理完成: {len(processed_data)} 条有效记录')
            
            # 3. 基础统计
            self._show_basic_stats(processed_data)
            
            # 4. 单因子分析
            print('\n🔍 单因子胜率分析')
            print('=' * 60)
            single_factor_results = self._analyze_single_factors(processed_data)
            
            # 5. 多因子组合分析
            print('\n🎯 多因子组合分析')
            print('=' * 60)
            combination_results = self._analyze_factor_combinations(processed_data)
            
            # 6. 生成优化建议
            print('\n💡 策略优化建议')
            print('=' * 60)
            self._generate_optimization_suggestions(single_factor_results, combination_results)
            
            return {
                'single_factors': single_factor_results,
                'combinations': combination_results,
                'data_stats': self._get_data_stats(processed_data)
            }
            
        except Exception as e:
            print(f'❌ 分析过程中出错: {e}')
            import traceback
            traceback.print_exc()
            return None
    
    def _load_sell_records(self):
        """加载卖出记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取所有卖出记录
            query = """
            SELECT * FROM trades 
            WHERE action = 'SELL' 
            AND net_profit_pct_sell IS NOT NULL
            ORDER BY timestamp DESC
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            return data
            
        except Exception as e:
            print(f'❌ 加载数据失败: {e}')
            return None
    
    def _preprocess_data(self, data):
        """数据预处理"""
        try:
            # 添加胜负标记
            data['is_win'] = data['net_profit_pct_sell'] > 0
            
            # 过滤掉异常数据
            data = data[data['net_profit_pct_sell'].between(-50, 50)]  # 过滤极端值
            
            # 选择关键指标列
            key_columns = [
                'symbol', 'timestamp', 'price', 'net_profit_pct_sell', 'is_win',
                'rsi_3d', 'rsi_5d', 'rsi_14d', 'rsi_30d',
                'ma_5d', 'ma_10d', 'ma_20d', 'ma_60d',
                'macd_signal', 'macd_histogram', 'macd_line',
                'bb_upper_20', 'bb_lower_20', 'bb_width_20',
                'volume_ma5_ratio', 'volume_ma10_ratio', 'volume_ma20_ratio',
                'price_momentum_3d', 'price_momentum_5d', 'price_momentum_10d',
                'volatility_3d', 'volatility_5d', 'volatility_10d',
                'atr_14d', 'adx_14d', 'cci_14d',
                'kdj_k', 'kdj_d', 'kdj_j'
            ]
            
            # 只保留存在的列
            available_columns = [col for col in key_columns if col in data.columns]
            data = data[available_columns].copy()
            
            # 删除包含NaN的行
            data = data.dropna()
            
            return data
            
        except Exception as e:
            print(f'❌ 数据预处理失败: {e}')
            return None
    
    def _show_basic_stats(self, data):
        """显示基础统计信息"""
        total_trades = len(data)
        winning_trades = data['is_win'].sum()
        win_rate = winning_trades / total_trades
        avg_profit = data['net_profit_pct_sell'].mean()
        avg_win = data[data['is_win']]['net_profit_pct_sell'].mean()
        avg_loss = data[~data['is_win']]['net_profit_pct_sell'].mean()
        
        print(f'📊 基础统计信息:')
        print(f'  总交易数: {total_trades:,}')
        print(f'  盈利交易: {winning_trades:,}')
        print(f'  亏损交易: {total_trades - winning_trades:,}')
        print(f'  整体胜率: {win_rate:.2%}')
        print(f'  平均收益: {avg_profit:.2f}%')
        print(f'  平均盈利: {avg_win:.2f}%')
        print(f'  平均亏损: {avg_loss:.2f}%')
        print(f'  盈亏比: {abs(avg_win/avg_loss):.2f}' if avg_loss != 0 else '  盈亏比: N/A')
    
    def _analyze_single_factors(self, data):
        """分析单因子胜率"""
        results = {}
        
        # 技术指标列
        factor_columns = [col for col in data.columns if col not in ['symbol', 'timestamp', 'price', 'net_profit_pct_sell', 'is_win']]
        
        for factor in factor_columns:
            try:
                # 将因子分为5个分位数
                data[f'{factor}_quintile'] = pd.qcut(data[factor], q=5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'], duplicates='drop')
                
                # 计算每个分位数的胜率
                quintile_stats = data.groupby(f'{factor}_quintile').agg({
                    'is_win': ['count', 'sum', 'mean'],
                    'net_profit_pct_sell': 'mean'
                }).round(4)
                
                # 找出胜率最高的分位数
                best_quintile = quintile_stats[('is_win', 'mean')].idxmax()
                best_win_rate = quintile_stats.loc[best_quintile, ('is_win', 'mean')]
                best_avg_profit = quintile_stats.loc[best_quintile, ('net_profit_pct_sell', 'mean')]
                
                results[factor] = {
                    'best_quintile': best_quintile,
                    'best_win_rate': best_win_rate,
                    'best_avg_profit': best_avg_profit,
                    'quintile_stats': quintile_stats
                }
                
            except Exception as e:
                continue
        
        # 按胜率排序显示前10个最有效的因子
        sorted_factors = sorted(results.items(), key=lambda x: x[1]['best_win_rate'], reverse=True)
        
        print('🏆 胜率最高的单因子 (Top 10):')
        for i, (factor, stats) in enumerate(sorted_factors[:10], 1):
            print(f'  {i:2d}. {factor:<20} 最佳胜率: {stats["best_win_rate"]:.2%} '
                  f'({stats["best_quintile"]}) 平均收益: {stats["best_avg_profit"]:.2f}%')
        
        return results
    
    def _analyze_factor_combinations(self, data):
        """分析多因子组合"""
        results = {}
        
        # 选择胜率较高的因子进行组合分析
        single_results = self._analyze_single_factors(data)
        top_factors = sorted(single_results.items(), key=lambda x: x[1]['best_win_rate'], reverse=True)[:8]
        
        print(f'🔍 分析前8个最有效因子的组合...')
        
        # 两因子组合
        for i in range(len(top_factors)):
            for j in range(i+1, len(top_factors)):
                factor1, stats1 = top_factors[i]
                factor2, stats2 = top_factors[j]
                
                try:
                    # 创建组合条件
                    condition1 = data[f'{factor1}_quintile'] == stats1['best_quintile']
                    condition2 = data[f'{factor2}_quintile'] == stats2['best_quintile']
                    combined_condition = condition1 & condition2
                    
                    if combined_condition.sum() >= 10:  # 至少10个样本
                        subset = data[combined_condition]
                        win_rate = subset['is_win'].mean()
                        avg_profit = subset['net_profit_pct_sell'].mean()
                        sample_count = len(subset)
                        
                        combo_name = f'{factor1}_{stats1["best_quintile"]} & {factor2}_{stats2["best_quintile"]}'
                        results[combo_name] = {
                            'win_rate': win_rate,
                            'avg_profit': avg_profit,
                            'sample_count': sample_count,
                            'factors': [factor1, factor2]
                        }
                        
                except Exception as e:
                    continue
        
        # 按胜率排序显示前10个最佳组合
        sorted_combos = sorted(results.items(), key=lambda x: x[1]['win_rate'], reverse=True)
        
        print('🎯 胜率最高的因子组合 (Top 10):')
        for i, (combo_name, stats) in enumerate(sorted_combos[:10], 1):
            print(f'  {i:2d}. 胜率: {stats["win_rate"]:.2%} '
                  f'平均收益: {stats["avg_profit"]:.2f}% '
                  f'样本数: {stats["sample_count"]} '
                  f'组合: {combo_name}')
        
        return results
    
    def _generate_optimization_suggestions(self, single_results, combo_results):
        """生成优化建议"""
        # 找出最佳单因子
        best_single = max(single_results.items(), key=lambda x: x[1]['best_win_rate'])
        
        # 找出最佳组合
        best_combo = max(combo_results.items(), key=lambda x: x[1]['win_rate']) if combo_results else None
        
        print('💡 策略优化建议:')
        print(f'  🏆 最佳单因子: {best_single[0]} ({best_single[1]["best_quintile"]})')
        print(f'     胜率: {best_single[1]["best_win_rate"]:.2%}')
        print(f'     平均收益: {best_single[1]["best_avg_profit"]:.2f}%')
        
        if best_combo:
            print(f'  🎯 最佳组合: {best_combo[0]}')
            print(f'     胜率: {best_combo[1]["win_rate"]:.2%}')
            print(f'     平均收益: {best_combo[1]["avg_profit"]:.2f}%')
            print(f'     样本数: {best_combo[1]["sample_count"]}')
        
        # 生成具体的策略建议
        print('\n📋 具体实施建议:')
        print('  1. 在买入条件中加入最佳单因子筛选')
        print('  2. 考虑使用最佳因子组合提高选股精度')
        print('  3. 根据历史胜率调整仓位大小')
        print('  4. 定期回测验证因子有效性')
    
    def _get_data_stats(self, data):
        """获取数据统计信息"""
        return {
            'total_trades': len(data),
            'win_rate': data['is_win'].mean(),
            'avg_profit': data['net_profit_pct_sell'].mean(),
            'date_range': (data['timestamp'].min(), data['timestamp'].max())
        }

def main():
    """主函数"""
    analyzer = SellRecordsAnalyzer()
    results = analyzer.analyze_winning_combinations()
    
    if results:
        print('\n✅ 分析完成！')
        print(f'📊 数据统计: {results["data_stats"]}')
    else:
        print('❌ 分析失败')

if __name__ == '__main__':
    main()
