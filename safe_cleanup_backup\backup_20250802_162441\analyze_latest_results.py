# coding=utf-8
"""
分析最新回测结果
评估最终优化效果并提供进一步改进建议
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def analyze_current_performance():
    """分析当前策略表现"""
    print('📊 分析最新回测数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有交易数据
        query = """
        SELECT 
            timestamp, symbol, action, price,
            sell_reason, net_profit_pct_sell, holding_hours,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy
        FROM trades 
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 总交易记录: {len(df)} 条')
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        
        # 分析时间分布
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        latest_time = df['timestamp'].max()
        earliest_time = df['timestamp'].min()
        
        print(f'   时间范围: {earliest_time.strftime("%Y-%m-%d")} 到 {latest_time.strftime("%Y-%m-%d")}')
        print(f'   数据跨度: {(latest_time - earliest_time).days} 天')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, None, None

def evaluate_optimization_impact(sell_records):
    """评估优化效果"""
    print(f'\n🎯 优化效果评估')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录')
        return None
    
    # 计算当前胜率
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return None
    
    current_wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    current_total = len(completed_trades)
    current_win_rate = current_wins / current_total * 100
    
    current_avg_profit = completed_trades['net_profit_pct_sell'].mean()
    current_median_profit = completed_trades['net_profit_pct_sell'].median()
    
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
    
    current_avg_win = winning_trades['net_profit_pct_sell'].mean() if len(winning_trades) > 0 else 0
    current_avg_loss = abs(losing_trades['net_profit_pct_sell'].mean()) if len(losing_trades) > 0 else 0
    
    print(f'📈 当前策略表现:')
    print(f'   总胜率: {current_win_rate:.1f}% ({current_wins}/{current_total})')
    print(f'   平均收益: {current_avg_profit:.2f}%')
    print(f'   中位数收益: {current_median_profit:.2f}%')
    print(f'   平均盈利: {current_avg_win:.2f}%')
    print(f'   平均亏损: {current_avg_loss:.2f}%')
    print(f'   盈亏比: {current_avg_win/current_avg_loss:.2f}' if current_avg_loss > 0 else '   盈亏比: N/A')
    
    # 与历史胜率对比
    previous_rates = [24.7, 40.8]  # 之前的胜率记录
    
    print(f'\n📊 胜率进化历程:')
    print(f'   初始胜率: 24.7%')
    print(f'   第一轮优化: 40.8% (+16.1%)')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    
    if len(previous_rates) > 0:
        latest_improvement = current_win_rate - previous_rates[-1]
        total_improvement = current_win_rate - previous_rates[0]
        
        print(f'   最新提升: {latest_improvement:+.1f}%')
        print(f'   总体提升: {total_improvement:+.1f}%')
        
        # 评估优化效果
        if current_win_rate >= 60:
            print(f'   🏆 已达到目标胜率 (60%+)!')
        elif current_win_rate >= 50:
            print(f'   🚀 胜率表现优秀!')
        elif current_win_rate >= 45:
            print(f'   📈 胜率有显著改善!')
        elif latest_improvement > 0:
            print(f'   📊 胜率有所提升')
        else:
            print(f'   ⚠️ 胜率需要进一步优化')
    
    return current_win_rate

def analyze_sell_reasons_performance(sell_records):
    """分析卖出原因表现"""
    print(f'\n📋 卖出原因表现分析')
    print('=' * 50)
    
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    # 统计各卖出原因
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', 'median', lambda x: (x > 0).mean() * 100],
        'holding_hours': ['mean', 'median']
    }).round(2)
    
    sell_reason_stats.columns = ['交易数', '平均收益%', '中位收益%', '胜率%', '平均持仓h', '中位持仓h']
    sell_reason_stats = sell_reason_stats.sort_values('胜率%', ascending=False)
    
    print(f'📊 各卖出原因表现 (按胜率排序):')
    print(sell_reason_stats.to_string())
    
    # 重点分析优化目标
    print(f'\n🎯 关键优化指标检查:')
    
    # 检查固定止损改善情况
    fixed_stop_loss = completed_trades[completed_trades['sell_reason'] == '固定止损']
    if len(fixed_stop_loss) > 0:
        fixed_rate = (fixed_stop_loss['net_profit_pct_sell'] > 0).mean() * 100
        fixed_avg = fixed_stop_loss['net_profit_pct_sell'].mean()
        fixed_count = len(fixed_stop_loss)
        
        print(f'   固定止损: {fixed_count}笔, 胜率{fixed_rate:.1f}%, 平均收益{fixed_avg:.2f}%')
        
        if fixed_rate > 10:
            print(f'   ✅ 固定止损胜率显著改善! (之前0%)')
        elif fixed_rate > 0:
            print(f'   📈 固定止损胜率有所改善 (之前0%)')
        else:
            print(f'   ⚠️ 固定止损胜率仍为0%，需要进一步优化')
    else:
        print(f'   ✅ 没有固定止损交易 (优化成功)')
    
    # 检查最大持仓天数表现
    max_holding = completed_trades[completed_trades['sell_reason'] == '最大持仓天数']
    if len(max_holding) > 0:
        max_rate = (max_holding['net_profit_pct_sell'] > 0).mean() * 100
        max_avg = max_holding['net_profit_pct_sell'].mean()
        max_count = len(max_holding)
        
        print(f'   最大持仓天数: {max_count}笔, 胜率{max_rate:.1f}%, 平均收益{max_avg:.2f}%')
        
        if max_rate >= 80:
            print(f'   ✅ 最大持仓天数表现优秀 (保持高胜率)')
        else:
            print(f'   💡 最大持仓天数胜率有所下降')
    
    # 检查跟踪止盈表现
    trailing_stop = completed_trades[completed_trades['sell_reason'] == '跟踪止盈']
    if len(trailing_stop) > 0:
        trailing_rate = (trailing_stop['net_profit_pct_sell'] > 0).mean() * 100
        trailing_avg = trailing_stop['net_profit_pct_sell'].mean()
        trailing_count = len(trailing_stop)
        
        print(f'   跟踪止盈: {trailing_count}笔, 胜率{trailing_rate:.1f}%, 平均收益{trailing_avg:.2f}%')
        
        if trailing_rate >= 50:
            print(f'   ✅ 跟踪止盈表现良好')
        else:
            print(f'   💡 跟踪止盈需要进一步优化')

def identify_improvement_opportunities(sell_records, current_win_rate):
    """识别改进机会"""
    print(f'\n💡 改进机会识别')
    print('=' * 50)
    
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有数据进行分析')
        return []
    
    opportunities = []
    
    # 分析各卖出原因的改进潜力
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', lambda x: (x > 0).mean() * 100, 'mean']
    })
    sell_reason_stats.columns = ['交易数', '胜率%', '平均收益%']
    
    print(f'🔍 改进机会分析:')
    
    for reason, stats in sell_reason_stats.iterrows():
        count = stats['交易数']
        win_rate = stats['胜率%']
        avg_profit = stats['平均收益%']
        
        # 识别改进机会
        if count >= 50:  # 足够的样本量
            if win_rate < 30:
                impact = count * (50 - win_rate) / 100  # 潜在改进影响
                opportunities.append({
                    'reason': reason,
                    'current_win_rate': win_rate,
                    'count': count,
                    'improvement_potential': impact,
                    'priority': 'high' if impact > 20 else 'medium'
                })
                print(f'   🚨 {reason}: {win_rate:.1f}%胜率, {count}笔 - 高优先级改进')
            elif win_rate < 50:
                impact = count * (60 - win_rate) / 100
                opportunities.append({
                    'reason': reason,
                    'current_win_rate': win_rate,
                    'count': count,
                    'improvement_potential': impact,
                    'priority': 'medium'
                })
                print(f'   📊 {reason}: {win_rate:.1f}%胜率, {count}笔 - 中等优先级改进')
    
    # 按改进潜力排序
    opportunities.sort(key=lambda x: x['improvement_potential'], reverse=True)
    
    return opportunities

def generate_optimization_recommendations(opportunities, current_win_rate):
    """生成优化建议"""
    print(f'\n🎯 优化建议生成')
    print('=' * 50)
    
    print(f'📊 当前状况:')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    print(f'   目标胜率: 60%+')
    print(f'   差距: {60 - current_win_rate:.1f}%')
    
    if not opportunities:
        if current_win_rate >= 60:
            print(f'\n🏆 策略表现优秀，建议保持当前配置')
        else:
            print(f'\n💡 需要更深入的分析来找到改进机会')
        return
    
    print(f'\n🎯 优化建议 (按优先级排序):')
    
    config_changes = {}
    
    for i, opp in enumerate(opportunities[:3], 1):  # 只显示前3个机会
        reason = opp['reason']
        win_rate = opp['current_win_rate']
        count = opp['count']
        priority = opp['priority']
        
        print(f'\n   {i}. {reason} ({priority}优先级)')
        print(f'      当前: {count}笔, {win_rate:.1f}%胜率')
        
        if reason == '固定止损':
            if win_rate < 10:
                print(f'      建议: 进一步放宽止损到5%或暂时禁用')
                config_changes['FIXED_STOP_LOSS_RATIO'] = 0.05
                config_changes['ENABLE_FIXED_STOP_LOSS'] = False
            else:
                print(f'      建议: 微调止损参数')
                
        elif reason == '跟踪止盈':
            if win_rate < 50:
                print(f'      建议: 优化跟踪止盈参数')
                config_changes['TRAILING_STOP_PCT'] = 0.8
                config_changes['TRAILING_STOP_ACTIVATION'] = 1.5
            
        elif reason == '最大持仓天数':
            if win_rate < 80:
                print(f'      建议: 延长最大持仓时间')
                config_changes['MAX_HOLDING_DAYS'] = 30
    
    # 生成配置代码
    if config_changes:
        print(f'\n⚙️ 建议的配置调整:')
        print(f'```python')
        for key, value in config_changes.items():
            if isinstance(value, bool):
                print(f'{key} = {value}')
            else:
                print(f'{key} = {value}')
        print(f'```')
    
    # 预期效果
    total_potential = sum(opp['improvement_potential'] for opp in opportunities[:3])
    expected_improvement = min(total_potential * 0.3, 15)  # 保守估计30%实现率
    
    print(f'\n📈 预期效果:')
    print(f'   预期胜率提升: +{expected_improvement:.1f}%')
    print(f'   目标胜率: {current_win_rate + expected_improvement:.1f}%')

def main():
    """主函数"""
    print('🚀 最新回测数据分析与优化建议')
    print('=' * 60)
    
    # 分析当前表现
    df, buy_records, sell_records = analyze_current_performance()
    
    if df is not None:
        # 评估优化效果
        current_win_rate = evaluate_optimization_impact(sell_records)
        
        if current_win_rate is not None:
            # 分析卖出原因表现
            analyze_sell_reasons_performance(sell_records)
            
            # 识别改进机会
            opportunities = identify_improvement_opportunities(sell_records, current_win_rate)
            
            # 生成优化建议
            generate_optimization_recommendations(opportunities, current_win_rate)
            
            print(f'\n🎯 总结')
            print('=' * 40)
            print('✅ 最新回测数据分析完成')
            print('📊 已评估当前优化效果')
            print('💡 已生成进一步优化建议')
            
            if current_win_rate >= 60:
                print('🏆 恭喜！策略已达到世界级水平!')
            elif current_win_rate >= 50:
                print('🚀 策略表现优秀，继续精进!')
            elif current_win_rate >= 40:
                print('📈 策略有显著改善，继续优化!')
            else:
                print('🔧 策略需要进一步调整!')
        else:
            print('⚠️ 无法获取胜率数据')
    else:
        print('❌ 无法获取回测数据')

if __name__ == '__main__':
    main()
