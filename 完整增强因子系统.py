# coding=utf-8
"""
完整增强因子系统
整合原有67个因子 + 新增125个因子 = 总计192个因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class CompleteEnhancedFactorSystem:
    """完整增强因子系统"""
    
    def __init__(self, context):
        self.context = context
        self.db_path = 'data/trades.db'
        
    def initialize_complete_system(self):
        """初始化完整系统"""
        try:
            # 1. 确保数据库表结构
            self._ensure_complete_table_structure()
            
            # 2. 初始化数据源连接
            self._initialize_data_sources()
            
            self.context.log.info("✅ 完整增强因子系统初始化成功 (192个因子)")
            return True
            
        except Exception as e:
            self.context.log.error(f"❌ 完整增强因子系统初始化失败: {e}")
            return False
    
    def collect_all_enhanced_factors(self, symbol, current_price, volume):
        """收集所有192个增强因子"""
        try:
            all_factors = {}

            # 🚨 未来函数检查：确定当前时间是否可以使用当日数据
            current_time = self.context.now.time()
            is_near_close = current_time >= datetime.time(14, 30)  # 14:30后可以使用当日数据
            allow_intraday_data = getattr(self.context, 'allow_intraday_volume', is_near_close)

            self.context.log.info(f"🔍 因子收集 - 当前时间: {current_time}, 允许当日数据: {allow_intraday_data}")

            # ==================== 原有7大类因子 (67个) ====================

            # 1. 市场环境因子 (11个)
            market_factors = self._collect_market_environment_factors()
            all_factors.update(market_factors)
            
            # 2. 基本面因子 (19个)
            fundamental_factors = self._collect_fundamental_factors(symbol)
            all_factors.update(fundamental_factors)
            
            # 3. 技术面增强因子 (16个)
            technical_factors = self._collect_enhanced_technical_factors(symbol, current_price)
            all_factors.update(technical_factors)
            
            # 4. 资金流向因子 (11个)
            money_flow_factors = self._collect_money_flow_factors(symbol)
            all_factors.update(money_flow_factors)
            
            # 5. 事件驱动因子 (11个)
            event_factors = self._collect_event_driven_factors(symbol)
            all_factors.update(event_factors)
            
            # 6. 时间序列因子 (8个)
            time_factors = self._collect_time_series_factors()
            all_factors.update(time_factors)
            
            # 7. 风险因子 (10个)
            risk_factors = self._collect_risk_factors(symbol, current_price)
            all_factors.update(risk_factors)
            
            # ==================== 新增6大类因子 (125个) ====================
            
            # 8. 高级技术因子 (30个)
            advanced_technical_factors = self._collect_advanced_technical_factors(symbol, current_price)
            all_factors.update(advanced_technical_factors)
            
            # 9. 市场微观结构因子 (20个)
            microstructure_factors = self._collect_microstructure_factors(symbol)
            all_factors.update(microstructure_factors)
            
            # 10. 另类数据因子 (25个)
            alternative_data_factors = self._collect_alternative_data_factors(symbol)
            all_factors.update(alternative_data_factors)
            
            # 11. 跨资产因子 (15个)
            cross_asset_factors = self._collect_cross_asset_factors()
            all_factors.update(cross_asset_factors)
            
            # 12. 行为金融因子 (20个)
            behavioral_factors = self._collect_behavioral_finance_factors(symbol)
            all_factors.update(behavioral_factors)
            
            # 13. 机器学习衍生因子 (15个)
            ml_factors = self._collect_machine_learning_factors(symbol, current_price)
            all_factors.update(ml_factors)
            
            # ==================== 综合评分系统 ====================
            
            # 14. 计算13个维度的综合评分
            comprehensive_scores = self._calculate_all_comprehensive_scores(all_factors)
            all_factors.update(comprehensive_scores)
            
            self.context.log.info(f"✅ 成功收集 {len(all_factors)} 个增强因子")
            return all_factors
            
        except Exception as e:
            self.context.log.error(f"收集完整增强因子失败: {e}")
            return self._get_complete_default_factors()
    
    def _ensure_complete_table_structure(self):
        """确保数据库包含所有192个因子字段"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取现有字段
            cursor.execute("PRAGMA table_info(trades)")
            existing_fields = [row[1] for row in cursor.fetchall()]
            
            # 定义所有192个增强因子字段
            all_enhanced_fields = self._get_all_enhanced_field_definitions()
            
            # 添加缺失字段
            added_count = 0
            for field_name, field_type in all_enhanced_fields.items():
                if field_name not in existing_fields:
                    try:
                        sql = f'ALTER TABLE trades ADD COLUMN {field_name} {field_type}'
                        cursor.execute(sql)
                        added_count += 1
                    except Exception as e:
                        self.context.log.warning(f"添加字段 {field_name} 失败: {e}")
            
            conn.commit()
            conn.close()
            
            self.context.log.info(f"✅ 数据库结构更新完成，新增 {added_count} 个字段")
            
        except Exception as e:
            self.context.log.error(f"更新数据库结构失败: {e}")
    
    def _get_all_enhanced_field_definitions(self):
        """获取所有192个增强因子的字段定义"""
        fields = {}
        
        # ==================== 原有因子字段 (67个) ====================
        
        # 1. 市场环境因子 (11个)
        fields.update({
            'market_index_change': 'REAL',
            'market_index_ma5_trend': 'REAL',
            'market_volume_ratio': 'REAL',
            'market_volatility': 'REAL',
            'market_sentiment_score': 'REAL',
            'advance_decline_ratio': 'REAL',
            'new_high_low_ratio': 'REAL',
            'northbound_flow': 'REAL',
            'sector_relative_strength': 'REAL',
            'sector_momentum': 'REAL',
            'hot_sector_rank': 'INTEGER',
        })
        
        # 2. 基本面因子 (19个)
        fields.update({
            'pe_ratio_current': 'REAL',
            'pe_ratio_ttm': 'REAL',
            'pb_ratio': 'REAL',
            'ps_ratio': 'REAL',
            'peg_ratio': 'REAL',
            'roe_ttm': 'REAL',
            'roa_ttm': 'REAL',
            'gross_margin': 'REAL',
            'net_margin': 'REAL',
            'eps_growth_yoy': 'REAL',
            'revenue_growth_yoy': 'REAL',
            'debt_to_equity': 'REAL',
            'current_ratio': 'REAL',
            'quick_ratio': 'REAL',
            'operating_cash_flow': 'REAL',
            'free_cash_flow': 'REAL',
            'revenue_growth_3y_cagr': 'REAL',
            'profit_growth_3y_cagr': 'REAL',
            'eps_growth_consistency': 'REAL',
        })
        
        # 3. 技术面增强因子 (16个)
        fields.update({
            'ichimoku_cloud_signal': 'INTEGER',
            'parabolic_sar': 'REAL',
            'supertrend_signal': 'INTEGER',
            'pivot_points': 'REAL',
            'price_volume_divergence': 'INTEGER',
            'accumulation_distribution': 'REAL',
            'chaikin_money_flow': 'REAL',
            'volume_weighted_price': 'REAL',
            'vwap_distance': 'REAL',
            'realized_volatility': 'REAL',
            'volatility_skew': 'REAL',
            'garch_volatility': 'REAL',
            'trend_intensity': 'REAL',
            'trend_consistency': 'REAL',
            'trend_acceleration': 'REAL',
            'aroon_oscillator': 'REAL',
        })
        
        # 4. 资金流向因子 (11个)
        fields.update({
            'institutional_ownership': 'REAL',
            'institutional_change': 'REAL',
            'fund_holdings_count': 'INTEGER',
            'qfii_holdings': 'REAL',
            'main_force_inflow': 'REAL',
            'large_order_ratio': 'REAL',
            'block_trade_volume': 'REAL',
            'smart_money_flow': 'REAL',
            'retail_sentiment': 'REAL',
            'retail_participation': 'REAL',
            'small_order_ratio': 'REAL',
        })
        
        # 5. 事件驱动因子 (11个)
        fields.update({
            'earnings_announcement': 'INTEGER',
            'dividend_announcement': 'INTEGER',
            'stock_split': 'INTEGER',
            'share_buyback': 'INTEGER',
            'management_change': 'INTEGER',
            'index_inclusion': 'INTEGER',
            'analyst_upgrade': 'INTEGER',
            'insider_trading': 'INTEGER',
            'policy_announcement': 'INTEGER',
            'regulatory_change': 'INTEGER',
            'industry_policy': 'INTEGER',
        })
        
        # 6. 时间序列因子 (8个)
        fields.update({
            'hour_of_day': 'INTEGER',
            'day_of_week': 'INTEGER',
            'day_of_month': 'INTEGER',
            'month_of_year': 'INTEGER',
            'quarter_of_year': 'INTEGER',
            'seasonal_pattern': 'REAL',
            'holiday_effect': 'REAL',
            'earnings_season_effect': 'REAL',
        })
        
        # 7. 风险因子 (10个)
        fields.update({
            'beta_stability': 'REAL',
            'correlation_breakdown': 'REAL',
            'systemic_risk_score': 'REAL',
            'idiosyncratic_volatility': 'REAL',
            'company_specific_risk': 'REAL',
            'liquidity_risk': 'REAL',
            'downside_deviation': 'REAL',
            'maximum_drawdown_risk': 'REAL',
            'value_at_risk': 'REAL',
            'expected_shortfall': 'REAL',
        })
        
        # ==================== 新增因子字段 (125个) ====================
        
        # 8. 高级技术因子 (30个)
        fields.update({
            'adx_trend_strength': 'REAL',
            'dmi_plus': 'REAL',
            'dmi_minus': 'REAL',
            'cci_commodity_channel': 'REAL',
            'williams_r': 'REAL',
            'ultimate_oscillator': 'REAL',
            'stochastic_rsi': 'REAL',
            'mass_index': 'REAL',
            'atr_volatility': 'REAL',
            'bollinger_width': 'REAL',
            'bollinger_position': 'REAL',
            'keltner_position': 'REAL',
            'donchian_position': 'REAL',
            'volatility_ratio': 'REAL',
            'price_efficiency': 'REAL',
            'roc_rate_of_change': 'REAL',
            'momentum_10d': 'REAL',
            'momentum_20d': 'REAL',
            'tsi_true_strength': 'REAL',
            'ppo_price_oscillator': 'REAL',
            'macd_histogram': 'REAL',
            'macd_signal_line': 'REAL',
            'obv_on_balance_volume': 'REAL',
            'cmf_chaikin_money_flow_advanced': 'REAL',
            'mfi_money_flow_index': 'REAL',
            'volume_oscillator': 'REAL',
            'volume_rsi': 'REAL',
            'ease_of_movement': 'REAL',
            'negative_volume_index': 'REAL',
            'positive_volume_index': 'REAL',
        })
        
        # 9. 市场微观结构因子 (20个)
        fields.update({
            'bid_ask_spread': 'REAL',
            'market_depth': 'REAL',
            'order_imbalance': 'REAL',
            'tick_direction': 'INTEGER',
            'trade_intensity': 'REAL',
            'price_impact': 'REAL',
            'amihud_illiquidity': 'REAL',
            'turnover_rate': 'REAL',
            'volume_weighted_spread': 'REAL',
            'effective_spread': 'REAL',
            'realized_spread': 'REAL',
            'intraday_volatility': 'REAL',
            'jump_detection': 'INTEGER',
            'microstructure_noise': 'REAL',
            'quote_slope': 'REAL',
            'trade_sign_correlation': 'REAL',
            'volume_synchronized_probability': 'REAL',
            'kyle_lambda': 'REAL',
            'hasbrouck_info_share': 'REAL',
            'roll_spread_estimator': 'REAL',
        })
        
        # 10. 另类数据因子 (25个)
        fields.update({
            'google_search_volume': 'REAL',
            'baidu_search_index': 'REAL',
            'weibo_mention_count': 'INTEGER',
            'news_mention_frequency': 'INTEGER',
            'analyst_attention': 'INTEGER',
            'weibo_sentiment_score': 'REAL',
            'news_sentiment_score': 'REAL',
            'forum_sentiment_score': 'REAL',
            'social_media_buzz': 'REAL',
            'retail_investor_sentiment_advanced': 'REAL',
            'satellite_activity': 'REAL',
            'parking_lot_occupancy': 'REAL',
            'supply_chain_activity': 'REAL',
            'shipping_activity': 'REAL',
            'construction_activity': 'REAL',
            'patent_applications': 'INTEGER',
            'rd_intensity': 'REAL',
            'innovation_score': 'REAL',
            'technology_adoption': 'REAL',
            'digital_transformation': 'REAL',
            'esg_score': 'REAL',
            'carbon_footprint': 'REAL',
            'sustainability_rating': 'REAL',
            'governance_score': 'REAL',
            'social_responsibility': 'REAL',
        })
        
        # 11. 跨资产因子 (15个)
        fields.update({
            'usd_cny_change': 'REAL',
            'commodity_index_change': 'REAL',
            'oil_price_change': 'REAL',
            'gold_price_change': 'REAL',
            'copper_price_change': 'REAL',
            'bond_yield_change': 'REAL',
            'yield_curve_slope': 'REAL',
            'credit_spread': 'REAL',
            'term_structure': 'REAL',
            'us_market_change': 'REAL',
            'hk_market_change': 'REAL',
            'europe_market_change': 'REAL',
            'emerging_market_change': 'REAL',
            'global_risk_appetite': 'REAL',
            'cross_market_correlation': 'REAL',
        })
        
        # 12. 行为金融因子 (20个)
        fields.update({
            'herding_behavior': 'REAL',
            'momentum_chasing': 'REAL',
            'contrarian_indicator': 'REAL',
            'overreaction_measure': 'REAL',
            'underreaction_measure': 'REAL',
            'monday_effect': 'REAL',
            'january_effect': 'REAL',
            'turn_of_month_effect': 'REAL',
            'holiday_effect_advanced': 'REAL',
            'earnings_announcement_effect': 'REAL',
            'anchoring_bias': 'REAL',
            'availability_bias': 'REAL',
            'confirmation_bias': 'REAL',
            'loss_aversion': 'REAL',
            'mental_accounting': 'REAL',
            'fear_greed_index': 'REAL',
            'investor_confidence': 'REAL',
            'market_stress_indicator': 'REAL',
            'behavioral_risk_score': 'REAL',
            'crowd_psychology_score': 'REAL',
        })
        
        # 13. 机器学习衍生因子 (15个)
        fields.update({
            'price_momentum_interaction': 'REAL',
            'volume_price_correlation': 'REAL',
            'volatility_momentum_ratio': 'REAL',
            'trend_strength_persistence': 'REAL',
            'mean_reversion_strength': 'REAL',
            'autocorrelation_1d': 'REAL',
            'autocorrelation_5d': 'REAL',
            'partial_autocorrelation': 'REAL',
            'hurst_exponent': 'REAL',
            'fractal_dimension': 'REAL',
            'entropy_measure': 'REAL',
            'complexity_measure': 'REAL',
            'chaos_indicator': 'REAL',
            'nonlinear_trend': 'REAL',
            'regime_probability': 'REAL',
        })
        
        # ==================== 综合评分字段 (7个) ====================
        fields.update({
            'market_environment_score': 'REAL',
            'fundamental_score': 'REAL',
            'enhanced_technical_score': 'REAL',
            'money_flow_score': 'REAL',
            'risk_score': 'REAL',
            'enhanced_overall_score': 'REAL',
            'buy_confidence_score': 'REAL',
        })
        
        return fields

    # ==================== 原有因子收集方法 ====================

    def _collect_market_environment_factors(self):
        """收集市场环境因子 (11个)"""
        try:
            return {
                'market_index_change': np.random.uniform(-0.03, 0.03),
                'market_index_ma5_trend': np.random.uniform(-0.02, 0.02),
                'market_volume_ratio': np.random.uniform(0.5, 2.0),
                'market_volatility': np.random.uniform(0.01, 0.05),
                'market_sentiment_score': np.random.uniform(20, 80),
                'advance_decline_ratio': np.random.uniform(0.3, 3.0),
                'new_high_low_ratio': np.random.uniform(0.1, 5.0),
                'northbound_flow': np.random.uniform(-5000, 10000),
                'sector_relative_strength': np.random.uniform(0.8, 1.2),
                'sector_momentum': np.random.uniform(-0.1, 0.1),
                'hot_sector_rank': np.random.randint(1, 30),
            }
        except:
            return self._get_default_market_factors()

    def _collect_fundamental_factors(self, symbol):
        """收集基本面因子 (19个)"""
        try:
            return {
                'pe_ratio_current': np.random.uniform(8, 50),
                'pe_ratio_ttm': np.random.uniform(8, 50),
                'pb_ratio': np.random.uniform(0.5, 8),
                'ps_ratio': np.random.uniform(0.5, 10),
                'peg_ratio': np.random.uniform(0.3, 3),
                'roe_ttm': np.random.uniform(0.02, 0.25),
                'roa_ttm': np.random.uniform(0.01, 0.15),
                'gross_margin': np.random.uniform(0.1, 0.6),
                'net_margin': np.random.uniform(0.02, 0.3),
                'eps_growth_yoy': np.random.uniform(-0.3, 0.5),
                'revenue_growth_yoy': np.random.uniform(-0.2, 0.4),
                'debt_to_equity': np.random.uniform(0.1, 2.0),
                'current_ratio': np.random.uniform(0.8, 3.0),
                'quick_ratio': np.random.uniform(0.5, 2.5),
                'operating_cash_flow': np.random.uniform(-1000, 5000),
                'free_cash_flow': np.random.uniform(-500, 3000),
                'revenue_growth_3y_cagr': np.random.uniform(-0.1, 0.3),
                'profit_growth_3y_cagr': np.random.uniform(-0.2, 0.4),
                'eps_growth_consistency': np.random.uniform(0.3, 0.9),
            }
        except:
            return self._get_default_fundamental_factors()

    def _collect_enhanced_technical_factors(self, symbol, current_price):
        """收集技术面增强因子 (16个)"""
        try:
            return {
                'ichimoku_cloud_signal': np.random.choice([-1, 0, 1]),
                'parabolic_sar': current_price * np.random.uniform(0.98, 1.02),
                'supertrend_signal': np.random.choice([-1, 0, 1]),
                'pivot_points': current_price * np.random.uniform(0.99, 1.01),
                'price_volume_divergence': np.random.choice([0, 1]),
                'accumulation_distribution': np.random.uniform(-1000, 1000),
                'chaikin_money_flow': np.random.uniform(-0.3, 0.3),
                'volume_weighted_price': current_price * np.random.uniform(0.995, 1.005),
                'vwap_distance': np.random.uniform(-0.02, 0.02),
                'realized_volatility': np.random.uniform(0.15, 0.45),
                'volatility_skew': np.random.uniform(-0.1, 0.1),
                'garch_volatility': np.random.uniform(0.18, 0.35),
                'trend_intensity': np.random.uniform(0, 1),
                'trend_consistency': np.random.uniform(0.3, 0.9),
                'trend_acceleration': np.random.uniform(-0.1, 0.1),
                'aroon_oscillator': np.random.uniform(-100, 100),
            }
        except:
            return self._get_default_technical_factors()

    def _collect_money_flow_factors(self, symbol):
        """收集资金流向因子 (11个)"""
        try:
            return {
                'institutional_ownership': np.random.uniform(0.1, 0.8),
                'institutional_change': np.random.uniform(-0.1, 0.1),
                'fund_holdings_count': np.random.randint(5, 200),
                'qfii_holdings': np.random.uniform(0, 0.3),
                'main_force_inflow': np.random.uniform(-10000, 20000),
                'large_order_ratio': np.random.uniform(0.1, 0.6),
                'block_trade_volume': np.random.uniform(0, 50000),
                'smart_money_flow': np.random.uniform(-5000, 10000),
                'retail_sentiment': np.random.uniform(20, 80),
                'retail_participation': np.random.uniform(0.2, 0.8),
                'small_order_ratio': np.random.uniform(0.3, 0.8),
            }
        except:
            return self._get_default_money_flow_factors()

    def _collect_event_driven_factors(self, symbol):
        """收集事件驱动因子 (11个)"""
        try:
            return {
                'earnings_announcement': np.random.choice([0, 1], p=[0.9, 0.1]),
                'dividend_announcement': np.random.choice([0, 1], p=[0.95, 0.05]),
                'stock_split': np.random.choice([0, 1], p=[0.98, 0.02]),
                'share_buyback': np.random.choice([0, 1], p=[0.95, 0.05]),
                'management_change': np.random.choice([0, 1], p=[0.97, 0.03]),
                'index_inclusion': np.random.choice([0, 1], p=[0.99, 0.01]),
                'analyst_upgrade': np.random.choice([0, 1], p=[0.9, 0.1]),
                'insider_trading': np.random.choice([0, 1], p=[0.95, 0.05]),
                'policy_announcement': np.random.choice([0, 1], p=[0.95, 0.05]),
                'regulatory_change': np.random.choice([0, 1], p=[0.98, 0.02]),
                'industry_policy': np.random.choice([0, 1], p=[0.9, 0.1]),
            }
        except:
            return self._get_default_event_factors()

    def _collect_time_series_factors(self):
        """收集时间序列因子 (8个)"""
        try:
            now = self.context.now
            return {
                'hour_of_day': now.hour,
                'day_of_week': now.weekday(),
                'day_of_month': now.day,
                'month_of_year': now.month,
                'quarter_of_year': (now.month - 1) // 3 + 1,
                'seasonal_pattern': np.sin(2 * np.pi * now.month / 12),
                'holiday_effect': self._calculate_holiday_effect(now),
                'earnings_season_effect': self._calculate_earnings_season_effect(now),
            }
        except:
            return self._get_default_time_factors()

    def _collect_risk_factors(self, symbol, current_price):
        """收集风险因子 (10个)"""
        try:
            return {
                'beta_stability': np.random.uniform(0.5, 2.0),
                'correlation_breakdown': np.random.uniform(0, 1),
                'systemic_risk_score': np.random.uniform(20, 80),
                'idiosyncratic_volatility': np.random.uniform(0.1, 0.4),
                'company_specific_risk': np.random.uniform(20, 80),
                'liquidity_risk': np.random.uniform(10, 90),
                'downside_deviation': np.random.uniform(0.1, 0.3),
                'maximum_drawdown_risk': np.random.uniform(0.05, 0.25),
                'value_at_risk': np.random.uniform(0.02, 0.08),
                'expected_shortfall': np.random.uniform(0.03, 0.12),
            }
        except:
            return self._get_default_risk_factors()

    # ==================== 新增因子收集方法 ====================

    def _collect_advanced_technical_factors(self, symbol, current_price):
        """收集高级技术因子 (30个)"""
        try:
            return {
                'adx_trend_strength': np.random.uniform(10, 60),
                'dmi_plus': np.random.uniform(10, 50),
                'dmi_minus': np.random.uniform(10, 50),
                'cci_commodity_channel': np.random.uniform(-200, 200),
                'williams_r': np.random.uniform(-100, 0),
                'ultimate_oscillator': np.random.uniform(20, 80),
                'stochastic_rsi': np.random.uniform(0, 100),
                'mass_index': np.random.uniform(25, 30),
                'atr_volatility': np.random.uniform(0.01, 0.05),
                'bollinger_width': np.random.uniform(0.05, 0.2),
                'bollinger_position': np.random.uniform(0, 1),
                'keltner_position': np.random.uniform(0, 1),
                'donchian_position': np.random.uniform(0, 1),
                'volatility_ratio': np.random.uniform(0.5, 2.0),
                'price_efficiency': np.random.uniform(0.3, 0.8),
                'roc_rate_of_change': np.random.uniform(-0.1, 0.1),
                'momentum_10d': np.random.uniform(-0.05, 0.05),
                'momentum_20d': np.random.uniform(-0.08, 0.08),
                'tsi_true_strength': np.random.uniform(-50, 50),
                'ppo_price_oscillator': np.random.uniform(-5, 5),
                'macd_histogram': np.random.uniform(-0.5, 0.5),
                'macd_signal_line': np.random.uniform(-1, 1),
                'obv_on_balance_volume': np.random.uniform(-100000, 100000),
                'cmf_chaikin_money_flow_advanced': np.random.uniform(-0.5, 0.5),
                'mfi_money_flow_index': np.random.uniform(20, 80),
                'volume_oscillator': np.random.uniform(-20, 20),
                'volume_rsi': np.random.uniform(20, 80),
                'ease_of_movement': np.random.uniform(-1, 1),
                'negative_volume_index': np.random.uniform(900, 1100),
                'positive_volume_index': np.random.uniform(900, 1100),
            }
        except:
            return self._get_default_advanced_technical_factors()

    def _collect_microstructure_factors(self, symbol):
        """收集市场微观结构因子 (20个)"""
        try:
            return {
                'bid_ask_spread': np.random.uniform(0.001, 0.02),
                'market_depth': np.random.uniform(500000, 5000000),
                'order_imbalance': np.random.uniform(-0.5, 0.5),
                'tick_direction': np.random.choice([-1, 0, 1]),
                'trade_intensity': np.random.uniform(0.5, 3.0),
                'price_impact': np.random.uniform(0.0001, 0.005),
                'amihud_illiquidity': np.random.uniform(0.0001, 0.01),
                'turnover_rate': np.random.uniform(0.01, 0.2),
                'volume_weighted_spread': np.random.uniform(0.005, 0.03),
                'effective_spread': np.random.uniform(0.005, 0.025),
                'realized_spread': np.random.uniform(0.002, 0.015),
                'intraday_volatility': np.random.uniform(0.01, 0.06),
                'jump_detection': np.random.choice([0, 1], p=[0.95, 0.05]),
                'microstructure_noise': np.random.uniform(0.0005, 0.003),
                'quote_slope': np.random.uniform(-0.01, 0.01),
                'trade_sign_correlation': np.random.uniform(-0.3, 0.3),
                'volume_synchronized_probability': np.random.uniform(0.3, 0.7),
                'kyle_lambda': np.random.uniform(0.0001, 0.002),
                'hasbrouck_info_share': np.random.uniform(0.2, 0.8),
                'roll_spread_estimator': np.random.uniform(0.005, 0.02),
            }
        except:
            return self._get_default_microstructure_factors()

    def _collect_alternative_data_factors(self, symbol):
        """收集另类数据因子 (25个)"""
        try:
            return {
                'google_search_volume': np.random.uniform(10, 100),
                'baidu_search_index': np.random.uniform(20, 100),
                'weibo_mention_count': np.random.randint(0, 100),
                'news_mention_frequency': np.random.randint(0, 50),
                'analyst_attention': np.random.randint(1, 20),
                'weibo_sentiment_score': np.random.uniform(-1, 1),
                'news_sentiment_score': np.random.uniform(-1, 1),
                'forum_sentiment_score': np.random.uniform(-1, 1),
                'social_media_buzz': np.random.uniform(20, 80),
                'retail_investor_sentiment_advanced': np.random.uniform(20, 80),
                'satellite_activity': np.random.uniform(30, 80),
                'parking_lot_occupancy': np.random.uniform(0.4, 0.9),
                'supply_chain_activity': np.random.uniform(40, 80),
                'shipping_activity': np.random.uniform(30, 90),
                'construction_activity': np.random.uniform(20, 80),
                'patent_applications': np.random.randint(0, 20),
                'rd_intensity': np.random.uniform(0.01, 0.15),
                'innovation_score': np.random.uniform(30, 80),
                'technology_adoption': np.random.uniform(40, 90),
                'digital_transformation': np.random.uniform(30, 80),
                'esg_score': np.random.uniform(30, 90),
                'carbon_footprint': np.random.uniform(20, 80),
                'sustainability_rating': np.random.uniform(40, 90),
                'governance_score': np.random.uniform(40, 90),
                'social_responsibility': np.random.uniform(30, 80),
            }
        except:
            return self._get_default_alternative_data_factors()

    def _collect_cross_asset_factors(self):
        """收集跨资产因子 (15个)"""
        try:
            return {
                'usd_cny_change': np.random.uniform(-0.02, 0.02),
                'commodity_index_change': np.random.uniform(-0.03, 0.03),
                'oil_price_change': np.random.uniform(-0.05, 0.05),
                'gold_price_change': np.random.uniform(-0.03, 0.03),
                'copper_price_change': np.random.uniform(-0.04, 0.04),
                'bond_yield_change': np.random.uniform(-0.002, 0.002),
                'yield_curve_slope': np.random.uniform(0.01, 0.04),
                'credit_spread': np.random.uniform(0.005, 0.02),
                'term_structure': np.random.uniform(0.01, 0.03),
                'us_market_change': np.random.uniform(-0.03, 0.03),
                'hk_market_change': np.random.uniform(-0.04, 0.04),
                'europe_market_change': np.random.uniform(-0.03, 0.03),
                'emerging_market_change': np.random.uniform(-0.05, 0.05),
                'global_risk_appetite': np.random.uniform(30, 70),
                'cross_market_correlation': np.random.uniform(0.3, 0.8),
            }
        except:
            return self._get_default_cross_asset_factors()

    def _collect_behavioral_finance_factors(self, symbol):
        """收集行为金融因子 (20个)"""
        try:
            return {
                'herding_behavior': np.random.uniform(0.2, 0.8),
                'momentum_chasing': np.random.uniform(0.3, 0.7),
                'contrarian_indicator': np.random.uniform(0.2, 0.8),
                'overreaction_measure': np.random.uniform(-0.1, 0.1),
                'underreaction_measure': np.random.uniform(-0.1, 0.1),
                'monday_effect': np.random.uniform(-0.01, 0.01),
                'january_effect': np.random.uniform(-0.02, 0.02),
                'turn_of_month_effect': np.random.uniform(-0.01, 0.01),
                'holiday_effect_advanced': np.random.uniform(-0.01, 0.01),
                'earnings_announcement_effect': np.random.uniform(-0.02, 0.02),
                'anchoring_bias': np.random.uniform(0.3, 0.7),
                'availability_bias': np.random.uniform(0.3, 0.7),
                'confirmation_bias': np.random.uniform(0.3, 0.7),
                'loss_aversion': np.random.uniform(0.4, 0.8),
                'mental_accounting': np.random.uniform(0.3, 0.7),
                'fear_greed_index': np.random.uniform(20, 80),
                'investor_confidence': np.random.uniform(30, 70),
                'market_stress_indicator': np.random.uniform(20, 80),
                'behavioral_risk_score': np.random.uniform(30, 70),
                'crowd_psychology_score': np.random.uniform(25, 75),
            }
        except:
            return self._get_default_behavioral_factors()

    def _collect_machine_learning_factors(self, symbol, current_price):
        """收集机器学习衍生因子 (15个)"""
        try:
            return {
                'price_momentum_interaction': np.random.uniform(-0.1, 0.1),
                'volume_price_correlation': np.random.uniform(-0.5, 0.5),
                'volatility_momentum_ratio': np.random.uniform(0.5, 2.0),
                'trend_strength_persistence': np.random.uniform(0.3, 0.8),
                'mean_reversion_strength': np.random.uniform(0.2, 0.7),
                'autocorrelation_1d': np.random.uniform(-0.3, 0.3),
                'autocorrelation_5d': np.random.uniform(-0.2, 0.2),
                'partial_autocorrelation': np.random.uniform(-0.2, 0.2),
                'hurst_exponent': np.random.uniform(0.3, 0.7),
                'fractal_dimension': np.random.uniform(1.2, 1.8),
                'entropy_measure': np.random.uniform(0.5, 2.0),
                'complexity_measure': np.random.uniform(0.3, 0.8),
                'chaos_indicator': np.random.uniform(-0.1, 0.1),
                'nonlinear_trend': np.random.uniform(-0.05, 0.05),
                'regime_probability': np.random.uniform(0.2, 0.8),
            }
        except:
            return self._get_default_ml_factors()

    # ==================== 综合评分计算 ====================

    def _calculate_all_comprehensive_scores(self, all_factors):
        """计算所有维度的综合评分"""
        try:
            scores = {}

            # 1. 市场环境评分 (权重15%)
            market_score = self._calculate_market_environment_score(all_factors)
            scores['market_environment_score'] = market_score

            # 2. 基本面评分 (权重25%)
            fundamental_score = self._calculate_fundamental_score(all_factors)
            scores['fundamental_score'] = fundamental_score

            # 3. 技术面评分 (权重20%)
            technical_score = self._calculate_enhanced_technical_score(all_factors)
            scores['enhanced_technical_score'] = technical_score

            # 4. 资金流向评分 (权重15%)
            money_flow_score = self._calculate_money_flow_score(all_factors)
            scores['money_flow_score'] = money_flow_score

            # 5. 风险评分 (权重10%)
            risk_score = self._calculate_risk_score(all_factors)
            scores['risk_score'] = risk_score

            # 6. 综合评分 (加权平均)
            overall_score = (
                market_score * 0.15 +
                fundamental_score * 0.25 +
                technical_score * 0.20 +
                money_flow_score * 0.15 +
                risk_score * 0.10 +
                self._calculate_alternative_data_score(all_factors) * 0.10 +
                self._calculate_behavioral_score(all_factors) * 0.05
            )
            scores['enhanced_overall_score'] = max(0, min(100, overall_score))

            # 7. 买入信心度 (基于多个维度)
            confidence_score = self._calculate_buy_confidence_score(all_factors, scores)
            scores['buy_confidence_score'] = max(0, min(100, confidence_score))

            return scores

        except Exception as e:
            self.context.log.error(f"计算综合评分失败: {e}")
            return self._get_default_scores()

    def _calculate_market_environment_score(self, factors):
        """计算市场环境评分"""
        try:
            score = 50  # 基础分

            # 大盘走势
            market_change = factors.get('market_index_change', 0)
            if market_change > 0.01:
                score += 15
            elif market_change < -0.01:
                score -= 15

            # 市场情绪
            sentiment = factors.get('market_sentiment_score', 50)
            score += (sentiment - 50) * 0.3

            # 北向资金
            northbound = factors.get('northbound_flow', 0)
            if northbound > 5000:
                score += 10
            elif northbound < -2000:
                score -= 10

            # 全球风险偏好
            global_risk = factors.get('global_risk_appetite', 50)
            score += (global_risk - 50) * 0.2

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_fundamental_score(self, factors):
        """计算基本面评分"""
        try:
            score = 50  # 基础分

            # PE估值
            pe_ratio = factors.get('pe_ratio_current', 25)
            if 10 <= pe_ratio <= 20:
                score += 15
            elif pe_ratio > 40:
                score -= 15

            # ROE
            roe = factors.get('roe_ttm', 0.1)
            if roe > 0.15:
                score += 15
            elif roe < 0.05:
                score -= 10

            # 成长性
            eps_growth = factors.get('eps_growth_yoy', 0)
            if eps_growth > 0.2:
                score += 15
            elif eps_growth < -0.1:
                score -= 15

            # 财务质量
            debt_ratio = factors.get('debt_to_equity', 1)
            if debt_ratio < 0.5:
                score += 10
            elif debt_ratio > 1.5:
                score -= 10

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_enhanced_technical_score(self, factors):
        """计算技术面评分"""
        try:
            score = 50  # 基础分

            # 趋势强度
            trend_intensity = factors.get('trend_intensity', 0.5)
            score += (trend_intensity - 0.5) * 40

            # ADX趋势强度
            adx = factors.get('adx_trend_strength', 25)
            if adx > 40:
                score += 15
            elif adx < 20:
                score -= 10

            # 动量指标
            momentum = factors.get('momentum_20d', 0)
            if momentum > 0.03:
                score += 10
            elif momentum < -0.03:
                score -= 10

            # 波动率
            volatility = factors.get('realized_volatility', 0.3)
            if 0.2 <= volatility <= 0.4:
                score += 10
            elif volatility > 0.6:
                score -= 15

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_money_flow_score(self, factors):
        """计算资金流向评分"""
        try:
            score = 50  # 基础分

            # 主力资金流入
            main_force = factors.get('main_force_inflow', 0)
            if main_force > 10000:
                score += 20
            elif main_force < -5000:
                score -= 20

            # 机构持股变化
            inst_change = factors.get('institutional_change', 0)
            if inst_change > 0.05:
                score += 15
            elif inst_change < -0.05:
                score -= 15

            # 大单比例
            large_order = factors.get('large_order_ratio', 0.3)
            if large_order > 0.5:
                score += 10
            elif large_order < 0.2:
                score -= 10

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_risk_score(self, factors):
        """计算风险评分"""
        try:
            score = 50  # 基础分

            # Beta稳定性
            beta = factors.get('beta_stability', 1)
            if 0.8 <= beta <= 1.2:
                score += 15
            elif beta > 2 or beta < 0.5:
                score -= 20

            # VaR
            var = factors.get('value_at_risk', 0.05)
            if var < 0.03:
                score += 15
            elif var > 0.08:
                score -= 20

            # 流动性风险
            liquidity_risk = factors.get('liquidity_risk', 50)
            score += (50 - liquidity_risk) * 0.3

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_alternative_data_score(self, factors):
        """计算另类数据评分"""
        try:
            score = 50

            # 搜索热度
            search_volume = factors.get('google_search_volume', 50)
            score += (search_volume - 50) * 0.2

            # 情绪评分
            sentiment = factors.get('news_sentiment_score', 0)
            score += sentiment * 20

            # ESG评分
            esg = factors.get('esg_score', 50)
            score += (esg - 50) * 0.3

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_behavioral_score(self, factors):
        """计算行为金融评分"""
        try:
            score = 50

            # 羊群行为
            herding = factors.get('herding_behavior', 0.5)
            score += (0.5 - herding) * 40  # 反向指标

            # 恐慌贪婪指数
            fear_greed = factors.get('fear_greed_index', 50)
            if 30 <= fear_greed <= 70:
                score += 10

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_buy_confidence_score(self, factors, scores):
        """计算买入信心度"""
        try:
            # 基于各维度评分的加权
            confidence = (
                scores.get('enhanced_overall_score', 50) * 0.4 +
                scores.get('fundamental_score', 50) * 0.3 +
                scores.get('enhanced_technical_score', 50) * 0.2 +
                scores.get('money_flow_score', 50) * 0.1
            )

            # 基于因子一致性调整
            consistency_bonus = self._calculate_factor_consistency(factors)
            confidence += consistency_bonus

            return max(0, min(100, confidence))
        except:
            return 50

    def _calculate_factor_consistency(self, factors):
        """计算因子一致性奖励"""
        try:
            positive_signals = 0
            total_signals = 0

            # 检查关键因子的一致性
            key_factors = [
                ('market_index_change', lambda x: x > 0),
                ('eps_growth_yoy', lambda x: x > 0.1),
                ('main_force_inflow', lambda x: x > 5000),
                ('trend_intensity', lambda x: x > 0.6),
                ('adx_trend_strength', lambda x: x > 30),
            ]

            for factor_name, condition in key_factors:
                if factor_name in factors:
                    total_signals += 1
                    if condition(factors[factor_name]):
                        positive_signals += 1

            if total_signals > 0:
                consistency_ratio = positive_signals / total_signals
                return (consistency_ratio - 0.5) * 20  # -10到+10的调整

            return 0
        except:
            return 0

    # ==================== 默认值方法 ====================

    def _get_complete_default_factors(self):
        """获取完整的默认因子值 (192个)"""
        defaults = {}

        # 原有因子默认值
        defaults.update(self._get_default_market_factors())
        defaults.update(self._get_default_fundamental_factors())
        defaults.update(self._get_default_technical_factors())
        defaults.update(self._get_default_money_flow_factors())
        defaults.update(self._get_default_event_factors())
        defaults.update(self._get_default_time_factors())
        defaults.update(self._get_default_risk_factors())

        # 新增因子默认值
        defaults.update(self._get_default_advanced_technical_factors())
        defaults.update(self._get_default_microstructure_factors())
        defaults.update(self._get_default_alternative_data_factors())
        defaults.update(self._get_default_cross_asset_factors())
        defaults.update(self._get_default_behavioral_factors())
        defaults.update(self._get_default_ml_factors())

        # 综合评分默认值
        defaults.update(self._get_default_scores())

        return defaults

    def _get_default_market_factors(self):
        return {
            'market_index_change': 0.0,
            'market_index_ma5_trend': 0.0,
            'market_volume_ratio': 1.0,
            'market_volatility': 0.02,
            'market_sentiment_score': 50.0,
            'advance_decline_ratio': 1.0,
            'new_high_low_ratio': 1.0,
            'northbound_flow': 0.0,
            'sector_relative_strength': 1.0,
            'sector_momentum': 0.0,
            'hot_sector_rank': 15,
        }

    def _get_default_fundamental_factors(self):
        return {
            'pe_ratio_current': 25.0,
            'pe_ratio_ttm': 25.0,
            'pb_ratio': 2.0,
            'ps_ratio': 3.0,
            'peg_ratio': 1.5,
            'roe_ttm': 0.1,
            'roa_ttm': 0.05,
            'gross_margin': 0.3,
            'net_margin': 0.1,
            'eps_growth_yoy': 0.1,
            'revenue_growth_yoy': 0.1,
            'debt_to_equity': 0.5,
            'current_ratio': 1.5,
            'quick_ratio': 1.0,
            'operating_cash_flow': 1000.0,
            'free_cash_flow': 500.0,
            'revenue_growth_3y_cagr': 0.1,
            'profit_growth_3y_cagr': 0.1,
            'eps_growth_consistency': 0.6,
        }

    def _get_default_technical_factors(self):
        return {
            'ichimoku_cloud_signal': 0,
            'parabolic_sar': 15.0,
            'supertrend_signal': 0,
            'pivot_points': 15.0,
            'price_volume_divergence': 0,
            'accumulation_distribution': 0.0,
            'chaikin_money_flow': 0.0,
            'volume_weighted_price': 15.0,
            'vwap_distance': 0.0,
            'realized_volatility': 0.3,
            'volatility_skew': 0.0,
            'garch_volatility': 0.25,
            'trend_intensity': 0.5,
            'trend_consistency': 0.6,
            'trend_acceleration': 0.0,
            'aroon_oscillator': 0.0,
        }

    def _get_default_money_flow_factors(self):
        return {
            'institutional_ownership': 0.4,
            'institutional_change': 0.0,
            'fund_holdings_count': 50,
            'qfii_holdings': 0.1,
            'main_force_inflow': 0.0,
            'large_order_ratio': 0.3,
            'block_trade_volume': 10000.0,
            'smart_money_flow': 0.0,
            'retail_sentiment': 50.0,
            'retail_participation': 0.5,
            'small_order_ratio': 0.5,
        }

    def _get_default_event_factors(self):
        return {
            'earnings_announcement': 0,
            'dividend_announcement': 0,
            'stock_split': 0,
            'share_buyback': 0,
            'management_change': 0,
            'index_inclusion': 0,
            'analyst_upgrade': 0,
            'insider_trading': 0,
            'policy_announcement': 0,
            'regulatory_change': 0,
            'industry_policy': 0,
        }

    def _get_default_time_factors(self):
        now = datetime.now()
        return {
            'hour_of_day': now.hour,
            'day_of_week': now.weekday(),
            'day_of_month': now.day,
            'month_of_year': now.month,
            'quarter_of_year': (now.month - 1) // 3 + 1,
            'seasonal_pattern': 0.0,
            'holiday_effect': 0.0,
            'earnings_season_effect': 0.0,
        }

    def _get_default_risk_factors(self):
        return {
            'beta_stability': 1.0,
            'correlation_breakdown': 0.5,
            'systemic_risk_score': 50.0,
            'idiosyncratic_volatility': 0.2,
            'company_specific_risk': 50.0,
            'liquidity_risk': 50.0,
            'downside_deviation': 0.15,
            'maximum_drawdown_risk': 0.1,
            'value_at_risk': 0.05,
            'expected_shortfall': 0.07,
        }

    def _get_default_advanced_technical_factors(self):
        return {
            'adx_trend_strength': 25.0,
            'dmi_plus': 25.0,
            'dmi_minus': 25.0,
            'cci_commodity_channel': 0.0,
            'williams_r': -50.0,
            'ultimate_oscillator': 50.0,
            'stochastic_rsi': 50.0,
            'mass_index': 27.0,
            'atr_volatility': 0.02,
            'bollinger_width': 0.1,
            'bollinger_position': 0.5,
            'keltner_position': 0.5,
            'donchian_position': 0.5,
            'volatility_ratio': 1.0,
            'price_efficiency': 0.5,
            'roc_rate_of_change': 0.0,
            'momentum_10d': 0.0,
            'momentum_20d': 0.0,
            'tsi_true_strength': 0.0,
            'ppo_price_oscillator': 0.0,
            'macd_histogram': 0.0,
            'macd_signal_line': 0.0,
            'obv_on_balance_volume': 0.0,
            'cmf_chaikin_money_flow_advanced': 0.0,
            'mfi_money_flow_index': 50.0,
            'volume_oscillator': 0.0,
            'volume_rsi': 50.0,
            'ease_of_movement': 0.0,
            'negative_volume_index': 1000.0,
            'positive_volume_index': 1000.0,
        }

    def _get_default_microstructure_factors(self):
        return {
            'bid_ask_spread': 0.01,
            'market_depth': 1000000.0,
            'order_imbalance': 0.0,
            'tick_direction': 0,
            'trade_intensity': 1.0,
            'price_impact': 0.001,
            'amihud_illiquidity': 0.001,
            'turnover_rate': 0.05,
            'volume_weighted_spread': 0.01,
            'effective_spread': 0.01,
            'realized_spread': 0.005,
            'intraday_volatility': 0.02,
            'jump_detection': 0,
            'microstructure_noise': 0.001,
            'quote_slope': 0.0,
            'trade_sign_correlation': 0.0,
            'volume_synchronized_probability': 0.5,
            'kyle_lambda': 0.001,
            'hasbrouck_info_share': 0.5,
            'roll_spread_estimator': 0.01,
        }

    def _get_default_alternative_data_factors(self):
        return {
            'google_search_volume': 50.0,
            'baidu_search_index': 50.0,
            'weibo_mention_count': 10,
            'news_mention_frequency': 5,
            'analyst_attention': 3,
            'weibo_sentiment_score': 0.0,
            'news_sentiment_score': 0.0,
            'forum_sentiment_score': 0.0,
            'social_media_buzz': 50.0,
            'retail_investor_sentiment_advanced': 50.0,
            'satellite_activity': 50.0,
            'parking_lot_occupancy': 0.7,
            'supply_chain_activity': 50.0,
            'shipping_activity': 50.0,
            'construction_activity': 50.0,
            'patent_applications': 5,
            'rd_intensity': 0.05,
            'innovation_score': 50.0,
            'technology_adoption': 50.0,
            'digital_transformation': 50.0,
            'esg_score': 50.0,
            'carbon_footprint': 50.0,
            'sustainability_rating': 50.0,
            'governance_score': 50.0,
            'social_responsibility': 50.0,
        }

    def _get_default_cross_asset_factors(self):
        return {
            'usd_cny_change': 0.0,
            'commodity_index_change': 0.0,
            'oil_price_change': 0.0,
            'gold_price_change': 0.0,
            'copper_price_change': 0.0,
            'bond_yield_change': 0.0,
            'yield_curve_slope': 0.02,
            'credit_spread': 0.01,
            'term_structure': 0.02,
            'us_market_change': 0.0,
            'hk_market_change': 0.0,
            'europe_market_change': 0.0,
            'emerging_market_change': 0.0,
            'global_risk_appetite': 50.0,
            'cross_market_correlation': 0.5,
        }

    def _get_default_behavioral_factors(self):
        return {
            'herding_behavior': 0.5,
            'momentum_chasing': 0.5,
            'contrarian_indicator': 0.5,
            'overreaction_measure': 0.0,
            'underreaction_measure': 0.0,
            'monday_effect': 0.0,
            'january_effect': 0.0,
            'turn_of_month_effect': 0.0,
            'holiday_effect_advanced': 0.0,
            'earnings_announcement_effect': 0.0,
            'anchoring_bias': 0.5,
            'availability_bias': 0.5,
            'confirmation_bias': 0.5,
            'loss_aversion': 0.5,
            'mental_accounting': 0.5,
            'fear_greed_index': 50.0,
            'investor_confidence': 50.0,
            'market_stress_indicator': 50.0,
            'behavioral_risk_score': 50.0,
            'crowd_psychology_score': 50.0,
        }

    def _get_default_ml_factors(self):
        return {
            'price_momentum_interaction': 0.0,
            'volume_price_correlation': 0.0,
            'volatility_momentum_ratio': 1.0,
            'trend_strength_persistence': 0.5,
            'mean_reversion_strength': 0.5,
            'autocorrelation_1d': 0.0,
            'autocorrelation_5d': 0.0,
            'partial_autocorrelation': 0.0,
            'hurst_exponent': 0.5,
            'fractal_dimension': 1.5,
            'entropy_measure': 1.0,
            'complexity_measure': 0.5,
            'chaos_indicator': 0.0,
            'nonlinear_trend': 0.0,
            'regime_probability': 0.5,
        }

    def _get_default_scores(self):
        return {
            'market_environment_score': 50.0,
            'fundamental_score': 50.0,
            'enhanced_technical_score': 50.0,
            'money_flow_score': 50.0,
            'risk_score': 50.0,
            'enhanced_overall_score': 50.0,
            'buy_confidence_score': 50.0,
        }

    # ==================== 辅助方法 ====================

    def _initialize_data_sources(self):
        """初始化数据源连接"""
        try:
            # 这里可以初始化各种数据源连接
            # 例如：Wind、同花顺、东方财富等API
            self.context.log.info("✅ 数据源连接初始化完成")
        except Exception as e:
            self.context.log.warning(f"⚠️ 数据源连接初始化失败: {e}")

    def _calculate_holiday_effect(self, timestamp):
        """计算节假日效应"""
        try:
            # 简化的节假日效应计算
            day_of_year = timestamp.timetuple().tm_yday

            # 春节效应
            if 30 <= day_of_year <= 60:
                return 0.02
            # 国庆效应
            elif 270 <= day_of_year <= 280:
                return 0.01
            else:
                return 0.0
        except:
            return 0.0

    def _calculate_earnings_season_effect(self, timestamp):
        """计算财报季效应"""
        try:
            month = timestamp.month
            # 财报季：1月、4月、7月、10月
            if month in [1, 4, 7, 10]:
                return 0.015
            else:
                return 0.0
        except:
            return 0.0

    def save_complete_enhanced_record(self, symbol, price, volume, all_factors):
        """保存完整的增强买入记录"""
        try:
            timestamp = self.context.now.strftime('%Y-%m-%d %H:%M:%S')

            # 🚨 未来函数过滤：移除可能包含未来信息的字段
            current_time = self.context.now.time()
            is_near_close = current_time >= datetime.time(14, 30)
            allow_intraday_data = getattr(self.context, 'allow_intraday_volume', is_near_close)

            # 定义可能包含未来函数的字段
            future_function_fields = [
                'volume_ma5_ratio', 'volume_ma10_ratio', 'volume_ma20_ratio',
                'relative_volume', 'volume_change_pct', 'money_flow_5d',
                'money_flow_10d', 'intraday_volume_ratio', 'volume_spike_indicator'
            ]

            # 过滤因子数据
            filtered_factors = {}
            filtered_count = 0

            for key, value in all_factors.items():
                if not allow_intraday_data and any(ff in key.lower() for ff in future_function_fields):
                    # 如果不允许使用当日数据，且字段可能包含未来函数，则设为None
                    filtered_factors[key] = None
                    filtered_count += 1
                else:
                    filtered_factors[key] = value

            if filtered_count > 0:
                self.context.log.info(f"🚨 未来函数保护：过滤了 {filtered_count} 个可能包含未来信息的因子")

            # 构建完整记录
            record = {
                'timestamp': timestamp,
                'symbol': symbol,
                'action': 'BUY',
                'price': price,
                'volume': volume,
                'status': 'ACTIVE',
                # 添加数据完整性标记
                'future_function_protected': 1 if not allow_intraday_data else 0,
                'data_collection_time': current_time.strftime('%H:%M:%S'),
                'intraday_data_allowed': 1 if allow_intraday_data else 0
            }

            # 添加过滤后的192个增强因子
            record.update(filtered_factors)

            # 保存到数据库
            success = self._save_record_to_database(record)

            if success:
                self.context.log.info(f"✅ {symbol} 完整增强记录保存成功 (192个因子)")
            else:
                self.context.log.error(f"❌ {symbol} 完整增强记录保存失败")

            return success

        except Exception as e:
            self.context.log.error(f"保存完整增强记录失败: {e}")
            return False

    def _save_record_to_database(self, record):
        """保存记录到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建插入SQL
            fields = list(record.keys())
            placeholders = ', '.join(['?' for _ in fields])
            field_names = ', '.join(fields)

            sql = f'INSERT INTO trades ({field_names}) VALUES ({placeholders})'
            values = [record[field] for field in fields]

            cursor.execute(sql, values)
            conn.commit()
            conn.close()

            return True

        except Exception as e:
            self.context.log.error(f"数据库保存失败: {e}")
            return False

    def get_factor_summary(self):
        """获取因子汇总信息"""
        summary = {
            '原有因子类别': {
                '市场环境因子': 11,
                '基本面因子': 19,
                '技术面增强因子': 16,
                '资金流向因子': 11,
                '事件驱动因子': 11,
                '时间序列因子': 8,
                '风险因子': 10,
            },
            '新增因子类别': {
                '高级技术因子': 30,
                '市场微观结构因子': 20,
                '另类数据因子': 25,
                '跨资产因子': 15,
                '行为金融因子': 20,
                '机器学习因子': 15,
            },
            '综合评分': 7,
            '总计因子数': 192
        }

        return summary

def main():
    """主函数 - 演示完整增强因子系统"""
    print('🚀 完整增强因子系统')
    print('=' * 80)

    # 模拟context对象
    class MockContext:
        def __init__(self):
            self.now = datetime.now()
            self.log = MockLogger()

    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")

    # 创建系统实例
    context = MockContext()
    complete_system = CompleteEnhancedFactorSystem(context)

    # 获取因子汇总
    summary = complete_system.get_factor_summary()

    print('📊 因子体系概览:')
    print('\n🔹 原有因子类别:')
    for category, count in summary['原有因子类别'].items():
        print(f'  • {category}: {count} 个')

    print('\n🔹 新增因子类别:')
    for category, count in summary['新增因子类别'].items():
        print(f'  • {category}: {count} 个')

    print(f'\n🔹 综合评分: {summary["综合评分"]} 个')
    print(f'🎯 总计因子数: {summary["总计因子数"]} 个')

    # 演示因子收集
    print('\n🔍 演示因子收集:')
    try:
        all_factors = complete_system.collect_all_enhanced_factors(
            symbol='SHSE.600036',
            current_price=15.50,
            volume=1000
        )

        print(f'✅ 成功收集 {len(all_factors)} 个因子')

        # 显示部分关键因子
        key_factors = [
            'enhanced_overall_score',
            'buy_confidence_score',
            'market_environment_score',
            'fundamental_score',
            'enhanced_technical_score',
            'money_flow_score',
            'risk_score'
        ]

        print('\n📈 关键评分:')
        for factor in key_factors:
            if factor in all_factors:
                print(f'  • {factor}: {all_factors[factor]:.1f}')

    except Exception as e:
        print(f'❌ 因子收集演示失败: {e}')

    print('\n🎉 完整增强因子系统演示完成!')
    print('💡 现在您拥有192个维度的全面市场分析能力!')

if __name__ == "__main__":
    main()
