#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略文件清理工具
自动识别和清理无用的脚本文件，优化项目结构
"""

import os
import shutil
import glob
import datetime
from pathlib import Path

class StrategyFileCleaner:
    def __init__(self, base_path="."):
        self.base_path = Path(base_path)
        self.backup_path = self.base_path / "cleanup_backup"
        self.archive_path = self.base_path / "archive"
        
        # 核心文件列表（不能删除）
        self.core_files = {
            'main.py', 'config.py', 'signal_generator.py', 'risk_manager.py',
            'trade_executor.py', 'trix_prefilter.py', 'enhanced_factor_engine.py',
            'intelligent_strategy_executor.py', 'intelligent_strategy_selector.py',
            'README.md', 'QUICKSTART.md', 'MAINTENANCE.md', 'CHANGELOG.md'
        }
        
        # 需要删除的文件模式
        self.delete_patterns = [
            'analyze_*.py', 'verify_*.py', 'debug_*.py', 'diagnose_*.py',
            'test_*.py', 'emergency_*.py', 'day*_*.py', 'stage*_*.py',
            'post_*.py', 'fix_*.py', 'check_*.py', 'final_*.py',
            'comprehensive_*.py', 'complete_*.py', 'detailed_*.py',
            'deep_*.py', 'enhanced_*.py', 'improved_*.py', 'optimized_*.py',
            'TRIX*.py', '*_analysis.py', '*_analyzer.py', '*_report.py',
            '*_summary.py', '*_verification.py', '*_diagnosis.py'
        ]
        
        # 需要归档的文件模式
        self.archive_patterns = [
            '*.md', '*_config.py', '*_strategy.py', '*_plan.py',
            '*_guide.py', '*_implementation.py', '*_system.py'
        ]
        
        # 配置备份文件模式
        self.config_backup_pattern = 'config_backup_*.py'
        
    def create_backup(self):
        """创建备份目录"""
        if not self.backup_path.exists():
            self.backup_path.mkdir()
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.backup_path / f"cleanup_{timestamp}"
        backup_dir.mkdir()
        
        return backup_dir
    
    def create_archive(self):
        """创建归档目录"""
        if not self.archive_path.exists():
            self.archive_path.mkdir()
        return self.archive_path
    
    def scan_files(self):
        """扫描文件并分类"""
        all_files = list(self.base_path.glob("*.py")) + list(self.base_path.glob("*.md"))
        
        files_to_delete = []
        files_to_archive = []
        config_backups = []
        core_files = []
        
        for file_path in all_files:
            file_name = file_path.name
            
            # 检查是否为核心文件
            if file_name in self.core_files:
                core_files.append(file_path)
                continue
            
            # 检查是否为配置备份
            if file_path.match(self.config_backup_pattern):
                config_backups.append(file_path)
                continue
            
            # 检查是否需要删除
            should_delete = any(file_path.match(pattern) for pattern in self.delete_patterns)
            if should_delete:
                files_to_delete.append(file_path)
                continue
            
            # 检查是否需要归档
            should_archive = any(file_path.match(pattern) for pattern in self.archive_patterns)
            if should_archive and file_name not in self.core_files:
                files_to_archive.append(file_path)
        
        return {
            'delete': files_to_delete,
            'archive': files_to_archive,
            'config_backups': config_backups,
            'core': core_files
        }
    
    def clean_config_backups(self, config_backups, keep_count=5):
        """清理配置备份，只保留最新的几个"""
        if len(config_backups) <= keep_count:
            return []
        
        # 按修改时间排序
        config_backups.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 保留最新的几个，其余删除
        to_keep = config_backups[:keep_count]
        to_delete = config_backups[keep_count:]
        
        return to_delete
    
    def preview_cleanup(self):
        """预览清理操作"""
        print("🔍 策略文件清理预览")
        print("=" * 60)
        
        file_categories = self.scan_files()
        
        print(f"\n📊 文件统计:")
        print(f"  核心文件: {len(file_categories['core'])}个")
        print(f"  待删除文件: {len(file_categories['delete'])}个")
        print(f"  待归档文件: {len(file_categories['archive'])}个")
        print(f"  配置备份: {len(file_categories['config_backups'])}个")
        
        # 配置备份清理预览
        config_to_delete = self.clean_config_backups(file_categories['config_backups'], keep_count=5)
        print(f"  配置备份待删除: {len(config_to_delete)}个")
        
        print(f"\n🗑️ 待删除文件预览（前10个）:")
        for i, file_path in enumerate(file_categories['delete'][:10]):
            print(f"  {i+1}. {file_path.name}")
        if len(file_categories['delete']) > 10:
            print(f"  ... 还有{len(file_categories['delete'])-10}个文件")
        
        print(f"\n📁 待归档文件预览（前10个）:")
        for i, file_path in enumerate(file_categories['archive'][:10]):
            print(f"  {i+1}. {file_path.name}")
        if len(file_categories['archive']) > 10:
            print(f"  ... 还有{len(file_categories['archive'])-10}个文件")
        
        print(f"\n🔧 配置备份清理预览:")
        print(f"  保留最新5个配置备份")
        print(f"  删除{len(config_to_delete)}个旧配置备份")
        
        total_delete = len(file_categories['delete']) + len(config_to_delete)
        total_archive = len(file_categories['archive'])
        
        print(f"\n📋 清理总结:")
        print(f"  总删除文件: {total_delete}个")
        print(f"  总归档文件: {total_archive}个")
        print(f"  预计减少文件: {total_delete}个")
        
        return file_categories, config_to_delete
    
    def execute_cleanup(self, dry_run=True):
        """执行清理操作"""
        print(f"\n{'🔍 模拟' if dry_run else '🚀 执行'}清理操作")
        print("=" * 60)
        
        file_categories, config_to_delete = self.preview_cleanup()
        
        if not dry_run:
            # 创建备份
            backup_dir = self.create_backup()
            archive_dir = self.create_archive()
            
            print(f"\n💾 创建备份目录: {backup_dir}")
            print(f"📁 创建归档目录: {archive_dir}")
        
        # 删除文件
        deleted_count = 0
        for file_path in file_categories['delete']:
            if not dry_run:
                # 备份后删除
                shutil.copy2(file_path, backup_dir / file_path.name)
                file_path.unlink()
            deleted_count += 1
        
        # 删除旧配置备份
        for file_path in config_to_delete:
            if not dry_run:
                shutil.copy2(file_path, backup_dir / file_path.name)
                file_path.unlink()
            deleted_count += 1
        
        # 归档文件
        archived_count = 0
        for file_path in file_categories['archive']:
            if not dry_run:
                shutil.move(str(file_path), str(archive_dir / file_path.name))
            archived_count += 1
        
        print(f"\n✅ 清理完成:")
        print(f"  删除文件: {deleted_count}个")
        print(f"  归档文件: {archived_count}个")
        
        if not dry_run:
            print(f"  备份位置: {backup_dir}")
            print(f"  归档位置: {archive_dir}")
        
        return deleted_count, archived_count
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_content = f"""# 策略文件清理报告

## 清理时间
{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 清理统计
"""
        
        file_categories, config_to_delete = self.preview_cleanup()
        
        report_content += f"""
- 核心文件保留: {len(file_categories['core'])}个
- 删除无用脚本: {len(file_categories['delete'])}个
- 归档文档文件: {len(file_categories['archive'])}个
- 清理配置备份: {len(config_to_delete)}个

## 核心文件列表
"""
        for file_path in file_categories['core']:
            report_content += f"- {file_path.name}\n"
        
        report_content += f"""
## 建议下一步操作

1. **验证核心功能** - 确保删除文件后策略正常运行
2. **整理目录结构** - 创建core/, utils/, docs/等目录
3. **更新文档** - 更新README和使用说明
4. **建立规范** - 制定文件命名和组织规范

## 清理效果

- 文件数量减少: {len(file_categories['delete']) + len(config_to_delete)}个
- 项目结构更清晰
- 维护成本降低
- 部署更简单
"""
        
        report_path = self.base_path / "文件清理完成报告.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📋 清理报告已生成: {report_path}")
        return report_path

def main():
    """主函数"""
    print("🧹 万和策略文件清理工具")
    print("=" * 60)
    
    cleaner = StrategyFileCleaner()
    
    # 预览清理
    print("\n第一步：预览清理操作")
    file_categories, config_to_delete = cleaner.preview_cleanup()
    
    # 询问用户是否继续
    print(f"\n⚠️ 警告：此操作将删除{len(file_categories['delete']) + len(config_to_delete)}个文件")
    print("所有删除的文件都会先备份到cleanup_backup目录")
    
    choice = input("\n是否继续执行清理？(y/N): ").strip().lower()
    
    if choice == 'y':
        # 执行清理
        print("\n第二步：执行清理操作")
        deleted_count, archived_count = cleaner.execute_cleanup(dry_run=False)
        
        # 生成报告
        print("\n第三步：生成清理报告")
        report_path = cleaner.generate_cleanup_report()
        
        print(f"\n🎉 清理完成！")
        print(f"删除了{deleted_count}个文件，归档了{archived_count}个文件")
        print(f"备份文件位于cleanup_backup目录")
        print(f"详细报告：{report_path}")
    else:
        print("\n❌ 清理操作已取消")
        print("如需查看详细信息，请查看生成的分析报告")

if __name__ == "__main__":
    main()
