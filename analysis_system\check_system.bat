@echo off
chcp 65001 > nul
cls
echo 万和策略分析系统 - 系统检查
echo.

rem 检查Python是否存在
echo 正在检查Python...
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未找到。请安装Python并确保它已添加到系统PATH中。
    echo 您可以从 https://www.python.org/downloads/ 下载Python。
    echo.
    echo 安装后，请确保在安装过程中勾选"Add Python to PATH"选项。
    pause
    exit /b 1
) else (
    echo [成功] Python已找到
)

echo.
echo 正在检查Python版本...
python --version
echo.

echo 系统检查完成。
pause 