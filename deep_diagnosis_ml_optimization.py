# coding=utf-8
"""
深度诊断机器学习和优化问题
检查胜率42%未改善的根本原因
"""

import pandas as pd
import numpy as np
import sqlite3
import os
import json
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_ml_model_training():
    """检查机器学习模型训练情况"""
    print("🤖 检查机器学习模型训练情况")
    print("=" * 60)
    
    try:
        # 1. 检查ML优化配置文件是否存在
        ml_config_files = [f for f in os.listdir('.') if f.startswith('ml_optimized_config_')]
        
        if ml_config_files:
            print(f"✅ 找到ML优化配置文件: {len(ml_config_files)}个")
            for file in ml_config_files:
                print(f"   - {file}")
                
            # 读取最新的ML配置
            latest_ml_config = max(ml_config_files)
            with open(latest_ml_config, 'r', encoding='utf-8') as f:
                ml_content = f.read()
            
            print(f"\n📊 ML优化配置内容检查:")
            if 'ml_importance' in ml_content:
                print("   ✅ 包含ML重要性分析结果")
            else:
                print("   ❌ 缺少ML重要性分析结果")
                
            if 'optimization_note' in ml_content:
                print("   ✅ 包含优化说明")
            else:
                print("   ❌ 缺少优化说明")
        else:
            print("❌ 未找到ML优化配置文件")
        
        # 2. 测试ML因子评估系统
        print(f"\n🧪 测试ML因子评估系统:")
        try:
            from ml_factor_evaluation_system import MLFactorEvaluationSystem
            
            ml_system = MLFactorEvaluationSystem()
            
            # 生成小规模测试数据
            training_data, factor_names = ml_system.generate_synthetic_training_data(100)
            print(f"   ✅ 生成训练数据: {len(training_data)}样本, {len(factor_names)}因子")
            
            # 评估因子重要性
            model_results, feature_importance = ml_system.evaluate_factor_importance(training_data, factor_names)
            
            if feature_importance:
                print(f"   ✅ ML模型训练成功: {len(model_results)}个模型")
                print(f"   ✅ 因子重要性计算完成: {len(feature_importance)}个因子")
                
                # 显示前5个最重要因子
                sorted_importance = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
                print(f"   📊 前5个最重要因子:")
                for i, (factor, importance) in enumerate(sorted_importance[:5], 1):
                    print(f"      {i}. {factor}: {importance:.4f}")
            else:
                print("   ❌ ML模型训练失败")
                
        except Exception as e:
            print(f"   ❌ ML系统测试失败: {e}")
        
        # 3. 检查预测模型
        print(f"\n🎯 测试预测模型集成:")
        try:
            from prediction_model_integration import PredictionModelIntegration
            
            prediction_system = PredictionModelIntegration()
            prediction_system.initialize_models()
            
            # 生成测试数据
            training_data = prediction_system.generate_prediction_training_data(100)
            print(f"   ✅ 生成预测训练数据: {len(training_data)}样本")
            
            # 训练模型
            success = prediction_system.train_individual_models(training_data, 'return_3d')
            if success:
                print(f"   ✅ 预测模型训练成功")
                
                # 检查模型性能
                if prediction_system.model_performance:
                    best_model = max(prediction_system.model_performance.items(), 
                                   key=lambda x: x[1]['r2'])
                    print(f"   📊 最佳模型: {best_model[0]}, R²={best_model[1]['r2']:.4f}")
                else:
                    print(f"   ❌ 模型性能数据缺失")
            else:
                print(f"   ❌ 预测模型训练失败")
                
        except Exception as e:
            print(f"   ❌ 预测模型测试失败: {e}")
            
    except Exception as e:
        print(f"❌ ML模型检查失败: {e}")

def check_optimization_modules_usage():
    """检查优化模块是否被实际使用"""
    print("\n🔧 检查优化模块实际使用情况")
    print("=" * 60)
    
    try:
        # 1. 检查main.py中的实际调用
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        print("📋 main.py中的优化模块使用检查:")
        
        # 检查关键函数调用
        optimization_checks = {
            'EnhancedMultiFactorEngine': 'enhanced_multi_factor_engine',
            'IntelligentStrategyExecutor': 'intelligent_strategy_executor', 
            'calculate_all_enhanced_factors': '68个因子计算',
            'apply_multi_dimensional_filters': '多维度筛选',
            'EFFECTIVE_FACTORS_CONFIG': '新配置使用'
        }
        
        for check, description in optimization_checks.items():
            if check in main_content:
                print(f"   ✅ {description}: 已集成")
            else:
                print(f"   ❌ {description}: 未集成")
        
        # 2. 检查实际执行路径
        print(f"\n🔍 分析实际执行路径:")
        
        # 查找analyze_single_symbol函数
        if 'def analyze_single_symbol' in main_content:
            print("   ✅ 找到analyze_single_symbol函数")
            
            # 检查函数内是否使用新的因子引擎
            analyze_start = main_content.find('def analyze_single_symbol')
            analyze_end = main_content.find('\ndef ', analyze_start + 1)
            if analyze_end == -1:
                analyze_end = len(main_content)
            
            analyze_function = main_content[analyze_start:analyze_end]
            
            if 'EnhancedMultiFactorEngine' in analyze_function:
                print("   ✅ analyze_single_symbol使用智能化因子引擎")
            else:
                print("   ❌ analyze_single_symbol未使用智能化因子引擎")
                
            if 'calculate_all_enhanced_factors' in analyze_function:
                print("   ✅ 调用68个因子计算")
            else:
                print("   ❌ 未调用68个因子计算")
        else:
            print("   ❌ 未找到analyze_single_symbol函数")
        
        # 3. 检查配置加载
        print(f"\n⚙️ 检查配置加载:")
        
        if 'from config import EFFECTIVE_FACTORS_CONFIG' in main_content:
            print("   ✅ 导入新配置")
        else:
            print("   ❌ 未导入新配置")
            
        # 检查是否还在使用旧配置
        old_config_patterns = [
            'enhanced_factors_config',
            'get_buy_threshold',
            'get_position_config'
        ]
        
        for pattern in old_config_patterns:
            if pattern in main_content:
                print(f"   ⚠️ 仍在使用旧配置: {pattern}")
                
    except Exception as e:
        print(f"❌ 优化模块使用检查失败: {e}")

def analyze_database_records():
    """分析数据库记录"""
    print("\n📊 分析数据库记录")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 1. 检查最近的交易记录
        print("📈 最近交易记录分析:")
        
        recent_trades_query = '''
            SELECT symbol, buy_date, sell_date, return_pct, 
                   buy_price, sell_price
            FROM trades 
            WHERE sell_date IS NOT NULL 
            ORDER BY sell_date DESC 
            LIMIT 20
        '''
        
        recent_trades = pd.read_sql_query(recent_trades_query, conn)
        
        if not recent_trades.empty:
            win_rate = (recent_trades['return_pct'] > 0).mean()
            avg_return = recent_trades['return_pct'].mean()
            
            print(f"   最近20笔交易胜率: {win_rate:.2%}")
            print(f"   平均收益率: {avg_return:.2%}")
            
            # 分析胜负分布
            wins = recent_trades[recent_trades['return_pct'] > 0]
            losses = recent_trades[recent_trades['return_pct'] <= 0]
            
            print(f"   盈利交易: {len(wins)}笔, 平均: {wins['return_pct'].mean():.2%}")
            print(f"   亏损交易: {len(losses)}笔, 平均: {losses['return_pct'].mean():.2%}")
            
            # 检查是否有异常交易
            extreme_losses = recent_trades[recent_trades['return_pct'] < -0.1]
            if len(extreme_losses) > 0:
                print(f"   ⚠️ 发现{len(extreme_losses)}笔极端亏损交易 (<-10%)")
        else:
            print("   ❌ 没有找到最近的交易记录")
        
        # 2. 检查买入记录的因子使用情况
        print(f"\n🔍 买入记录因子分析:")
        
        # 检查表结构
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(buy_records)")
        columns = [col[1] for col in cursor.fetchall()]
        
        print(f"   buy_records表字段: {len(columns)}个")
        
        # 检查是否有新因子字段
        new_factor_fields = [
            'overall_score', 'technical_score', 'fundamental_score', 
            'sentiment_score', 'cross_market_score'
        ]
        
        for field in new_factor_fields:
            if field in columns:
                print(f"   ✅ 包含新因子字段: {field}")
            else:
                print(f"   ❌ 缺少新因子字段: {field}")
        
        # 3. 分析最近买入记录的质量
        if 'combined_score' in columns:
            recent_buys_query = '''
                SELECT symbol, date, combined_score, factors_count,
                       cci_14, atr_pct, adx_14, rsi_14
                FROM buy_records 
                WHERE date >= date('now', '-7 days')
                ORDER BY date DESC
            '''
            
            recent_buys = pd.read_sql_query(recent_buys_query, conn)
            
            if not recent_buys.empty:
                print(f"\n📊 最近7天买入记录: {len(recent_buys)}条")
                print(f"   平均综合评分: {recent_buys['combined_score'].mean():.4f}")
                print(f"   平均因子数量: {recent_buys['factors_count'].mean():.1f}")
                
                # 检查是否符合新的筛选标准
                high_quality = recent_buys[
                    (recent_buys['combined_score'] >= 0.55) & 
                    (recent_buys['factors_count'] >= 6)
                ]
                
                print(f"   符合新标准的信号: {len(high_quality)}/{len(recent_buys)} ({len(high_quality)/len(recent_buys)*100:.1f}%)")
                
                if len(high_quality) == 0:
                    print(f"   ⚠️ 没有信号符合新的筛选标准，可能配置未生效")
            else:
                print(f"   ❌ 最近7天没有买入记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库分析失败: {e}")

def check_log_files():
    """检查日志文件"""
    print("\n📋 检查日志文件")
    print("=" * 60)
    
    try:
        # 查找日志文件
        log_files = []
        for file in os.listdir('.'):
            if file.endswith('.log') or 'log' in file.lower():
                log_files.append(file)
        
        if log_files:
            print(f"📁 找到日志文件: {len(log_files)}个")
            for log_file in log_files:
                print(f"   - {log_file}")
            
            # 分析最新的日志文件
            if log_files:
                latest_log = max(log_files, key=lambda x: os.path.getmtime(x))
                print(f"\n🔍 分析最新日志: {latest_log}")
                
                try:
                    with open(latest_log, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                    
                    # 检查关键信息
                    key_patterns = {
                        '智能化多因子引擎': '智能化系统使用',
                        'EnhancedMultiFactorEngine': '新因子引擎',
                        'calculate_all_enhanced_factors': '68个因子计算',
                        'overall_score': '综合评分计算',
                        'ERROR': '错误信息',
                        'WARNING': '警告信息'
                    }
                    
                    for pattern, description in key_patterns.items():
                        count = log_content.count(pattern)
                        if count > 0:
                            print(f"   ✅ {description}: {count}次")
                        else:
                            print(f"   ❌ {description}: 未找到")
                    
                    # 检查最近的错误
                    lines = log_content.split('\n')
                    recent_errors = [line for line in lines[-100:] if 'ERROR' in line]
                    
                    if recent_errors:
                        print(f"\n⚠️ 最近的错误信息:")
                        for error in recent_errors[-5:]:  # 显示最近5个错误
                            print(f"   {error}")
                    else:
                        print(f"\n✅ 最近没有错误信息")
                        
                except Exception as e:
                    print(f"   ❌ 日志文件读取失败: {e}")
        else:
            print("❌ 未找到日志文件")
            
    except Exception as e:
        print(f"❌ 日志检查失败: {e}")

def check_config_actual_usage():
    """检查配置实际使用情况"""
    print("\n⚙️ 检查配置实际使用情况")
    print("=" * 60)
    
    try:
        # 1. 检查config.py是否被正确导入
        import config
        
        if hasattr(config, 'EFFECTIVE_FACTORS_CONFIG'):
            factors_config = config.EFFECTIVE_FACTORS_CONFIG
            print("✅ EFFECTIVE_FACTORS_CONFIG导入成功")
            
            # 检查关键配置
            if 'buy_conditions' in factors_config:
                buy_conditions = factors_config['buy_conditions']
                print(f"📊 当前买入条件:")
                print(f"   min_combined_score: {buy_conditions.get('min_combined_score')}")
                print(f"   min_factors_count: {buy_conditions.get('min_factors_count')}")
            
            # 检查权重配置
            if 'scoring_weights' in factors_config:
                weights = factors_config['scoring_weights']
                print(f"📊 当前权重配置:")
                for key, value in weights.items():
                    if key != 'optimization_note':
                        print(f"   {key}: {value}")
        else:
            print("❌ EFFECTIVE_FACTORS_CONFIG未找到")
        
        # 2. 检查是否有配置冲突
        print(f"\n🔍 检查配置冲突:")
        
        # 检查是否同时存在多个配置文件
        config_files = [
            'config.py',
            'enhanced_factors_config.py',
            'backtest_config.py'
        ]
        
        existing_configs = [f for f in config_files if os.path.exists(f)]
        print(f"   存在的配置文件: {existing_configs}")
        
        if len(existing_configs) > 1:
            print(f"   ⚠️ 存在多个配置文件，可能导致冲突")
        
        # 3. 测试配置加载
        print(f"\n🧪 测试配置加载:")
        
        try:
            from intelligent_strategy_executor import IntelligentStrategyExecutor
            executor = IntelligentStrategyExecutor()
            
            # 测试配置使用
            test_factors = {
                'overall_score': 0.60,
                'technical_score': 0.65,
                'cci_14': 50,
                'atr_pct': 3.5,
                'adx_14': 30
            }
            
            passed, result = executor.apply_multi_dimensional_filters(test_factors, factors_config)
            print(f"   配置测试结果: {'通过' if passed else '未通过'}")
            print(f"   筛选详情: {result.get('passed_count', 0)}/{result.get('min_required', 0)}")
            
        except Exception as e:
            print(f"   ❌ 配置测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")

def identify_root_causes():
    """识别根本原因"""
    print("\n🎯 根本原因分析")
    print("=" * 60)
    
    issues = []
    
    print("基于以上检查，可能的问题原因:")
    
    # 常见问题分析
    potential_issues = [
        "1. 🔧 新配置未被实际策略使用 - main.py仍调用旧系统",
        "2. 🤖 智能化模块虽然集成但未在关键路径执行",
        "3. 📊 数据库记录显示仍在使用旧的筛选标准",
        "4. ⚙️ 配置文件冲突导致新配置被覆盖",
        "5. 🔄 策略系统未重启，仍在使用缓存的旧配置",
        "6. 📋 日志显示错误但被忽略",
        "7. 🎯 筛选条件设置过严，导致无信号生成",
        "8. 📈 新系统运行但数据质量问题影响效果"
    ]
    
    for issue in potential_issues:
        print(f"   {issue}")
    
    print(f"\n💡 建议的解决步骤:")
    print("   1. 🔄 完全重启策略系统")
    print("   2. 🔍 强制使用新配置和智能化模块")
    print("   3. 📊 实时监控新系统是否真正运行")
    print("   4. 🎯 调整筛选条件确保有足够信号")
    print("   5. 📋 建立详细的执行日志")

def generate_fix_plan():
    """生成修复计划"""
    print("\n🚀 紧急修复计划")
    print("=" * 60)
    
    plan = '''
⚡ 立即执行 (今天):

1. 🔧 强制配置生效:
   - 备份当前main.py
   - 修改main.py强制使用新系统
   - 删除旧配置文件避免冲突
   - 添加详细日志确认新系统运行

2. 🎯 降低筛选阈值测试:
   - 临时降低min_combined_score到0.50
   - 临时降低min_factors_count到5
   - 确保有信号生成后再逐步提升

3. 📊 实时验证:
   - 运行1天测试确认新系统工作
   - 检查数据库记录是否使用新因子
   - 监控日志确认68个因子计算
   - 验证智能化筛选是否生效

4. 🔍 问题定位:
   - 如果仍无改善，逐个模块测试
   - 确认每个优化环节是否生效
   - 建立详细的执行追踪

📅 预期时间表:
   - 今天: 强制修复和测试
   - 明天: 验证效果和微调
   - 第3天: 确认胜率改善
   - 第1周: 稳定在50%+胜率
'''
    
    print(plan)

def main():
    """主函数"""
    print("🔍 深度诊断 - 胜率42%未改善问题分析")
    print("=" * 80)
    
    print("🎯 目标: 找出胜率未改善的根本原因并制定修复方案")
    
    # 1. 检查机器学习模型训练
    check_ml_model_training()
    
    # 2. 检查优化模块实际使用
    check_optimization_modules_usage()
    
    # 3. 分析数据库记录
    analyze_database_records()
    
    # 4. 检查日志文件
    check_log_files()
    
    # 5. 检查配置实际使用
    check_config_actual_usage()
    
    # 6. 识别根本原因
    identify_root_causes()
    
    # 7. 生成修复计划
    generate_fix_plan()
    
    print(f"\n🏆 诊断完成")
    print("=" * 40)
    print("基于深度分析，胜率42%未改善的主要原因可能是:")
    print("1. 新系统虽然创建但未被实际调用")
    print("2. 配置修改未生效或被旧配置覆盖") 
    print("3. 需要强制重启和验证新系统运行")
    
    print(f"\n🎯 下一步: 执行紧急修复计划")

if __name__ == '__main__':
    main()
