# coding=utf-8
"""
分析记录误保存检查
检查是否将分析记录误当作买入记录保存
"""

import sqlite3
import pandas as pd
from datetime import datetime

def check_analysis_vs_trade_records():
    """检查分析记录与交易记录的区别"""
    print('🔍 分析记录与交易记录区别检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 1. 检查数据库表结构
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        
        print('📊 数据库表结构:')
        print(f'  总字段数: {len(columns)}')
        
        # 检查关键字段
        field_names = [col[1] for col in columns]
        key_fields = ['timestamp', 'symbol', 'action', 'price', 'volume']
        analysis_fields = ['TRIX_Current', 'Signal_Type', 'Final_Buy_Signal']
        
        print('\n🔍 关键字段检查:')
        for field in key_fields:
            if field in field_names:
                print(f'  ✅ {field}: 存在')
            else:
                print(f'  ❌ {field}: 缺失')
        
        print('\n🔍 分析字段检查:')
        for field in analysis_fields:
            if field in field_names:
                print(f'  ✅ {field}: 存在')
            else:
                print(f'  ❌ {field}: 缺失')
        
        # 2. 检查买入记录的特征
        print('\n📊 买入记录特征分析:')
        
        df = pd.read_sql_query("SELECT * FROM trades WHERE action = 'BUY' LIMIT 10", conn)
        
        if len(df) > 0:
            print(f'  买入记录样本数: {len(df)}条')
            
            # 检查是否有价格和数量
            has_price = df['price'].notna().all()
            has_volume = df['volume'].notna().all()
            
            print(f'  价格字段完整性: {"✅ 完整" if has_price else "❌ 不完整"}')
            print(f'  数量字段完整性: {"✅ 完整" if has_volume else "❌ 不完整"}')
            
            # 检查价格和数量的合理性
            if has_price and has_volume:
                price_range = (df['price'].min(), df['price'].max())
                volume_range = (df['volume'].min(), df['volume'].max())
                
                print(f'  价格范围: ¥{price_range[0]:.2f} - ¥{price_range[1]:.2f}')
                print(f'  数量范围: {int(volume_range[0]):,} - {int(volume_range[1]):,}股')
                
                # 检查是否有异常值
                abnormal_prices = len(df[(df['price'] <= 0) | (df['price'] > 1000)])
                abnormal_volumes = len(df[(df['volume'] <= 0) | (df['volume'] > 1000000)])
                
                if abnormal_prices > 0:
                    print(f'  ⚠️ 异常价格记录: {abnormal_prices}条')
                else:
                    print(f'  ✅ 价格数据正常')
                
                if abnormal_volumes > 0:
                    print(f'  ⚠️ 异常数量记录: {abnormal_volumes}条')
                else:
                    print(f'  ✅ 数量数据正常')
        
        conn.close()
        return df
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return None

def check_data_source_patterns():
    """检查数据来源模式"""
    print('\n🔍 数据来源模式分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 1. 检查买入记录的时间分布
        print('📅 买入记录时间分布:')
        
        cursor = conn.cursor()
        cursor.execute("""
            SELECT DATE(timestamp) as trade_date, COUNT(*) as buy_count 
            FROM trades 
            WHERE action = 'BUY' 
            GROUP BY DATE(timestamp) 
            ORDER BY trade_date DESC 
            LIMIT 10
        """)
        
        daily_buys = cursor.fetchall()
        
        for date, count in daily_buys:
            print(f'  {date}: {count}笔买入')
        
        # 2. 检查买入记录的时间间隔
        print('\n⏰ 买入记录时间间隔分析:')
        
        cursor.execute("""
            SELECT timestamp 
            FROM trades 
            WHERE action = 'BUY' 
            ORDER BY timestamp 
            LIMIT 100
        """)
        
        timestamps = [row[0] for row in cursor.fetchall()]
        
        if len(timestamps) > 1:
            # 转换为datetime对象
            dt_timestamps = [pd.to_datetime(ts) for ts in timestamps]
            
            # 计算时间间隔
            intervals = []
            for i in range(1, len(dt_timestamps)):
                interval = (dt_timestamps[i] - dt_timestamps[i-1]).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                min_interval = min(intervals)
                max_interval = max(intervals)
                
                print(f'  平均间隔: {avg_interval:.1f}秒')
                print(f'  最小间隔: {min_interval:.1f}秒')
                print(f'  最大间隔: {max_interval:.1f}秒')
                
                # 检查是否有大量同时间的记录（可能是分析记录）
                same_time_count = sum(1 for interval in intervals if interval == 0)
                if same_time_count > len(intervals) * 0.5:
                    print(f'  ⚠️ 发现大量同时间记录: {same_time_count}条 (可能是分析记录)')
                else:
                    print(f'  ✅ 时间间隔正常')
        
        # 3. 检查是否有分析字段数据
        print('\n🔍 分析字段数据检查:')
        
        analysis_fields = ['TRIX_Current', 'Signal_Type', 'Final_Buy_Signal', 'Signal_Reason']
        
        for field in analysis_fields:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM trades WHERE action = 'BUY' AND {field} IS NOT NULL")
                count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
                total = cursor.fetchone()[0]
                
                percentage = (count / total * 100) if total > 0 else 0
                print(f'  {field}: {count}/{total} ({percentage:.1f}%)')
                
            except Exception as e:
                print(f'  {field}: 字段不存在或查询失败')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_save_analysis_usage():
    """检查save_analysis的使用情况"""
    print('\n🔍 save_analysis使用情况检查')
    print('=' * 50)
    
    try:
        # 检查signal_generator.py中的save_analysis调用
        with open('signal_generator.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 signal_generator.py中的save_analysis:')
        
        # 查找save_analysis调用
        import re
        save_analysis_pattern = r'save_analysis\((.*?)\)'
        matches = re.findall(save_analysis_pattern, content)
        
        print(f'  save_analysis调用次数: {len(matches)}')
        
        # 查找调用上下文
        context_pattern = r'if.*enable_analysis_log.*save_analysis\((.*?)\)'
        context_matches = re.findall(context_pattern, content, re.DOTALL)
        
        if context_matches:
            print(f'  条件调用: 检查enable_analysis_log开关')
            print(f'  ⚠️ 可能在保存分析数据到trades表')
        
        # 检查analysis_data的构建
        analysis_data_pattern = r'analysis_data\s*=\s*\{(.*?)\}'
        analysis_matches = re.findall(analysis_data_pattern, content, re.DOTALL)
        
        if analysis_matches:
            print(f'  analysis_data构建: {len(analysis_matches)}处')
            
            # 检查是否包含Action字段
            for match in analysis_matches:
                if "'Action'" in match or '"Action"' in match:
                    print(f'  ⚠️ analysis_data包含Action字段，可能被误认为交易记录')
                    break
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def diagnose_record_source():
    """诊断记录来源"""
    print('\n💡 记录来源诊断')
    print('=' * 50)
    
    print('📊 可能的情况分析:')
    
    scenarios = [
        {
            'scenario': '正常买入记录',
            'indicators': [
                '价格和数量数据合理',
                '时间间隔正常',
                '只有基本交易字段',
                '与实际买入操作对应'
            ],
            'probability': '期望情况'
        },
        {
            'scenario': '分析记录误保存',
            'indicators': [
                '大量同时间记录',
                '包含分析字段数据',
                '数量异常（如都是100股）',
                '与实际买入不符'
            ],
            'probability': '需要验证'
        },
        {
            'scenario': '混合保存',
            'indicators': [
                '部分记录正常，部分异常',
                '有真实买入，也有分析数据',
                '时间分布不均匀'
            ],
            'probability': '可能存在'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f'\n{i}. {scenario["scenario"]} ({scenario["probability"]})')
        print(f'   特征指标:')
        for indicator in scenario["indicators"]:
            print(f'     - {indicator}')

def suggest_verification_steps():
    """建议验证步骤"""
    print('\n📋 建议验证步骤')
    print('=' * 50)
    
    steps = [
        {
            'step': '1. 检查enable_analysis_log开关',
            'description': '确认是否启用了分析日志保存',
            'action': '检查策略配置或代码中的enable_analysis_log设置',
            'expected': '如果为True，可能在保存分析记录'
        },
        {
            'step': '2. 对比买入记录与实际操作',
            'description': '检查买入记录数量是否与实际买入操作匹配',
            'action': '查看策略日志中的实际买入操作数量',
            'expected': '如果记录数远大于实际操作，可能有分析记录'
        },
        {
            'step': '3. 检查数据表结构',
            'description': '确认trades表是否同时存储交易和分析数据',
            'action': '检查数据管理器的表结构设计',
            'expected': '理想情况下应该分离交易和分析数据'
        },
        {
            'step': '4. 临时禁用分析日志',
            'description': '暂时禁用分析日志保存，观察记录变化',
            'action': '设置enable_analysis_log=False重新运行',
            'expected': '如果记录数量大幅减少，确认是分析记录'
        }
    ]
    
    for step in steps:
        print(f'{step["step"]}: {step["description"]}')
        print(f'   操作: {step["action"]}')
        print(f'   预期: {step["expected"]}')
        print()

def main():
    """主函数"""
    print('🔍 分析记录误保存检查报告')
    print('=' * 60)
    
    # 检查分析记录与交易记录的区别
    df = check_analysis_vs_trade_records()
    
    # 检查数据来源模式
    check_data_source_patterns()
    
    # 检查save_analysis使用情况
    check_save_analysis_usage()
    
    # 诊断记录来源
    diagnose_record_source()
    
    # 建议验证步骤
    suggest_verification_steps()
    
    print(f'\n🎯 检查结论')
    print('=' * 40)
    print('🔍 发现signal_generator.py中有save_analysis调用')
    print('⚠️ 可能在enable_analysis_log开启时保存分析数据')
    print('💡 需要确认这些记录是否被误当作买入记录')
    print('🔧 建议按照验证步骤逐一排查')

if __name__ == '__main__':
    main()
