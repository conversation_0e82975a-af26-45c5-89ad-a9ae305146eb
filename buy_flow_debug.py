# coding=utf-8
"""
买入流程调试脚本
添加详细的调试日志来追踪买入流程
"""

import re

def add_debug_logs_to_buy_strategy():
    """在买入策略中添加调试日志"""
    print('🔧 添加买入流程调试日志')
    print('=' * 60)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 在buy_strategy开始添加调试日志
        debug_log_1 = '''        # 🔍 调试日志：买入策略开始
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: 买入策略开始执行")'''
        
        # 2. 在信号分析结果处添加调试日志
        debug_log_2 = '''        # 🔍 调试日志：信号分析结果
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: 信号分析结果数量: {len(signal_analysis_results)}")
        if signal_analysis_results:
            context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: 第一个信号: {signal_analysis_results[0]}")'''
        
        # 3. 在execute_backup_buy_logic调用前添加调试日志
        debug_log_3 = '''        # 🔍 调试日志：准备执行买入
        context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: 准备调用execute_backup_buy_logic")'''
        
        # 查找插入位置
        insertions = [
            {
                'pattern': r'(def buy_strategy\(context\):\s*""".*?"""\s*try:\s*)',
                'replacement': r'\1' + debug_log_1 + '\n',
                'description': '买入策略开始'
            },
            {
                'pattern': r'(# 如果没有找到买入信号，直接返回\s*if not signal_analysis_results:)',
                'replacement': debug_log_2 + '\n\n        \\1',
                'description': '信号分析结果'
            },
            {
                'pattern': r'(# 直接使用备用买入逻辑（原生API \+ 数据存储）\s*execute_backup_buy_logic)',
                'replacement': debug_log_3 + '\n        \\1',
                'description': '执行买入前'
            }
        ]
        
        modified_content = content
        for insertion in insertions:
            if re.search(insertion['pattern'], modified_content):
                modified_content = re.sub(insertion['pattern'], insertion['replacement'], modified_content)
                print(f'  ✅ 已添加调试日志: {insertion["description"]}')
            else:
                print(f'  ❌ 未找到插入位置: {insertion["description"]}')
        
        # 保存修改后的文件
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f'\n✅ 调试日志添加完成')
        
    except Exception as e:
        print(f'❌ 添加调试日志失败: {e}')

def add_debug_logs_to_execute_buy():
    """在execute_backup_buy_logic中添加调试日志"""
    print('\n🔧 添加买入执行调试日志')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找execute_backup_buy_logic函数
        execute_pattern = r'(def execute_backup_buy_logic\(.*?\):\s*""".*?"""\s*)'
        
        debug_log = '''    # 🔍 调试日志：买入执行开始
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: execute_backup_buy_logic开始，信号数量: {len(signal_analysis_results)}")
    
'''
        
        if re.search(execute_pattern, content, re.DOTALL):
            modified_content = re.sub(execute_pattern, r'\1' + debug_log, content, flags=re.DOTALL)
            
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f'  ✅ 已添加买入执行调试日志')
        else:
            print(f'  ❌ 未找到execute_backup_buy_logic函数')
        
    except Exception as e:
        print(f'❌ 添加买入执行调试日志失败: {e}')

def add_debug_logs_to_save_record():
    """在save_original_buy_record中添加调试日志"""
    print('\n🔧 添加记录保存调试日志')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找save_original_buy_record函数
        save_pattern = r'(def save_original_buy_record\(.*?\):\s*""".*?"""\s*)'
        
        debug_log = '''    # 🔍 调试日志：记录保存开始
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: save_original_buy_record开始")
    context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 🔍 DEBUG: 数据管理器状态: {hasattr(context, 'data_manager') and context.data_manager is not None}")
    
'''
        
        if re.search(save_pattern, content, re.DOTALL):
            modified_content = re.sub(save_pattern, r'\1' + debug_log, content, flags=re.DOTALL)
            
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f'  ✅ 已添加记录保存调试日志')
        else:
            print(f'  ❌ 未找到save_original_buy_record函数')
        
    except Exception as e:
        print(f'❌ 添加记录保存调试日志失败: {e}')

def create_simple_test_script():
    """创建简单的测试脚本"""
    print('\n📝 创建测试脚本')
    print('=' * 50)
    
    test_script = '''# coding=utf-8
"""
简单的买入流程测试脚本
"""

import sqlite3

def test_buy_record_saving():
    """测试买入记录保存"""
    print('🧪 测试买入记录保存')
    
    # 模拟买入记录
    test_record = {
        'timestamp': '2025-07-20 20:30:00',
        'symbol': 'TEST.000001',
        'action': 'BUY',
        'price': 10.50,
        'volume': 1000
    }
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        field_names = [col[1] for col in columns]
        
        print(f'数据库字段: {field_names[:10]}...')  # 显示前10个字段
        
        # 检查关键字段
        key_fields = ['timestamp', 'symbol', 'action', 'price', 'volume']
        missing_fields = [f for f in key_fields if f not in field_names]
        
        if missing_fields:
            print(f'❌ 缺少字段: {missing_fields}')
        else:
            print(f'✅ 所有关键字段都存在')
            
            # 尝试插入测试记录
            insert_sql = "INSERT INTO trades (timestamp, symbol, action, price, volume) VALUES (?, ?, ?, ?, ?)"
            cursor.execute(insert_sql, (
                test_record['timestamp'],
                test_record['symbol'], 
                test_record['action'],
                test_record['price'],
                test_record['volume']
            ))
            conn.commit()
            
            print(f'✅ 测试记录插入成功')
            
            # 验证插入
            cursor.execute("SELECT * FROM trades WHERE symbol = ?", (test_record['symbol'],))
            result = cursor.fetchone()
            if result:
                print(f'✅ 测试记录验证成功: {result[:5]}')
            else:
                print(f'❌ 测试记录验证失败')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')

if __name__ == '__main__':
    test_buy_record_saving()
'''
    
    with open('test_buy_record.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f'✅ 测试脚本已创建: test_buy_record.py')

def main():
    """主函数"""
    print('🔧 买入流程调试工具')
    print('=' * 60)
    
    # 添加各种调试日志
    add_debug_logs_to_buy_strategy()
    add_debug_logs_to_execute_buy()
    add_debug_logs_to_save_record()
    
    # 创建测试脚本
    create_simple_test_script()
    
    print(f'\n🎯 调试准备完成')
    print('=' * 40)
    print('✅ 已添加详细的调试日志')
    print('✅ 已创建测试脚本')
    print('🔄 建议现在运行策略查看调试输出')
    print('🧪 也可以运行 python test_buy_record.py 测试数据库写入')

if __name__ == '__main__':
    main()
