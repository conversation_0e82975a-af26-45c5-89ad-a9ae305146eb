# coding=utf-8
"""
分析重新回测后的数据 (修复版)
评估优化效果并给出进一步建议
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def analyze_latest_performance():
    """分析最新的策略表现"""
    print('📊 分析重新回测后的最新数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT 
            timestamp, symbol, action, price,
            sell_reason, net_profit_pct_sell, holding_hours,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score
        FROM trades 
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 总交易记录: {len(df)} 条')
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        
        # 分析时间分布 (修复时间处理)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        latest_time = df['timestamp'].max()
        earliest_time = df['timestamp'].min()
        
        print(f'   时间范围: {earliest_time} 到 {latest_time}')
        print(f'   数据跨度: {(latest_time - earliest_time).days} 天')
        
        # 分析最近的活动 (使用相对时间)
        cutoff_24h = latest_time - timedelta(hours=24)
        cutoff_7d = latest_time - timedelta(days=7)
        
        recent_24h = df[df['timestamp'] >= cutoff_24h]
        recent_7d = df[df['timestamp'] >= cutoff_7d]
        
        print(f'   最近24小时: {len(recent_24h)} 条记录')
        print(f'   最近7天: {len(recent_7d)} 条记录')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, None, None

def compare_optimization_results(sell_records):
    """对比优化效果"""
    print(f'\n📊 优化效果评估')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录，无法进行分析')
        return None, None
    
    # 计算当前总体胜率
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return None, None
    
    current_wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    current_total = len(completed_trades)
    current_win_rate = current_wins / current_total * 100
    
    current_avg_profit = completed_trades['net_profit_pct_sell'].mean()
    current_median_profit = completed_trades['net_profit_pct_sell'].median()
    
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
    
    current_avg_win = winning_trades['net_profit_pct_sell'].mean() if len(winning_trades) > 0 else 0
    current_avg_loss = abs(losing_trades['net_profit_pct_sell'].mean()) if len(losing_trades) > 0 else 0
    
    print(f'📈 当前策略表现:')
    print(f'   总胜率: {current_win_rate:.1f}% ({current_wins}/{current_total})')
    print(f'   平均收益: {current_avg_profit:.2f}%')
    print(f'   中位数收益: {current_median_profit:.2f}%')
    print(f'   平均盈利: {current_avg_win:.2f}%')
    print(f'   平均亏损: {current_avg_loss:.2f}%')
    print(f'   盈亏比: {current_avg_win/current_avg_loss:.2f}' if current_avg_loss > 0 else '   盈亏比: N/A')
    
    # 与之前的24.7%胜率对比
    previous_win_rate = 24.7
    improvement = current_win_rate - previous_win_rate
    
    print(f'\n🎯 优化效果对比:')
    print(f'   优化前胜率: {previous_win_rate:.1f}%')
    print(f'   优化后胜率: {current_win_rate:.1f}%')
    
    if improvement > 0:
        print(f'   ✅ 胜率提升: +{improvement:.1f}%')
        if improvement >= 20:
            print(f'   🎉 优化效果卓越!')
        elif improvement >= 10:
            print(f'   🚀 优化效果显著!')
        elif improvement >= 5:
            print(f'   📈 优化效果良好')
        else:
            print(f'   📊 优化效果一般')
    elif improvement < 0:
        print(f'   ❌ 胜率下降: {improvement:.1f}%')
        print(f'   💡 需要重新调整策略')
    else:
        print(f'   ➡️ 胜率无变化')
    
    # 评估是否达到目标
    target_win_rate = 60
    if current_win_rate >= target_win_rate:
        print(f'   🏆 已达到目标胜率 ({target_win_rate}%)')
    else:
        gap = target_win_rate - current_win_rate
        print(f'   🎯 距离目标还差: {gap:.1f}%')
    
    return current_win_rate, improvement

def analyze_sell_reasons_detailed(sell_records):
    """详细分析卖出原因"""
    print(f'\n📋 卖出原因详细分析')
    print('=' * 50)
    
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    # 统计卖出原因
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', 'median', lambda x: (x > 0).mean() * 100],
        'holding_hours': ['mean', 'median']
    }).round(2)
    
    sell_reason_stats.columns = ['交易数', '平均收益%', '中位收益%', '胜率%', '平均持仓h', '中位持仓h']
    sell_reason_stats = sell_reason_stats.sort_values('胜率%', ascending=False)
    
    print(f'📊 各卖出原因表现 (按胜率排序):')
    print(sell_reason_stats.to_string())
    
    # 重点分析优化目标
    print(f'\n🎯 关键优化指标检查:')
    
    # 1. 检查固定止损
    fixed_stop_loss = completed_trades[completed_trades['sell_reason'] == '固定止损']
    if len(fixed_stop_loss) > 0:
        fixed_rate = (fixed_stop_loss['net_profit_pct_sell'] > 0).mean() * 100
        fixed_avg = fixed_stop_loss['net_profit_pct_sell'].mean()
        print(f'   固定止损: {len(fixed_stop_loss)}笔, 胜率{fixed_rate:.1f}%, 平均收益{fixed_avg:.2f}%')
        
        if fixed_rate > 0:
            print(f'   ✅ 固定止损胜率改善 (之前0%)')
        else:
            print(f'   ⚠️ 固定止损胜率仍为0%，需要进一步放宽')
    else:
        print(f'   ✅ 没有固定止损交易')
    
    # 2. 检查时间止损
    time_stop_loss = completed_trades[completed_trades['sell_reason'] == '时间止损']
    if len(time_stop_loss) > 0:
        time_rate = (time_stop_loss['net_profit_pct_sell'] > 0).mean() * 100
        print(f'   ⚠️ 仍有时间止损: {len(time_stop_loss)}笔, 胜率{time_rate:.1f}%')
    else:
        print(f'   ✅ 时间止损已消除')
    
    # 3. 检查跟踪止盈
    trailing_stop = completed_trades[completed_trades['sell_reason'] == '跟踪止盈']
    if len(trailing_stop) > 0:
        trailing_rate = (trailing_stop['net_profit_pct_sell'] > 0).mean() * 100
        trailing_avg = trailing_stop['net_profit_pct_sell'].mean()
        print(f'   跟踪止盈: {len(trailing_stop)}笔, 胜率{trailing_rate:.1f}%, 平均收益{trailing_avg:.2f}%')
        
        if trailing_rate >= 70:
            print(f'   ✅ 跟踪止盈表现优秀')
        else:
            print(f'   💡 跟踪止盈可进一步优化')
    
    # 4. 检查最大持仓天数
    max_holding = completed_trades[completed_trades['sell_reason'] == '最大持仓天数']
    if len(max_holding) > 0:
        max_rate = (max_holding['net_profit_pct_sell'] > 0).mean() * 100
        max_avg = max_holding['net_profit_pct_sell'].mean()
        print(f'   最大持仓天数: {len(max_holding)}笔, 胜率{max_rate:.1f}%, 平均收益{max_avg:.2f}%')

def analyze_buy_signal_quality(buy_records):
    """分析买入信号质量"""
    print(f'\n🎯 买入信号质量分析')
    print('=' * 50)
    
    if len(buy_records) == 0:
        print('⚠️ 没有买入记录')
        return
    
    print(f'📊 买入信号统计 (基于{len(buy_records)}条记录):')
    
    # 分析多因子评分分布
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    for col in score_columns:
        if col in buy_records.columns:
            values = buy_records[col].dropna()
            if len(values) > 0:
                mean_val = values.mean()
                median_val = values.median()
                std_val = values.std()
                
                print(f'   {col}: 均值{mean_val:.3f}, 中位{median_val:.3f}, 标准差{std_val:.3f}')
    
    # 分析最近的买入活动
    buy_records['timestamp'] = pd.to_datetime(buy_records['timestamp'])
    latest_time = buy_records['timestamp'].max()
    recent_buys = buy_records[buy_records['timestamp'] >= (latest_time - timedelta(days=7))]
    
    print(f'\n📈 最近7天买入活动:')
    print(f'   买入数量: {len(recent_buys)} 条')
    
    if len(recent_buys) > 0:
        daily_buys = recent_buys.groupby(recent_buys['timestamp'].dt.date).size()
        print(f'   日均买入: {daily_buys.mean():.1f} 条')
        print(f'   最多单日: {daily_buys.max()} 条')

def generate_next_optimization(current_win_rate, improvement):
    """生成下一步优化建议"""
    print(f'\n💡 下一步优化建议')
    print('=' * 50)
    
    print(f'📊 当前状况:')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    print(f'   改善幅度: {improvement:+.1f}%')
    print(f'   目标胜率: 60%')
    
    recommendations = []
    config_changes = {}
    
    if current_win_rate < 30:
        print(f'\n🚨 胜率仍然偏低，需要激进优化:')
        recommendations = [
            '大幅放宽止损条件 (2.5% → 4.0%)',
            '延长最大持仓时间 (20天 → 30天)',
            '进一步降低多因子阈值',
            '考虑暂时禁用固定止损'
        ]
        config_changes = {
            'FIXED_STOP_LOSS_RATIO': 0.04,
            'DYNAMIC_STOP_LOSS_RATIO': 0.04,
            'MAX_HOLDING_DAYS': 30,
            'ENABLE_FIXED_STOP_LOSS': False
        }
        
    elif current_win_rate < 40:
        print(f'\n📊 胜率有改善，继续优化:')
        recommendations = [
            '适度放宽止损条件 (2.5% → 3.5%)',
            '优化跟踪止盈参数',
            '延长持仓时间',
            '微调多因子阈值'
        ]
        config_changes = {
            'FIXED_STOP_LOSS_RATIO': 0.035,
            'DYNAMIC_STOP_LOSS_RATIO': 0.035,
            'MAX_HOLDING_DAYS': 25
        }
        
    elif current_win_rate < 50:
        print(f'\n📈 胜率接近目标，精细调整:')
        recommendations = [
            '微调止损参数',
            '优化仓位管理',
            '加强选股质量',
            '监控策略稳定性'
        ]
        config_changes = {
            'FIXED_STOP_LOSS_RATIO': 0.03,
            'TRAILING_STOP_PCT': 1.2
        }
        
    else:
        print(f'\n🎉 胜率表现优秀:')
        recommendations = [
            '保持当前策略稳定',
            '监控长期表现',
            '考虑扩大投资规模',
            '探索新的优化方向'
        ]
    
    # 输出具体建议
    for i, rec in enumerate(recommendations, 1):
        print(f'   {i}. {rec}')
    
    # 生成配置代码
    if config_changes:
        print(f'\n⚙️ 建议的配置调整:')
        print(f'```python')
        for key, value in config_changes.items():
            if isinstance(value, bool):
                print(f'{key} = {value}')
            else:
                print(f'{key} = {value}')
        print(f'```')
    
    return recommendations

def main():
    """主函数"""
    print('🚀 重新回测数据分析与优化建议')
    print('=' * 60)
    
    # 分析最新表现
    df, buy_records, sell_records = analyze_latest_performance()
    
    if df is not None:
        # 对比优化效果
        current_win_rate, improvement = compare_optimization_results(sell_records)
        
        # 详细分析卖出原因
        analyze_sell_reasons_detailed(sell_records)
        
        # 分析买入信号质量
        analyze_buy_signal_quality(buy_records)
        
        # 生成下一步优化建议
        if current_win_rate is not None:
            generate_next_optimization(current_win_rate, improvement)
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print('✅ 重新回测数据分析完成')
        print('📊 已评估当前策略表现')
        print('💡 已生成针对性优化建议')
        print('')
        
        if current_win_rate is not None:
            if current_win_rate >= 50:
                print('🏆 策略表现优秀，继续保持!')
            elif current_win_rate >= 35:
                print('📈 策略有明显改善，继续优化!')
            else:
                print('🔧 策略需要进一步调整!')
    else:
        print('❌ 无法获取回测数据')

if __name__ == '__main__':
    main()
