# coding=utf-8
"""
时序策略集成器
将时序分析结果集成到现有策略中
"""

from timeseries_pattern_analyzer import TimeSeriesPatternAnalyzer
from timeseries_factor_collector import TimeSeriesFactorCollector
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Tuple, Optional

class TimeSeriesStrategyIntegrator:
    """时序策略集成器"""
    
    def __init__(self, context=None):
        self.context = context
        self.pattern_analyzer = TimeSeriesPatternAnalyzer()
        self.factor_collector = TimeSeriesFactorCollector(context)
        
        # 策略权重配置
        self.weights = {
            'timeseries_analysis': 0.4,  # 时序分析权重
            'original_strategy': 0.6     # 原始策略权重
        }
    
    def enhanced_buy_signal_analysis(self, symbol: str, original_signal_data: Dict) -> Tuple[bool, str, Dict]:
        """增强的买入信号分析"""
        try:
            print(f'🔍 对{symbol}进行增强买入信号分析')
            
            # 执行时序模式分析
            timeseries_result = self.pattern_analyzer.comprehensive_pattern_analysis(symbol)
            
            # 获取原始策略的信号强度
            original_score = self.calculate_original_signal_score(original_signal_data)
            
            # 获取时序分析得分
            timeseries_score = timeseries_result.get('overall_score', 0)
            
            # 计算综合得分
            combined_score = (
                original_score * self.weights['original_strategy'] +
                timeseries_score * self.weights['timeseries_analysis']
            )
            
            # 生成增强的买入决策
            enhanced_signal = combined_score >= 0.6
            
            # 构建详细的分析结果
            analysis_result = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'original_score': original_score,
                'timeseries_score': timeseries_score,
                'combined_score': combined_score,
                'enhanced_signal': enhanced_signal,
                'original_signal_data': original_signal_data,
                'timeseries_analysis': timeseries_result,
                'decision_factors': []
            }
            
            # 分析决策因素
            decision_factors = []
            
            # 原始策略因素
            if original_score > 0.5:
                decision_factors.append(f"原始策略信号强度: {original_score:.2f}")
            
            # 时序分析因素
            if 'patterns' in timeseries_result:
                patterns = timeseries_result['patterns']
                
                if patterns.get('macd_golden_cross', {}).get('signal', False):
                    decision_factors.append("MACD金叉信号确认")
                
                if patterns.get('bb_expansion', {}).get('signal', False):
                    decision_factors.append("布林带扩张模式")
                
                if patterns.get('volume_pattern', {}).get('signal', False):
                    decision_factors.append("成交量放大确认")
                
                if 'atr_check' in patterns and patterns['atr_check'].get('above_threshold', False):
                    atr_info = patterns['atr_check']
                    decision_factors.append(f"ATR波动率{atr_info['current_atr']:.2f} > 阈值{atr_info['threshold']:.2f}")
            
            analysis_result['decision_factors'] = decision_factors
            
            # 生成决策说明
            if enhanced_signal:
                reason = f"增强买入信号 (综合得分: {combined_score:.2f})"
                if decision_factors:
                    reason += f" - {', '.join(decision_factors[:3])}"
            else:
                reason = f"信号不足 (综合得分: {combined_score:.2f})"
                if combined_score > 0.4:
                    reason += " - 建议观察"
            
            print(f'✅ {symbol} 增强分析完成: {"买入" if enhanced_signal else "观察"} (得分: {combined_score:.2f})')
            
            return enhanced_signal, reason, analysis_result
            
        except Exception as e:
            error_msg = f"增强分析失败: {str(e)}"
            print(f'❌ {symbol} {error_msg}')
            return False, error_msg, {'error': str(e)}
    
    def calculate_original_signal_score(self, signal_data: Dict) -> float:
        """计算原始策略信号得分"""
        try:
            score = 0.0
            
            # 基于现有的高胜率组合A条件计算得分
            # ATR > 2.7 + BB_width > 10.8 + MACD_hist > 0
            
            # ATR条件 (权重: 0.4)
            atr_pct = signal_data.get('atr_pct', 0)
            if atr_pct > 2.7:
                score += 0.4
            elif atr_pct > 2.2:
                score += 0.2
            
            # 布林带宽度条件 (权重: 0.3)
            bb_width = signal_data.get('bb_width', 0)
            if bb_width > 10.8:
                score += 0.3
            elif bb_width > 7.0:
                score += 0.15
            
            # MACD金叉条件 (权重: 0.3)
            macd_hist = signal_data.get('macd_hist', 0)
            if macd_hist > 0:
                score += 0.3
            elif macd_hist > -0.1:
                score += 0.1
            
            return min(score, 1.0)
            
        except Exception as e:
            print(f'❌ 计算原始信号得分失败: {e}')
            return 0.0
    
    def get_dynamic_thresholds(self, symbol: str) -> Dict:
        """获取动态阈值"""
        try:
            # 获取动态ATR阈值
            atr_threshold, atr_info = self.pattern_analyzer.calculate_dynamic_atr_threshold(symbol)
            
            # 可以添加其他动态阈值计算
            # 例如：动态MACD阈值、动态RSI阈值等
            
            thresholds = {
                'atr_threshold': atr_threshold,
                'atr_info': atr_info,
                'bb_width_threshold': 10.8,  # 暂时使用固定值，后续可以动态化
                'macd_hist_threshold': 0.0,  # 暂时使用固定值
                'volume_threshold': 1.5      # 暂时使用固定值
            }
            
            return thresholds
            
        except Exception as e:
            print(f'❌ 获取{symbol}动态阈值失败: {e}')
            return {
                'atr_threshold': 2.7,
                'bb_width_threshold': 10.8,
                'macd_hist_threshold': 0.0,
                'volume_threshold': 1.5
            }
    
    def should_buy_with_timeseries(self, symbol: str, signal_data: Dict) -> Tuple[bool, str]:
        """基于时序分析的买入决策"""
        try:
            # 获取动态阈值
            thresholds = self.get_dynamic_thresholds(symbol)
            
            # 执行增强分析
            enhanced_signal, reason, analysis_result = self.enhanced_buy_signal_analysis(symbol, signal_data)
            
            # 额外的安全检查
            safety_checks = []
            
            # 检查是否有足够的时序数据
            timeseries_data = self.pattern_analyzer.get_timeseries_data(symbol, lookback_hours=24)
            if len(timeseries_data) < 5:
                safety_checks.append("时序数据不足，降低信号权重")
                enhanced_signal = enhanced_signal and analysis_result.get('original_score', 0) > 0.7
            
            # 检查最近的价格波动
            if len(timeseries_data) > 0 and 'price' in timeseries_data.columns:
                recent_prices = timeseries_data['price'].dropna().tail(5)
                if len(recent_prices) >= 2:
                    price_volatility = recent_prices.std() / recent_prices.mean()
                    if price_volatility > 0.1:  # 价格波动过大
                        safety_checks.append(f"价格波动过大 ({price_volatility:.2%})")
            
            # 最终决策
            final_decision = enhanced_signal
            final_reason = reason
            
            if safety_checks:
                final_reason += f" [安全检查: {'; '.join(safety_checks)}]"
            
            # 记录决策信息
            if self.context and hasattr(self.context, 'log'):
                self.context.log.info(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 🔍 {symbol} 时序增强决策: {final_decision} - {final_reason}")
            
            return final_decision, final_reason
            
        except Exception as e:
            error_msg = f"时序买入决策失败: {str(e)}"
            print(f'❌ {symbol} {error_msg}')
            return False, error_msg
    
    def get_enhanced_signal_data(self, symbol: str, original_signal_data: Dict) -> Dict:
        """获取增强的信号数据"""
        try:
            # 复制原始信号数据
            enhanced_data = original_signal_data.copy()
            
            # 添加时序分析结果
            timeseries_analysis = self.pattern_analyzer.comprehensive_pattern_analysis(symbol)
            enhanced_data['timeseries_analysis'] = timeseries_analysis
            
            # 添加动态阈值
            dynamic_thresholds = self.get_dynamic_thresholds(symbol)
            enhanced_data['dynamic_thresholds'] = dynamic_thresholds
            
            # 添加时序模式标记
            if 'patterns' in timeseries_analysis:
                patterns = timeseries_analysis['patterns']
                enhanced_data['macd_golden_cross_detected'] = patterns.get('macd_golden_cross', {}).get('signal', False)
                enhanced_data['bb_expansion_detected'] = patterns.get('bb_expansion', {}).get('signal', False)
                enhanced_data['volume_surge_detected'] = patterns.get('volume_pattern', {}).get('signal', False)
            
            # 更新ATR阈值为动态值
            if 'atr_threshold' in dynamic_thresholds:
                enhanced_data['dynamic_atr_threshold'] = dynamic_thresholds['atr_threshold']
            
            return enhanced_data
            
        except Exception as e:
            print(f'❌ 获取{symbol}增强信号数据失败: {e}')
            return original_signal_data
    
    def start_timeseries_collection(self):
        """启动时序数据采集"""
        try:
            print('🚀 启动时序因子数据采集...')
            self.factor_collector.start_collection()
            return True
        except Exception as e:
            print(f'❌ 启动时序数据采集失败: {e}')
            return False
    
    def stop_timeseries_collection(self):
        """停止时序数据采集"""
        try:
            print('🛑 停止时序因子数据采集...')
            self.factor_collector.stop_collection()
            return True
        except Exception as e:
            print(f'❌ 停止时序数据采集失败: {e}')
            return False

def main():
    """测试函数"""
    print('🧪 时序策略集成器测试')
    print('=' * 50)
    
    integrator = TimeSeriesStrategyIntegrator()
    
    # 模拟原始信号数据
    test_signal_data = {
        'atr_pct': 3.2,
        'bb_width': 12.5,
        'macd_hist': 0.15,
        'macd': -0.05,
        'rsi': 45.0,
        'volume': 50000
    }
    
    # 获取测试股票
    try:
        import sqlite3
        conn = sqlite3.connect('data/trades.db')
        query = "SELECT DISTINCT symbol FROM trades WHERE action = 'BUY' LIMIT 2"
        df = pd.read_sql_query(query, conn)
        conn.close()
        test_symbols = df['symbol'].tolist()
    except:
        test_symbols = ['SHSE.600036', 'SZSE.000001']
    
    print(f'📊 测试股票: {test_symbols}')
    
    for symbol in test_symbols:
        print(f'\n🔍 测试 {symbol}:')
        
        # 测试买入决策
        should_buy, reason = integrator.should_buy_with_timeseries(symbol, test_signal_data)
        print(f'   买入决策: {"✅ 买入" if should_buy else "❌ 不买入"}')
        print(f'   决策理由: {reason}')
        
        # 测试增强信号数据
        enhanced_data = integrator.get_enhanced_signal_data(symbol, test_signal_data)
        print(f'   增强数据字段数: {len(enhanced_data)}')
        
        if 'dynamic_thresholds' in enhanced_data:
            thresholds = enhanced_data['dynamic_thresholds']
            print(f'   动态ATR阈值: {thresholds.get("atr_threshold", "N/A")}')

if __name__ == '__main__':
    main()
