# coding=utf-8
"""
买入信息存储方式深度分析
分析策略为什么没有进行买入记录的根本原因
"""

import re
import os
import sqlite3

def analyze_buy_execution_flow():
    """分析买入执行流程"""
    print('🔍 买入执行流程深度分析')
    print('=' * 60)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 买入执行链路分析:')
        
        # 1. 检查买入策略入口
        buy_strategy_calls = re.findall(r'buy_strategy\(', content)
        print(f'  1. 买入策略调用: {len(buy_strategy_calls)}处')
        
        # 2. 检查买入信号生成
        signal_generation = re.findall(r'analyze_single_symbol\(', content)
        print(f'  2. 信号生成调用: {len(signal_generation)}处')
        
        # 3. 检查买入执行
        buy_execution = re.findall(r'execute_backup_buy_logic\(', content)
        print(f'  3. 买入执行调用: {len(buy_execution)}处')
        
        # 4. 检查order_volume调用
        order_calls = re.findall(r'order_volume\(', content)
        print(f'  4. 下单API调用: {len(order_calls)}处')
        
        # 5. 检查买入记录保存
        save_record_calls = re.findall(r'save_original_buy_record\(', content)
        print(f'  5. 买入记录保存: {len(save_record_calls)}处')
        
        # 6. 检查数据管理器调用
        data_manager_calls = re.findall(r'context\.data_manager\.save_trade\(', content)
        print(f'  6. 数据管理器保存: {len(data_manager_calls)}处')
        
        # 7. 检查直接保存调用
        direct_save_calls = re.findall(r'save_analysis\(', content)
        print(f'  7. 直接保存调用: {len(direct_save_calls)}处')
        
        return {
            'buy_strategy_calls': len(buy_strategy_calls),
            'signal_generation': len(signal_generation),
            'buy_execution': len(buy_execution),
            'order_calls': len(order_calls),
            'save_record_calls': len(save_record_calls),
            'data_manager_calls': len(data_manager_calls),
            'direct_save_calls': len(direct_save_calls)
        }
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return {}

def check_buy_conditions():
    """检查买入条件和触发机制"""
    print('\n🔍 买入条件和触发机制分析')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 买入条件检查:')
        
        # 1. 检查买入信号条件
        trix_conditions = re.findall(r'trix_reversal_confirmed', content)
        print(f'  1. TRIX反转信号: {len(trix_conditions)}处')
        
        # 2. 检查智能评分条件
        smart_score_conditions = re.findall(r'smart_score', content)
        print(f'  2. 智能评分条件: {len(smart_score_conditions)}处')
        
        # 3. 检查资金条件
        cash_conditions = re.findall(r'available_cash', content)
        print(f'  3. 资金检查: {len(cash_conditions)}处')
        
        # 4. 检查持仓限制
        position_limits = re.findall(r'max_positions', content)
        print(f'  4. 持仓限制: {len(position_limits)}处')
        
        # 5. 检查交易时间
        trading_time = re.findall(r'is_trading', content)
        print(f'  5. 交易时间检查: {len(trading_time)}处')
        
        # 6. 检查买入信号筛选
        signal_filter = re.findall(r'signal_analysis_results', content)
        print(f'  6. 信号筛选: {len(signal_filter)}处')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def analyze_potential_blocking_points():
    """分析可能的阻塞点"""
    print('\n🔍 潜在阻塞点分析')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 可能阻塞买入的条件:')
        
        # 1. 检查早期返回条件
        early_returns = [
            (r'if not signal_analysis_results:', '没有买入信号'),
            (r'if max_buy_stocks <= 0:', '达到最大持仓'),
            (r'if available_cash < ', '资金不足'),
            (r'if not is_trading', '非交易时间'),
            (r'return False', '条件不满足返回'),
            (r'return None', '条件不满足返回None'),
            (r'continue', '跳过当前循环')
        ]
        
        for pattern, description in early_returns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ⚠️ {description}: {len(matches)}处')
        
        # 2. 检查异常处理
        exception_blocks = re.findall(r'except.*Exception.*:', content)
        print(f'  🔍 异常处理块: {len(exception_blocks)}处')
        
        # 3. 检查日志记录
        log_patterns = [
            (r'没有找到买入信号', '无买入信号日志'),
            (r'已达到最大持仓', '持仓限制日志'),
            (r'资金不足', '资金不足日志'),
            (r'买入失败', '买入失败日志'),
            (r'买入异常', '买入异常日志')
        ]
        
        for pattern, description in log_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  📝 {description}: {len(matches)}处')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_data_manager_initialization():
    """检查数据管理器初始化"""
    print('\n🔍 数据管理器初始化检查')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 数据管理器相关检查:')
        
        # 1. 检查数据管理器导入
        data_manager_import = re.findall(r'from.*data_manager.*import', content)
        print(f'  1. 数据管理器导入: {len(data_manager_import)}处')
        
        # 2. 检查数据管理器初始化
        data_manager_init = re.findall(r'context\.data_manager\s*=', content)
        print(f'  2. 数据管理器初始化: {len(data_manager_init)}处')
        
        # 3. 检查数据管理器可用性检查
        data_manager_check = re.findall(r'hasattr\(context, [\'"]data_manager[\'"]', content)
        print(f'  3. 数据管理器可用性检查: {len(data_manager_check)}处')
        
        # 4. 检查备用保存方式
        backup_save = re.findall(r'save_analysis\(', content)
        print(f'  4. 备用保存方式: {len(backup_save)}处')
        
        # 5. 查找数据管理器初始化代码
        init_pattern = r'context\.data_manager\s*=\s*([^\\n]+)'
        init_matches = re.findall(init_pattern, content)
        if init_matches:
            print(f'  📝 数据管理器初始化代码:')
            for match in init_matches:
                print(f'    {match.strip()}')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def analyze_order_execution_logic():
    """分析下单执行逻辑"""
    print('\n🔍 下单执行逻辑分析')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 下单执行分析:')
        
        # 查找order_volume调用的上下文
        order_pattern = r'order_result\s*=\s*order_volume\((.*?)\)'
        order_matches = re.findall(order_pattern, content, re.DOTALL)
        
        if order_matches:
            print(f'  📝 发现{len(order_matches)}处order_volume调用')
            for i, match in enumerate(order_matches, 1):
                print(f'    调用{i}: order_volume({match.strip()[:100]}...)')
        
        # 检查订单结果处理
        order_result_checks = re.findall(r'if order_result:', content)
        print(f'  🔍 订单结果检查: {len(order_result_checks)}处')
        
        # 检查买入成功处理
        buy_success_patterns = [
            r'买入成功',
            r'原生API买入成功',
            r'✅.*买入',
            r'保存买入记录'
        ]
        
        for pattern in buy_success_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ✅ 买入成功处理: {len(matches)}处')
        
        # 检查买入失败处理
        buy_failure_patterns = [
            r'买入失败',
            r'❌.*买入',
            r'买入异常'
        ]
        
        for pattern in buy_failure_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ❌ 买入失败处理: {len(matches)}处')
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_historical_data():
    """检查历史数据中是否有买入记录痕迹"""
    print('\n🔍 历史数据痕迹检查')
    print('=' * 50)
    
    try:
        if os.path.exists('data/trades.db'):
            conn = sqlite3.connect('data/trades.db')
            cursor = conn.cursor()
            
            # 检查是否有任何BUY相关的痕迹
            print('📊 数据库痕迹检查:')
            
            # 1. 检查action字段的所有值
            cursor.execute("SELECT DISTINCT action FROM trades WHERE action IS NOT NULL")
            actions = cursor.fetchall()
            print(f'  1. action字段值: {[a[0] for a in actions]}')
            
            # 2. 检查是否有大写的BUY
            cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
            buy_count = cursor.fetchone()[0]
            print(f'  2. BUY记录数: {buy_count}')
            
            # 3. 检查是否有小写的buy
            cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'buy'")
            buy_lower_count = cursor.fetchone()[0]
            print(f'  3. buy记录数: {buy_lower_count}')
            
            # 4. 检查是否有其他可能的买入标识
            other_patterns = ['Buy', 'purchase', 'open', 'long']
            for pattern in other_patterns:
                cursor.execute(f"SELECT COUNT(*) FROM trades WHERE action = ?", (pattern,))
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f'  4. {pattern}记录数: {count}')
            
            # 5. 检查最早和最新的记录时间
            cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM trades")
            time_range = cursor.fetchone()
            print(f'  5. 数据时间范围: {time_range[0]} 到 {time_range[1]}')
            
            # 6. 检查是否有持仓成本记录
            cursor.execute("SELECT COUNT(*) FROM trades WHERE cost_price_sell IS NOT NULL")
            cost_records = cursor.fetchone()[0]
            print(f'  6. 有成本价的记录: {cost_records}条（说明有买入历史）')
            
            conn.close()
        else:
            print('❌ 数据库文件不存在')
            
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def diagnose_root_cause():
    """诊断根本原因"""
    print('\n💡 根本原因诊断')
    print('=' * 50)
    
    possible_causes = [
        {
            'cause': '策略从未执行买入操作',
            'indicators': [
                '没有买入信号生成',
                '买入条件从未满足',
                '策略配置问题'
            ],
            'check_method': '检查日志中是否有买入信号'
        },
        {
            'cause': '买入操作执行但order_volume失败',
            'indicators': [
                '有买入信号但无买入记录',
                '资金或权限问题',
                'API调用失败'
            ],
            'check_method': '检查日志中的订单执行结果'
        },
        {
            'cause': '买入成功但记录保存失败',
            'indicators': [
                '有持仓但无买入记录',
                '数据管理器问题',
                '字段映射问题'
            ],
            'check_method': '检查数据管理器初始化和字段映射'
        },
        {
            'cause': '记录保存到错误的位置',
            'indicators': [
                '买入记录存在但位置错误',
                '数据库路径问题',
                '表结构问题'
            ],
            'check_method': '检查所有可能的数据库文件'
        },
        {
            'cause': '历史数据清理导致买入记录丢失',
            'indicators': [
                '只有卖出记录',
                '数据时间不连续',
                '记录数量异常'
            ],
            'check_method': '检查数据备份和清理日志'
        }
    ]
    
    print('🔍 可能的根本原因:')
    for i, cause in enumerate(possible_causes, 1):
        print(f'\n{i}. {cause["cause"]}')
        print(f'   指标: {", ".join(cause["indicators"])}')
        print(f'   检查方法: {cause["check_method"]}')

def main():
    """主函数"""
    print('🔍 买入信息存储方式深度分析报告')
    print('=' * 60)
    
    # 分析买入执行流程
    flow_stats = analyze_buy_execution_flow()
    
    # 检查买入条件
    check_buy_conditions()
    
    # 分析潜在阻塞点
    analyze_potential_blocking_points()
    
    # 检查数据管理器初始化
    check_data_manager_initialization()
    
    # 分析下单执行逻辑
    analyze_order_execution_logic()
    
    # 检查历史数据
    check_historical_data()
    
    # 诊断根本原因
    diagnose_root_cause()
    
    print(f'\n📋 分析总结:')
    print('=' * 40)
    print('🔍 已完成买入存储方式的深度分析')
    print('💡 发现了多个可能的问题点')
    print('🔧 建议按照诊断结果逐一排查')
    print('📊 重点检查策略是否真正执行了买入操作')

if __name__ == '__main__':
    main()
