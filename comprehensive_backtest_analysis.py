# coding=utf-8
"""
全面回测数据分析
关注胜率、盈亏比、最大止盈止损、买入点分析、市场行情影响、未来函数检查
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def analyze_core_metrics():
    """分析核心指标：胜率、盈亏比、最大止盈止损"""
    print('📊 核心指标分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有交易数据
        query = """
        SELECT 
            timestamp, symbol, action, price,
            sell_reason, net_profit_pct_sell, holding_hours,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy
        FROM trades 
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
        
        print(f'📈 交易统计:')
        print(f'   总记录: {len(df)} 条')
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        print(f'   已完成交易: {len(completed_trades)} 条')
        
        if len(completed_trades) == 0:
            print('⚠️ 没有已完成的交易')
            return None, None, None
        
        # 1. 胜率分析
        wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
        total = len(completed_trades)
        win_rate = wins / total * 100
        
        print(f'\n🎯 胜率分析:')
        print(f'   总胜率: {win_rate:.2f}% ({wins}/{total})')
        
        # 2. 盈亏比分析
        winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
        losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
        
        avg_win = winning_trades['net_profit_pct_sell'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades['net_profit_pct_sell'].mean()) if len(losing_trades) > 0 else 0
        profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf')
        
        print(f'\n💰 盈亏比分析:')
        print(f'   平均盈利: {avg_win:.2f}%')
        print(f'   平均亏损: {avg_loss:.2f}%')
        print(f'   盈亏比: {profit_loss_ratio:.2f}')
        
        # 3. 最大止盈止损分析
        max_profit = completed_trades['net_profit_pct_sell'].max()
        max_loss = completed_trades['net_profit_pct_sell'].min()
        
        print(f'\n📊 极值分析:')
        print(f'   最大止盈: {max_profit:.2f}%')
        print(f'   最大止损: {max_loss:.2f}%')
        
        # 4. 收益分布分析
        print(f'\n📈 收益分布:')
        profit_ranges = [
            ('大亏 (<-5%)', completed_trades['net_profit_pct_sell'] < -5),
            ('中亏 (-5% to -2%)', (completed_trades['net_profit_pct_sell'] >= -5) & (completed_trades['net_profit_pct_sell'] < -2)),
            ('小亏 (-2% to 0%)', (completed_trades['net_profit_pct_sell'] >= -2) & (completed_trades['net_profit_pct_sell'] < 0)),
            ('小盈 (0% to 2%)', (completed_trades['net_profit_pct_sell'] >= 0) & (completed_trades['net_profit_pct_sell'] < 2)),
            ('中盈 (2% to 5%)', (completed_trades['net_profit_pct_sell'] >= 2) & (completed_trades['net_profit_pct_sell'] < 5)),
            ('大盈 (>5%)', completed_trades['net_profit_pct_sell'] >= 5)
        ]
        
        for range_name, condition in profit_ranges:
            count = condition.sum()
            percentage = count / len(completed_trades) * 100
            print(f'   {range_name}: {count}笔 ({percentage:.1f}%)')
        
        # 5. 持仓时间分析
        print(f'\n⏰ 持仓时间分析:')
        avg_holding = completed_trades['holding_hours'].mean()
        median_holding = completed_trades['holding_hours'].median()
        max_holding = completed_trades['holding_hours'].max()
        
        print(f'   平均持仓: {avg_holding:.1f}小时 ({avg_holding/24:.1f}天)')
        print(f'   中位持仓: {median_holding:.1f}小时 ({median_holding/24:.1f}天)')
        print(f'   最长持仓: {max_holding:.1f}小时 ({max_holding/24:.1f}天)')
        
        return df, buy_records, completed_trades
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None, None, None

def analyze_profitable_entry_points(buy_records, completed_trades):
    """分析哪些买入点更有希望盈利"""
    print(f'\n🎯 盈利买入点分析')
    print('=' * 50)
    
    if len(buy_records) == 0 or len(completed_trades) == 0:
        print('⚠️ 数据不足，无法分析')
        return
    
    # 创建买入-卖出匹配
    buy_sell_matched = []
    
    for _, sell_trade in completed_trades.iterrows():
        # 寻找对应的买入记录
        matching_buys = buy_records[
            (buy_records['symbol'] == sell_trade['symbol']) &
            (pd.to_datetime(buy_records['timestamp']) <= pd.to_datetime(sell_trade['timestamp']))
        ].sort_values('timestamp', ascending=False)
        
        if len(matching_buys) > 0:
            buy_trade = matching_buys.iloc[0]
            
            matched_trade = {
                'symbol': sell_trade['symbol'],
                'buy_time': buy_trade['timestamp'],
                'sell_time': sell_trade['timestamp'],
                'profit_pct': sell_trade['net_profit_pct_sell'],
                'is_profitable': sell_trade['net_profit_pct_sell'] > 0,
                'overall_score': buy_trade['overall_score'],
                'technical_score': buy_trade['technical_score'],
                'momentum_score': buy_trade['momentum_score'],
                'volume_score': buy_trade['volume_score'],
                'volatility_score': buy_trade['volatility_score'],
                'trend_score': buy_trade['trend_score'],
                'buy_signal_strength': buy_trade['buy_signal_strength'],
                'risk_adjusted_score': buy_trade['risk_adjusted_score'],
                'atr_pct': buy_trade['atr_pct'],
                'bb_width': buy_trade['bb_width'],
                'macd_hist': buy_trade['macd_hist'],
                'rsi': buy_trade['rsi'],
                'trix_buy': buy_trade['trix_buy']
            }
            buy_sell_matched.append(matched_trade)
    
    if len(buy_sell_matched) == 0:
        print('⚠️ 无法匹配买入卖出记录')
        return
    
    matched_df = pd.DataFrame(buy_sell_matched)
    
    print(f'📊 成功匹配交易: {len(matched_df)} 条')
    
    # 分析盈利vs亏损交易的买入特征
    profitable_trades = matched_df[matched_df['is_profitable']]
    losing_trades = matched_df[~matched_df['is_profitable']]
    
    print(f'\n📈 盈利 vs 亏损交易对比:')
    print(f'   盈利交易: {len(profitable_trades)} 条')
    print(f'   亏损交易: {len(losing_trades)} 条')
    
    # 分析各评分指标的差异
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    print(f'\n🔍 盈利买入点特征分析:')
    for col in score_columns:
        if col in matched_df.columns:
            profitable_values = profitable_trades[col].dropna()
            losing_values = losing_trades[col].dropna()
            
            if len(profitable_values) > 0 and len(losing_values) > 0:
                profitable_avg = profitable_values.mean()
                losing_avg = losing_values.mean()
                diff = profitable_avg - losing_avg
                
                significance = "🔥显著" if abs(diff) > 0.05 else "📊一般" if abs(diff) > 0.02 else "🔹微弱"
                direction = "📈" if diff > 0 else "📉"
                
                print(f'   {col}: 盈利{profitable_avg:.3f} vs 亏损{losing_avg:.3f} ({direction}{diff:+.3f}) [{significance}]')
    
    # 分析技术指标差异
    tech_columns = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    
    print(f'\n📋 技术指标对比:')
    for col in tech_columns:
        if col in matched_df.columns:
            profitable_values = profitable_trades[col].dropna()
            losing_values = losing_trades[col].dropna()
            
            if len(profitable_values) > 0 and len(losing_values) > 0:
                profitable_avg = profitable_values.mean()
                losing_avg = losing_values.mean()
                diff = profitable_avg - losing_avg
                
                significance = "🔥显著" if abs(diff) > profitable_values.std() * 0.5 else "📊一般"
                direction = "📈" if diff > 0 else "📉"
                
                print(f'   {col}: 盈利{profitable_avg:.3f} vs 亏损{losing_avg:.3f} ({direction}{diff:+.3f}) [{significance}]')
    
    # 找出最佳买入条件
    print(f'\n🏆 最佳买入条件识别:')
    
    # 按评分分组分析胜率
    for col in ['overall_score', 'technical_score', 'trend_score']:
        if col in matched_df.columns:
            values = matched_df[col].dropna()
            if len(values) > 20:
                # 按分位数分组
                q75 = values.quantile(0.75)
                q50 = values.quantile(0.50)
                q25 = values.quantile(0.25)
                
                high_score = matched_df[matched_df[col] >= q75]
                mid_score = matched_df[(matched_df[col] >= q25) & (matched_df[col] < q75)]
                low_score = matched_df[matched_df[col] < q25]
                
                if len(high_score) > 0:
                    high_win_rate = (high_score['is_profitable']).mean() * 100
                    print(f'   {col} 高分组 (>={q75:.2f}): {len(high_score)}笔, 胜率{high_win_rate:.1f}%')
                
                if len(mid_score) > 0:
                    mid_win_rate = (mid_score['is_profitable']).mean() * 100
                    print(f'   {col} 中分组 ({q25:.2f}-{q75:.2f}): {len(mid_score)}笔, 胜率{mid_win_rate:.1f}%')
                
                if len(low_score) > 0:
                    low_win_rate = (low_score['is_profitable']).mean() * 100
                    print(f'   {col} 低分组 (<{q25:.2f}): {len(low_score)}笔, 胜率{low_win_rate:.1f}%')

def analyze_market_impact(df):
    """分析市场行情对策略的影响"""
    print(f'\n📊 市场行情影响分析')
    print('=' * 50)
    
    if len(df) == 0:
        print('⚠️ 数据不足')
        return
    
    # 转换时间戳
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # 按时间段分析
    df['date'] = df['timestamp'].dt.date
    df['hour'] = df['timestamp'].dt.hour
    df['weekday'] = df['timestamp'].dt.weekday  # 0=Monday, 6=Sunday
    
    # 分析不同时间段的表现
    sell_records = df[df['action'] == 'SELL'].dropna(subset=['net_profit_pct_sell'])
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录')
        return
    
    print(f'📅 时间段表现分析:')
    
    # 按小时分析
    hourly_performance = sell_records.groupby('hour').agg({
        'net_profit_pct_sell': ['count', 'mean', lambda x: (x > 0).mean() * 100]
    }).round(2)
    hourly_performance.columns = ['交易数', '平均收益%', '胜率%']
    
    # 只显示有足够交易的时间段
    significant_hours = hourly_performance[hourly_performance['交易数'] >= 10]
    if len(significant_hours) > 0:
        print(f'\n   按小时分析 (>=10笔交易):')
        for hour, stats in significant_hours.iterrows():
            print(f'     {hour:02d}:00: {stats["交易数"]}笔, 胜率{stats["胜率%"]:.1f}%, 平均{stats["平均收益%"]:.2f}%')
    
    # 按星期分析
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    weekday_performance = sell_records.groupby('weekday').agg({
        'net_profit_pct_sell': ['count', 'mean', lambda x: (x > 0).mean() * 100]
    }).round(2)
    weekday_performance.columns = ['交易数', '平均收益%', '胜率%']
    
    print(f'\n   按星期分析:')
    for weekday, stats in weekday_performance.iterrows():
        if stats['交易数'] >= 5:  # 至少5笔交易
            weekday_name = weekday_names[weekday] if weekday < 7 else f'星期{weekday}'
            print(f'     {weekday_name}: {stats["交易数"]}笔, 胜率{stats["胜率%"]:.1f}%, 平均{stats["平均收益%"]:.2f}%')
    
    # 分析交易密度
    daily_trades = df.groupby('date').size()
    
    print(f'\n📈 交易密度分析:')
    print(f'   日均交易: {daily_trades.mean():.1f} 笔')
    print(f'   最多单日: {daily_trades.max()} 笔')
    print(f'   最少单日: {daily_trades.min()} 笔')
    
    # 分析市场波动对策略的影响
    print(f'\n🌊 市场波动影响:')
    
    # 按ATR分组分析
    buy_records = df[df['action'] == 'BUY']
    if 'atr_pct' in buy_records.columns:
        atr_values = buy_records['atr_pct'].dropna()
        if len(atr_values) > 20:
            atr_q75 = atr_values.quantile(0.75)
            atr_q25 = atr_values.quantile(0.25)
            
            print(f'   高波动期 (ATR>{atr_q75:.3f}): {len(buy_records[buy_records["atr_pct"] > atr_q75])} 笔买入')
            print(f'   低波动期 (ATR<{atr_q25:.3f}): {len(buy_records[buy_records["atr_pct"] < atr_q25])} 笔买入')

def check_future_function_bias(df):
    """检查是否存在未来函数"""
    print(f'\n🔍 未来函数检查')
    print('=' * 50)
    
    if len(df) == 0:
        print('⚠️ 数据不足')
        return
    
    print('🎯 检查要点:')
    print('   1. 买卖信号是否基于实时数据')
    print('   2. 技术指标计算是否使用未来数据')
    print('   3. 评分系统是否存在前瞻偏差')
    
    # 检查时间序列的合理性
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df_sorted = df.sort_values('timestamp')
    
    print(f'\n📊 时间序列检查:')
    print(f'   数据时间跨度: {df_sorted["timestamp"].min()} 到 {df_sorted["timestamp"].max()}')
    print(f'   总时间跨度: {(df_sorted["timestamp"].max() - df_sorted["timestamp"].min()).days} 天')
    
    # 检查买入卖出的时间逻辑
    buy_records = df[df['action'] == 'BUY'].sort_values('timestamp')
    sell_records = df[df['action'] == 'SELL'].sort_values('timestamp')
    
    print(f'\n⏰ 买卖时序检查:')
    print(f'   买入记录: {len(buy_records)} 条')
    print(f'   卖出记录: {len(sell_records)} 条')
    
    if len(buy_records) > 0 and len(sell_records) > 0:
        first_buy = buy_records['timestamp'].min()
        first_sell = sell_records['timestamp'].min()
        
        print(f'   首次买入: {first_buy}')
        print(f'   首次卖出: {first_sell}')
        
        if first_sell < first_buy:
            print('   ⚠️ 警告: 首次卖出早于首次买入，可能存在逻辑问题')
        else:
            print('   ✅ 买卖时序正常')
    
    # 检查技术指标的合理性
    print(f'\n📋 技术指标检查:')
    
    technical_indicators = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    
    for indicator in technical_indicators:
        if indicator in df.columns:
            values = df[indicator].dropna()
            if len(values) > 0:
                # 检查是否有异常值
                q99 = values.quantile(0.99)
                q01 = values.quantile(0.01)
                outliers = len(values[(values > q99) | (values < q01)])
                
                print(f'   {indicator}: 范围[{values.min():.3f}, {values.max():.3f}], 异常值{outliers}个')
                
                # 特定指标的合理性检查
                if indicator == 'rsi':
                    invalid_rsi = len(values[(values < 0) | (values > 100)])
                    if invalid_rsi > 0:
                        print(f'     ⚠️ RSI值超出0-100范围: {invalid_rsi}个')
                    else:
                        print(f'     ✅ RSI值范围正常')
    
    # 检查评分系统
    print(f'\n🎯 评分系统检查:')
    
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    for score in score_columns:
        if score in df.columns:
            values = df[score].dropna()
            if len(values) > 0:
                # 检查评分范围
                if values.min() < 0 or values.max() > 1:
                    print(f'   {score}: 范围[{values.min():.3f}, {values.max():.3f}] ⚠️ 超出0-1范围')
                else:
                    print(f'   {score}: 范围[{values.min():.3f}, {values.max():.3f}] ✅ 正常')
    
    print(f'\n🔍 未来函数风险评估:')
    
    risk_factors = []
    
    # 检查是否有完美的预测
    sell_records = df[df['action'] == 'SELL'].dropna(subset=['net_profit_pct_sell'])
    if len(sell_records) > 0:
        win_rate = (sell_records['net_profit_pct_sell'] > 0).mean() * 100
        if win_rate > 80:
            risk_factors.append(f'胜率过高 ({win_rate:.1f}%)，可能存在前瞻偏差')
        
        # 检查是否有异常的收益分布
        profits = sell_records['net_profit_pct_sell']
        if profits.std() < 0.5:
            risk_factors.append('收益分布过于集中，可能存在过拟合')
    
    # 检查买入信号的分布
    buy_records = df[df['action'] == 'BUY']
    if len(buy_records) > 0:
        # 检查评分分布是否过于集中在高分区
        if 'overall_score' in buy_records.columns:
            high_score_ratio = (buy_records['overall_score'] > 0.8).mean()
            if high_score_ratio > 0.8:
                risk_factors.append('买入信号过于集中在高分区，可能存在过拟合')
    
    if risk_factors:
        print('   ⚠️ 发现潜在风险:')
        for i, risk in enumerate(risk_factors, 1):
            print(f'     {i}. {risk}')
    else:
        print('   ✅ 未发现明显的未来函数风险')
    
    print(f'\n💡 建议:')
    print('   1. 确保所有技术指标只使用历史数据计算')
    print('   2. 验证买卖信号的生成逻辑')
    print('   3. 检查评分系统是否使用了未来信息')
    print('   4. 进行样本外测试验证策略稳定性')

def main():
    """主函数"""
    print('🚀 全面回测数据分析')
    print('=' * 60)
    
    # 核心指标分析
    df, buy_records, completed_trades = analyze_core_metrics()
    
    if df is not None:
        # 盈利买入点分析
        analyze_profitable_entry_points(buy_records, completed_trades)
        
        # 市场行情影响分析
        analyze_market_impact(df)
        
        # 未来函数检查
        check_future_function_bias(df)
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print('✅ 全面回测数据分析完成')
        print('📊 已分析核心指标、买入点、市场影响')
        print('🔍 已检查未来函数风险')
        print('')
        print('💡 请根据分析结果进行策略优化')
    else:
        print('❌ 无法获取回测数据')

if __name__ == '__main__':
    main()
