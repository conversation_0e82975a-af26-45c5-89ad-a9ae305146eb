# coding=utf-8
"""
深度因子分析
检查胜率未变化的真实原因，分析因子实际生效情况
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def check_recent_trading_data():
    """检查最近的交易数据"""
    print('📊 最近交易数据检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 检查最近的买入记录
        buy_query = """
        SELECT 
            timestamp,
            symbol,
            action,
            price,
            cci,
            rsi,
            adx,
            macd_hist,
            atr_pct,
            overall_score,
            strftime('%H:%M', timestamp) as trade_time,
            DATE(timestamp) as trade_date
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 50
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        
        print(f'📈 最近50条买入记录:')
        print(f'总记录数: {len(buy_df)}')
        
        if len(buy_df) > 0:
            # 分析交易时间分布
            print(f'\n⏰ 交易时间分布:')
            time_dist = buy_df['trade_time'].value_counts().sort_index()
            for time, count in time_dist.items():
                print(f'   {time}: {count}条')
            
            # 分析交易日期分布
            print(f'\n📅 交易日期分布:')
            date_dist = buy_df['trade_date'].value_counts().sort_index()
            for date, count in date_dist.items():
                print(f'   {date}: {count}条')
            
            # 分析因子值分布
            print(f'\n📊 因子值统计:')
            factor_cols = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct', 'overall_score']
            for col in factor_cols:
                if col in buy_df.columns:
                    valid_data = buy_df[col].dropna()
                    if len(valid_data) > 0:
                        print(f'   {col.upper()}: 均值{valid_data.mean():.2f}, 中位{valid_data.median():.2f}, 标准差{valid_data.std():.2f}')
                        print(f'            范围[{valid_data.min():.2f}, {valid_data.max():.2f}], 有效数据{len(valid_data)}条')
        
        # 检查最近的卖出记录和胜率
        sell_query = """
        SELECT 
            timestamp,
            symbol,
            action,
            price,
            net_profit_pct_sell,
            strftime('%H:%M', timestamp) as trade_time,
            DATE(timestamp) as trade_date
        FROM trades 
        WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 100
        """
        
        sell_df = pd.read_sql_query(sell_query, conn)
        
        print(f'\n📉 最近100条卖出记录胜率分析:')
        if len(sell_df) > 0:
            profitable = sell_df[sell_df['net_profit_pct_sell'] > 0]
            losing = sell_df[sell_df['net_profit_pct_sell'] <= 0]
            
            win_rate = len(profitable) / len(sell_df) * 100
            avg_profit = sell_df['net_profit_pct_sell'].mean()
            
            print(f'   总卖出: {len(sell_df)}条')
            print(f'   盈利: {len(profitable)}条')
            print(f'   亏损: {len(losing)}条')
            print(f'   胜率: {win_rate:.1f}%')
            print(f'   平均收益: {avg_profit:.2f}%')
            
            # 按日期分析胜率变化
            print(f'\n📈 按日期胜率变化:')
            sell_df['profitable'] = sell_df['net_profit_pct_sell'] > 0
            daily_stats = sell_df.groupby('trade_date').agg({
                'profitable': ['count', 'sum', 'mean'],
                'net_profit_pct_sell': 'mean'
            }).round(3)
            
            for date in daily_stats.index[-10:]:  # 最近10天
                total = daily_stats.loc[date, ('profitable', 'count')]
                wins = daily_stats.loc[date, ('profitable', 'sum')]
                win_rate_daily = daily_stats.loc[date, ('profitable', 'mean')] * 100
                avg_profit_daily = daily_stats.loc[date, ('net_profit_pct_sell', 'mean')]
                print(f'   {date}: {wins}/{total} = {win_rate_daily:.1f}%, 平均收益{avg_profit_daily:.2f}%')
        
        conn.close()
        return buy_df, sell_df
        
    except Exception as e:
        print(f'❌ 数据库检查失败: {e}')
        return None, None

def analyze_factor_configuration_effectiveness():
    """分析因子配置的实际生效情况"""
    print(f'\n🔧 因子配置生效情况分析')
    print('=' * 60)
    
    # 检查配置文件中的关键设置
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        print(f'📋 关键配置检查:')
        
        # 检查买入时间配置
        if 'BUY_CHECK_TIMES' in config_content:
            print(f'   ✅ BUY_CHECK_TIMES 配置存在')
            # 提取配置内容
            import re
            pattern = r'BUY_CHECK_TIMES\s*=\s*\[(.*?)\]'
            match = re.search(pattern, config_content, re.DOTALL)
            if match:
                times_content = match.group(1)
                times = re.findall(r"'(\d{2}:\d{2}:\d{2})'", times_content)
                print(f'   配置的检查时间: {times}')
        
        # 检查实时因子配置
        if 'FACTOR_REALTIME_CONFIG' in config_content:
            print(f'   ✅ FACTOR_REALTIME_CONFIG 配置存在')
        else:
            print(f'   ❌ FACTOR_REALTIME_CONFIG 配置缺失')
        
        # 检查USE_REALTIME_PRICE
        if 'USE_REALTIME_PRICE = True' in config_content:
            print(f'   ✅ USE_REALTIME_PRICE = True')
        else:
            print(f'   ⚠️ USE_REALTIME_PRICE 可能未启用')
        
        # 检查因子周期配置
        if 'cci_period' in config_content:
            print(f'   ✅ 短周期因子配置存在')
        else:
            print(f'   ❌ 短周期因子配置缺失')
            
    except Exception as e:
        print(f'❌ 配置文件检查失败: {e}')

def check_factor_calculation_logic():
    """检查因子计算逻辑是否真正使用了新配置"""
    print(f'\n🔍 因子计算逻辑检查')
    print('=' * 60)
    
    analysis = '''
🚨 关键问题分析:

1. 📊 配置vs实际实现的差距:
   - 我们修改了config.py中的配置
   - 但因子计算代码可能没有使用这些配置
   - enhanced_factor_engine.py中可能仍使用硬编码的周期

2. 🔧 因子计算代码检查要点:
   - CCI计算: talib.CCI(high, low, close, timeperiod=14)
   - RSI计算: talib.RSI(close, timeperiod=14)  
   - ADX计算: talib.ADX(high, low, close, timeperiod=14)
   - 这些可能仍然使用固定的14天周期

3. ⏰ 更新频率问题:
   - BUY_CHECK_TIMES配置了新的时间点
   - 但策略执行逻辑可能没有使用这个配置
   - 可能仍然按照旧的时间间隔执行

4. 🎯 实时价格替代问题:
   - USE_REALTIME_PRICE = True已配置
   - 但具体的因子计算函数可能没有正确实现
   - 可能只在某些模块中生效，其他模块仍用历史数据

🔍 需要检查的关键文件:
   1. enhanced_factor_engine.py - 因子计算核心逻辑
   2. signal_calculator.py - 信号计算逻辑
   3. main.py - 主策略执行逻辑
   4. history_data_manager.py - 数据获取逻辑

💡 可能的问题:
   - 配置修改了，但代码没有读取新配置
   - 因子计算仍使用硬编码参数
   - 实时价格替代没有在所有模块中生效
   - 新的检查时间点没有被策略使用
'''
    
    print(analysis)

def analyze_database_factor_patterns():
    """分析数据库中因子的实际模式"""
    print(f'\n📊 数据库因子模式分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 分析因子值的时间变化模式
        query = """
        SELECT 
            timestamp,
            symbol,
            cci,
            rsi,
            adx,
            macd_hist,
            atr_pct,
            strftime('%H:%M', timestamp) as trade_time,
            strftime('%Y-%m-%d %H', timestamp) as trade_hour
        FROM trades 
        WHERE action = 'BUY'
        AND timestamp >= datetime('now', '-3 days')
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        
        if len(df) > 0:
            print(f'📈 最近3天买入记录: {len(df)}条')
            
            # 分析因子值是否真的在变化
            print(f'\n🔍 因子值变化分析:')
            factor_cols = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct']
            
            for col in factor_cols:
                if col in df.columns:
                    valid_data = df[col].dropna()
                    if len(valid_data) > 1:
                        # 计算变异系数
                        cv = valid_data.std() / valid_data.mean() if valid_data.mean() != 0 else 0
                        # 计算相邻值的变化
                        changes = valid_data.diff().abs().mean()
                        print(f'   {col.upper()}: 变异系数{cv:.3f}, 平均变化{changes:.3f}')
                        
                        # 检查是否有明显的时间模式
                        if len(valid_data) > 10:
                            # 按小时分组看是否有差异
                            df_with_factor = df[df[col].notna()].copy()
                            df_with_factor['hour'] = pd.to_datetime(df_with_factor['timestamp']).dt.hour
                            hour_stats = df_with_factor.groupby('hour')[col].agg(['mean', 'std', 'count'])
                            
                            if len(hour_stats) > 1:
                                hour_variance = hour_stats['mean'].std()
                                print(f'   {col.upper()} 小时间差异: {hour_variance:.3f}')
            
            # 分析交易时间是否真的分散了
            print(f'\n⏰ 交易时间分散情况:')
            time_dist = df['trade_time'].value_counts().sort_index()
            
            opening_trades = 0
            total_trades = len(df)
            
            for time, count in time_dist.items():
                hour, minute = map(int, time.split(':'))
                is_opening = (hour == 9 and minute >= 30) or (hour == 10 and minute == 0)
                if is_opening:
                    opening_trades += count
                print(f'   {time}: {count}条 {"(开盘时段)" if is_opening else ""}')
            
            opening_pct = opening_trades / total_trades * 100 if total_trades > 0 else 0
            print(f'\n📊 开盘时段买入比例: {opening_pct:.1f}% (目标: <30%)')
            
            if opening_pct > 70:
                print(f'   🚨 问题: 开盘时段买入仍然过多，配置可能未生效')
            elif opening_pct > 30:
                print(f'   ⚠️ 注意: 开盘时段买入仍较多，需要进一步优化')
            else:
                print(f'   ✅ 良好: 开盘时段买入比例已降低')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 数据库模式分析失败: {e}')

def check_code_implementation_gaps():
    """检查代码实现的差距"""
    print(f'\n🔧 代码实现差距检查')
    print('=' * 60)
    
    gaps_analysis = '''
🚨 可能的实现差距:

1. 📊 因子计算周期问题:
   问题: enhanced_factor_engine.py中可能仍使用硬编码的timeperiod=14
   检查: 搜索代码中的"timeperiod=14"
   解决: 需要修改为读取配置中的周期参数

2. ⏰ 买入检查时间问题:
   问题: 主策略可能没有使用BUY_CHECK_TIMES配置
   检查: 主策略是否按照新的时间点执行
   解决: 确保策略读取并使用新的检查时间

3. 🔄 实时价格替代问题:
   问题: 可能只在部分模块中生效
   检查: 所有因子计算是否都使用了实时价格
   解决: 统一实时价格替代逻辑

4. 📈 配置读取问题:
   问题: 因子计算代码可能没有读取FACTOR_REALTIME_CONFIG
   检查: 代码是否使用了新的配置参数
   解决: 修改因子计算逻辑读取配置

🔍 需要立即检查的代码位置:
   1. enhanced_factor_engine.py 第310-330行 (CCI, RSI, ADX计算)
   2. signal_calculator.py 中的因子计算调用
   3. main.py 中的策略执行时间逻辑
   4. 配置读取和应用的代码

💡 诊断方法:
   1. 检查最近的交易时间是否真的分散了
   2. 检查因子值是否真的在变化
   3. 检查代码是否读取了新配置
   4. 检查实时价格是否真的被使用
'''
    
    print(gaps_analysis)

def generate_action_plan():
    """生成行动计划"""
    print(f'\n🚀 立即行动计划')
    print('=' * 60)
    
    action_plan = '''
🎯 基于分析的立即行动计划:

阶段1: 诊断问题根源 (立即执行)
   1. 🔍 检查因子计算代码是否使用了新配置
   2. 📊 验证交易时间是否真的分散了
   3. ⏰ 确认实时价格替代是否在所有模块生效
   4. 🔧 检查配置读取逻辑是否正确

阶段2: 修复实现差距 (紧急)
   1. 修改enhanced_factor_engine.py使用配置的周期参数
   2. 确保主策略使用新的BUY_CHECK_TIMES
   3. 统一所有模块的实时价格替代逻辑
   4. 验证配置参数的正确读取和应用

阶段3: 验证修复效果 (持续)
   1. 监控交易时间分布变化
   2. 检查因子值的实时变化
   3. 验证胜率是否开始改善
   4. 调整参数直到达到预期效果

🚨 关键检查点:
   - 如果交易时间仍然集中在开盘，说明BUY_CHECK_TIMES未生效
   - 如果因子值变化很小，说明实时计算未生效
   - 如果胜率没有变化，说明根本问题未解决
   - 需要从代码层面确保配置真正生效

💡 下一步具体行动:
   1. 立即检查enhanced_factor_engine.py的因子计算逻辑
   2. 验证主策略是否使用了新的时间配置
   3. 确认实时价格在因子计算中的实际使用情况
   4. 修复发现的实现差距
'''
    
    print(action_plan)

def main():
    """主函数"""
    print('🔍 深度因子分析 - 胜率未变化原因挖掘')
    print('=' * 60)
    
    print('🚨 问题: 胜率没有任何变化，需要深入分析真实原因')
    
    # 检查最近交易数据
    buy_df, sell_df = check_recent_trading_data()
    
    # 分析因子配置生效情况
    analyze_factor_configuration_effectiveness()
    
    # 检查因子计算逻辑
    check_factor_calculation_logic()
    
    # 分析数据库因子模式
    analyze_database_factor_patterns()
    
    # 检查代码实现差距
    check_code_implementation_gaps()
    
    # 生成行动计划
    generate_action_plan()
    
    print(f'\n🎯 核心结论')
    print('=' * 40)
    print('🚨 胜率未变化的可能原因:')
    print('   1. 配置修改了，但代码没有使用新配置')
    print('   2. 因子计算仍使用硬编码参数')
    print('   3. 交易时间可能没有真正分散')
    print('   4. 实时价格替代可能没有全面生效')
    
    print(f'\n🔧 立即需要检查:')
    print('   1. enhanced_factor_engine.py的实际实现')
    print('   2. 主策略的时间执行逻辑')
    print('   3. 配置读取和应用的代码')
    print('   4. 最近交易的实际时间分布')

if __name__ == '__main__':
    main()
