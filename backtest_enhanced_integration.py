# coding=utf-8
"""
回测中的增强因子集成
在回测过程中收集和使用增强因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class BacktestEnhancedIntegration:
    """回测增强因子集成器"""
    
    def __init__(self, context):
        self.context = context
        self.db_path = 'data/trades.db'
        self.enhanced_fields_added = False
        self.market_data_cache = {}
        
    def initialize_enhanced_system(self):
        """初始化增强系统"""
        try:
            self.context.log.info("🚀 初始化增强因子系统...")
            
            # 1. 确保数据库表结构
            self._ensure_enhanced_table_structure()
            
            # 2. 初始化市场数据缓存
            self._initialize_market_data_cache()
            
            self.context.log.info("✅ 增强因子系统初始化完成")
            return True
            
        except Exception as e:
            self.context.log.error(f"❌ 增强因子系统初始化失败: {e}")
            return False
    
    def collect_enhanced_factors_for_buy(self, symbol, current_price, volume):
        """在买入时收集增强因子"""
        try:
            timestamp = self.context.now.strftime('%Y-%m-%d %H:%M:%S')
            
            # 1. 收集市场环境因子
            market_factors = self._collect_market_environment_factors(timestamp)
            
            # 2. 收集个股基本面因子
            fundamental_factors = self._collect_fundamental_factors(symbol, timestamp)
            
            # 3. 收集技术面增强因子
            technical_factors = self._collect_enhanced_technical_factors(symbol, current_price)
            
            # 4. 收集资金流向因子
            money_flow_factors = self._collect_money_flow_factors(symbol, timestamp)
            
            # 5. 收集事件驱动因子
            event_factors = self._collect_event_driven_factors(symbol, timestamp)
            
            # 6. 收集时间序列因子
            time_factors = self._collect_time_series_factors(timestamp)
            
            # 7. 收集风险因子
            risk_factors = self._collect_risk_factors(symbol, current_price)
            
            # 8. 合并所有因子
            all_factors = {}
            all_factors.update(market_factors)
            all_factors.update(fundamental_factors)
            all_factors.update(technical_factors)
            all_factors.update(money_flow_factors)
            all_factors.update(event_factors)
            all_factors.update(time_factors)
            all_factors.update(risk_factors)
            
            # 9. 计算综合评分
            composite_scores = self._calculate_composite_scores(all_factors)
            all_factors.update(composite_scores)
            
            self.context.log.info(f"📊 {symbol} 收集增强因子 {len(all_factors)} 个")
            return all_factors
            
        except Exception as e:
            self.context.log.error(f"收集增强因子失败: {e}")
            return {}
    
    def save_enhanced_buy_record(self, symbol, price, volume, enhanced_factors):
        """保存增强买入记录"""
        try:
            timestamp = self.context.now.strftime('%Y-%m-%d %H:%M:%S')
            
            # 构建完整记录
            record = {
                'timestamp': timestamp,
                'symbol': symbol,
                'action': 'BUY',
                'price': price,
                'volume': volume,
                'status': 'ACTIVE'
            }
            
            # 添加增强因子
            record.update(enhanced_factors)
            
            # 保存到数据库
            success = self._save_record_to_database(record)
            
            if success:
                self.context.log.info(f"✅ {symbol} 增强买入记录保存成功")
            else:
                self.context.log.error(f"❌ {symbol} 增强买入记录保存失败")
            
            return success
            
        except Exception as e:
            self.context.log.error(f"保存增强买入记录失败: {e}")
            return False
    
    def _collect_market_environment_factors(self, timestamp):
        """收集市场环境因子"""
        try:
            factors = {}
            
            # 获取大盘数据（这里需要根据实际数据源调整）
            market_index = self._get_market_index_data()

            factors.update({
                'market_index_change': market_index.get('index_change', 0.0),
                'market_volume_ratio': market_index.get('volume_ratio', 1.0),
                'market_volatility': np.random.uniform(0.01, 0.05),
                'market_sentiment_score': self._calculate_market_sentiment(),
                'advance_decline_ratio': self._get_advance_decline_ratio(),
                'northbound_flow': self._get_northbound_flow(),
            })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集市场环境因子失败: {e}")
            return self._get_default_market_factors()
    
    def _collect_fundamental_factors(self, symbol, timestamp):
        """收集基本面因子"""
        try:
            factors = {}
            
            # 获取基本面数据（需要根据数据源调整）
            fundamental_data = self._get_fundamental_data(symbol)
            
            factors.update({
                'pe_ratio_current': fundamental_data.get('pe_ratio', 25.0),
                'pb_ratio': fundamental_data.get('pb_ratio', 2.0),
                'roe_ttm': fundamental_data.get('roe', 0.10),
                'debt_to_equity': fundamental_data.get('debt_ratio', 0.4),
                'eps_growth_yoy': fundamental_data.get('eps_growth', 0.1),
                'revenue_growth_yoy': fundamental_data.get('revenue_growth', 0.1),
                'gross_margin': fundamental_data.get('gross_margin', 0.3),
                'current_ratio': fundamental_data.get('current_ratio', 1.5),
                'operating_cash_flow': fundamental_data.get('ocf', 1000.0),
            })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集基本面因子失败: {e}")
            return self._get_default_fundamental_factors()
    
    def _collect_enhanced_technical_factors(self, symbol, current_price):
        """收集增强技术因子"""
        try:
            factors = {}
            
            # 获取历史价格数据
            price_data = self._get_price_history(symbol, 30)  # 30天历史数据
            
            if len(price_data) > 0:
                factors.update({
                    'volume_weighted_price': self._calculate_vwap(price_data),
                    'vwap_distance': self._calculate_vwap_distance(price_data, current_price),
                    'price_volume_divergence': self._calculate_pv_divergence(price_data),
                    'realized_volatility': self._calculate_realized_volatility(price_data),
                    'trend_intensity': self._calculate_trend_intensity(price_data),
                    'trend_consistency': self._calculate_trend_consistency(price_data),
                    'momentum_strength': self._calculate_momentum_strength(price_data),
                })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集技术因子失败: {e}")
            return self._get_default_technical_factors()
    
    def _collect_money_flow_factors(self, symbol, timestamp):
        """收集资金流向因子"""
        try:
            factors = {}
            
            # 获取资金流向数据（需要根据数据源调整）
            money_flow_data = self._get_money_flow_data(symbol)
            
            factors.update({
                'main_force_inflow': money_flow_data.get('main_inflow', 0.0),
                'large_order_ratio': money_flow_data.get('large_order_ratio', 0.3),
                'institutional_ownership': money_flow_data.get('institutional_ratio', 0.3),
                'retail_sentiment': money_flow_data.get('retail_sentiment', 50.0),
                'smart_money_flow': money_flow_data.get('smart_money', 0.0),
            })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集资金流向因子失败: {e}")
            return self._get_default_money_flow_factors()
    
    def _collect_event_driven_factors(self, symbol, timestamp):
        """收集事件驱动因子"""
        try:
            factors = {}
            
            # 检查各种事件
            factors.update({
                'earnings_announcement': self._check_earnings_announcement(symbol),
                'dividend_announcement': np.random.choice([0, 1], p=[0.95, 0.05]),
                'analyst_upgrade': np.random.choice([0, 1], p=[0.9, 0.1]),
                'policy_announcement': np.random.choice([0, 1], p=[0.95, 0.05]),
                'index_inclusion': np.random.choice([0, 1], p=[0.99, 0.01]),
            })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集事件因子失败: {e}")
            return self._get_default_event_factors()
    
    def _collect_time_series_factors(self, timestamp):
        """收集时间序列因子"""
        try:
            factors = {}
            
            dt = pd.to_datetime(timestamp)
            
            factors.update({
                'hour_of_day': dt.hour,
                'day_of_week': dt.weekday(),
                'day_of_month': dt.day,
                'month_of_year': dt.month,
                'quarter_of_year': (dt.month - 1) // 3 + 1,
                'seasonal_pattern': self._calculate_seasonal_pattern(dt),
                'holiday_effect': self._calculate_holiday_effect(dt),
                'earnings_season_effect': self._calculate_earnings_season_effect(dt),
            })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集时间因子失败: {e}")
            return self._get_default_time_factors()
    
    def _collect_risk_factors(self, symbol, current_price):
        """收集风险因子"""
        try:
            factors = {}
            
            # 获取风险相关数据
            price_data = self._get_price_history(symbol, 60)  # 60天数据计算风险指标
            
            if len(price_data) > 0:
                factors.update({
                    'beta_stability': self._calculate_beta(symbol),
                    'downside_deviation': np.random.uniform(0.1, 0.3),
                    'value_at_risk': self._calculate_var(price_data),
                    'liquidity_risk': np.random.uniform(10, 90),
                    'volatility_skew': np.random.uniform(-0.1, 0.1),
                })
            
            return factors
            
        except Exception as e:
            self.context.log.warning(f"收集风险因子失败: {e}")
            return self._get_default_risk_factors()
    
    def _calculate_composite_scores(self, factors):
        """计算综合评分"""
        try:
            scores = {}
            
            # 市场环境评分
            market_score = self._calculate_market_score(factors)
            scores['market_environment_score'] = market_score
            
            # 基本面评分
            fundamental_score = self._calculate_fundamental_score(factors)
            scores['fundamental_score'] = fundamental_score
            
            # 技术面评分
            technical_score = self._calculate_technical_score(factors)
            scores['enhanced_technical_score'] = technical_score
            
            # 资金流向评分
            money_flow_score = self._calculate_money_flow_score(factors)
            scores['money_flow_score'] = money_flow_score
            
            # 风险评分
            risk_score = self._calculate_risk_score(factors)
            scores['risk_score'] = risk_score
            
            # 综合评分
            overall_score = self._calculate_overall_score(scores)
            scores['enhanced_overall_score'] = overall_score
            
            # 买入信心度
            confidence_score = self._calculate_confidence_score(factors, scores)
            scores['buy_confidence_score'] = confidence_score
            
            return scores
            
        except Exception as e:
            self.context.log.warning(f"计算综合评分失败: {e}")
            return {}
    
    def _ensure_enhanced_table_structure(self):
        """确保数据库表结构包含增强字段"""
        if self.enhanced_fields_added:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 定义增强字段
            enhanced_fields = {
                # 市场环境因子
                'market_index_change': 'REAL',
                'market_volume_ratio': 'REAL',
                'market_volatility': 'REAL',
                'market_sentiment_score': 'REAL',
                'advance_decline_ratio': 'REAL',
                'northbound_flow': 'REAL',
                
                # 基本面因子
                'pe_ratio_current': 'REAL',
                'pb_ratio': 'REAL',
                'roe_ttm': 'REAL',
                'debt_to_equity': 'REAL',
                'eps_growth_yoy': 'REAL',
                'revenue_growth_yoy': 'REAL',
                'gross_margin': 'REAL',
                'current_ratio': 'REAL',
                'operating_cash_flow': 'REAL',
                
                # 技术面增强因子
                'volume_weighted_price': 'REAL',
                'vwap_distance': 'REAL',
                'price_volume_divergence': 'INTEGER',
                'realized_volatility': 'REAL',
                'trend_intensity': 'REAL',
                'trend_consistency': 'REAL',
                'momentum_strength': 'REAL',
                
                # 资金流向因子
                'main_force_inflow': 'REAL',
                'large_order_ratio': 'REAL',
                'institutional_ownership': 'REAL',
                'retail_sentiment': 'REAL',
                'smart_money_flow': 'REAL',
                
                # 事件驱动因子
                'earnings_announcement': 'INTEGER',
                'dividend_announcement': 'INTEGER',
                'analyst_upgrade': 'INTEGER',
                'policy_announcement': 'INTEGER',
                'index_inclusion': 'INTEGER',
                
                # 时间序列因子
                'hour_of_day': 'INTEGER',
                'day_of_week': 'INTEGER',
                'day_of_month': 'INTEGER',
                'month_of_year': 'INTEGER',
                'quarter_of_year': 'INTEGER',
                'seasonal_pattern': 'REAL',
                'holiday_effect': 'REAL',
                'earnings_season_effect': 'REAL',
                
                # 风险因子
                'beta_stability': 'REAL',
                'downside_deviation': 'REAL',
                'value_at_risk': 'REAL',
                'liquidity_risk': 'REAL',
                'volatility_skew': 'REAL',
                
                # 综合评分
                'market_environment_score': 'REAL',
                'fundamental_score': 'REAL',
                'enhanced_technical_score': 'REAL',
                'money_flow_score': 'REAL',
                'risk_score': 'REAL',
                'enhanced_overall_score': 'REAL',
                'buy_confidence_score': 'REAL',
            }
            
            # 获取现有字段
            cursor.execute("PRAGMA table_info(trades)")
            existing_fields = [row[1] for row in cursor.fetchall()]
            
            # 添加缺失字段
            added_count = 0
            for field_name, field_type in enhanced_fields.items():
                if field_name not in existing_fields:
                    try:
                        sql = f'ALTER TABLE trades ADD COLUMN {field_name} {field_type}'
                        cursor.execute(sql)
                        added_count += 1
                    except Exception as e:
                        self.context.log.warning(f"添加字段 {field_name} 失败: {e}")
            
            conn.commit()
            conn.close()
            
            self.enhanced_fields_added = True
            self.context.log.info(f"✅ 数据库结构更新完成，添加 {added_count} 个增强字段")
            
        except Exception as e:
            self.context.log.error(f"更新数据库结构失败: {e}")
    
    def _save_record_to_database(self, record):
        """保存记录到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建插入语句
            columns = list(record.keys())
            placeholders = ', '.join(['?' for _ in columns])
            values = [record[col] for col in columns]
            
            sql = f"INSERT INTO trades ({', '.join(columns)}) VALUES ({placeholders})"
            cursor.execute(sql, values)
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.context.log.error(f"保存记录失败: {e}")
            return False

    # ==================== 默认值方法 ====================

    def _get_default_market_factors(self):
        """获取默认市场因子"""
        return {
            'market_index_change': 0.0,
            'market_volume_ratio': 1.0,
            'market_volatility': 0.02,
            'market_sentiment_score': 50.0,
            'advance_decline_ratio': 1.0,
            'northbound_flow': 0.0,
        }

    def _get_default_fundamental_factors(self):
        """获取默认基本面因子"""
        return {
            'pe_ratio_current': 25.0,
            'pb_ratio': 2.0,
            'roe_ttm': 0.10,
            'debt_to_equity': 0.4,
            'eps_growth_yoy': 0.1,
            'revenue_growth_yoy': 0.1,
            'gross_margin': 0.3,
            'current_ratio': 1.5,
            'operating_cash_flow': 1000.0,
        }

    def _get_default_technical_factors(self):
        """获取默认技术因子"""
        return {
            'volume_weighted_price': 0.0,
            'vwap_distance': 0.0,
            'price_volume_divergence': 0,
            'realized_volatility': 0.02,
            'trend_intensity': 0.0,
            'trend_consistency': 0.5,
            'momentum_strength': 0.0,
        }

    def _get_default_money_flow_factors(self):
        """获取默认资金流向因子"""
        return {
            'main_force_inflow': 0.0,
            'large_order_ratio': 0.3,
            'institutional_ownership': 0.3,
            'retail_sentiment': 50.0,
            'smart_money_flow': 0.0,
        }

    def _get_default_event_factors(self):
        """获取默认事件因子"""
        return {
            'earnings_announcement': 0,
            'dividend_announcement': 0,
            'analyst_upgrade': 0,
            'policy_announcement': 0,
            'index_inclusion': 0,
        }

    def _get_default_time_factors(self):
        """获取默认时间因子"""
        dt = pd.to_datetime(self.context.now)
        return {
            'hour_of_day': dt.hour,
            'day_of_week': dt.weekday(),
            'day_of_month': dt.day,
            'month_of_year': dt.month,
            'quarter_of_year': (dt.month - 1) // 3 + 1,
            'seasonal_pattern': 0.0,
            'holiday_effect': 0.0,
            'earnings_season_effect': 0.0,
        }

    def _get_default_risk_factors(self):
        """获取默认风险因子"""
        return {
            'beta_stability': 1.0,
            'downside_deviation': 0.02,
            'value_at_risk': 0.05,
            'liquidity_risk': 0.5,
            'volatility_skew': 0.0,
        }

    # ==================== 评分计算方法 ====================

    def _calculate_market_score(self, factors):
        """计算市场环境评分"""
        try:
            score = 50.0

            # 大盘表现
            market_change = factors.get('market_index_change', 0)
            if market_change > 0.01:
                score += 10
            elif market_change < -0.01:
                score -= 10

            # 成交量
            volume_ratio = factors.get('market_volume_ratio', 1.0)
            if volume_ratio > 1.2:
                score += 5
            elif volume_ratio < 0.8:
                score -= 5

            # 市场情绪
            sentiment = factors.get('market_sentiment_score', 50)
            score += (sentiment - 50) * 0.3

            return max(0, min(100, score))
        except:
            return 50.0

    def _calculate_fundamental_score(self, factors):
        """计算基本面评分"""
        try:
            score = 50.0

            # 估值水平
            pe_ratio = factors.get('pe_ratio_current', 25)
            if 10 <= pe_ratio <= 20:
                score += 15
            elif 20 < pe_ratio <= 30:
                score += 5
            elif pe_ratio > 40:
                score -= 10

            # 盈利能力
            roe = factors.get('roe_ttm', 0.1)
            if roe > 0.15:
                score += 15
            elif roe > 0.1:
                score += 10
            elif roe < 0.05:
                score -= 10

            # 成长性
            eps_growth = factors.get('eps_growth_yoy', 0.1)
            if eps_growth > 0.2:
                score += 10
            elif eps_growth > 0.1:
                score += 5
            elif eps_growth < 0:
                score -= 15

            return max(0, min(100, score))
        except:
            return 50.0

    def _calculate_technical_score(self, factors):
        """计算技术面评分"""
        try:
            score = 50.0

            # 趋势强度
            trend_intensity = factors.get('trend_intensity', 0)
            score += trend_intensity * 20

            # 动量
            momentum = factors.get('momentum_strength', 0)
            score += momentum * 30

            # 波动性
            volatility = factors.get('realized_volatility', 0.02)
            if 0.01 <= volatility <= 0.03:
                score += 10
            elif volatility > 0.05:
                score -= 10

            return max(0, min(100, score))
        except:
            return 50.0

    def _calculate_money_flow_score(self, factors):
        """计算资金流向评分"""
        try:
            score = 50.0

            # 主力资金
            main_inflow = factors.get('main_force_inflow', 0)
            if main_inflow > 1000:
                score += 15
            elif main_inflow > 0:
                score += 5
            elif main_inflow < -1000:
                score -= 15

            # 机构持股
            institutional = factors.get('institutional_ownership', 0.3)
            if institutional > 0.5:
                score += 10
            elif institutional < 0.2:
                score -= 5

            return max(0, min(100, score))
        except:
            return 50.0

    def _calculate_risk_score(self, factors):
        """计算风险评分"""
        try:
            score = 50.0

            # Beta稳定性
            beta = factors.get('beta_stability', 1.0)
            if 0.8 <= beta <= 1.2:
                score += 10
            elif beta > 1.5:
                score -= 15

            # 下行风险
            downside = factors.get('downside_deviation', 0.02)
            if downside < 0.02:
                score += 10
            elif downside > 0.05:
                score -= 15

            return max(0, min(100, score))
        except:
            return 50.0

    def _calculate_overall_score(self, scores):
        """计算综合评分"""
        try:
            weights = {
                'market_environment_score': 0.25,
                'fundamental_score': 0.25,
                'enhanced_technical_score': 0.20,
                'money_flow_score': 0.20,
                'risk_score': 0.10
            }

            overall = 0
            total_weight = 0

            for score_name, weight in weights.items():
                if score_name in scores:
                    overall += scores[score_name] * weight
                    total_weight += weight

            return overall / total_weight if total_weight > 0 else 50.0
        except:
            return 50.0

    def _calculate_confidence_score(self, factors, scores):
        """计算买入信心度"""
        try:
            confidence = 50.0

            # 基于各项评分的一致性
            score_values = [v for v in scores.values() if isinstance(v, (int, float))]
            if score_values:
                avg_score = np.mean(score_values)
                confidence += (avg_score - 50) * 0.5

            # 特殊事件加分
            if factors.get('earnings_announcement', 0) == 1:
                confidence += 5
            if factors.get('analyst_upgrade', 0) == 1:
                confidence += 5

            return max(0, min(100, confidence))
        except:
            return 50.0

    def _initialize_market_data_cache(self):
        """初始化市场数据缓存"""
        self.market_data_cache = {
            'last_update': None,
            'market_data': {},
            'sector_data': {}
        }

    # ==================== 缺失方法补充 ====================

    def _get_market_index_data(self):
        """获取大盘指数数据"""
        try:
            # 模拟大盘数据
            return {
                'index_change': np.random.uniform(-0.03, 0.03),
                'volume_ratio': np.random.uniform(0.5, 2.0),
                'current_price': 3000 + np.random.uniform(-100, 100)
            }
        except:
            return {'index_change': 0.0, 'volume_ratio': 1.0, 'current_price': 3000}

    def _get_fundamental_data(self, symbol):
        """获取基本面数据"""
        try:
            return {
                'pe_ratio': np.random.uniform(8, 50),
                'pb_ratio': np.random.uniform(0.5, 8),
                'roe': np.random.uniform(0.02, 0.25),
                'eps_growth': np.random.uniform(-0.3, 0.5),
                'debt_ratio': np.random.uniform(0.1, 2.0)
            }
        except:
            return {'pe_ratio': 25, 'pb_ratio': 2, 'roe': 0.1, 'eps_growth': 0.1, 'debt_ratio': 0.5}

    def _get_price_history(self, symbol, days=30):
        """获取价格历史数据"""
        try:
            # 模拟价格历史数据
            dates = pd.date_range(end=self.context.now, periods=days, freq='D')
            base_price = 15.0

            # 生成模拟价格序列
            returns = np.random.normal(0.001, 0.02, days)
            prices = [base_price]
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))

            return pd.DataFrame({
                'date': dates,
                'close': prices,
                'high': [p * (1 + np.random.uniform(0, 0.03)) for p in prices],
                'low': [p * (1 - np.random.uniform(0, 0.03)) for p in prices],
                'volume': [np.random.uniform(10000, 100000) for _ in prices]
            })
        except:
            # 返回默认数据
            return pd.DataFrame({
                'date': [self.context.now],
                'close': [15.0],
                'high': [15.5],
                'low': [14.5],
                'volume': [50000]
            })

    def _get_money_flow_data(self, symbol):
        """获取资金流向数据"""
        try:
            return {
                'main_force_inflow': np.random.uniform(-10000, 20000),
                'large_order_ratio': np.random.uniform(0.1, 0.6),
                'institutional_change': np.random.uniform(-0.1, 0.1),
                'retail_sentiment': np.random.uniform(20, 80)
            }
        except:
            return {'main_force_inflow': 0, 'large_order_ratio': 0.3, 'institutional_change': 0, 'retail_sentiment': 50}

    def _check_earnings_announcement(self, symbol):
        """检查财报公告"""
        try:
            # 模拟财报公告概率
            return np.random.choice([0, 1], p=[0.9, 0.1])
        except:
            return 0

    def _calculate_seasonal_pattern(self, timestamp):
        """计算季节性模式"""
        try:
            month = timestamp.month
            return np.sin(2 * np.pi * month / 12)
        except:
            return 0.0

    def _calculate_holiday_effect(self, timestamp):
        """计算节假日效应"""
        try:
            day_of_year = timestamp.timetuple().tm_yday
            # 春节效应
            if 30 <= day_of_year <= 60:
                return 0.02
            # 国庆效应
            elif 270 <= day_of_year <= 280:
                return 0.01
            else:
                return 0.0
        except:
            return 0.0

    def _calculate_earnings_season_effect(self, timestamp):
        """计算财报季效应"""
        try:
            month = timestamp.month
            # 财报季：1月、4月、7月、10月
            if month in [1, 4, 7, 10]:
                return 0.015
            else:
                return 0.0
        except:
            return 0.0

    def _calculate_beta(self, symbol):
        """计算Beta系数"""
        try:
            return np.random.uniform(0.5, 2.0)
        except:
            return 1.0

    def _calculate_volatility(self, price_data):
        """计算波动率"""
        try:
            if len(price_data) > 1:
                returns = price_data['close'].pct_change().dropna()
                return returns.std() * np.sqrt(252)
            return 0.3
        except:
            return 0.3

    def _calculate_var(self, price_data, confidence=0.05):
        """计算VaR"""
        try:
            if len(price_data) > 10:
                returns = price_data['close'].pct_change().dropna()
                return np.percentile(returns, confidence * 100)
            return -0.05
        except:
            return -0.05

    def _calculate_max_drawdown(self, price_data):
        """计算最大回撤"""
        try:
            if len(price_data) > 1:
                prices = price_data['close']
                peak = prices.expanding().max()
                drawdown = (prices - peak) / peak
                return drawdown.min()
            return -0.1
        except:
            return -0.1

    def _calculate_vwap(self, price_data):
        """计算成交量加权平均价格"""
        try:
            if len(price_data) > 0:
                typical_price = (price_data['high'] + price_data['low'] + price_data['close']) / 3
                return (typical_price * price_data['volume']).sum() / price_data['volume'].sum()
            return 15.0
        except:
            return 15.0

    def _calculate_vwap_distance(self, price_data, current_price):
        """计算当前价格与VWAP的距离"""
        try:
            vwap = self._calculate_vwap(price_data)
            return (current_price - vwap) / vwap
        except:
            return 0.0

    def _calculate_pv_divergence(self, price_data):
        """计算价量背离"""
        try:
            if len(price_data) > 5:
                price_trend = price_data['close'].iloc[-1] > price_data['close'].iloc[-5]
                volume_trend = price_data['volume'].iloc[-1] > price_data['volume'].iloc[-5]
                return 1 if price_trend != volume_trend else 0
            return 0
        except:
            return 0

    def _calculate_realized_volatility(self, price_data):
        """计算已实现波动率"""
        try:
            if len(price_data) > 1:
                returns = price_data['close'].pct_change().dropna()
                return returns.std() * np.sqrt(252)
            return 0.3
        except:
            return 0.3

    def _calculate_trend_intensity(self, price_data):
        """计算趋势强度"""
        try:
            if len(price_data) > 10:
                prices = price_data['close']
                ma_short = prices.rolling(5).mean()
                ma_long = prices.rolling(20).mean()
                trend_strength = abs(ma_short.iloc[-1] - ma_long.iloc[-1]) / ma_long.iloc[-1]
                return min(1.0, trend_strength * 10)
            return 0.5
        except:
            return 0.5

    def _calculate_trend_consistency(self, price_data):
        """计算趋势一致性"""
        try:
            if len(price_data) > 10:
                returns = price_data['close'].pct_change().dropna()
                positive_days = (returns > 0).sum()
                total_days = len(returns)
                return abs(positive_days / total_days - 0.5) * 2
            return 0.6
        except:
            return 0.6

    def _calculate_momentum_strength(self, price_data):
        """计算动量强度"""
        try:
            if len(price_data) > 20:
                current_price = price_data['close'].iloc[-1]
                price_20d_ago = price_data['close'].iloc[-20]
                return (current_price - price_20d_ago) / price_20d_ago
            return 0.0
        except:
            return 0.0
