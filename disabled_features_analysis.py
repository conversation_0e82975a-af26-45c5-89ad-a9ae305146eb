# coding=utf-8
"""
策略中禁用功能深度分析报告
分析各个模块的作用、依赖关系和移除建议
"""

import os
import re

def analyze_disabled_features():
    """分析禁用的功能"""
    print('🔍 策略禁用功能深度分析')
    print('=' * 60)
    
    disabled_features = [
        {
            'name': '振幅过滤器',
            'config': 'AMPLITUDE_FILTER_ENABLED = False',
            'purpose': '过滤振幅过小的股票，确保股票有足够活跃度',
            'implementation': 'calculate_amplitude()函数，检查最近5天振幅',
            'dependencies': ['历史价格数据', 'history_n函数'],
            'code_location': 'main.py:3349-3400',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': '当前策略已有其他活跃度筛选机制'
        },
        {
            'name': '均线过滤器',
            'config': 'MA_FILTER_ENABLED = False',
            'purpose': '基于均线趋势过滤股票，确保趋势向上',
            'implementation': '5日均线方向检查',
            'dependencies': ['历史价格数据', '均线计算'],
            'code_location': 'config.py:288-290',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': 'TRIX指标已包含趋势判断'
        },
        {
            'name': '因子筛选系统',
            'config': 'ENABLE_FACTOR_FILTERING = False',
            'purpose': '基于192个增强因子进行股票筛选',
            'implementation': 'should_buy_with_enhanced_factors()函数',
            'dependencies': ['完整增强因子系统.py', 'enhanced_factors_config.py'],
            'code_location': 'main.py:4436-4500',
            'usage_frequency': '中',
            'complexity': '高',
            'recommendation': '保留但简化',
            'reason': '功能强大但复杂，可简化为基础版本'
        },
        {
            'name': '增强买入信号',
            'config': 'ENABLE_ENHANCED_BUY_SIGNALS = False',
            'purpose': '提供额外的买入信号确认',
            'implementation': 'EnhancedBuyIntegration类',
            'dependencies': ['enhanced_buy_integration.py'],
            'code_location': 'main.py:814-850',
            'usage_frequency': '低',
            'complexity': '中',
            'recommendation': '可安全移除',
            'reason': '与智能评分系统功能重叠'
        },
        {
            'name': '增强选股过滤器',
            'config': 'ENABLE_ENHANCED_STOCK_FILTER = False',
            'purpose': '额外的股票过滤逻辑',
            'implementation': '集成在增强买入信号中',
            'dependencies': ['enhanced_buy_integration.py'],
            'code_location': 'main.py:814-850',
            'usage_frequency': '低',
            'complexity': '中',
            'recommendation': '可安全移除',
            'reason': '与现有筛选逻辑重叠'
        },
        {
            'name': '均线交叉买入信号',
            'config': 'ENABLE_MA_CROSS_BUY_SIGNAL = False',
            'purpose': '基于均线交叉的买入信号',
            'implementation': '均线计算和交叉判断',
            'dependencies': ['历史价格数据'],
            'code_location': 'config.py:429',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': 'TRIX信号已足够'
        },
        {
            'name': 'TRIX拐点信号',
            'config': 'ENABLE_TRIX_REVERSAL_SIGNAL = False',
            'purpose': 'TRIX拐点买入信号',
            'implementation': 'TRIX三日比较逻辑',
            'dependencies': ['TRIX计算'],
            'code_location': 'config.py:430',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': '当前TRIX反转信号已足够'
        },
        {
            'name': '反弹买入策略',
            'config': 'ENABLE_REBOUND_BUY = False',
            'purpose': '在股票反弹时买入',
            'implementation': 'confirmed_lows跟踪和update_confirmed_lows()函数',
            'dependencies': ['历史价格数据', '最低价跟踪'],
            'code_location': 'main.py:3301-3400, 1280-1283',
            'usage_frequency': '中',
            'complexity': '中',
            'recommendation': '可安全移除',
            'reason': '增加复杂度，与主策略逻辑不符'
        },
        {
            'name': '固定止盈',
            'config': 'ENABLE_FIXED_PROFIT_STOP = False',
            'purpose': '固定比例止盈卖出',
            'implementation': '8%固定止盈逻辑',
            'dependencies': ['持仓成本跟踪'],
            'code_location': 'config.py:795-797',
            'usage_frequency': '高',
            'complexity': '简单',
            'recommendation': '建议启用',
            'reason': '简单有效的风险控制机制'
        },
        {
            'name': 'TRIX死叉卖出',
            'config': 'ENABLE_TRIX_SELL_SIGNAL = False',
            'purpose': 'TRIX死叉时卖出',
            'implementation': 'TRIX死叉判断逻辑',
            'dependencies': ['TRIX计算'],
            'code_location': 'config.py:826-827',
            'usage_frequency': '中',
            'complexity': '简单',
            'recommendation': '可考虑启用',
            'reason': '与买入逻辑对称，逻辑一致'
        },
        {
            'name': '持仓摘要',
            'config': 'ENABLE_POSITION_SUMMARY = False',
            'purpose': '定期输出持仓摘要信息',
            'implementation': 'generate_position_summary()函数',
            'dependencies': ['持仓数据'],
            'code_location': 'main.py:640-760',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': '仅用于监控，不影响交易逻辑'
        },
        {
            'name': 'CSV日志记录',
            'config': 'ENABLE_CSV_LOGGING = False',
            'purpose': '将交易记录写入CSV文件',
            'implementation': 'CSV写入逻辑',
            'dependencies': ['CSV文件操作'],
            'code_location': 'config.py:649',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '可安全移除',
            'reason': '已有数据库记录，CSV冗余'
        },
        {
            'name': '风险检查',
            'config': 'RISK_CHECK_ENABLED = False',
            'purpose': '额外的风险检查机制',
            'implementation': '风险检查逻辑',
            'dependencies': ['风险评估算法'],
            'code_location': 'config.py:612',
            'usage_frequency': '中',
            'complexity': '中',
            'recommendation': '保留配置',
            'reason': '性能优化需要，但可能需要时启用'
        },
        {
            'name': '性能分析',
            'config': 'ENABLE_PERFORMANCE_PROFILING = False',
            'purpose': '性能分析和监控',
            'implementation': '@profile装饰器和性能统计',
            'dependencies': ['性能监控工具'],
            'code_location': 'config.py:588-590',
            'usage_frequency': '低',
            'complexity': '简单',
            'recommendation': '保留',
            'reason': '调试和优化时有用'
        }
    ]
    
    return disabled_features

def categorize_features_by_recommendation(features):
    """按建议分类功能"""
    print('\n📊 功能移除建议分类')
    print('=' * 50)
    
    categories = {
        '可安全移除': [],
        '建议启用': [],
        '可考虑启用': [],
        '保留但简化': [],
        '保留配置': [],
        '保留': []
    }
    
    for feature in features:
        recommendation = feature['recommendation']
        categories[recommendation].append(feature)
    
    for category, feature_list in categories.items():
        if feature_list:
            print(f'\n🎯 {category} ({len(feature_list)}个):')
            for feature in feature_list:
                print(f'  • {feature["name"]} - {feature["reason"]}')
    
    return categories

def estimate_code_reduction(features):
    """估算代码减少量"""
    print('\n📈 代码减少估算')
    print('=' * 50)
    
    removable_features = [f for f in features if f['recommendation'] == '可安全移除']
    
    total_lines_estimate = 0
    complexity_score = 0
    
    for feature in removable_features:
        # 根据复杂度估算代码行数
        if feature['complexity'] == '简单':
            lines = 20
            complexity_score += 1
        elif feature['complexity'] == '中':
            lines = 50
            complexity_score += 2
        else:  # 高
            lines = 100
            complexity_score += 3
        
        total_lines_estimate += lines
        print(f'  • {feature["name"]}: ~{lines}行 (复杂度: {feature["complexity"]})')
    
    print(f'\n📊 估算结果:')
    print(f'  可移除功能: {len(removable_features)}个')
    print(f'  估算减少代码: ~{total_lines_estimate}行')
    print(f'  复杂度评分: {complexity_score}/30')
    print(f'  维护成本降低: {complexity_score/30*100:.1f}%')
    
    return total_lines_estimate, len(removable_features)

def analyze_dependencies(features):
    """分析依赖关系"""
    print('\n🔗 依赖关系分析')
    print('=' * 50)
    
    # 统计依赖文件
    dependency_files = {}
    for feature in features:
        for dep in feature['dependencies']:
            if '.py' in dep:
                if dep not in dependency_files:
                    dependency_files[dep] = []
                dependency_files[dep].append(feature['name'])
    
    print('📁 外部文件依赖:')
    for file, features_list in dependency_files.items():
        exists = os.path.exists(file)
        status = '✅ 存在' if exists else '❌ 不存在'
        print(f'  • {file} ({status}) - 被{len(features_list)}个功能依赖')
        for feature_name in features_list:
            print(f'    - {feature_name}')
    
    # 分析可安全删除的文件
    removable_features = [f['name'] for f in features if f['recommendation'] == '可安全移除']
    potentially_removable_files = []
    
    for file, features_list in dependency_files.items():
        if all(feature in removable_features for feature in features_list):
            potentially_removable_files.append(file)
    
    if potentially_removable_files:
        print(f'\n🗑️ 可能可以删除的文件:')
        for file in potentially_removable_files:
            print(f'  • {file}')

def generate_removal_plan(features):
    """生成移除计划"""
    print('\n📋 功能移除执行计划')
    print('=' * 50)
    
    removable_features = [f for f in features if f['recommendation'] == '可安全移除']
    
    # 按复杂度排序，简单的先移除
    complexity_order = {'简单': 1, '中': 2, '高': 3}
    removable_features.sort(key=lambda x: complexity_order[x['complexity']])
    
    print('🎯 建议移除顺序 (按复杂度从低到高):')
    
    for i, feature in enumerate(removable_features, 1):
        print(f'\n{i}. {feature["name"]} (复杂度: {feature["complexity"]})')
        print(f'   📍 位置: {feature["code_location"]}')
        print(f'   🎯 操作: 删除相关代码和配置')
        print(f'   ⚠️ 注意: {feature["reason"]}')
        
        # 具体操作步骤
        if feature['name'] == '振幅过滤器':
            print(f'   📝 步骤: 删除calculate_amplitude()函数和相关配置')
        elif feature['name'] == '反弹买入策略':
            print(f'   📝 步骤: 删除confirmed_lows相关代码和update_confirmed_lows()函数')
        elif feature['name'] == '持仓摘要':
            print(f'   📝 步骤: 删除generate_position_summary()函数和调用')
        else:
            print(f'   📝 步骤: 删除相关配置项和实现代码')

def main():
    """主函数"""
    print('🔍 策略禁用功能深度分析报告')
    print('=' * 60)
    
    # 分析禁用功能
    features = analyze_disabled_features()
    
    # 按建议分类
    categories = categorize_features_by_recommendation(features)
    
    # 估算代码减少
    lines_estimate, removable_count = estimate_code_reduction(features)
    
    # 分析依赖关系
    analyze_dependencies(features)
    
    # 生成移除计划
    generate_removal_plan(features)
    
    print(f'\n🎊 分析总结')
    print('=' * 50)
    print(f'✅ 总功能数: {len(features)}个')
    print(f'🗑️ 可安全移除: {removable_count}个')
    print(f'📊 估算减少代码: ~{lines_estimate}行')
    print(f'🚀 预期收益: 降低维护成本、提高代码简洁性')
    print(f'⚠️ 风险评估: 低风险，不影响核心交易逻辑')
    
    print(f'\n💡 建议执行顺序:')
    print('1. 先移除简单功能（振幅过滤、均线过滤等）')
    print('2. 再移除中等复杂度功能（反弹买入、持仓摘要等）')
    print('3. 最后考虑复杂功能的简化（因子筛选系统）')
    print('4. 保留有价值的功能配置（固定止盈、TRIX死叉等）')

if __name__ == '__main__':
    main()
