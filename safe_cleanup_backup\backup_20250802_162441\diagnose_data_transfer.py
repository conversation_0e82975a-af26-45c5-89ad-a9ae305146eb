# coding=utf-8
"""
诊断数据传递链路
分析因子计算成功但数据库为NULL的问题
"""

import sqlite3
import pandas as pd
import numpy as np

def analyze_problem():
    """分析问题"""
    print('🔍 数据传递链路问题分析')
    print('=' * 60)
    
    print('📊 确认的事实:')
    facts = [
        '✅ 因子计算完全成功 (3029条开始日志, 4985条结果日志)',
        '✅ 计算了112个增强因子 (每次都成功)',
        '✅ 7/7个关键指标都有效 (506条统计日志)',
        '✅ 没有计算异常 (0条异常日志)',
        '✅ 买入记录包含127个字段 (31条买入日志)',
        '❌ 数据库中所有技术指标都是NULL',
        '❌ 只有3个字段有数据 (relative_volume, volume_change_rate, distance_from_high)'
    ]
    
    for fact in facts:
        print(f'  {fact}')
    
    print(f'\n💡 问题定位:')
    print('  核心问题: 因子计算成功，但数据传递到数据库失败')
    print('  传递链路: enhanced_factors → signal_data → buy_record → database')
    print('  失败环节: 可能在signal_data.update(enhanced_factors)之后')

def test_factor_engine_output():
    """测试因子引擎输出的字段名"""
    print(f'\n🧪 测试因子引擎输出字段名')
    print('=' * 50)
    
    try:
        from enhanced_factor_engine import EnhancedFactorEngine
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [10.0]
        for _ in range(49):
            prices.append(prices[-1] * (1 + np.random.normal(0.001, 0.02)))
        
        test_data = pd.DataFrame({
            'open': np.array(prices) * 1.01,
            'high': np.array(prices) * 1.02,
            'low': np.array(prices) * 0.98,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 50)
        }, index=dates)
        
        engine = EnhancedFactorEngine()
        factors = engine.calculate_all_factors(test_data, 'TEST')
        
        print(f'✅ 计算了 {len(factors)} 个因子')
        
        # 检查关键指标的字段名
        key_indicators = ['rsi', 'macd', 'macd_signal', 'macd_hist', 'adx', 'cci', 'atr_pct', 'bb_width', 'bb_position', 'ma20', 'trix_buy']
        
        print(f'\n📊 关键指标字段检查:')
        found_indicators = []
        missing_indicators = []
        
        for indicator in key_indicators:
            if indicator in factors:
                value = factors[indicator]
                if isinstance(value, (int, float)) and not np.isnan(value) and np.isfinite(value):
                    found_indicators.append((indicator, value))
                    print(f'  ✅ {indicator}: {value:.6f}')
                else:
                    print(f'  ⚠️ {indicator}: {value} (无效值)')
            else:
                missing_indicators.append(indicator)
                print(f'  ❌ {indicator}: 缺失')
        
        print(f'\n📈 统计结果:')
        print(f'  找到有效指标: {len(found_indicators)}个')
        print(f'  缺失指标: {len(missing_indicators)}个')
        
        # 检查只有数据的3个字段
        working_fields = ['relative_volume', 'volume_change_rate', 'distance_from_high']
        print(f'\n🔍 检查有数据的字段:')
        
        for field in working_fields:
            if field in factors:
                value = factors[field]
                print(f'  ✅ {field}: {value:.6f} (在数据库中有数据)')
            else:
                print(f'  ❌ {field}: 缺失 (但数据库中有数据)')
        
        return factors
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return {}

def check_database_field_types():
    """检查数据库字段类型"""
    print(f'\n🔍 检查数据库字段类型')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        
        # 查找技术指标字段的类型
        tech_fields = []
        for col in columns:
            field_name = col[1]
            field_type = col[2]
            if field_name in ['rsi', 'macd', 'macd_signal', 'macd_hist', 'adx', 'cci', 'atr_pct', 'bb_width', 'bb_position', 'ma20', 'trix_buy', 'relative_volume', 'volume_change_rate']:
                tech_fields.append((field_name, field_type))
        
        print('📊 关键技术指标字段类型:')
        for field_name, field_type in tech_fields:
            print(f'  {field_name}: {field_type}')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def analyze_data_type_mismatch(factors):
    """分析数据类型不匹配问题"""
    print(f'\n🔍 分析数据类型不匹配问题')
    print('=' * 50)
    
    if not factors:
        print('❌ 没有因子数据进行分析')
        return
    
    # 检查因子数据的类型
    key_indicators = ['rsi', 'macd', 'adx', 'cci', 'atr_pct', 'bb_width', 'ma20', 'relative_volume', 'volume_change_rate']
    
    print('📊 因子数据类型分析:')
    
    for indicator in key_indicators:
        if indicator in factors:
            value = factors[indicator]
            value_type = type(value).__name__
            
            # 检查是否是有效的数值
            is_valid = isinstance(value, (int, float)) and not np.isnan(value) and np.isfinite(value)
            
            # 检查是否可以转换为SQL兼容的类型
            try:
                float_value = float(value)
                sql_compatible = True
            except:
                sql_compatible = False
            
            status = '✅' if is_valid and sql_compatible else '❌'
            print(f'  {status} {indicator}: {value_type} = {value} (有效: {is_valid}, SQL兼容: {sql_compatible})')
        else:
            print(f'  ❌ {indicator}: 缺失')

def check_signal_data_structure():
    """检查signal_data结构问题"""
    print(f'\n🔍 检查signal_data结构问题')
    print('=' * 50)
    
    print('💡 可能的问题:')
    problems = [
        {
            'problem': 'signal_data.update(enhanced_factors)没有执行',
            'evidence': '因子计算成功但数据库为NULL',
            'check': '需要在main.py中添加signal_data内容的调试日志'
        },
        {
            'problem': 'enhanced_factors中的字段名与数据库不匹配',
            'evidence': '只有3个字段有数据，其他都为NULL',
            'check': '需要检查字段映射是否在运行时生效'
        },
        {
            'problem': '数据类型转换问题',
            'evidence': 'numpy类型可能无法正确写入SQLite',
            'check': '需要检查数据类型转换'
        },
        {
            'problem': 'buy_record保存时字段过滤',
            'evidence': '127个字段但只有少数有数据',
            'check': '需要检查save_original_buy_record函数'
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f'\n{i}. {problem["problem"]}')
        print(f'   证据: {problem["evidence"]}')
        print(f'   检查: {problem["check"]}')

def suggest_next_debugging_steps():
    """建议下一步调试步骤"""
    print(f'\n🚀 下一步调试建议')
    print('=' * 50)
    
    steps = [
        {
            'priority': '高',
            'step': '添加signal_data内容调试日志',
            'description': '在signal_data.update(enhanced_factors)后添加日志，显示signal_data的内容',
            'location': 'main.py analyze_single_symbol函数'
        },
        {
            'priority': '高',
            'step': '检查字段映射运行时效果',
            'description': '验证enhanced_factors中的字段名是否正确映射',
            'location': 'enhanced_factor_engine.py _map_factor_names_to_db_fields方法'
        },
        {
            'priority': '中',
            'step': '添加buy_record保存调试',
            'description': '在save_original_buy_record中添加日志，显示实际保存的字段和值',
            'location': 'data_manager.py或相关保存函数'
        },
        {
            'priority': '中',
            'step': '检查数据类型转换',
            'description': '确保numpy类型正确转换为Python原生类型',
            'location': 'enhanced_factor_engine.py calculate_all_factors方法'
        },
        {
            'priority': '低',
            'step': '验证数据库写入SQL',
            'description': '检查实际执行的INSERT语句',
            'location': '数据库写入相关代码'
        }
    ]
    
    for step in steps:
        print(f'\n🎯 {step["priority"]}优先级: {step["step"]}')
        print(f'   描述: {step["description"]}')
        print(f'   位置: {step["location"]}')

def main():
    """主函数"""
    print('🔧 数据传递链路诊断报告')
    print('=' * 60)
    
    # 分析问题
    analyze_problem()
    
    # 测试因子引擎输出
    factors = test_factor_engine_output()
    
    # 检查数据库字段类型
    check_database_field_types()
    
    # 分析数据类型不匹配
    analyze_data_type_mismatch(factors)
    
    # 检查signal_data结构问题
    check_signal_data_structure()
    
    # 建议下一步调试步骤
    suggest_next_debugging_steps()
    
    print(f'\n🎯 诊断结论')
    print('=' * 40)
    print('✅ 因子计算系统工作完全正常')
    print('❌ 问题在于数据传递链路的某个环节')
    print('🔧 需要添加signal_data内容的调试日志')
    print('💡 重点检查字段映射和数据类型转换')

if __name__ == '__main__':
    main()
