# coding=utf-8
"""
数据库初始化模块
确保数据库包含所有必要的字段，包括完整的增强指标字段
"""

import sqlite3
import os
from datetime import datetime

class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self):
        self.trades_db_path = 'data/trades.db'
        self.positions_db_path = 'data/positions.db'
    
    def get_complete_trades_schema(self):
        """获取完整的trades表结构定义"""
        
        # 基础字段
        basic_fields = [
            'id INTEGER PRIMARY KEY AUTOINCREMENT',
            'timestamp TEXT NOT NULL',
            'symbol TEXT NOT NULL',
            'action TEXT NOT NULL',
            'price REAL NOT NULL',
            'volume INTEGER NOT NULL',
            
            # 卖出相关字段
            'sell_reason TEXT',
            'cost_price_sell REAL',
            'net_profit_pct_sell REAL',
            'confirmed_high_sell REAL',
            'confirmed_high_time TEXT',
            'holding_hours REAL',
            'max_profit_pct REAL',
            'final_drawdown_pct REAL',
            'status TEXT'
        ]
        
        # 增强指标字段 - 价格指标
        price_fields = [
            'price_change_pct REAL',
            'price_momentum_3d REAL',
            'price_momentum_5d REAL',
            'price_momentum_10d REAL',
            'gap_pct REAL',
            'intraday_range_pct REAL',
            'price_position_in_range REAL',
            'volume_price_correlation REAL'
        ]
        
        # 成交量指标
        volume_fields = [
            'volume_ma5_ratio REAL',
            'volume_ma10_ratio REAL',
            'volume_ma20_ratio REAL',
            'volume_change_pct REAL',
            'volume_momentum_3d REAL',
            'volume_momentum_5d REAL',
            'money_flow_5d REAL',
            'money_flow_10d REAL',
            'relative_volume REAL'
        ]
        
        # 技术指标
        technical_fields = [
            'rsi_3d REAL',
            'rsi_5d REAL',
            'rsi_10d REAL',
            'rsi_20d REAL',
            'macd_12_26 REAL',
            'macd_signal_9 REAL',
            'macd_histogram REAL',
            'macd_slope REAL',
            'bb_upper_20 REAL',
            'bb_middle_20 REAL',
            'bb_lower_20 REAL',
            'bb_width_20 REAL',
            'bb_position_20 REAL',
            'bb_squeeze REAL',
            'adx_14 REAL',
            'adx_slope REAL',
            'kdj_k REAL',
            'kdj_d REAL',
            'kdj_j REAL',
            'cci_14 REAL',
            'williams_r REAL',
            'stoch_k REAL',
            'stoch_d REAL'
        ]
        
        # 均线指标
        ma_fields = [
            'ma5 REAL',
            'ma10 REAL',
            'ma20 REAL',
            'ma60 REAL',
            'ma120 REAL',
            'ma5_distance_pct REAL',
            'ma10_distance_pct REAL',
            'ma20_distance_pct REAL',
            'ma5_slope REAL',
            'ma10_slope REAL',
            'ma20_slope REAL',
            'ma_alignment_score REAL'
        ]
        
        # 波动性指标
        volatility_fields = [
            'volatility_3d REAL',
            'volatility_5d REAL',
            'volatility_10d REAL',
            'volatility_20d REAL',
            'volatility_ratio_5_20 REAL',
            'atr_3d REAL',
            'atr_5d REAL',
            'atr_10d REAL',
            'atr_normalized REAL',
            'volatility_score_enhanced REAL'
        ]
        
        # 时间指标
        time_fields = [
            'hour_of_day INTEGER',
            'minute_of_hour INTEGER',
            'day_of_week INTEGER',
            'day_of_month INTEGER',
            'week_of_year INTEGER',
            'month_of_year INTEGER',
            'quarter_of_year INTEGER',
            'market_session TEXT',
            'time_to_close_minutes INTEGER',
            'is_month_end INTEGER',
            'is_quarter_end INTEGER',
            'is_year_end INTEGER',
            'seasonal_factor REAL'
        ]
        
        # 风险指标
        risk_fields = [
            'max_drawdown_3d REAL',
            'max_drawdown_5d REAL',
            'max_drawdown_10d REAL',
            'sharpe_ratio_5d REAL',
            'beta_market REAL',
            'correlation_market REAL'
        ]
        
        # 形态指标
        pattern_fields = [
            'pattern_doji INTEGER',
            'pattern_hammer INTEGER',
            'pattern_engulfing INTEGER',
            'pattern_morning_star INTEGER',
            'pattern_evening_star INTEGER',
            'pattern_score_total REAL'
        ]
        
        # 综合指标
        composite_fields = [
            'technical_score REAL',
            'momentum_score REAL',
            'volume_score REAL',
            'volatility_score REAL',
            'trend_score REAL',
            'overall_score REAL',
            'buy_signal_strength REAL',
            'risk_adjusted_score REAL'
        ]
        
        # 原有的其他指标字段（避免重复）
        legacy_fields = [
            'rsi REAL',
            'macd_signal REAL',
            'trend_strength_buy REAL',
            'ma3_buy REAL',
            'ma7_buy REAL',
            'trix_buy REAL',
            'volatility REAL',
            'atr_pct REAL',
            'allocation_factor REAL',
            'amplitude_pct REAL',
            'bb_width REAL',
            'adx REAL',
            'obv REAL',
            'industry_relative_strength REAL',
            'market_correlation REAL',
            'pe_ratio REAL',
            'pb_ratio REAL',
            'roe REAL'
        ]
        
        # 合并所有字段
        all_fields = (
            basic_fields + price_fields + volume_fields + technical_fields +
            ma_fields + volatility_fields + time_fields + risk_fields +
            pattern_fields + composite_fields + legacy_fields
        )
        
        return all_fields
    
    def initialize_trades_database(self):
        """初始化trades数据库"""
        print('🔧 初始化trades数据库...')
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        # 获取完整的表结构
        fields = self.get_complete_trades_schema()
        
        # 创建表
        conn = sqlite3.connect(self.trades_db_path)
        cursor = conn.cursor()
        
        # 删除现有表（如果存在）
        cursor.execute('DROP TABLE IF EXISTS trades')
        
        # 创建新表
        create_sql = f'''
        CREATE TABLE trades (
            {', '.join(fields)}
        )
        '''
        
        cursor.execute(create_sql)
        
        # 创建索引
        indexes = [
            'CREATE INDEX idx_trades_timestamp ON trades(timestamp)',
            'CREATE INDEX idx_trades_symbol ON trades(symbol)',
            'CREATE INDEX idx_trades_action ON trades(action)',
            'CREATE INDEX idx_trades_symbol_action ON trades(symbol, action)',
            'CREATE INDEX idx_trades_timestamp_action ON trades(timestamp, action)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        
        print(f'✅ trades数据库初始化完成，包含 {len(fields)} 个字段')
        return len(fields)
    
    def initialize_positions_database(self):
        """初始化positions数据库"""
        print('🔧 初始化positions数据库...')
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        conn = sqlite3.connect(self.positions_db_path)
        cursor = conn.cursor()
        
        # 创建positions表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            buy_date TEXT NOT NULL,
            buy_price REAL NOT NULL,
            volume INTEGER NOT NULL,
            cost REAL NOT NULL,
            highest_price REAL,
            last_update TEXT,
            is_open INTEGER DEFAULT 1,
            sell_date TEXT,
            sell_price REAL,
            profit REAL,
            notes TEXT
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_is_open ON positions(is_open)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_positions_buy_date ON positions(buy_date)')
        
        conn.commit()
        conn.close()
        
        print('✅ positions数据库初始化完成')
    
    def verify_database_schema(self):
        """验证数据库结构"""
        print('🔍 验证数据库结构...')
        
        try:
            conn = sqlite3.connect(self.trades_db_path)
            cursor = conn.cursor()
            
            # 检查trades表结构
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            
            field_count = len(columns)
            print(f'trades表字段数: {field_count}')
            
            # 检查关键增强指标字段
            field_names = [col[1] for col in columns]
            key_indicators = [
                'overall_score', 'technical_score', 'rsi_5d', 'macd_12_26',
                'volatility_5d', 'volume_ma5_ratio', 'hour_of_day'
            ]
            
            missing_fields = []
            for indicator in key_indicators:
                if indicator not in field_names:
                    missing_fields.append(indicator)
            
            if missing_fields:
                print(f'❌ 缺失关键字段: {missing_fields}')
                return False
            else:
                print('✅ 所有关键增强指标字段都存在')
                return True
            
        except Exception as e:
            print(f'❌ 验证失败: {e}')
            return False
        finally:
            conn.close()
    
    def initialize_all_databases(self):
        """初始化所有数据库"""
        print('🚀 开始初始化所有数据库')
        print('=' * 60)
        
        try:
            # 1. 初始化trades数据库
            field_count = self.initialize_trades_database()
            
            # 2. 初始化positions数据库
            self.initialize_positions_database()
            
            # 3. 验证数据库结构
            if self.verify_database_schema():
                print('\n🎉 数据库初始化成功!')
                print(f'📊 trades表包含 {field_count} 个字段')
                print('✅ 支持完整的增强指标保存')
                return True
            else:
                print('\n❌ 数据库初始化验证失败')
                return False
                
        except Exception as e:
            print(f'\n❌ 数据库初始化失败: {e}')
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    initializer = DatabaseInitializer()
    success = initializer.initialize_all_databases()
    
    if success:
        print('\n💡 数据库已准备就绪，可以开始回测')
    else:
        print('\n🔧 请检查错误并重新初始化')

if __name__ == "__main__":
    main()
