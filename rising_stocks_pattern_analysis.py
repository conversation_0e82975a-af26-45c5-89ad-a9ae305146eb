# coding=utf-8
"""
上涨股票模式分析
分析上涨股票的前一日因子和当日开盘数据，提取成功模式
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import akshare as ak
from collections import defaultdict

def get_rising_stocks_data():
    """获取上涨股票数据"""
    print('📈 获取上涨股票数据')
    print('=' * 60)
    
    try:
        # 获取今日涨幅榜
        today_risers = ak.stock_zh_a_spot_em()
        
        # 筛选涨幅>3%的股票
        rising_stocks = today_risers[today_risers['涨跌幅'] > 3.0].copy()
        rising_stocks = rising_stocks.head(50)  # 取前50只
        
        print(f'📊 今日涨幅>3%股票: {len(rising_stocks)}只')
        
        # 显示前10只
        print(f'\n🔥 涨幅前10名:')
        print(f'{"代码":<10} {"名称":<10} {"涨幅%":<8} {"现价":<8} {"成交量":<12}')
        print('-' * 60)
        
        for _, row in rising_stocks.head(10).iterrows():
            print(f'{row["代码"]:<10} {row["名称"]:<10} {row["涨跌幅"]:<8.2f} {row["最新价"]:<8.2f} {row["成交量"]:<12.0f}')
        
        return rising_stocks
        
    except Exception as e:
        print(f'❌ 获取上涨股票数据失败: {e}')
        return None

def analyze_rising_stock_factors(stock_code, stock_name):
    """分析单只上涨股票的因子模式"""
    try:
        # 获取历史数据 (最近30天)
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=40)).strftime('%Y%m%d')
        
        hist_data = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                      start_date=start_date, end_date=end_date, adjust="qfq")
        
        if len(hist_data) < 10:
            return None
        
        # 重命名列
        hist_data.columns = ['date', 'open', 'close', 'high', 'low', 'volume', 'turnover', 'amplitude', 'change_pct', 'change_amount', 'turnover_rate']
        hist_data['date'] = pd.to_datetime(hist_data['date'])
        hist_data = hist_data.sort_values('date')
        
        # 计算技术指标
        factors = calculate_technical_factors(hist_data)
        
        # 分析前一日和当日模式
        patterns = analyze_pre_rise_patterns(hist_data, factors)
        
        return {
            'code': stock_code,
            'name': stock_name,
            'data': hist_data,
            'factors': factors,
            'patterns': patterns
        }
        
    except Exception as e:
        print(f'❌ 分析{stock_code}失败: {e}')
        return None

def calculate_technical_factors(hist_data):
    """计算技术因子"""
    factors = {}
    
    try:
        import talib
        
        close = hist_data['close'].values
        high = hist_data['high'].values
        low = hist_data['low'].values
        volume = hist_data['volume'].values
        open_prices = hist_data['open'].values
        
        if len(close) < 20:
            return factors
        
        # 1. 趋势指标
        factors['sma_5'] = talib.SMA(close, timeperiod=5)
        factors['sma_10'] = talib.SMA(close, timeperiod=10)
        factors['sma_20'] = talib.SMA(close, timeperiod=20)
        
        # 2. 动量指标
        factors['rsi_14'] = talib.RSI(close, timeperiod=14)
        factors['rsi_6'] = talib.RSI(close, timeperiod=6)
        
        # 3. 超买超卖指标
        factors['cci_14'] = talib.CCI(high, low, close, timeperiod=14)
        
        # 4. 趋势强度
        factors['adx_14'] = talib.ADX(high, low, close, timeperiod=14)
        
        # 5. 波动率
        factors['atr_14'] = talib.ATR(high, low, close, timeperiod=14)
        factors['atr_pct'] = factors['atr_14'] / close * 100
        
        # 6. 成交量指标
        factors['volume_sma_5'] = talib.SMA(volume.astype(float), timeperiod=5)
        factors['volume_ratio'] = volume / factors['volume_sma_5']
        
        # 7. 价格位置
        factors['price_position'] = (close - talib.MIN(low, timeperiod=20)) / (talib.MAX(high, timeperiod=20) - talib.MIN(low, timeperiod=20)) * 100
        
        # 8. 开盘动量
        factors['opening_gap'] = (open_prices - np.roll(close, 1)) / np.roll(close, 1) * 100
        factors['intraday_momentum'] = (close - open_prices) / open_prices * 100
        
        # 9. 布林带
        factors['bb_upper'], factors['bb_middle'], factors['bb_lower'] = talib.BBANDS(close, timeperiod=20)
        factors['bb_position'] = (close - factors['bb_lower']) / (factors['bb_upper'] - factors['bb_lower']) * 100
        
        # 10. MACD
        factors['macd'], factors['macd_signal'], factors['macd_hist'] = talib.MACD(close)
        
        return factors
        
    except Exception as e:
        print(f'❌ 计算技术因子失败: {e}')
        return {}

def analyze_pre_rise_patterns(hist_data, factors):
    """分析上涨前的模式"""
    patterns = {}
    
    try:
        if len(hist_data) < 5:
            return patterns
        
        # 找到最近的大涨日 (涨幅>5%)
        big_rise_days = hist_data[hist_data['change_pct'] > 5.0]
        
        if len(big_rise_days) == 0:
            return patterns
        
        # 分析每个大涨日前一天的特征
        pre_rise_features = []
        
        for _, rise_day in big_rise_days.iterrows():
            rise_date = rise_day['date']
            rise_idx = hist_data[hist_data['date'] == rise_date].index[0]
            
            if rise_idx > 0:  # 确保有前一天数据
                pre_day_idx = rise_idx - 1
                
                # 前一日特征
                pre_day_features = {}
                
                for factor_name, factor_values in factors.items():
                    if hasattr(factor_values, '__len__') and len(factor_values) > pre_day_idx:
                        if not np.isnan(factor_values[pre_day_idx]):
                            pre_day_features[f'pre_{factor_name}'] = factor_values[pre_day_idx]
                
                # 当日开盘特征
                rise_day_features = {
                    'rise_pct': rise_day['change_pct'],
                    'opening_gap': (rise_day['open'] - hist_data.iloc[pre_day_idx]['close']) / hist_data.iloc[pre_day_idx]['close'] * 100,
                    'volume_ratio': rise_day['volume'] / hist_data.iloc[max(0, pre_day_idx-4):pre_day_idx+1]['volume'].mean(),
                    'amplitude': rise_day['amplitude']
                }
                
                # 合并特征
                combined_features = {**pre_day_features, **rise_day_features}
                pre_rise_features.append(combined_features)
        
        # 统计分析
        if pre_rise_features:
            patterns = analyze_common_patterns(pre_rise_features)
        
        return patterns
        
    except Exception as e:
        print(f'❌ 分析上涨前模式失败: {e}')
        return {}

def analyze_common_patterns(features_list):
    """分析共同模式"""
    patterns = {}
    
    try:
        if not features_list:
            return patterns
        
        # 转换为DataFrame便于分析
        df = pd.DataFrame(features_list)
        
        # 统计各因子的分布
        patterns['sample_count'] = len(df)
        patterns['factor_stats'] = {}
        
        for col in df.columns:
            if df[col].dtype in ['float64', 'int64']:
                patterns['factor_stats'][col] = {
                    'mean': df[col].mean(),
                    'median': df[col].median(),
                    'std': df[col].std(),
                    'min': df[col].min(),
                    'max': df[col].max(),
                    'q25': df[col].quantile(0.25),
                    'q75': df[col].quantile(0.75)
                }
        
        return patterns
        
    except Exception as e:
        print(f'❌ 分析共同模式失败: {e}')
        return {}

def extract_rising_patterns():
    """提取上涨模式"""
    print(f'\n🔍 提取上涨股票模式')
    print('=' * 60)
    
    # 获取上涨股票
    rising_stocks = get_rising_stocks_data()
    
    if rising_stocks is None or len(rising_stocks) == 0:
        print('❌ 没有获取到上涨股票数据')
        return None
    
    # 分析前20只股票
    all_patterns = []
    successful_analysis = 0
    
    print(f'\n📊 开始分析前20只上涨股票...')
    
    for i, (_, stock) in enumerate(rising_stocks.head(20).iterrows()):
        stock_code = stock['代码']
        stock_name = stock['名称']
        
        print(f'分析 {i+1}/20: {stock_code} {stock_name}')
        
        result = analyze_rising_stock_factors(stock_code, stock_name)
        
        if result and result['patterns']:
            all_patterns.append(result)
            successful_analysis += 1
        
        # 避免请求过快
        import time
        time.sleep(0.5)
    
    print(f'\n✅ 成功分析: {successful_analysis}/20 只股票')
    
    return all_patterns

def summarize_common_patterns(all_patterns):
    """总结共同模式"""
    print(f'\n📊 总结共同上涨模式')
    print('=' * 60)
    
    if not all_patterns:
        print('❌ 没有可分析的模式数据')
        return None
    
    # 收集所有因子数据
    all_factor_data = defaultdict(list)
    
    for pattern in all_patterns:
        if 'patterns' in pattern and 'factor_stats' in pattern['patterns']:
            for factor_name, stats in pattern['patterns']['factor_stats'].items():
                all_factor_data[factor_name].append(stats['mean'])
    
    # 分析关键模式
    key_patterns = {}
    
    print(f'📈 分析了 {len(all_patterns)} 只上涨股票的模式')
    print(f'\n🔍 关键因子模式分析:')
    print(f'{"因子名称":<25} {"平均值":<10} {"中位数":<10} {"标准差":<10} {"样本数":<8}')
    print('-' * 70)
    
    for factor_name, values in all_factor_data.items():
        if len(values) >= 3:  # 至少3个样本
            mean_val = np.mean(values)
            median_val = np.median(values)
            std_val = np.std(values)
            count = len(values)
            
            key_patterns[factor_name] = {
                'mean': mean_val,
                'median': median_val,
                'std': std_val,
                'count': count
            }
            
            print(f'{factor_name:<25} {mean_val:<10.2f} {median_val:<10.2f} {std_val:<10.2f} {count:<8d}')
    
    return key_patterns

def identify_success_factors(key_patterns):
    """识别成功因子"""
    print(f'\n🎯 识别关键成功因子')
    print('=' * 60)
    
    success_factors = '''
🔍 基于上涨股票分析的关键发现:

📊 前一日技术指标特征:
'''
    
    if key_patterns:
        # 分析前一日指标
        pre_day_factors = {k: v for k, v in key_patterns.items() if k.startswith('pre_')}
        
        print(f'📈 前一日关键指标:')
        
        for factor, stats in pre_day_factors.items():
            factor_name = factor.replace('pre_', '')
            mean_val = stats['mean']
            
            if 'rsi' in factor_name:
                if 30 <= mean_val <= 70:
                    print(f'   ✅ {factor_name}: {mean_val:.1f} (适中区间，有上涨空间)')
                elif mean_val < 30:
                    print(f'   🔥 {factor_name}: {mean_val:.1f} (超卖反弹)')
                else:
                    print(f'   ⚠️ {factor_name}: {mean_val:.1f} (可能过热)')
            
            elif 'cci' in factor_name:
                if -100 <= mean_val <= 100:
                    print(f'   ✅ {factor_name}: {mean_val:.1f} (正常区间)')
                elif mean_val < -100:
                    print(f'   🔥 {factor_name}: {mean_val:.1f} (超卖反弹机会)')
                else:
                    print(f'   ⚠️ {factor_name}: {mean_val:.1f} (可能过热)')
            
            elif 'atr_pct' in factor_name:
                print(f'   📊 {factor_name}: {mean_val:.1f}% (波动率水平)')
            
            elif 'adx' in factor_name:
                if mean_val > 25:
                    print(f'   🎯 {factor_name}: {mean_val:.1f} (强趋势)')
                else:
                    print(f'   📊 {factor_name}: {mean_val:.1f} (趋势一般)')
        
        # 分析当日开盘特征
        opening_factors = {k: v for k, v in key_patterns.items() if not k.startswith('pre_')}
        
        print(f'\n🚀 当日开盘关键特征:')
        
        for factor, stats in opening_factors.items():
            mean_val = stats['mean']
            
            if factor == 'opening_gap':
                if mean_val > 2:
                    print(f'   🔥 开盘跳空: {mean_val:.1f}% (高开强势)')
                elif mean_val > 0:
                    print(f'   ✅ 开盘跳空: {mean_val:.1f}% (温和高开)')
                else:
                    print(f'   📊 开盘跳空: {mean_val:.1f}% (平开或低开)')
            
            elif factor == 'volume_ratio':
                if mean_val > 2:
                    print(f'   🔥 成交量放大: {mean_val:.1f}倍 (强烈关注)')
                elif mean_val > 1.5:
                    print(f'   ✅ 成交量放大: {mean_val:.1f}倍 (适度放量)')
                else:
                    print(f'   📊 成交量放大: {mean_val:.1f}倍 (正常水平)')
            
            elif factor == 'amplitude':
                print(f'   📊 振幅: {mean_val:.1f}% (当日波动)')
            
            elif factor == 'rise_pct':
                print(f'   🚀 涨幅: {mean_val:.1f}% (实际涨幅)')
    
    return success_factors

def generate_strategy_recommendations(key_patterns):
    """生成策略建议"""
    print(f'\n🚀 基于上涨模式的策略建议')
    print('=' * 60)
    
    recommendations = '''
📋 基于上涨股票分析的策略优化建议:

🎯 买入信号优化:
'''
    
    if key_patterns:
        print(f'🔍 基于分析结果的具体建议:')
        
        # RSI建议
        rsi_factors = [k for k in key_patterns.keys() if 'rsi' in k and k.startswith('pre_')]
        if rsi_factors:
            rsi_mean = np.mean([key_patterns[f]['mean'] for f in rsi_factors])
            print(f'\n📊 RSI优化建议:')
            print(f'   当前配置: RSI 7天周期')
            print(f'   上涨股票RSI均值: {rsi_mean:.1f}')
            if 30 <= rsi_mean <= 60:
                print(f'   ✅ 建议: 关注RSI 30-60区间的股票')
            elif rsi_mean < 30:
                print(f'   🔥 建议: 重点关注RSI<30的超卖反弹')
        
        # CCI建议
        cci_factors = [k for k in key_patterns.keys() if 'cci' in k and k.startswith('pre_')]
        if cci_factors:
            cci_mean = np.mean([key_patterns[f]['mean'] for f in cci_factors])
            print(f'\n📊 CCI优化建议:')
            print(f'   当前配置: CCI [-50, 150]')
            print(f'   上涨股票CCI均值: {cci_mean:.1f}')
            if cci_mean < 0:
                print(f'   🔥 建议: 重点关注CCI<0的反弹机会')
            else:
                print(f'   ✅ 建议: 当前CCI配置合理')
        
        # ATR建议
        atr_factors = [k for k in key_patterns.keys() if 'atr_pct' in k and k.startswith('pre_')]
        if atr_factors:
            atr_mean = np.mean([key_patterns[f]['mean'] for f in atr_factors])
            print(f'\n📊 ATR优化建议:')
            print(f'   当前配置: ATR > 1.8%')
            print(f'   上涨股票ATR均值: {atr_mean:.1f}%')
            if atr_mean > 2.5:
                print(f'   🔥 建议: 提高ATR阈值到2.5%+')
            else:
                print(f'   ✅ 建议: 当前ATR配置合理')
        
        # 开盘跳空建议
        if 'opening_gap' in key_patterns:
            gap_mean = key_patterns['opening_gap']['mean']
            print(f'\n🚀 开盘跳空建议:')
            print(f'   上涨股票开盘跳空均值: {gap_mean:.1f}%')
            if gap_mean > 1:
                print(f'   🔥 建议: 增加开盘跳空>1%的筛选条件')
            else:
                print(f'   📊 建议: 关注平开或微幅高开的机会')
        
        # 成交量建议
        if 'volume_ratio' in key_patterns:
            vol_mean = key_patterns['volume_ratio']['mean']
            print(f'\n💰 成交量建议:')
            print(f'   上涨股票成交量放大均值: {vol_mean:.1f}倍')
            if vol_mean > 1.5:
                print(f'   🔥 建议: 增加成交量放大>1.5倍的条件')
            else:
                print(f'   📊 建议: 关注成交量适度放大的股票')
    
    print(f'\n🎯 新策略要素建议:')
    print(f'   1. 增加前一日RSI超卖筛选')
    print(f'   2. 增加开盘跳空动量因子')
    print(f'   3. 增加成交量突破确认')
    print(f'   4. 优化CCI反弹区间识别')
    print(f'   5. 结合ATR波动率筛选')

def main():
    """主函数"""
    print('🔍 上涨股票模式分析')
    print('=' * 60)
    
    print('🎯 分析思路: 从成功案例中提取规律')
    print('📊 分析内容: 上涨前一日因子 + 当日开盘特征')
    print('🚀 目标: 提取可用的策略要素')
    
    # 提取上涨模式
    all_patterns = extract_rising_patterns()
    
    if all_patterns:
        # 总结共同模式
        key_patterns = summarize_common_patterns(all_patterns)
        
        # 识别成功因子
        success_factors = identify_success_factors(key_patterns)
        
        # 生成策略建议
        generate_strategy_recommendations(key_patterns)
        
        print(f'\n🎯 分析完成')
        print('=' * 40)
        print(f'✅ 成功分析: {len(all_patterns)} 只上涨股票')
        print(f'📊 提取因子: {len(key_patterns) if key_patterns else 0} 个关键模式')
        print(f'🚀 策略建议: 已生成具体优化方向')
        
        return key_patterns
    else:
        print('❌ 分析失败，请检查网络连接和数据源')
        return None

if __name__ == '__main__':
    main()
