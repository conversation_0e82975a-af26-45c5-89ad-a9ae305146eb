# coding=utf-8
"""
数据完整性和未来函数检查器
检查回测数据的完整性、一致性和是否存在未来函数
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DataIntegrityChecker:
    """数据完整性检查器"""
    
    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        self.issues = []
        
    def check_all(self):
        """执行所有检查"""
        print('🔍 数据完整性和未来函数全面检查')
        print('=' * 80)

        results = {}

        # 0. 全面字段覆盖检查
        print('\n📋 0. 全面字段覆盖检查')
        results['field_coverage'] = self.check_field_coverage()

        # 1. 基础数据完整性检查
        print('\n📊 1. 基础数据完整性检查')
        results['basic_integrity'] = self.check_basic_integrity()

        # 2. 买卖记录对应性检查
        print('\n🔗 2. 买卖记录对应性检查')
        results['buy_sell_matching'] = self.check_buy_sell_matching()

        # 3. 时间序列完整性检查
        print('\n⏰ 3. 时间序列完整性检查')
        results['time_series'] = self.check_time_series_integrity()

        # 4. 未来函数检查
        print('\n🚨 4. 未来函数检查')
        results['future_function'] = self.check_future_function()

        # 5. 指标计算验证
        print('\n📈 5. 指标计算验证')
        results['indicator_validation'] = self.check_indicator_calculation()

        # 6. 价格数据一致性检查
        print('\n💰 6. 价格数据一致性检查')
        results['price_consistency'] = self.check_price_consistency()

        # 7. 增强因子完整性检查
        print('\n🚀 7. 增强因子完整性检查')
        results['enhanced_factors'] = self.check_enhanced_factors()

        # 8. 生成综合报告
        print('\n📋 8. 生成综合报告')
        self.generate_comprehensive_report(results)

        return results

    def check_field_coverage(self):
        """检查字段覆盖情况"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 获取所有字段
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            all_fields = structure['name'].tolist()

            # 按类别分类字段
            field_categories = {
                '基础字段': ['id', 'timestamp', 'symbol', 'action', 'price', 'volume'],
                '卖出相关': [f for f in all_fields if 'sell' in f.lower() or f in ['cost_price_sell', 'net_profit_pct_sell']],
                '价格动量': [f for f in all_fields if 'price_' in f and ('change' in f or 'momentum' in f)],
                '成交量指标': [f for f in all_fields if 'volume' in f.lower() and f != 'volume'],
                '技术指标RSI': [f for f in all_fields if 'rsi' in f.lower()],
                '技术指标MACD': [f for f in all_fields if 'macd' in f.lower()],
                '技术指标布林带': [f for f in all_fields if 'bb_' in f or 'bollinger' in f.lower()],
                '技术指标ADX': [f for f in all_fields if 'adx' in f.lower()],
                '技术指标KDJ': [f for f in all_fields if 'kdj' in f.lower()],
                '均线指标': [f for f in all_fields if f.startswith('ma') and any(c.isdigit() for c in f)],
                '均线距离': [f for f in all_fields if 'ma' in f and 'distance' in f],
                '波动率指标': [f for f in all_fields if 'volatility' in f.lower() or 'atr' in f.lower()],
                '时间因子': [f for f in all_fields if any(t in f for t in ['hour', 'day', 'week', 'month', 'quarter', 'year', 'time'])],
                '基本面指标': [f for f in all_fields if any(t in f for t in ['pe_', 'pb_', 'roe', 'roa', 'eps', 'revenue'])],
                '市场环境': [f for f in all_fields if 'market' in f.lower()],
                '资金流向': [f for f in all_fields if any(t in f for t in ['money_flow', 'inflow', 'institutional'])],
                '情绪指标': [f for f in all_fields if any(t in f for t in ['sentiment', 'fear', 'greed', 'confidence'])],
                '增强评分': [f for f in all_fields if 'score' in f.lower()],
                '风险指标': [f for f in all_fields if any(t in f for t in ['risk', 'drawdown', 'var', 'beta'])],
                '模式识别': [f for f in all_fields if 'pattern' in f.lower()],
                '高级技术': [f for f in all_fields if any(t in f for t in ['ichimoku', 'parabolic', 'supertrend', 'pivot'])],
                '微观结构': [f for f in all_fields if any(t in f for t in ['bid_ask', 'spread', 'depth', 'liquidity'])],
                '另类数据': [f for f in all_fields if any(t in f for t in ['satellite', 'parking', 'patent', 'search'])],
                '行为金融': [f for f in all_fields if any(t in f for t in ['herding', 'anchoring', 'bias', 'psychology'])],
                '其他指标': []
            }

            # 分类所有字段
            categorized_fields = set()
            for category, fields in field_categories.items():
                categorized_fields.update(fields)

            # 找出未分类的字段
            uncategorized = [f for f in all_fields if f not in categorized_fields]
            field_categories['其他指标'] = uncategorized

            # 统计每个类别的字段数量和完整度
            sample_query = "SELECT * FROM trades WHERE action = 'BUY' LIMIT 100"
            sample_data = pd.read_sql_query(sample_query, conn)

            category_stats = {}
            for category, fields in field_categories.items():
                if fields:
                    valid_fields = [f for f in fields if f in sample_data.columns]
                    if valid_fields:
                        completeness = {}
                        for field in valid_fields:
                            non_null_rate = sample_data[field].notna().mean()
                            completeness[field] = non_null_rate

                        category_stats[category] = {
                            'total_fields': len(fields),
                            'valid_fields': len(valid_fields),
                            'completeness': completeness,
                            'avg_completeness': np.mean(list(completeness.values())) if completeness else 0
                        }

            conn.close()

            print(f'  📊 总字段数: {len(all_fields)}')
            print(f'  📋 字段分类: {len([c for c in field_categories.values() if c])} 个类别')

            # 显示主要类别的统计
            for category, stats in category_stats.items():
                if stats['total_fields'] > 0:
                    print(f'  📈 {category}: {stats["valid_fields"]}/{stats["total_fields"]} 字段, 平均完整度: {stats["avg_completeness"]:.1%}')

            return {
                'total_fields': len(all_fields),
                'field_categories': field_categories,
                'category_stats': category_stats,
                'status': 'PASS'
            }

        except Exception as e:
            print(f'  ❌ 字段覆盖检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}

    def check_basic_integrity(self):
        """检查基础数据完整性"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取表结构
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            
            # 获取数据样本
            sample_query = "SELECT * FROM trades LIMIT 1000"
            sample_data = pd.read_sql_query(sample_query, conn)
            
            # 检查关键字段
            required_fields = ['symbol', 'timestamp', 'action', 'price', 'volume']
            missing_fields = [field for field in required_fields if field not in sample_data.columns]
            
            # 检查空值情况
            null_stats = {}
            for col in sample_data.columns:
                null_count = sample_data[col].isnull().sum()
                null_pct = null_count / len(sample_data) * 100
                if null_pct > 0:
                    null_stats[col] = {'count': null_count, 'percentage': null_pct}
            
            # 检查数据类型
            dtype_issues = []
            for col in ['price', 'volume']:
                if col in sample_data.columns:
                    if not pd.api.types.is_numeric_dtype(sample_data[col]):
                        dtype_issues.append(f'{col} 不是数值类型')
            
            # 检查记录总数
            count_query = "SELECT COUNT(*) as total FROM trades"
            total_count = pd.read_sql_query(count_query, conn)['total'].iloc[0]
            
            conn.close()
            
            print(f'  📊 总记录数: {total_count:,}')
            print(f'  📋 字段数量: {len(structure)}')
            print(f'  ❌ 缺失关键字段: {missing_fields if missing_fields else "无"}')
            print(f'  🔢 数据类型问题: {len(dtype_issues)}')
            print(f'  ⚠️ 空值字段数: {len(null_stats)}')
            
            if null_stats:
                print('  空值详情:')
                for field, stats in list(null_stats.items())[:5]:
                    print(f'    {field}: {stats["count"]} ({stats["percentage"]:.1f}%)')
            
            return {
                'total_records': total_count,
                'total_fields': len(structure),
                'missing_required_fields': missing_fields,
                'dtype_issues': dtype_issues,
                'null_stats': null_stats,
                'status': 'PASS' if not missing_fields and not dtype_issues else 'FAIL'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}
    
    def check_buy_sell_matching(self):
        """检查买卖记录对应性"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 统计买卖记录数量
            action_stats_query = '''
                SELECT action, COUNT(*) as count
                FROM trades
                GROUP BY action
            '''
            action_stats = pd.read_sql_query(action_stats_query, conn)
            
            # 按股票统计买卖记录
            symbol_stats_query = '''
                SELECT symbol, action, COUNT(*) as count
                FROM trades
                GROUP BY symbol, action
                ORDER BY symbol
            '''
            symbol_stats = pd.read_sql_query(symbol_stats_query, conn)
            
            # 检查买卖不匹配的股票
            unmatched_symbols = []
            symbol_groups = symbol_stats.groupby('symbol')
            
            for symbol, group in symbol_groups:
                buy_count = group[group['action'] == 'BUY']['count'].sum()
                sell_count = group[group['action'] == 'SELL']['count'].sum()
                
                if abs(buy_count - sell_count) > 1:  # 允许1的差异（可能是未完成的交易）
                    unmatched_symbols.append({
                        'symbol': symbol,
                        'buy_count': buy_count,
                        'sell_count': sell_count,
                        'difference': buy_count - sell_count
                    })
            
            # 检查孤立的买入记录（没有对应卖出）
            orphan_buys_query = '''
                SELECT b.symbol, COUNT(*) as orphan_count
                FROM trades b
                WHERE b.action = 'BUY'
                AND NOT EXISTS (
                    SELECT 1 FROM trades s 
                    WHERE s.symbol = b.symbol 
                    AND s.action = 'SELL' 
                    AND s.timestamp > b.timestamp
                )
                GROUP BY b.symbol
                HAVING COUNT(*) > 0
            '''
            orphan_buys = pd.read_sql_query(orphan_buys_query, conn)
            
            conn.close()
            
            print(f'  📊 动作统计:')
            for _, row in action_stats.iterrows():
                print(f'    {row["action"]}: {row["count"]:,}')
            
            print(f'  🔗 不匹配股票数: {len(unmatched_symbols)}')
            print(f'  👤 孤立买入股票数: {len(orphan_buys)}')
            
            if unmatched_symbols:
                print('  不匹配详情 (前5个):')
                for item in unmatched_symbols[:5]:
                    print(f'    {item["symbol"]}: 买{item["buy_count"]} 卖{item["sell_count"]} (差{item["difference"]})')
            
            return {
                'action_stats': action_stats.to_dict('records'),
                'unmatched_count': len(unmatched_symbols),
                'unmatched_symbols': unmatched_symbols[:10],  # 只保存前10个
                'orphan_buys_count': len(orphan_buys),
                'status': 'PASS' if len(unmatched_symbols) < 10 else 'WARNING'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}
    
    def check_time_series_integrity(self):
        """检查时间序列完整性"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 检查时间格式和范围
            time_stats_query = '''
                SELECT 
                    MIN(timestamp) as min_time,
                    MAX(timestamp) as max_time,
                    COUNT(DISTINCT DATE(timestamp)) as unique_dates,
                    COUNT(*) as total_records
                FROM trades
            '''
            time_stats = pd.read_sql_query(time_stats_query, conn)
            
            # 检查交易时间分布
            hour_dist_query = '''
                SELECT 
                    strftime('%H', timestamp) as hour,
                    COUNT(*) as count
                FROM trades
                GROUP BY strftime('%H', timestamp)
                ORDER BY hour
            '''
            hour_dist = pd.read_sql_query(hour_dist_query, conn)
            
            # 检查异常时间点
            abnormal_times_query = '''
                SELECT timestamp, COUNT(*) as count
                FROM trades
                GROUP BY timestamp
                HAVING COUNT(*) > 100
                ORDER BY count DESC
                LIMIT 10
            '''
            abnormal_times = pd.read_sql_query(abnormal_times_query, conn)
            
            # 检查时间顺序
            time_order_query = '''
                SELECT symbol, timestamp, action, 
                       LAG(timestamp) OVER (PARTITION BY symbol ORDER BY timestamp) as prev_time
                FROM trades
                WHERE action = 'BUY'
                ORDER BY symbol, timestamp
                LIMIT 1000
            '''
            time_order_check = pd.read_sql_query(time_order_query, conn)
            
            conn.close()
            
            # 分析结果
            min_time = time_stats['min_time'].iloc[0]
            max_time = time_stats['max_time'].iloc[0]
            unique_dates = time_stats['unique_dates'].iloc[0]
            total_records = time_stats['total_records'].iloc[0]
            
            print(f'  📅 时间范围: {min_time} 到 {max_time}')
            print(f'  📊 交易天数: {unique_dates}')
            print(f'  📈 平均每日记录: {total_records / unique_dates:.1f}')
            print(f'  ⏰ 异常时间点: {len(abnormal_times)}')
            
            # 检查交易时间是否在正常范围内
            normal_hours = set(['09', '10', '11', '13', '14', '15'])
            abnormal_hours = []
            for _, row in hour_dist.iterrows():
                if row['hour'] not in normal_hours and row['count'] > 10:
                    abnormal_hours.append(f"{row['hour']}:xx ({row['count']}笔)")
            
            if abnormal_hours:
                print(f'  🚨 异常交易时间: {", ".join(abnormal_hours)}')
            
            return {
                'time_range': {'min': min_time, 'max': max_time},
                'unique_dates': unique_dates,
                'avg_daily_records': total_records / unique_dates,
                'abnormal_times_count': len(abnormal_times),
                'abnormal_hours': abnormal_hours,
                'status': 'PASS' if len(abnormal_hours) == 0 else 'WARNING'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}
    
    def check_future_function(self):
        """检查未来函数"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 检查当日数据使用情况
            print('  🔍 检查当日数据使用...')

            # 首先获取实际存在的字段
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            available_columns = structure['name'].tolist()

            # 构建查询，只使用存在的字段
            base_columns = ['timestamp', 'symbol', 'action', 'price']
            indicator_columns = []

            # 检查哪些指标字段存在
            potential_indicators = [
                'ma5', 'ma10', 'ma20', 'ma60', 'ma120',
                'rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d',
                'macd_12_26', 'macd_signal_9', 'macd_histogram',
                'volume_ma5_ratio', 'volume_ma10_ratio', 'relative_volume',
                'volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d',
                'price_change_pct', 'price_momentum_3d', 'price_momentum_5d',
                'ma5_distance_pct', 'ma10_distance_pct', 'ma20_distance_pct',
                'atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized',
                'bb_upper_20', 'bb_middle_20', 'bb_lower_20'
            ]

            for col in potential_indicators:
                if col in available_columns:
                    indicator_columns.append(col)

            # 构建查询语句
            all_columns = base_columns + indicator_columns[:15]  # 限制字段数量避免查询过长
            columns_str = ', '.join(all_columns)

            sample_query = f'''
                SELECT {columns_str}
                FROM trades
                WHERE action = 'BUY'
                AND timestamp IS NOT NULL
                ORDER BY timestamp
                LIMIT 500
            '''

            print(f'  📊 使用字段: {len(all_columns)} 个')
            sample_data = pd.read_sql_query(sample_query, conn)
            
            if len(sample_data) == 0:
                print('  ⚠️ 没有找到买入记录')
                return {'status': 'WARNING', 'message': '没有买入记录'}
            
            # 转换时间格式
            sample_data['timestamp'] = pd.to_datetime(sample_data['timestamp'])
            sample_data['date'] = sample_data['timestamp'].dt.date
            sample_data['time'] = sample_data['timestamp'].dt.time
            
            # 检查各个时间段的指标计算
            future_function_issues = []
            
            # 1. 检查开盘时段的指标
            morning_trades = sample_data[
                (sample_data['timestamp'].dt.hour >= 9) &
                (sample_data['timestamp'].dt.hour <= 10)
            ]

            if len(morning_trades) > 0:
                # 检查当日价格变化指标
                if 'price_change_pct' in sample_data.columns:
                    morning_with_price_change = morning_trades[
                        morning_trades['price_change_pct'].notna()
                    ]

                    if len(morning_with_price_change) > 0:
                        print(f'  ⚠️ 发现 {len(morning_with_price_change)} 笔早盘交易使用了当日价格变化指标')
                        future_function_issues.append({
                            'type': '当日价格变化',
                            'count': len(morning_with_price_change),
                            'description': '早盘交易使用了可能包含未来信息的当日价格变化'
                        })
            
            # 2. 检查成交量相关指标
            if 'volume_ma5_ratio' in sample_data.columns:
                volume_issues = sample_data[
                    (sample_data['volume_ma5_ratio'].notna()) &
                    (sample_data['timestamp'].dt.hour < 15)  # 收盘前
                ]

                if len(volume_issues) > 0:
                    print(f'  ⚠️ 发现 {len(volume_issues)} 笔收盘前交易使用了成交量比率指标')
                    future_function_issues.append({
                        'type': '成交量比率',
                        'count': len(volume_issues),
                        'description': '收盘前使用了可能包含当日全天成交量的指标'
                    })
            
            # 3. 检查波动率指标的时效性
            volatility_issues = []
            volatility_columns = [col for col in sample_data.columns if 'volatility' in col.lower()]

            if volatility_columns:
                for _, trade in sample_data.head(50).iterrows():
                    # 检查第一个波动率字段
                    vol_col = volatility_columns[0]
                    if pd.notna(trade[vol_col]):
                        # 这里应该检查波动率计算是否使用了当日完整数据
                        # 简化检查：如果是开盘后很短时间内的交易，波动率不应该包含当日数据
                        if trade['timestamp'].hour == 9 and trade['timestamp'].minute < 45:
                            volatility_issues.append(trade['symbol'])

                if volatility_issues:
                    print(f'  ⚠️ 发现 {len(volatility_issues)} 笔早盘交易可能使用了包含当日数据的波动率')
                    future_function_issues.append({
                        'type': '波动率计算',
                        'count': len(volatility_issues),
                        'description': '早盘交易使用的波动率可能包含当日数据'
                    })
            
            # 4. 检查技术指标的计算基准
            ma_base_issues = []
            if 'ma5' in sample_data.columns:
                for _, trade in sample_data.head(100).iterrows():
                    if pd.notna(trade['ma5']) and pd.notna(trade['price']):
                        # 检查均线是否异常接近当前价格（可能使用了当日收盘价）
                        ma_price_diff = abs(trade['ma5'] - trade['price']) / trade['price']
                        if ma_price_diff < 0.001:  # 差异小于0.1%
                            ma_base_issues.append(trade['symbol'])

                if ma_base_issues:
                    print(f'  ⚠️ 发现 {len(ma_base_issues)} 笔交易的均线值异常接近当前价格')
                    future_function_issues.append({
                        'type': '均线计算基准',
                        'count': len(ma_base_issues),
                        'description': '均线值异常接近当前价格，可能使用了当日收盘价计算'
                    })
            
            conn.close()
            
            print(f'  📊 检查样本数: {len(sample_data)}')
            print(f'  🚨 发现问题类型: {len(future_function_issues)}')
            
            return {
                'sample_size': len(sample_data),
                'issues_count': len(future_function_issues),
                'issues': future_function_issues,
                'status': 'PASS' if len(future_function_issues) == 0 else 'FAIL'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}
    
    def check_indicator_calculation(self):
        """检查指标计算验证"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 获取实际存在的字段
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            available_columns = structure['name'].tolist()

            # 构建查询，只使用存在的字段
            base_columns = ['timestamp', 'symbol', 'price', 'volume']
            indicator_columns = []

            # 检查哪些指标字段存在
            potential_indicators = [
                'ma5', 'ma10', 'ma20', 'ma60', 'ma120',
                'rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d',
                'macd_12_26', 'macd_signal_9', 'macd_histogram',
                'price_change_pct', 'ma5_distance_pct', 'ma10_distance_pct',
                'volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d',
                'atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized'
            ]

            for col in potential_indicators:
                if col in available_columns:
                    indicator_columns.append(col)

            if not indicator_columns:
                print('  ⚠️ 没有找到技术指标字段')
                conn.close()
                return {'status': 'WARNING', 'message': '没有技术指标字段'}

            # 构建查询语句
            all_columns = base_columns + indicator_columns
            columns_str = ', '.join(all_columns)

            # 使用第一个找到的指标作为筛选条件
            first_indicator = indicator_columns[0]

            indicator_query = f'''
                SELECT {columns_str}
                FROM trades
                WHERE action = 'BUY'
                AND {first_indicator} IS NOT NULL
                ORDER BY timestamp
                LIMIT 200
            '''

            print(f'  📊 检查指标: {len(indicator_columns)} 个')
            indicator_data = pd.read_sql_query(indicator_query, conn)
            conn.close()
            
            if len(indicator_data) == 0:
                print('  ⚠️ 没有找到包含指标的记录')
                return {'status': 'WARNING', 'message': '没有指标数据'}
            
            calculation_issues = []
            
            # 1. 检查价格变化百分比计算
            price_change_issues = 0
            for _, row in indicator_data.head(50).iterrows():
                if pd.notna(row['price_change_pct']):
                    # 价格变化应该是合理范围内
                    if abs(row['price_change_pct']) > 20:  # 超过20%的变化可能异常
                        price_change_issues += 1
            
            if price_change_issues > 0:
                calculation_issues.append({
                    'type': '价格变化异常',
                    'count': price_change_issues,
                    'description': '发现异常大的价格变化百分比'
                })
            
            # 2. 检查均线距离计算
            ma_distance_issues = 0
            for _, row in indicator_data.head(50).iterrows():
                if pd.notna(row['ma5_distance_pct']) and pd.notna(row['ma5']) and pd.notna(row['price']):
                    # 计算理论上的均线距离
                    expected_distance = (row['price'] - row['ma5']) / row['ma5'] * 100
                    actual_distance = row['ma5_distance_pct']
                    
                    if abs(expected_distance - actual_distance) > 1:  # 差异超过1%
                        ma_distance_issues += 1
            
            if ma_distance_issues > 0:
                calculation_issues.append({
                    'type': '均线距离计算',
                    'count': ma_distance_issues,
                    'description': '均线距离计算结果与预期不符'
                })
            
            # 3. 检查指标值的合理性
            # 检查RSI类指标（寻找实际存在的RSI字段）
            rsi_columns = [col for col in indicator_data.columns if 'rsi' in col.lower()]
            for rsi_col in rsi_columns:
                rsi_issues = indicator_data[
                    (indicator_data[rsi_col] < 0) | (indicator_data[rsi_col] > 100)
                ]

                if len(rsi_issues) > 0:
                    calculation_issues.append({
                        'type': f'{rsi_col}值异常',
                        'count': len(rsi_issues),
                        'description': f'{rsi_col}值超出0-100范围'
                    })
            
            # 4. 检查指标的非空率
            completeness_stats = {}
            for col in indicator_columns:
                if col in indicator_data.columns:
                    non_null_rate = indicator_data[col].notna().mean()
                    completeness_stats[col] = non_null_rate

                    if non_null_rate < 0.8:  # 完整度低于80%
                        calculation_issues.append({
                            'type': f'{col}完整度低',
                            'count': f'{non_null_rate:.1%}',
                            'description': f'{col}指标的完整度较低'
                        })
            
            print(f'  📊 检查样本数: {len(indicator_data)}')
            print(f'  📈 指标完整度:')
            for col, rate in completeness_stats.items():
                print(f'    {col}: {rate:.1%}')
            print(f'  🚨 计算问题: {len(calculation_issues)}')
            
            return {
                'sample_size': len(indicator_data),
                'completeness_stats': completeness_stats,
                'calculation_issues': calculation_issues,
                'status': 'PASS' if len(calculation_issues) == 0 else 'WARNING'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}
    
    def check_price_consistency(self):
        """检查价格数据一致性"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 检查价格的合理性
            price_stats_query = '''
                SELECT 
                    MIN(price) as min_price,
                    MAX(price) as max_price,
                    AVG(price) as avg_price,
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN price <= 0 THEN 1 END) as invalid_price_count
                FROM trades
                WHERE price IS NOT NULL
            '''
            
            price_stats = pd.read_sql_query(price_stats_query, conn)
            
            # 检查同一时间点同一股票的价格一致性
            price_consistency_query = '''
                SELECT symbol, timestamp, COUNT(DISTINCT price) as price_variants
                FROM trades
                WHERE timestamp IS NOT NULL AND price IS NOT NULL
                GROUP BY symbol, timestamp
                HAVING COUNT(DISTINCT price) > 1
                LIMIT 20
            '''
            
            price_inconsistency = pd.read_sql_query(price_consistency_query, conn)
            
            # 检查价格跳跃异常
            price_jump_query = '''
                SELECT symbol, timestamp, price,
                       LAG(price) OVER (PARTITION BY symbol ORDER BY timestamp) as prev_price
                FROM trades
                WHERE action = 'BUY' AND price IS NOT NULL
                ORDER BY symbol, timestamp
                LIMIT 1000
            '''
            
            price_jumps = pd.read_sql_query(price_jump_query, conn)
            
            conn.close()
            
            # 分析价格跳跃
            abnormal_jumps = []
            for _, row in price_jumps.iterrows():
                if pd.notna(row['prev_price']) and row['prev_price'] > 0:
                    jump_pct = abs(row['price'] - row['prev_price']) / row['prev_price']
                    if jump_pct > 0.2:  # 价格跳跃超过20%
                        abnormal_jumps.append({
                            'symbol': row['symbol'],
                            'timestamp': row['timestamp'],
                            'jump_pct': jump_pct
                        })
            
            min_price = price_stats['min_price'].iloc[0]
            max_price = price_stats['max_price'].iloc[0]
            avg_price = price_stats['avg_price'].iloc[0]
            invalid_count = price_stats['invalid_price_count'].iloc[0]
            
            print(f'  💰 价格范围: {min_price:.2f} - {max_price:.2f}')
            print(f'  📊 平均价格: {avg_price:.2f}')
            print(f'  ❌ 无效价格: {invalid_count}')
            print(f'  🔄 价格不一致: {len(price_inconsistency)}')
            print(f'  📈 异常跳跃: {len(abnormal_jumps)}')
            
            return {
                'price_range': {'min': min_price, 'max': max_price, 'avg': avg_price},
                'invalid_price_count': invalid_count,
                'inconsistency_count': len(price_inconsistency),
                'abnormal_jumps_count': len(abnormal_jumps),
                'abnormal_jumps': abnormal_jumps[:5],  # 只保存前5个
                'status': 'PASS' if invalid_count == 0 and len(abnormal_jumps) < 10 else 'WARNING'
            }
            
        except Exception as e:
            print(f'  ❌ 检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}

    def check_enhanced_factors(self):
        """检查增强因子完整性"""
        try:
            conn = sqlite3.connect(self.db_path)

            # 获取增强因子相关字段
            enhanced_factor_fields = [
                'enhanced_overall_score', 'buy_confidence_score', 'market_environment_score',
                'fundamental_score', 'enhanced_technical_score', 'money_flow_score',
                'risk_score', 'technical_score', 'momentum_score', 'volume_score',
                'volatility_score', 'trend_score', 'overall_score', 'buy_signal_strength',
                'risk_adjusted_score'
            ]

            # 检查字段存在性
            structure_query = "PRAGMA table_info(trades);"
            structure = pd.read_sql_query(structure_query, conn)
            available_fields = structure['name'].tolist()

            existing_enhanced_fields = [f for f in enhanced_factor_fields if f in available_fields]
            missing_enhanced_fields = [f for f in enhanced_factor_fields if f not in available_fields]

            # 获取样本数据检查完整度
            if existing_enhanced_fields:
                fields_str = ', '.join(['timestamp', 'symbol', 'action'] + existing_enhanced_fields)
                sample_query = f'''
                    SELECT {fields_str}
                    FROM trades
                    WHERE action = 'BUY'
                    ORDER BY timestamp DESC
                    LIMIT 200
                '''

                sample_data = pd.read_sql_query(sample_query, conn)

                # 分析增强因子的完整度和分布
                enhanced_stats = {}
                for field in existing_enhanced_fields:
                    if field in sample_data.columns:
                        non_null_count = sample_data[field].notna().sum()
                        non_null_rate = non_null_count / len(sample_data)

                        if non_null_count > 0:
                            field_data = sample_data[field].dropna()
                            enhanced_stats[field] = {
                                'completeness': non_null_rate,
                                'count': non_null_count,
                                'min': field_data.min(),
                                'max': field_data.max(),
                                'mean': field_data.mean(),
                                'std': field_data.std()
                            }
                        else:
                            enhanced_stats[field] = {
                                'completeness': 0,
                                'count': 0,
                                'min': None, 'max': None, 'mean': None, 'std': None
                            }

                # 检查增强因子的合理性
                issues = []

                # 检查评分字段是否在合理范围内
                score_fields = [f for f in existing_enhanced_fields if 'score' in f.lower()]
                for field in score_fields:
                    if field in enhanced_stats and enhanced_stats[field]['count'] > 0:
                        min_val = enhanced_stats[field]['min']
                        max_val = enhanced_stats[field]['max']

                        # 大多数评分应该在0-100范围内
                        if min_val < -10 or max_val > 200:
                            issues.append(f'{field} 值范围异常: [{min_val:.2f}, {max_val:.2f}]')

                # 检查信心度字段
                if 'buy_confidence_score' in enhanced_stats:
                    conf_stats = enhanced_stats['buy_confidence_score']
                    if conf_stats['count'] > 0:
                        if conf_stats['min'] < 0 or conf_stats['max'] > 100:
                            issues.append(f'buy_confidence_score 超出0-100范围')

                conn.close()

                print(f'  📊 增强因子字段: {len(existing_enhanced_fields)}/{len(enhanced_factor_fields)}')
                print(f'  ❌ 缺失字段: {len(missing_enhanced_fields)}')
                print(f'  📈 有数据字段: {len([f for f in enhanced_stats if enhanced_stats[f]["count"] > 0])}')
                print(f'  ⚠️ 数据异常: {len(issues)}')

                if missing_enhanced_fields:
                    print(f'  缺失字段: {", ".join(missing_enhanced_fields[:5])}{"..." if len(missing_enhanced_fields) > 5 else ""}')

                if issues:
                    print(f'  异常详情:')
                    for issue in issues[:3]:
                        print(f'    {issue}')

                # 显示完整度最高的几个字段
                complete_fields = sorted(
                    [(f, stats['completeness']) for f, stats in enhanced_stats.items() if stats['count'] > 0],
                    key=lambda x: x[1], reverse=True
                )

                if complete_fields:
                    print(f'  📈 完整度最高字段:')
                    for field, completeness in complete_fields[:3]:
                        print(f'    {field}: {completeness:.1%}')

                return {
                    'existing_fields': existing_enhanced_fields,
                    'missing_fields': missing_enhanced_fields,
                    'enhanced_stats': enhanced_stats,
                    'issues': issues,
                    'sample_size': len(sample_data),
                    'status': 'PASS' if len(issues) == 0 else 'WARNING'
                }

            else:
                conn.close()
                print(f'  ❌ 没有找到任何增强因子字段')
                return {
                    'existing_fields': [],
                    'missing_fields': enhanced_factor_fields,
                    'status': 'FAIL',
                    'message': '没有增强因子数据'
                }

        except Exception as e:
            print(f'  ❌ 增强因子检查失败: {e}')
            return {'status': 'ERROR', 'error': str(e)}

    def generate_comprehensive_report(self, results):
        """生成综合报告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'数据完整性检查报告_{timestamp}.txt'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write('数据完整性和未来函数检查报告\n')
                f.write('=' * 60 + '\n\n')
                f.write(f'检查时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
                
                # 总体评估
                overall_status = 'PASS'
                critical_issues = 0
                warnings = 0
                
                for check_name, result in results.items():
                    if result.get('status') == 'FAIL':
                        overall_status = 'FAIL'
                        critical_issues += 1
                    elif result.get('status') == 'WARNING':
                        warnings += 1
                        if overall_status == 'PASS':
                            overall_status = 'WARNING'
                
                f.write(f'总体评估: {overall_status}\n')
                f.write(f'严重问题: {critical_issues}\n')
                f.write(f'警告问题: {warnings}\n\n')
                
                # 详细结果
                for check_name, result in results.items():
                    f.write(f'{check_name.upper()}:\n')
                    f.write(f'状态: {result.get("status", "UNKNOWN")}\n')
                    
                    if check_name == 'basic_integrity':
                        f.write(f'总记录数: {result.get("total_records", 0):,}\n')
                        f.write(f'缺失字段: {result.get("missing_required_fields", [])}\n')
                    
                    elif check_name == 'buy_sell_matching':
                        f.write(f'不匹配股票数: {result.get("unmatched_count", 0)}\n')
                        f.write(f'孤立买入数: {result.get("orphan_buys_count", 0)}\n')
                    
                    elif check_name == 'future_function':
                        f.write(f'未来函数问题: {result.get("issues_count", 0)}\n')
                        for issue in result.get("issues", []):
                            f.write(f'  - {issue["type"]}: {issue["count"]} ({issue["description"]})\n')
                    
                    f.write('\n')
                
                # 建议
                f.write('建议和后续行动:\n')
                f.write('-' * 30 + '\n')
                
                if overall_status == 'FAIL':
                    f.write('🚨 发现严重问题，建议:\n')
                    f.write('1. 立即停止使用当前数据进行策略分析\n')
                    f.write('2. 修复数据完整性问题\n')
                    f.write('3. 重新验证指标计算逻辑\n')
                    f.write('4. 消除未来函数\n')
                elif overall_status == 'WARNING':
                    f.write('⚠️ 发现一些问题，建议:\n')
                    f.write('1. 谨慎使用当前分析结果\n')
                    f.write('2. 优化数据质量\n')
                    f.write('3. 验证关键发现\n')
                else:
                    f.write('✅ 数据质量良好，可以继续分析\n')
                    f.write('1. 定期进行数据质量检查\n')
                    f.write('2. 监控新数据的质量\n')
            
            print(f'✅ 综合报告已保存: {report_file}')
            
            # 控制台总结
            print(f'\n📋 检查总结:')
            print(f'  总体状态: {overall_status}')
            print(f'  严重问题: {critical_issues}')
            print(f'  警告问题: {warnings}')
            
            if overall_status == 'FAIL':
                print(f'  🚨 建议: 立即修复数据问题后再进行分析')
            elif overall_status == 'WARNING':
                print(f'  ⚠️ 建议: 谨慎使用分析结果，优化数据质量')
            else:
                print(f'  ✅ 建议: 数据质量良好，可以继续分析')
            
        except Exception as e:
            print(f'❌ 生成报告失败: {e}')

def main():
    """主函数"""
    print('🔍 数据完整性和未来函数检查器')
    print('=' * 80)
    
    checker = DataIntegrityChecker()
    results = checker.check_all()
    
    return results

if __name__ == "__main__":
    main()
