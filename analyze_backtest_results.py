# coding=utf-8
"""
分析回测结果数据
基于实际数据优化多因子策略胜率
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def analyze_recent_performance():
    """分析最近的策略表现"""
    print('📊 实际回测数据分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 分析所有买入记录
        query = """
        SELECT 
            timestamp, symbol, action, buy_signal_type, signal_reason,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy,
            net_profit_pct_sell, holding_hours
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 总买入记录: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有找到买入记录')
            return None
        
        # 分析信号类型分布
        if 'buy_signal_type' in df.columns:
            signal_types = df['buy_signal_type'].value_counts()
            print(f'\n📋 信号类型分布:')
            for signal_type, count in signal_types.items():
                percentage = count / len(df) * 100
                print(f'   {signal_type}: {count}条 ({percentage:.1f}%)')
        
        # 分析多因子策略表现
        multifactor_df = df[df['buy_signal_type'] == 'multifactor_comprehensive']
        print(f'\n🎯 多因子策略分析:')
        print(f'   多因子信号数: {len(multifactor_df)} 条')
        
        if len(multifactor_df) > 0:
            # 计算胜率
            completed_trades = multifactor_df.dropna(subset=['net_profit_pct_sell'])
            if len(completed_trades) > 0:
                wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
                win_rate = wins / len(completed_trades) * 100
                avg_profit = completed_trades['net_profit_pct_sell'].mean()
                avg_win = completed_trades[completed_trades['net_profit_pct_sell'] > 0]['net_profit_pct_sell'].mean()
                avg_loss = abs(completed_trades[completed_trades['net_profit_pct_sell'] <= 0]['net_profit_pct_sell'].mean())
                
                print(f'   已完成交易: {len(completed_trades)} 条')
                print(f'   胜率: {win_rate:.1f}% ({wins}/{len(completed_trades)})')
                print(f'   平均收益: {avg_profit:.2f}%')
                print(f'   平均盈利: {avg_win:.2f}%')
                print(f'   平均亏损: {avg_loss:.2f}%')
                print(f'   盈亏比: {avg_win/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
            else:
                print(f'   ⚠️ 暂无已完成的多因子交易')
        
        return df
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def analyze_score_distribution(df):
    """分析评分分布"""
    print(f'\n📊 多因子评分分布分析')
    print('=' * 50)
    
    # 过滤出有评分数据的记录
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    score_df = df.dropna(subset=score_columns, how='all')
    
    if len(score_df) == 0:
        print('⚠️ 没有找到评分数据')
        return
    
    print(f'📈 有评分数据的记录: {len(score_df)} 条')
    
    # 分析各评分的分布
    print(f'\n📊 评分统计:')
    for col in score_columns:
        if col in score_df.columns:
            values = score_df[col].dropna()
            if len(values) > 0:
                print(f'   {col}:')
                print(f'     平均值: {values.mean():.3f}')
                print(f'     中位数: {values.median():.3f}')
                print(f'     最小值: {values.min():.3f}')
                print(f'     最大值: {values.max():.3f}')
                print(f'     标准差: {values.std():.3f}')

def analyze_winning_patterns(df):
    """分析获胜模式"""
    print(f'\n🏆 获胜模式分析')
    print('=' * 40)
    
    # 过滤出已完成且盈利的交易
    winning_trades = df[(df['net_profit_pct_sell'].notna()) & (df['net_profit_pct_sell'] > 0)]
    losing_trades = df[(df['net_profit_pct_sell'].notna()) & (df['net_profit_pct_sell'] <= 0)]
    
    print(f'📈 盈利交易: {len(winning_trades)} 条')
    print(f'📉 亏损交易: {len(losing_trades)} 条')
    
    if len(winning_trades) == 0:
        print('⚠️ 暂无盈利交易数据')
        return
    
    # 分析盈利交易的评分特征
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    print(f'\n📊 盈利交易评分特征:')
    for col in score_columns:
        if col in winning_trades.columns:
            win_values = winning_trades[col].dropna()
            if len(win_values) > 0:
                win_avg = win_values.mean()
                
                if len(losing_trades) > 0:
                    loss_values = losing_trades[col].dropna()
                    if len(loss_values) > 0:
                        loss_avg = loss_values.mean()
                        diff = win_avg - loss_avg
                        print(f'   {col}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} (差值: {diff:+.3f})')
                    else:
                        print(f'   {col}: 盈利{win_avg:.3f} (无亏损对比)')
                else:
                    print(f'   {col}: 盈利{win_avg:.3f}')
    
    # 分析技术指标特征
    tech_columns = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    print(f'\n📋 盈利交易技术指标特征:')
    for col in tech_columns:
        if col in winning_trades.columns:
            win_values = winning_trades[col].dropna()
            if len(win_values) > 0:
                win_avg = win_values.mean()
                
                if len(losing_trades) > 0:
                    loss_values = losing_trades[col].dropna()
                    if len(loss_values) > 0:
                        loss_avg = loss_values.mean()
                        diff = win_avg - loss_avg
                        print(f'   {col}: 盈利{win_avg:.3f} vs 亏损{loss_avg:.3f} (差值: {diff:+.3f})')
                    else:
                        print(f'   {col}: 盈利{win_avg:.3f} (无亏损对比)')
                else:
                    print(f'   {col}: 盈利{win_avg:.3f}')

def find_optimal_thresholds(df):
    """寻找最优阈值"""
    print(f'\n🎯 最优阈值分析')
    print('=' * 40)
    
    # 过滤出已完成的交易
    completed_trades = df.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) < 10:
        print('⚠️ 已完成交易数量不足，无法进行阈值优化')
        return
    
    print(f'📊 分析样本: {len(completed_trades)} 条已完成交易')
    
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    optimal_thresholds = {}
    
    for col in score_columns:
        if col in completed_trades.columns:
            values = completed_trades[col].dropna()
            profits = completed_trades.loc[values.index, 'net_profit_pct_sell']
            
            if len(values) < 5:
                continue
            
            # 测试不同阈值的胜率
            best_threshold = None
            best_win_rate = 0
            best_sample_size = 0
            
            # 测试从10%到90%的阈值
            for percentile in range(10, 91, 10):
                threshold = values.quantile(percentile / 100)
                
                # 计算超过阈值的交易胜率
                above_threshold = values >= threshold
                if above_threshold.sum() >= 3:  # 至少3个样本
                    above_profits = profits[above_threshold]
                    win_rate = (above_profits > 0).mean() * 100
                    sample_size = len(above_profits)
                    
                    # 优先考虑胜率，但也要保证足够的样本量
                    if (win_rate > best_win_rate and sample_size >= 3) or \
                       (win_rate >= best_win_rate and sample_size > best_sample_size):
                        best_threshold = threshold
                        best_win_rate = win_rate
                        best_sample_size = sample_size
            
            if best_threshold is not None:
                optimal_thresholds[col] = {
                    'threshold': best_threshold,
                    'win_rate': best_win_rate,
                    'sample_size': best_sample_size
                }
                
                print(f'   {col}: {best_threshold:.3f} (胜率: {best_win_rate:.1f}%, 样本: {best_sample_size})')
    
    return optimal_thresholds

def generate_optimized_config(optimal_thresholds):
    """生成优化的配置"""
    print(f'\n⚙️ 生成优化配置')
    print('=' * 40)
    
    if not optimal_thresholds:
        print('⚠️ 没有足够数据生成优化配置')
        return
    
    # 映射评分名称到配置键名
    score_mapping = {
        'overall_score': 'min_overall_score',
        'technical_score': 'min_technical_score',
        'momentum_score': 'min_momentum_score',
        'volume_score': 'min_volume_score',
        'volatility_score': 'min_volatility_score',
        'trend_score': 'min_trend_score',
        'buy_signal_strength': 'min_buy_signal_strength',
        'risk_adjusted_score': 'min_risk_adjusted_score'
    }
    
    print(f'📝 基于实际数据的优化配置:')
    print(f'```python')
    print(f'# 基于回测数据优化的多因子阈值')
    print(f'MULTIFACTOR_THRESHOLDS = {{')
    
    for score_name, config_key in score_mapping.items():
        if score_name in optimal_thresholds:
            threshold_data = optimal_thresholds[score_name]
            threshold = threshold_data['threshold']
            win_rate = threshold_data['win_rate']
            sample_size = threshold_data['sample_size']
            
            # 适当调整阈值，避免过拟合
            adjusted_threshold = max(0.1, threshold * 0.9)  # 降低10%避免过拟合
            
            print(f"    '{config_key}': {adjusted_threshold:.3f},  # 胜率: {win_rate:.1f}%, 样本: {sample_size}")
        else:
            # 使用当前配置的默认值
            from config import get_config_value
            current_thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
            current_value = current_thresholds.get(config_key, 0.3)
            print(f"    '{config_key}': {current_value:.3f},  # 保持当前值")
    
    print(f'}}')
    print(f'```')
    
    # 建议确认条件
    total_scores = len(optimal_thresholds)
    suggested_min_count = max(2, min(4, total_scores // 2))
    
    print(f'\n📋 建议确认条件:')
    print(f'```python')
    print(f'MULTIFACTOR_CONFIRMATIONS = {{')
    print(f"    'require_multiple_scores': True,")
    print(f"    'min_score_count': {suggested_min_count},  # 基于可用评分数量调整")
    print(f"    'require_technical_confirmation': True,")
    print(f"    'require_momentum_confirmation': True,")
    print(f"    'require_volume_confirmation': True,")
    print(f'}}')
    print(f'```')

def analyze_time_patterns(df):
    """分析时间模式"""
    print(f'\n⏰ 时间模式分析')
    print('=' * 40)
    
    if 'timestamp' not in df.columns:
        print('⚠️ 没有时间戳数据')
        return
    
    # 转换时间戳
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df['hour'] = df['timestamp'].dt.hour
    df['day_of_week'] = df['timestamp'].dt.dayofweek
    
    # 分析小时分布
    hourly_dist = df['hour'].value_counts().sort_index()
    print(f'📊 小时分布 (前5个):')
    for hour, count in hourly_dist.head().items():
        percentage = count / len(df) * 100
        print(f'   {hour:02d}:00: {count}条 ({percentage:.1f}%)')
    
    # 分析星期分布
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    weekly_dist = df['day_of_week'].value_counts().sort_index()
    print(f'\n📅 星期分布:')
    for day, count in weekly_dist.items():
        percentage = count / len(df) * 100
        day_name = weekday_names[day] if day < len(weekday_names) else f'第{day}天'
        print(f'   {day_name}: {count}条 ({percentage:.1f}%)')

def main():
    """主函数"""
    print('🚀 基于实际回测数据的胜率优化')
    print('=' * 60)
    
    # 分析最近表现
    df = analyze_recent_performance()
    
    if df is not None and len(df) > 0:
        # 分析评分分布
        analyze_score_distribution(df)
        
        # 分析获胜模式
        analyze_winning_patterns(df)
        
        # 寻找最优阈值
        optimal_thresholds = find_optimal_thresholds(df)
        
        # 生成优化配置
        if optimal_thresholds:
            generate_optimized_config(optimal_thresholds)
        
        # 分析时间模式
        analyze_time_patterns(df)
        
        print(f'\n🎯 优化建议总结')
        print('=' * 40)
        print('✅ 基于实际回测数据的分析完成')
        print('📊 已识别盈利交易的关键特征')
        print('🎯 已生成数据驱动的优化配置')
        print('')
        print('🚀 下一步: 应用优化配置并继续监控表现')
    else:
        print('❌ 没有足够的回测数据进行分析')
        print('💡 建议: 继续运行策略收集更多数据')

if __name__ == '__main__':
    main()
