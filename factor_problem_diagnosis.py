# coding=utf-8
"""
因子问题诊断
分析为什么开盘时符合条件比较多，因子分析是否合理
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def diagnose_factor_problems():
    """诊断因子问题"""
    print('🔍 因子问题深度诊断')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入记录
        query = """
        SELECT 
            timestamp, symbol, action, price,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 买入记录: {len(df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['minute'] = df['timestamp'].dt.minute
        df['time_label'] = df['hour'].astype(str) + ':' + df['minute'].astype(str).str.zfill(2)
        
        return df
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None

def analyze_signal_concentration(df):
    """分析信号集中度问题"""
    print(f'\n📊 买入信号集中度分析')
    print('=' * 50)
    
    # 按时间分析买入信号分布
    hourly_distribution = df.groupby('hour').size().sort_values(ascending=False)
    
    print(f'🕐 按小时买入信号分布:')
    total_signals = len(df)
    
    for hour, count in hourly_distribution.head(10).items():
        percentage = count / total_signals * 100
        print(f'   {hour:02d}:00: {count}笔 ({percentage:.1f}%)')
    
    # 分析开盘时段的问题
    opening_signals = df[df['hour'] == 9]
    if len(opening_signals) > 0:
        opening_percentage = len(opening_signals) / total_signals * 100
        print(f'\n🚨 开盘时段(09:00)问题分析:')
        print(f'   买入信号: {len(opening_signals)}笔 ({opening_percentage:.1f}%)')
        print(f'   问题: 信号过度集中在开盘时段')
        
        # 分析开盘时段的因子特征
        print(f'\n📋 开盘时段因子特征:')
        
        factor_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                         'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
        
        for factor in factor_columns:
            if factor in opening_signals.columns:
                opening_values = opening_signals[factor].dropna()
                all_values = df[factor].dropna()
                
                if len(opening_values) > 0 and len(all_values) > 0:
                    opening_mean = opening_values.mean()
                    all_mean = all_values.mean()
                    diff = opening_mean - all_mean
                    
                    print(f'   {factor}: 开盘{opening_mean:.3f} vs 全天{all_mean:.3f} ({diff:+.3f})')

def analyze_threshold_problems(df):
    """分析阈值设置问题"""
    print(f'\n🎯 阈值设置问题分析')
    print('=' * 50)
    
    # 获取当前阈值
    from config import get_config_value
    current_thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    print(f'📋 当前阈值vs实际数据分析:')
    
    threshold_mapping = {
        'min_overall_score': 'overall_score',
        'min_technical_score': 'technical_score',
        'min_momentum_score': 'momentum_score',
        'min_volume_score': 'volume_score',
        'min_volatility_score': 'volatility_score',
        'min_trend_score': 'trend_score',
        'min_buy_signal_strength': 'buy_signal_strength',
        'min_risk_adjusted_score': 'risk_adjusted_score'
    }
    
    threshold_problems = []
    
    for threshold_key, factor_name in threshold_mapping.items():
        if threshold_key in current_thresholds and factor_name in df.columns:
            threshold_value = current_thresholds[threshold_key]
            factor_values = df[factor_name].dropna()
            
            if len(factor_values) > 0:
                # 计算满足阈值的比例
                above_threshold_count = (factor_values >= threshold_value).sum()
                above_threshold_ratio = above_threshold_count / len(factor_values)
                
                # 计算分位数
                percentile_rank = (factor_values < threshold_value).mean() * 100
                
                print(f'   {factor_name}:')
                print(f'     阈值: {threshold_value}')
                print(f'     满足条件: {above_threshold_count}/{len(factor_values)} ({above_threshold_ratio:.1%})')
                print(f'     阈值分位数: {percentile_rank:.1f}%')
                
                # 判断阈值是否合理
                if above_threshold_ratio > 0.8:
                    threshold_problems.append(f'{factor_name}: 阈值过低，{above_threshold_ratio:.1%}满足条件')
                elif above_threshold_ratio < 0.1:
                    threshold_problems.append(f'{factor_name}: 阈值过高，仅{above_threshold_ratio:.1%}满足条件')
                elif percentile_rank < 10:
                    threshold_problems.append(f'{factor_name}: 阈值设在{percentile_rank:.1f}%分位数，过于宽松')
    
    if threshold_problems:
        print(f'\n⚠️ 阈值设置问题:')
        for i, problem in enumerate(threshold_problems, 1):
            print(f'   {i}. {problem}')
    else:
        print(f'\n✅ 阈值设置基本合理')

def analyze_factor_calculation_logic(df):
    """分析因子计算逻辑问题"""
    print(f'\n🔧 因子计算逻辑分析')
    print('=' * 50)
    
    print(f'🎯 检查因子计算是否存在问题:')
    
    # 检查评分因子的分布
    score_factors = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    for factor in score_factors:
        if factor in df.columns:
            values = df[factor].dropna()
            if len(values) > 0:
                # 检查分布特征
                mean_val = values.mean()
                std_val = values.std()
                min_val = values.min()
                max_val = values.max()
                
                # 检查是否存在异常
                issues = []
                
                # 检查是否所有值都相同或接近
                if std_val < 0.01:
                    issues.append(f'标准差过小({std_val:.4f})，可能计算有误')
                
                # 检查是否超出正常范围
                if min_val < -0.1 or max_val > 1.1:
                    issues.append(f'值超出正常范围[{min_val:.3f}, {max_val:.3f}]')
                
                # 检查是否过度集中
                unique_values = len(values.unique())
                if unique_values < 10:
                    issues.append(f'唯一值过少({unique_values}个)，可能离散化过度')
                
                status = "⚠️" if issues else "✅"
                print(f'   {factor}: 均值{mean_val:.3f}, 标准差{std_val:.3f}, 唯一值{unique_values} {status}')
                
                for issue in issues:
                    print(f'     - {issue}')

def analyze_signal_quality_by_time(df):
    """按时间分析信号质量"""
    print(f'\n📈 按时间段分析信号质量')
    print('=' * 50)
    
    # 按小时分组分析信号质量
    hourly_analysis = df.groupby('hour').agg({
        'overall_score': ['count', 'mean', 'std'],
        'technical_score': 'mean',
        'volatility_score': 'mean',
        'atr_pct': 'mean',
        'bb_width': 'mean'
    }).round(3)
    
    print(f'📊 各时段信号质量对比:')
    print(f'时段    信号数   综合评分   技术评分   波动评分   ATR      BB宽度')
    print(f'-' * 70)
    
    for hour in sorted(df['hour'].unique()):
        hour_data = df[df['hour'] == hour]
        if len(hour_data) > 10:  # 只显示有足够样本的时段
            signal_count = len(hour_data)
            overall_mean = hour_data['overall_score'].mean() if 'overall_score' in hour_data.columns else 0
            technical_mean = hour_data['technical_score'].mean() if 'technical_score' in hour_data.columns else 0
            volatility_mean = hour_data['volatility_score'].mean() if 'volatility_score' in hour_data.columns else 0
            atr_mean = hour_data['atr_pct'].mean() if 'atr_pct' in hour_data.columns else 0
            bb_mean = hour_data['bb_width'].mean() if 'bb_width' in hour_data.columns else 0
            
            print(f'{hour:02d}:00   {signal_count:6d}   {overall_mean:8.3f}   {technical_mean:8.3f}   '
                  f'{volatility_mean:8.3f}   {atr_mean:7.2f}   {bb_mean:7.2f}')
    
    # 分析开盘时段是否因子值异常
    opening_hour = 9
    if opening_hour in df['hour'].values:
        opening_data = df[df['hour'] == opening_hour]
        other_data = df[df['hour'] != opening_hour]
        
        print(f'\n🔍 开盘时段vs其他时段对比:')
        
        comparison_factors = ['overall_score', 'technical_score', 'volatility_score', 'atr_pct', 'bb_width']
        
        for factor in comparison_factors:
            if factor in df.columns:
                opening_mean = opening_data[factor].mean()
                other_mean = other_data[factor].mean()
                diff = opening_mean - other_mean
                
                significance = "🔥显著差异" if abs(diff) > other_data[factor].std() else "📊一般差异" if abs(diff) > other_data[factor].std() * 0.5 else "🔹微小差异"
                
                print(f'   {factor}: 开盘{opening_mean:.3f} vs 其他{other_mean:.3f} ({diff:+.3f}) [{significance}]')

def generate_factor_fix_recommendations():
    """生成因子修复建议"""
    print(f'\n💡 因子修复建议')
    print('=' * 50)
    
    print(f'🎯 基于诊断结果的修复建议:')
    
    recommendations = [
        {
            'problem': '因子有效性极低 (IC值最高仅0.048)',
            'solution': '重新设计因子计算逻辑',
            'priority': 'high',
            'actions': [
                '增加更多有效的技术指标因子',
                '优化现有因子的计算方法',
                '引入基本面因子',
                '考虑因子组合和交互效应'
            ]
        },
        {
            'problem': '开盘时段信号过度集中 (可能74.9%)',
            'solution': '优化信号生成逻辑',
            'priority': 'high',
            'actions': [
                '分析开盘时段因子异常的原因',
                '调整开盘时段的因子权重',
                '增加时间衰减因子',
                '优化隔夜跳空的处理'
            ]
        },
        {
            'problem': '阈值设置可能不合理',
            'solution': '重新校准阈值',
            'priority': 'medium',
            'actions': [
                '基于历史数据重新计算最优阈值',
                '使用分位数方法设置阈值',
                '实施动态阈值调整',
                '增加阈值有效性监控'
            ]
        },
        {
            'problem': '评分因子大部分无统计显著性',
            'solution': '重构评分体系',
            'priority': 'high',
            'actions': [
                '移除无效因子',
                '重新分配因子权重',
                '引入机器学习方法',
                '增加因子有效性验证'
            ]
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        priority_icon = '🔥' if rec['priority'] == 'high' else '📊' if rec['priority'] == 'medium' else '💡'
        
        print(f'\n{i}. {priority_icon} {rec["problem"]}')
        print(f'   解决方案: {rec["solution"]}')
        print(f'   具体行动:')
        for action in rec['actions']:
            print(f'     - {action}')
    
    print(f'\n🚀 立即行动计划:')
    print(f'   1. 重新分析最有效的3个因子 (ATR, BB宽度, 波动率评分)')
    print(f'   2. 移除或降低无效因子权重 (TRIX等)')
    print(f'   3. 优化开盘时段的信号生成逻辑')
    print(f'   4. 重新校准所有阈值设置')
    print(f'   5. 增加因子有效性实时监控')

def main():
    """主函数"""
    print('🚀 因子问题深度诊断')
    print('=' * 60)
    
    # 获取数据
    df = diagnose_factor_problems()
    
    if df is not None:
        # 分析信号集中度问题
        analyze_signal_concentration(df)
        
        # 分析阈值设置问题
        analyze_threshold_problems(df)
        
        # 分析因子计算逻辑
        analyze_factor_calculation_logic(df)
        
        # 按时间分析信号质量
        analyze_signal_quality_by_time(df)
        
        # 生成修复建议
        generate_factor_fix_recommendations()
        
        print(f'\n🎯 诊断总结')
        print('=' * 40)
        print('✅ 因子问题诊断完成')
        print('🔍 已识别关键问题和原因')
        print('💡 已生成修复建议')
        print('')
        print('🚨 核心问题: 因子有效性极低，需要重构因子体系')
    else:
        print('❌ 无法获取数据进行诊断')

if __name__ == '__main__':
    main()
