@echo off
REM 设置UTF-8编码
chcp 65001 > nul
echo ===============================================================================
echo                          万和策略分析系统 - 启动器
echo ===============================================================================
echo.

REM 检查Python是否已安装
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未检测到Python安装，请先安装Python 3.7或更高版本。
    echo.
    pause
    exit /b 1
)

echo 检查必要的Python包...
echo.

REM 检查是否已安装Streamlit
python -c "import streamlit" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装Streamlit和必要依赖项...
    python -m pip install --upgrade pip
    python -m pip install streamlit pandas numpy matplotlib seaborn plotly scikit-learn jinja2
    echo.
)

REM 检查其他必要的包
python -c "import pandas, numpy, matplotlib, seaborn" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装数据分析相关依赖项...
    python -m pip install pandas numpy matplotlib seaborn
    echo.
)

python -c "import plotly, sklearn, jinja2" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装机器学习和报告生成相关依赖项...
    python -m pip install plotly scikit-learn jinja2
    echo.
)

REM 创建Streamlit配置目录（如果不存在）
if not exist "%USERPROFILE%\.streamlit" mkdir "%USERPROFILE%\.streamlit"

REM 创建或更新Streamlit配置文件以禁用首次运行提示
echo [general] > "%USERPROFILE%\.streamlit\credentials.toml"
echo email = "" >> "%USERPROFILE%\.streamlit\credentials.toml"

REM 复制config.toml到用户的.streamlit目录
echo 正在配置Streamlit设置...
if exist "%~dp0\config.toml" (
    copy /Y "%~dp0\config.toml" "%USERPROFILE%\.streamlit\config.toml" > nul
)

REM 获取当前脚本的绝对路径
set SCRIPT_DIR=%~dp0
echo 脚本目录: %SCRIPT_DIR%

REM 确保数据目录存在
if not exist "%SCRIPT_DIR%..\..\data" mkdir "%SCRIPT_DIR%..\..\data"
if not exist "%SCRIPT_DIR%..\..\reports" mkdir "%SCRIPT_DIR%..\..\reports"
if not exist "%SCRIPT_DIR%..\..\reports\html" mkdir "%SCRIPT_DIR%..\..\reports\html"

REM 切换到项目根目录
cd /d "%SCRIPT_DIR%..\.."
echo 当前工作目录: %CD%
echo.
echo 正在启动应用，请稍候...
echo.

REM 启动Streamlit应用 - 使用绝对路径
set APP_PATH=%CD%\analysis_system\streamlit_app\app.py
set CONFIG_PATH=%CD%\analysis_system\streamlit_app\config.toml

echo 应用路径: %APP_PATH%
echo 配置路径: %CONFIG_PATH%
echo.

REM 启动Streamlit应用
start "" python -m streamlit run "%APP_PATH%" --server.port=8501 --server.headless=true

REM 等待2秒，确保Streamlit服务已启动
timeout /t 2 > nul

REM 自动打开浏览器访问应用
start http://localhost:8501

echo.
echo 应用已启动，浏览器将自动打开: http://localhost:8501
echo 如果浏览器没有自动打开，请手动打开上述地址
echo.
echo 按任意键关闭此窗口...
pause > nul 