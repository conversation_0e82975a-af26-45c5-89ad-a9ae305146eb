#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import csv
import json
import pandas as pd
from datetime import datetime
import shutil
import logging
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("html_reports.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入数据管理模块
try:
    from scripts.data_manager import get_data_manager, DATA_DIR, REPORTS_DIR
    data_manager = get_data_manager()
    logger.info("成功导入数据管理模块")
except ImportError:
    logger.warning("无法导入数据管理模块，将使用默认路径")
    DATA_DIR = 'data'
    REPORTS_DIR = 'reports'

# 文件路径常量
TEMPLATE_DIR = 'templates'
HTML_OUTPUT_DIR = os.path.join(REPORTS_DIR, 'html')
TRADE_RESULTS_FILE = os.path.join(REPORTS_DIR, 'trade_analysis_results.csv')
OPTIMAL_RULES_TXT = os.path.join(REPORTS_DIR, 'optimal_strategy_rules.txt')
OPTIMAL_GB_RULES_TXT = os.path.join(REPORTS_DIR, 'gb_optimal_strategy_rules.txt')
BEST_PARAMS_CSV = os.path.join(REPORTS_DIR, 'best_parameter_combinations.csv')

def read_optimal_rules(file_path):
    """读取策略规则文件并提取关键信息"""
    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'gbk', 'latin1', 'cp936']
        content = None
        
        # 尝试不同的编码方式读取文件
        for encoding in encodings:
            try:
                logger.info(f"尝试使用 {encoding} 编码读取文件: {file_path}")
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                logger.info(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                logger.warning(f"{encoding} 编码读取失败，尝试下一种编码")
                continue
        
        if content is None:
            logger.error("所有编码方式都无法读取文件")
            return {}
            
        # 提取单因子规则
        trix_rule = re.search(r'TRIX_Buy >= ([\d\.]+).*?胜率: ([\d\.]+)%.*?平均收益: ([\d\.]+)%', content, re.DOTALL)
        volatility_score_rule = re.search(r'Volatility_Score_Buy >= ([\d\.]+).*?胜率: ([\d\.]+)%.*?平均收益: ([\d\.]+)%', content, re.DOTALL)
        atr_rule = re.search(r'ATR_Pct_Buy >= ([\d\.]+).*?胜率: ([\d\.]+)%.*?平均收益: ([\d\.]+)%', content, re.DOTALL)
        volatility_rule = re.search(r'Volatility_Buy >= ([\d\.]+).*?胜率: ([\d\.]+)%.*?平均收益: ([\d\.]+)%', content, re.DOTALL)
        allocation_rule = re.search(r'Allocation_Factor_Buy >= ([\d\.]+).*?胜率: ([\d\.]+)%.*?平均收益: ([\d\.]+)%', content, re.DOTALL)
        
        rules = {}
        if trix_rule:
            rules['trix'] = {
                'threshold': float(trix_rule.group(1)),
                'win_rate': float(trix_rule.group(2)),
                'avg_return': float(trix_rule.group(3))
            }
        if volatility_score_rule:
            rules['volatility_score'] = {
                'threshold': float(volatility_score_rule.group(1)),
                'win_rate': float(volatility_score_rule.group(2)),
                'avg_return': float(volatility_score_rule.group(3))
            }
        if atr_rule:
            rules['atr'] = {
                'threshold': float(atr_rule.group(1)),
                'win_rate': float(atr_rule.group(2)),
                'avg_return': float(atr_rule.group(3))
            }
        if volatility_rule:
            rules['volatility'] = {
                'threshold': float(volatility_rule.group(1)),
                'win_rate': float(volatility_rule.group(2)),
                'avg_return': float(volatility_rule.group(3))
            }
        if allocation_rule:
            rules['allocation'] = {
                'threshold': float(allocation_rule.group(1)),
                'win_rate': float(allocation_rule.group(2)),
                'avg_return': float(allocation_rule.group(3))
            }
        
        # 如果没有提取到任何规则，记录原始内容的部分以辅助调试
        if not rules:
            logger.warning(f"未能从文件中提取任何规则，文件内容前200个字符: {content[:200]}")
        
        return rules
    except Exception as e:
        logger.error(f"读取策略规则文件失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return {}

def analyze_trade_data(csv_path=None):
    """分析交易数据，提取关键统计信息"""
    try:
        # 使用数据管理器获取交易数据
        if csv_path is None or not os.path.exists(csv_path):
            logger.info("使用数据管理器分析交易数据")
            try:
                from scripts.data_manager import analyze_trades
                result_df = analyze_trades()
                if result_df is None or result_df.empty:
                    logger.warning("数据管理器未返回有效的交易数据")
                    return get_empty_trade_stats()
                
                # 使用数据管理器返回的DataFrame进行分析
                df = result_df
                logger.info(f"成功从数据管理器获取交易数据，共{len(df)}条记录")
            except Exception as e:
                logger.error(f"使用数据管理器分析交易数据失败: {str(e)}")
                logger.debug(traceback.format_exc())
                return get_empty_trade_stats()
        else:
            # 直接从CSV文件读取
            logger.info(f"开始读取交易数据文件: {csv_path}")
            df = pd.read_csv(csv_path)
            logger.info(f"成功读取交易数据, 共{len(df)}条记录")
        
        # 计算基本统计信息
        total_trades = len(df)
        winning_trades = len(df[df['Profit_Pct'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_return = df['Profit_Pct'].mean() if 'Profit_Pct' in df.columns else 0
        avg_profit = df[df['Profit_Pct'] > 0]['Profit_Pct'].mean() if 'Profit_Pct' in df.columns else 0
        avg_loss = df[df['Profit_Pct'] <= 0]['Profit_Pct'].mean() if 'Profit_Pct' in df.columns else 0
        
        logger.info(f"计算统计信息完成: 总交易{total_trades}笔, 胜率{win_rate*100:.2f}%, 平均收益{avg_return*100:.2f}%")
        
        # 按卖出原因分析
        if 'Sell_Reason' in df.columns:
            sell_reason_stats = df.groupby('Sell_Reason').agg({
                'Profit_Pct': ['count', 'mean'],
                'Symbol': lambda x: sum(df.loc[x.index, 'Profit_Pct'] > 0) / len(x)
            })
            sell_reason_stats.columns = ['count', 'avg_return', 'win_rate']
            sell_reason_stats = sell_reason_stats.reset_index()
        else:
            sell_reason_stats = pd.DataFrame(columns=['Sell_Reason', 'count', 'avg_return', 'win_rate'])
        
        # 高绩效案例
        required_columns = ['Symbol', 'Buy_Time', 'TRIX_Buy', 'Volatility_Score_Buy', 'Profit_Pct', 'Sell_Reason']
        if all(col in df.columns for col in required_columns):
            top_performers = df.sort_values('Profit_Pct', ascending=False).head(5)[required_columns]
        else:
            # 如果缺少某些列，使用可用的列
            available_columns = [col for col in required_columns if col in df.columns]
            top_performers = df.sort_values('Profit_Pct', ascending=False).head(5)[available_columns] if available_columns else pd.DataFrame()
        
        # 返回结果
        stats = {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'sell_reason_stats': sell_reason_stats.to_dict('records'),
            'top_performers': top_performers.to_dict('records') if not top_performers.empty else []
        }
        
        return stats
    except Exception as e:
        logger.error(f"分析交易数据失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return get_empty_trade_stats()

def get_empty_trade_stats():
    """返回空的交易统计信息"""
    return {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'win_rate': 0,
        'avg_return': 0,
        'avg_profit': 0,
        'avg_loss': 0,
        'sell_reason_stats': [],
        'top_performers': []
    }

def read_feature_importance():
    """读取特征重要性数据"""
    try:
        # 尝试从文件读取特征重要性，这里使用模拟数据
        feature_importance = {
            'TRIX_Buy': 49.8,
            'Volatility_Score_Buy': 18.4,
            'ATR_Pct_Buy': 14.2,
            'Volatility_Buy': 11.5,
            'Allocation_Factor_Buy': 6.1
        }
        return feature_importance
    except Exception as e:
        logger.error(f"读取特征重要性失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return {}

def generate_high_performance_report():
    """生成高胜率高收益交易模式HTML报告"""
    try:
        logger.info("开始生成高性能交易报告...")
        
        # 读取交易统计数据
        trade_stats = analyze_trade_data(TRADE_RESULTS_FILE)
        
        # 读取优化规则
        optimal_rules = {}
        if os.path.exists(OPTIMAL_RULES_TXT):
            optimal_rules = read_optimal_rules(OPTIMAL_RULES_TXT)
        
        # 读取梯度提升模型规则
        gb_rules = {}
        if os.path.exists(OPTIMAL_GB_RULES_TXT):
            gb_rules = read_optimal_rules(OPTIMAL_GB_RULES_TXT)
            
        # 读取特征重要性
        feature_importance = read_feature_importance()
        
        # 读取模板文件
        template_path = os.path.join(TEMPLATE_DIR, 'high_performance_template.html')
        if not os.path.exists(template_path):
            logger.error(f"模板文件不存在: {template_path}")
            return False
            
        logger.info(f"读取模板文件: {template_path}")
        with open(template_path, 'r', encoding='utf-8') as template_file:
            template = template_file.read()
        
        # 替换模板中的变量
        template = template.replace('{{update_time}}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        template = template.replace('{{total_trades}}', str(trade_stats['total_trades']))
        template = template.replace('{{winning_trades}}', str(trade_stats['winning_trades']))
        template = template.replace('{{losing_trades}}', str(trade_stats['losing_trades']))
        template = template.replace('{{win_rate}}', f"{trade_stats['win_rate']*100:.2f}%")
        template = template.replace('{{avg_return}}', f"{trade_stats['avg_return']*100:.2f}%")
        template = template.replace('{{avg_profit}}', f"{trade_stats['avg_profit']*100:.2f}%" if trade_stats['avg_profit'] != 0 else "0.00%")
        template = template.replace('{{avg_loss}}', f"{trade_stats['avg_loss']*100:.2f}%" if trade_stats['avg_loss'] != 0 else "0.00%")
        
        # 替换TRIX规则
        trix_rule = optimal_rules.get('trix', {})
        template = template.replace('{{trix_threshold}}', f"{trix_rule.get('threshold', 0):.6f}")
        template = template.replace('{{trix_win_rate}}', f"{trix_rule.get('win_rate', 0):.2f}%")
        template = template.replace('{{trix_avg_return}}', f"{trix_rule.get('avg_return', 0):.2f}%")
        
        # 替换波动性得分规则
        volatility_score_rule = optimal_rules.get('volatility_score', {})
        template = template.replace('{{volatility_score_threshold}}', f"{volatility_score_rule.get('threshold', 0):.6f}")
        template = template.replace('{{volatility_score_win_rate}}', f"{volatility_score_rule.get('win_rate', 0):.2f}%")
        template = template.replace('{{volatility_score_avg_return}}', f"{volatility_score_rule.get('avg_return', 0):.2f}%")
        
        # 替换ATR规则
        atr_rule = optimal_rules.get('atr', {})
        template = template.replace('{{atr_threshold}}', f"{atr_rule.get('threshold', 0):.6f}")
        template = template.replace('{{atr_win_rate}}', f"{atr_rule.get('win_rate', 0):.2f}%")
        template = template.replace('{{atr_avg_return}}', f"{atr_rule.get('avg_return', 0):.2f}%")
        
        # 替换波动性规则
        volatility_rule = optimal_rules.get('volatility', {})
        template = template.replace('{{volatility_threshold}}', f"{volatility_rule.get('threshold', 0):.6f}")
        template = template.replace('{{volatility_win_rate}}', f"{volatility_rule.get('win_rate', 0):.2f}%")
        template = template.replace('{{volatility_avg_return}}', f"{volatility_rule.get('avg_return', 0):.2f}%")
        
        # 替换资金分配规则
        allocation_rule = optimal_rules.get('allocation', {})
        template = template.replace('{{allocation_threshold}}', f"{allocation_rule.get('threshold', 0):.6f}")
        template = template.replace('{{allocation_win_rate}}', f"{allocation_rule.get('win_rate', 0):.2f}%")
        template = template.replace('{{allocation_avg_return}}', f"{allocation_rule.get('avg_return', 0):.2f}%")
        
        # 生成卖出原因表格
        sell_reason_table = ""
        for reason in trade_stats['sell_reason_stats']:
            sell_reason_table += f"""
            <tr>
                <td>{reason['Sell_Reason']}</td>
                <td>{reason['count']}</td>
                <td>{reason['win_rate']*100:.2f}%</td>
                <td>{reason['avg_return']*100:.2f}%</td>
            </tr>
            """
        template = template.replace('{{sell_reason_table}}', sell_reason_table)
        
        # 生成表现最佳交易表格
        top_performers_table = ""
        for performer in trade_stats['top_performers']:
            top_performers_table += f"""
            <tr>
                <td>{performer.get('Symbol', 'N/A')}</td>
                <td>{performer.get('Buy_Time', 'N/A')}</td>
                <td>{performer.get('TRIX_Buy', 0):.6f}</td>
                <td>{performer.get('Volatility_Score_Buy', 0):.6f}</td>
                <td>{performer.get('Profit_Pct', 0)*100:.2f}%</td>
                <td>{performer.get('Sell_Reason', 'N/A')}</td>
            </tr>
            """
        template = template.replace('{{top_performers_table}}', top_performers_table)
        
        # 生成特征重要性图表数据
        feature_importance_data = "["
        for feature, importance in feature_importance.items():
            feature_importance_data += f"{{name: '{feature}', y: {importance}}}, "
        feature_importance_data = feature_importance_data.rstrip(", ") + "]"
        template = template.replace('{{feature_importance_data}}', feature_importance_data)
        
        # 确保输出目录存在
        os.makedirs(HTML_OUTPUT_DIR, exist_ok=True)
        
        # 写入输出文件
        output_path = os.path.join(HTML_OUTPUT_DIR, 'high_performance_trades.html')
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(template)
        
        # 同时复制到根目录，保持向后兼容
        root_output_path = 'high_performance_trades.html'
        with open(root_output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(template)
            
        logger.info(f"高性能交易报告已生成: {output_path}")
        return True
    except Exception as e:
        logger.error(f"生成高性能交易报告失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return False

def generate_reports_guide():
    """生成报告指南HTML"""
    try:
        logger.info("开始生成报告指南...")
        # 获取reports目录中的文件列表
        reports_files = []
        for root, dirs, files in os.walk(REPORTS_DIR):
            if 'html' in dirs:  # 跳过html子目录
                dirs.remove('html')
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                file_time = os.path.getmtime(file_path)
                reports_files.append({
                    'name': file,
                    'path': file_path.replace('\\', '/'),
                    'size': file_size,
                    'time': datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 按文件类型分组
        data_files = [f for f in reports_files if f['name'].endswith('.csv')]
        image_files = [f for f in reports_files if f['name'].endswith(('.png', '.jpg', '.jpeg', '.gif'))]
        model_files = [f for f in reports_files if f['name'].endswith('.pkl')]
        rule_files = [f for f in reports_files if f['name'].endswith('.txt')]
        
        # 读取模板文件
        template_path = os.path.join(TEMPLATE_DIR, 'reports_guide_template.html')
        if not os.path.exists(template_path):
            logger.error(f"模板文件不存在: {template_path}")
            return False
            
        logger.info(f"读取模板文件: {template_path}")
        with open(template_path, 'r', encoding='utf-8') as template_file:
            template = template_file.read()
        
        # 替换模板中的变量
        template = template.replace('{{update_time}}', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        template = template.replace('{{total_reports}}', str(len(reports_files)))
        template = template.replace('{{data_files_count}}', str(len(data_files)))
        template = template.replace('{{image_files_count}}', str(len(image_files)))
        template = template.replace('{{model_files_count}}', str(len(model_files)))
        template = template.replace('{{rule_files_count}}', str(len(rule_files)))
        
        # 生成数据文件列表HTML
        data_files_html = ""
        for file in data_files:
            size_kb = file['size'] / 1024
            size_text = f"{size_kb:.1f} KB" if size_kb < 1024 else f"{size_kb/1024:.1f} MB"
            data_files_html += f"""
            <li><a href="./{file['path']}">{file['name']}</a> - {size_text} ({file['time']})</li>
            """
        template = template.replace('{{data_files_list}}', data_files_html)
        
        # 生成图表文件列表HTML，包含缩略图
        image_files_html = ""
        for file in image_files:
            size_kb = file['size'] / 1024
            size_text = f"{size_kb:.1f} KB" if size_kb < 1024 else f"{size_kb/1024:.1f} MB"
            image_files_html += f"""
            <div class="image-item">
                <a href="./{file['path']}" target="_blank">
                    <img src="./{file['path']}" alt="{file['name']}" class="thumbnail">
                    <span class="image-name">{file['name']}</span>
                    <span class="image-info">{size_text} ({file['time']})</span>
                </a>
            </div>
            """
        template = template.replace('{{image_files_list}}', image_files_html)
        
        # 生成规则文件列表HTML
        rule_files_html = ""
        for file in rule_files:
            size_kb = file['size'] / 1024
            size_text = f"{size_kb:.1f} KB" if size_kb < 1024 else f"{size_kb/1024:.1f} MB"
            rule_files_html += f"""
            <li><a href="./{file['path']}">{file['name']}</a> - {size_text} ({file['time']})</li>
            """
        template = template.replace('{{rule_files_list}}', rule_files_html)
        
        # 生成模型文件列表HTML
        model_files_html = ""
        for file in model_files:
            size_mb = file['size'] / (1024*1024)
            size_text = f"{size_mb:.1f} MB"
            model_files_html += f"""
            <li><a href="./{file['path']}">{file['name']}</a> - {size_text} ({file['time']})</li>
            """
        template = template.replace('{{model_files_list}}', model_files_html)
        
        # 确保输出目录存在
        os.makedirs(HTML_OUTPUT_DIR, exist_ok=True)
        
        # 写入输出文件
        output_path = os.path.join(HTML_OUTPUT_DIR, 'reports_guide.html')
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(template)
        
        # 同时复制到根目录，保持向后兼容
        root_output_path = 'reports_guide.html'
        with open(root_output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(template)
            
        logger.info(f"报告指南已生成: {output_path}")
        return True
    except Exception as e:
        logger.error(f"生成报告指南失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return False

def create_default_templates():
    """创建默认的模板文件"""
    try:
        os.makedirs(TEMPLATE_DIR, exist_ok=True)
        
        # 高性能交易模板
        high_performance_template_path = os.path.join(TEMPLATE_DIR, 'high_performance_template.html')
        if not os.path.exists(high_performance_template_path):
            logger.info("创建默认的高性能交易模板文件")
            high_performance_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高胜率高收益交易模式分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 5px solid #3498db;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-box {
            flex: 1;
            min-width: 200px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-box h3 {
            margin-top: 0;
            color: #3498db;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .chart {
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        .strategy-rules {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            border-left: 5px solid #2ecc71;
        }
        .feature-importance {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .feature-bar {
            flex: 1;
            min-width: 200px;
            margin-bottom: 15px;
        }
        .feature-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .bar-container {
            background-color: #ecf0f1;
            height: 30px;
            border-radius: 5px;
            overflow: hidden;
        }
        .bar {
            height: 100%;
            background-color: #3498db;
            text-align: right;
            padding-right: 10px;
            color: white;
            line-height: 30px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>高胜率高收益交易模式分析</h1>
        <p>更新时间：{{update_time}}</p>
    </div>

    <div class="stats-container">
        <div class="stat-box">
            <h3>总交易数</h3>
            <div class="stat-value">{{total_trades}}</div>
        </div>
        <div class="stat-box">
            <h3>总胜率</h3>
            <div class="stat-value">{{win_rate}}%</div>
        </div>
        <div class="stat-box">
            <h3>平均收益</h3>
            <div class="stat-value">{{avg_return}}%</div>
        </div>
    </div>

    <h2>最佳策略规则</h2>
    <div class="strategy-rules">
        <h3>单因子策略</h3>
        <ul>
            <li><strong>波动评分 (Volatility Score) ≥ {{vs_threshold}}</strong>：胜率 {{vs_win_rate}}%</li>
            <li><strong>TRIX指标 ≥ {{trix_threshold}}</strong>：胜率 {{trix_win_rate}}%，平均收益 {{trix_return}}%</li>
        </ul>
    </div>

    <h2>特征重要性分析</h2>
    <div class="feature-importance">
        <div class="feature-bar">
            <div class="feature-name">TRIX指标</div>
            <div class="bar-container">
                <div class="bar" style="width: {{trix_importance}}%;">{{trix_importance}}%</div>
            </div>
        </div>
        <div class="feature-bar">
            <div class="feature-name">波动评分</div>
            <div class="bar-container">
                <div class="bar" style="width: {{vs_importance}}%;">{{vs_importance}}%</div>
            </div>
        </div>
        <div class="feature-bar">
            <div class="feature-name">ATR百分比</div>
            <div class="bar-container">
                <div class="bar" style="width: {{atr_importance}}%;">{{atr_importance}}%</div>
            </div>
        </div>
        <div class="feature-bar">
            <div class="feature-name">波动率</div>
            <div class="bar-container">
                <div class="bar" style="width: {{volatility_importance}}%;">{{volatility_importance}}%</div>
            </div>
        </div>
        <div class="feature-bar">
            <div class="feature-name">资金分配因子</div>
            <div class="bar-container">
                <div class="bar" style="width: {{allocation_importance}}%;">{{allocation_importance}}%</div>
            </div>
        </div>
    </div>

    <h2>卖出策略分析</h2>
    <table>
        <thead>
            <tr>
                <th>卖出原因</th>
                <th>交易数量</th>
                <th>胜率</th>
                <th>平均收益</th>
            </tr>
        </thead>
        <tbody>
            {{sell_reason_table}}
        </tbody>
    </table>

    <h2>高绩效交易案例</h2>
    <table>
        <thead>
            <tr>
                <th>股票代码</th>
                <th>买入日期</th>
                <th>TRIX指标</th>
                <th>波动评分</th>
                <th>收益率</th>
                <th>卖出原因</th>
            </tr>
        </thead>
        <tbody>
            {{top_performers_table}}
        </tbody>
    </table>

    <div class="footer">
        <p>© 万和策略分析系统 - 技术支持</p>
    </div>
</body>
</html>"""
            with open(high_performance_template_path, 'w', encoding='utf-8') as f:
                f.write(high_performance_template)
        
        # 报告指南模板
        reports_guide_template_path = os.path.join(TEMPLATE_DIR, 'reports_guide_template.html')
        if not os.path.exists(reports_guide_template_path):
            logger.info("创建默认的报告指南模板文件")
            reports_guide_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略分析报告指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 5px solid #3498db;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }
        .stat-box {
            flex: 1;
            min-width: 150px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-box h3 {
            margin-top: 0;
            color: #3498db;
            font-size: 16px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .file-list {
            list-style-type: none;
            padding: 0;
        }
        .file-list li {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
        .file-list li a {
            color: #3498db;
            text-decoration: none;
        }
        .file-list li a:hover {
            text-decoration: underline;
        }
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            width: calc(33.333% - 20px);
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .image-item a {
            display: block;
            text-decoration: none;
            color: #333;
        }
        .thumbnail {
            width: 100%;
            height: 180px;
            object-fit: cover;
            display: block;
        }
        .image-name {
            padding: 10px;
            font-weight: bold;
            display: block;
        }
        .image-info {
            padding: 0 10px 10px;
            color: #7f8c8d;
            font-size: 14px;
            display: block;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
        @media (max-width: 768px) {
            .image-item {
                width: calc(50% - 20px);
            }
        }
        @media (max-width: 480px) {
            .image-item {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>策略分析报告指南</h1>
        <p>更新时间：{{update_time}}</p>
    </div>

    <div class="stats-container">
        <div class="stat-box">
            <h3>报告总数</h3>
            <div class="stat-value">{{total_reports}}</div>
        </div>
        <div class="stat-box">
            <h3>数据文件</h3>
            <div class="stat-value">{{data_files_count}}</div>
        </div>
        <div class="stat-box">
            <h3>图表文件</h3>
            <div class="stat-value">{{image_files_count}}</div>
        </div>
        <div class="stat-box">
            <h3>模型文件</h3>
            <div class="stat-value">{{model_files_count}}</div>
        </div>
        <div class="stat-box">
            <h3>规则文件</h3>
            <div class="stat-value">{{rule_files_count}}</div>
        </div>
    </div>

    <h2>数据文件列表</h2>
    <div class="section">
        <p>包含交易记录、分析结果、参数优化等数据文件：</p>
        <ul class="file-list">
            {{data_files_list}}
        </ul>
    </div>

    <h2>可视化图表</h2>
    <div class="section">
        <p>包含各类交易表现、参数优化、模型评估等可视化图表：</p>
        <div class="image-gallery">
            {{image_files_list}}
        </div>
    </div>

    <h2>策略规则文件</h2>
    <div class="section">
        <p>包含优化后的策略规则、筛选条件等：</p>
        <ul class="file-list">
            {{rule_files_list}}
        </ul>
    </div>

    <h2>模型文件</h2>
    <div class="section">
        <p>包含训练好的机器学习模型：</p>
        <ul class="file-list">
            {{model_files_list}}
        </ul>
    </div>

    <div class="footer">
        <p>© 万和策略分析系统 - 技术支持</p>
    </div>
</body>
</html>"""
            with open(reports_guide_template_path, 'w', encoding='utf-8') as f:
                f.write(reports_guide_template)
                
        return True
    except Exception as e:
        logger.error(f"创建默认模板文件失败: {str(e)}")
        logger.debug(traceback.format_exc())
        return False

def main():
    """主函数"""
    try:
        # 记录开始时间
        start_time = datetime.now()
        logger.info(f"HTML报告生成开始: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 确保目录存在
        os.makedirs(TEMPLATE_DIR, exist_ok=True)
        os.makedirs(HTML_OUTPUT_DIR, exist_ok=True)
        logger.info("确保目录结构存在")
        
        # 检查模板文件是否存在，如果不存在则从当前HTML文件复制
        if not os.path.exists(os.path.join(TEMPLATE_DIR, 'high_performance_template.html')):
            if os.path.exists('high_performance_trades.html'):
                shutil.copy('high_performance_trades.html', os.path.join(TEMPLATE_DIR, 'high_performance_template.html'))
                logger.info("已创建高性能交易模板文件")
            else:
                logger.warning("未找到高性能交易模板源文件")
        
        if not os.path.exists(os.path.join(TEMPLATE_DIR, 'reports_guide_template.html')):
            if os.path.exists('reports_guide.html'):
                shutil.copy('reports_guide.html', os.path.join(TEMPLATE_DIR, 'reports_guide_template.html'))
                logger.info("已创建报告指南模板文件")
            else:
                logger.warning("未找到报告指南模板源文件")
                
        # 如果模板文件仍然不存在，则创建默认模板
        templates_exist = (
            os.path.exists(os.path.join(TEMPLATE_DIR, 'high_performance_template.html')) and
            os.path.exists(os.path.join(TEMPLATE_DIR, 'reports_guide_template.html'))
        )
        
        if not templates_exist:
            logger.info("创建默认模板文件")
            create_default_templates()
        
        # 生成报告
        high_performance_success = generate_high_performance_report()
        reports_guide_success = generate_reports_guide()
        
        # 记录结束时间和耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 输出执行结果
        if high_performance_success and reports_guide_success:
            logger.info(f"HTML报告生成成功，耗时: {duration:.2f}秒")
            logger.info(f"生成的报告文件:")
            logger.info(f"  - {os.path.join(HTML_OUTPUT_DIR, 'high_performance_trades.html')}")
            logger.info(f"  - {os.path.join(HTML_OUTPUT_DIR, 'reports_guide.html')}")
            return True
        elif high_performance_success:
            logger.warning(f"仅高性能交易报告生成成功，报告指南生成失败，耗时: {duration:.2f}秒")
            return True
        elif reports_guide_success:
            logger.warning(f"仅报告指南生成成功，高性能交易报告生成失败，耗时: {duration:.2f}秒")
            return True
        else:
            logger.error(f"HTML报告生成失败，耗时: {duration:.2f}秒")
            return False
    except Exception as e:
        logger.error(f"HTML报告生成过程中发生异常: {str(e)}")
        logger.debug(traceback.format_exc())
        return False

if __name__ == "__main__":
    main() 