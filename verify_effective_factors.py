# coding=utf-8
"""
验证高效因子配置
确认基于8个高效因子的策略配置已正确应用
"""

from config import get_config_value

def verify_effective_factors_config():
    """验证高效因子配置"""
    print('✅ 验证高效因子配置')
    print('=' * 60)
    
    print('🎯 优化目标:')
    print('   基于8个高效因子重构策略')
    print('   利用IC>0.03的强预测因子')
    print('   预期胜率提升10-15%')
    print('   从失效多因子转向有效技术指标')
    
    # 验证高效因子策略启用
    print(f'\n🔧 高效因子策略验证:')
    
    enable_effective = get_config_value('ENABLE_EFFECTIVE_FACTORS_STRATEGY', False)
    effective_config = get_config_value('EFFECTIVE_FACTORS_CONFIG', {})
    
    if enable_effective == True:
        print(f'   ✅ 高效因子策略: 已启用')
    else:
        print(f'   ❌ 高效因子策略: {enable_effective} (期望: True)')
    
    if effective_config.get('enable', False):
        print(f'   ✅ 高效因子配置: 已启用')
    else:
        print(f'   ❌ 高效因子配置: 未启用')
    
    # 验证8个高效因子配置
    print(f'\n🏆 8个高效因子验证:')
    
    factors = effective_config.get('factors', {})
    
    expected_factors = {
        'cci': {'weight': 0.170, 'ic': 0.1107, 'rank': 1},
        'adx': {'weight': 0.162, 'ic': 0.1056, 'rank': 2},
        'bb_position': {'weight': 0.141, 'ic': 0.0917, 'rank': 3},
        'rsi': {'weight': 0.128, 'ic': 0.0832, 'rank': 4},
        'macd_hist': {'weight': 0.125, 'ic': 0.0813, 'rank': 5},
        'macd': {'weight': 0.102, 'ic': 0.0665, 'rank': 6},
        'bb_width': {'weight': 0.099, 'ic': 0.0644, 'rank': 7},
        'atr_pct': {'weight': 0.074, 'ic': 0.0484, 'rank': 8}
    }
    
    factors_correct = True
    
    for factor_name, expected in expected_factors.items():
        if factor_name in factors:
            actual_weight = factors[factor_name].get('weight', 0)
            actual_ic = factors[factor_name].get('ic', 0)
            
            if abs(actual_weight - expected['weight']) < 0.001:
                print(f'   ✅ {factor_name}: 权重{actual_weight:.3f}, IC{actual_ic:.4f} (排名{expected["rank"]})')
            else:
                print(f'   ❌ {factor_name}: 权重{actual_weight:.3f} (期望: {expected["weight"]:.3f})')
                factors_correct = False
        else:
            print(f'   ❌ {factor_name}: 未配置 (期望: 权重{expected["weight"]:.3f})')
            factors_correct = False
    
    # 验证买入条件
    print(f'\n📊 买入条件验证:')
    
    buy_conditions = effective_config.get('buy_conditions', {})
    
    min_score = buy_conditions.get('min_combined_score', 0)
    min_factors = buy_conditions.get('min_factors_count', 0)
    require_top3 = buy_conditions.get('require_top3_factors', False)
    
    if min_score == 0.6:
        print(f'   ✅ 最小综合得分: {min_score*100}% (提高标准)')
    else:
        print(f'   ❌ 最小综合得分: {min_score*100}% (期望: 60%)')
    
    if min_factors == 6:
        print(f'   ✅ 最少因子数量: {min_factors}个 (8个中至少6个)')
    else:
        print(f'   ❌ 最少因子数量: {min_factors} (期望: 6)')
    
    if require_top3 == True:
        print(f'   ✅ 要求前3因子: 已启用 (CCI, ADX, BB位置必须满足)')
    else:
        print(f'   ❌ 要求前3因子: {require_top3} (期望: True)')
    
    # 验证阈值设置
    print(f'\n🎯 因子阈值验证:')
    
    threshold_checks = [
        ('cci', 'min_threshold', 50, 'CCI > 50'),
        ('adx', 'min_threshold', 25, 'ADX > 25 (强趋势)'),
        ('bb_position', 'min_threshold', 0.3, 'BB位置 > 0.3'),
        ('rsi', 'min_threshold', 40, 'RSI > 40'),
        ('macd_hist', 'min_threshold', 0, 'MACD柱 > 0'),
    ]
    
    threshold_correct = True
    
    for factor, threshold_type, expected_value, description in threshold_checks:
        if factor in factors:
            actual_value = factors[factor].get(threshold_type, None)
            if actual_value == expected_value:
                print(f'   ✅ {factor}: {description}')
            else:
                print(f'   ❌ {factor}: {threshold_type}={actual_value} (期望: {expected_value})')
                threshold_correct = False
        else:
            print(f'   ❌ {factor}: 未配置')
            threshold_correct = False
    
    # 总体验证结果
    all_correct = (
        enable_effective == True and
        effective_config.get('enable', False) and
        factors_correct and
        min_score == 0.6 and
        min_factors == 6 and
        require_top3 == True and
        threshold_correct
    )
    
    print(f'\n🎯 验证总结:')
    if all_correct:
        print('✅ 所有高效因子配置已正确应用')
        print('🚀 策略已准备就绪，可以重启程序')
        return True
    else:
        print('❌ 部分配置未正确应用')
        print('💡 请检查config.py文件并手动修正')
        return False

def show_factor_analysis_summary():
    """显示因子分析总结"""
    print(f'\n📋 因子分析总结')
    print('=' * 50)
    
    summary = '''
🎯 重大发现:
   1. ✅ 策略存储了133个因子 (远超预期)
   2. ✅ 发现8个高效预测因子 (IC>0.03)
   3. ✅ 技术指标类最有效 (平均IC=0.0952)
   4. ✅ CCI和ADX表现最佳 (IC>0.10)

📊 8个高效因子:
   1. CCI: IC=0.1107, 胜率差异+14.9%
   2. ADX: IC=0.1056, 胜率差异+10.5%
   3. BB位置: IC=0.0917, 胜率差异+11.5%
   4. RSI: IC=0.0832, 胜率差异+12.7%
   5. MACD柱: IC=0.0813, 胜率差异+7.9%
   6. MACD: IC=0.0665, 胜率差异+10.7%
   7. BB宽度: IC=0.0644, 胜率差异+4.5%
   8. ATR: IC=0.0484, 胜率差异+3.5%

🔧 策略重构:
   - 从失效的多因子评分 → 高效技术指标
   - 从低IC值因子 → 高IC值因子 (0.03+)
   - 从复杂评分系统 → 简洁有效因子
   - 从96.7%开盘集中 → 基于因子质量选择

📈 预期效果:
   - 胜率提升: 预期+10-15%
   - 信号质量: 显著提升
   - 策略稳定性: 基于成熟技术指标
   - 可解释性: 技术分析逻辑清晰
'''
    
    print(summary)

def create_monitoring_plan():
    """创建监控计划"""
    print(f'\n📋 高效因子监控计划')
    print('=' * 50)
    
    plan = '''
🔍 重启后立即检查 (前1小时):
   □ 策略是否正常启动
   □ 8个高效因子是否正常计算
   □ 买入信号是否基于新因子生成
   □ 信号数量是否合理 (不会过少)

📊 24小时内效果监控:
   □ 各因子的实际值分布
   □ 买入信号的因子得分情况
   □ 信号时间分布是否改善
   □ 是否还有96.7%开盘集中问题

📈 一周内表现评估:
   □ 胜率是否向55%+靠近
   □ 各因子的实际预测效果
   □ 技术指标策略vs多因子策略对比
   □ 交易质量是否提升

🎯 持续优化方向:
   □ 监控8个因子的IC值变化
   □ 根据实际表现调整权重
   □ 考虑添加其他有效因子
   □ 优化因子阈值设置

⚠️ 风险控制:
   □ 如果信号过少: 适当降低阈值
   □ 如果胜率下降: 检查因子计算
   □ 如果出现异常: 快速回退机制
   □ 保持风险控制不变
'''
    
    print(plan)

def main():
    """主函数"""
    print('🚀 高效因子配置验证')
    print('=' * 60)
    
    # 验证高效因子配置
    success = verify_effective_factors_config()
    
    # 显示因子分析总结
    show_factor_analysis_summary()
    
    # 创建监控计划
    create_monitoring_plan()
    
    if success:
        print(f'\n🏆 高效因子配置验证成功!')
        print('🚀 策略已完全重构，准备重启程序!')
        print('')
        print('🎯 下一步: python main.py')
        print('📈 目标: 基于8个高效因子，胜率提升10-15%')
        print('💎 从失效多因子 → 高效技术指标的历史性转变!')
    else:
        print(f'\n⚠️ 高效因子配置验证失败!')
        print('💡 请检查并修正配置文件')

if __name__ == '__main__':
    main()
