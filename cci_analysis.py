
# CCI因子深度分析脚本
import sqlite3
import pandas as pd
import numpy as np

def analyze_cci_effectiveness():
    """分析CCI因子有效性"""
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取CCI相关数据
        query = """
        SELECT 
            timestamp, symbol, cci, net_profit_pct_sell,
            CAST(strftime('%H', timestamp) AS INTEGER) as hour
        FROM trades 
        WHERE action = 'BUY' AND cci IS NOT NULL
        ORDER BY timestamp DESC
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 CCI数据分析 ({len(df)}条记录):')
        
        # CCI值分布分析
        cci_stats = df['cci'].describe()
        print(f'\n📈 CCI值分布:')
        print(f'   均值: {cci_stats["mean"]:.2f}')
        print(f'   中位数: {cci_stats["50%"]:.2f}')
        print(f'   标准差: {cci_stats["std"]:.2f}')
        print(f'   范围: [{cci_stats["min"]:.2f}, {cci_stats["max"]:.2f}]')
        
        # 按时段分析CCI
        hourly_cci = df.groupby('hour')['cci'].agg(['mean', 'count'])
        print(f'\n🕐 各时段CCI均值:')
        for hour, stats in hourly_cci.iterrows():
            print(f'   {hour:02d}:00: CCI={stats["mean"]:.1f} (样本{stats["count"]})')
        
        # CCI阈值效果分析
        thresholds = [15, 20, 25, 30, 35]
        print(f'\n🎯 CCI阈值效果分析:')
        
        for threshold in thresholds:
            high_cci = df[df['cci'] >= threshold]
            low_cci = df[df['cci'] < threshold]
            
            if len(high_cci) > 10 and len(low_cci) > 10:
                high_profit = high_cci['net_profit_pct_sell'].mean()
                low_profit = low_cci['net_profit_pct_sell'].mean()
                
                print(f'   CCI>={threshold}: 平均收益{high_profit:.2f}% (样本{len(high_cci)})')
                print(f'   CCI<{threshold}: 平均收益{low_profit:.2f}% (样本{len(low_cci)})')
                print(f'   差异: {high_profit-low_profit:+.2f}%')
                print()
        
        return df
        
    except Exception as e:
        print(f'❌ CCI分析失败: {e}')
        return None

def optimize_cci_thresholds(df):
    """优化CCI阈值"""
    if df is None:
        return
    
    print(f'🔧 CCI阈值优化:')
    
    # 测试不同阈值组合
    best_threshold = None
    best_performance = -999
    
    for low_threshold in [10, 15, 20]:
        for high_threshold in [25, 30, 35, 40]:
            if high_threshold <= low_threshold:
                continue
            
            # 分析该阈值组合的效果
            medium_cci = df[(df['cci'] >= low_threshold) & (df['cci'] < high_threshold)]
            
            if len(medium_cci) > 50:
                avg_profit = medium_cci['net_profit_pct_sell'].mean()
                win_rate = (medium_cci['net_profit_pct_sell'] > 0).mean() * 100
                
                # 综合评分 (胜率权重60%, 收益权重40%)
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                print(f'   CCI [{low_threshold}, {high_threshold}): 胜率{win_rate:.1f}%, 收益{avg_profit:.2f}%, 评分{score:.1f}')
                
                if score > best_performance:
                    best_performance = score
                    best_threshold = (low_threshold, high_threshold)
    
    if best_threshold:
        print(f'\n🏆 最优CCI阈值: [{best_threshold[0]}, {best_threshold[1]})')
        print(f'   综合评分: {best_performance:.1f}')
    
    return best_threshold

# 执行CCI分析
if __name__ == '__main__':
    df = analyze_cci_effectiveness()
    best_cci = optimize_cci_thresholds(df)
