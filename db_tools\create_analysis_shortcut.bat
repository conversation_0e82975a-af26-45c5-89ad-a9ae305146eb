@echo off
echo 正在创建万和策略分析系统 - 数据分析工具的桌面快捷方式...

:: 获取当前脚本所在的目录
set SCRIPT_DIR=%~dp0
set TARGET_BAT=%SCRIPT_DIR%start_analysis.bat
set DESKTOP_DIR=%USERPROFILE%\Desktop

:: 创建VBScript来生成快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateAnalysisShortcut.vbs"
echo sLinkFile = "%DESKTOP_DIR%\万和策略分析系统 - 数据分析工具.lnk" >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo oLink.TargetPath = "%TARGET_BAT%" >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo oLink.Description = "万和策略分析系统 - 数据分析工具" >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo oLink.IconLocation = "%SystemRoot%\System32\SHELL32.dll,44" >> "%TEMP%\CreateAnalysisShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateAnalysisShortcut.vbs"

:: 运行VBScript
cscript //nologo "%TEMP%\CreateAnalysisShortcut.vbs"
del "%TEMP%\CreateAnalysisShortcut.vbs"

echo 桌面快捷方式已创建完成！
echo 您现在可以直接从桌面启动数据分析工具。
pause 