@echo off
echo 正在测试数据库连接...
echo.

:: 获取当前脚本所在的目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

:: 尝试找到Python解释器
set PYTHON_PATH=python
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    :: 如果默认的python命令不可用，尝试特定路径
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
        set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
    ) else if exist "C:\Python39\python.exe" (
        set PYTHON_PATH=C:\Python39\python.exe
    ) else if exist "C:\Program Files\Python39\python.exe" (
        set PYTHON_PATH=C:\Program Files\Python39\python.exe
    ) else (
        echo 错误: 找不到Python解释器!
        echo 请确保已安装Python 3.6+，或者手动修改此批处理文件中的Python路径。
        pause
        exit /b 1
    )
)

echo 使用Python解释器: %PYTHON_PATH%
echo.

:: 运行数据库连接测试脚本
"%PYTHON_PATH%" test_db_connection.py

:: 脚本结束
pause
exit /b 