# coding=utf-8
import os
import sys
import subprocess
import time
from datetime import datetime

def print_header(message):
    """打印带格式的标题"""
    print("\n" + "=" * 80)
    print(f" {message} ".center(80, "="))
    print("=" * 80 + "\n")

def run_command(command, description):
    """运行命令并显示输出"""
    print_header(description)
    
    # Replace 'python' with the full path to Python executable
    python_exe = "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe"
    if command.startswith("python "):
        command = f'"{python_exe}" ' + command[7:]
    elif command.startswith("py -3 "):
        command = f'"{python_exe}" ' + command[6:]
    
    print(f"执行命令: {command}")
    print("-" * 80)
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # 实时输出命令执行结果
        for line in process.stdout:
            print(line.strip())
            
        process.wait()
        
        if process.returncode == 0:
            print(f"\n{description}成功完成!")
            return True
        else:
            print(f"\n{description}失败，返回代码: {process.returncode}")
            return False
    except Exception as e:
        print(f"\n执行命令时出错: {e}")
        return False

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✓ {description}文件已找到: {filepath}")
        return True
    else:
        print(f"✗ {description}文件不存在: {filepath}")
        return False

def main():
    """主函数，运行整个分析流程"""
    print_header("交易策略分析工具")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n这个工具将分析交易日志，找出最佳买卖点指标组合，并生成优化后的交易策略。")
    
    # 检查必要文件
    print("\n检查必要文件...")
    trade_log_exists = check_file_exists("data/trade_log.csv", "交易日志")
    analysis_log_exists = check_file_exists("data/analysis_log.csv", "分析日志")
    
    if not trade_log_exists:
        print("\n错误: 交易日志文件(trade_log.csv)不存在，无法继续分析。")
        print("请确保策略已经运行并生成了交易日志文件。")
        return
    
    # 步骤1: 运行交易分析
    if run_command("python scripts/analyze_trades.py", "运行交易分析"):
        # 检查分析结果
        if not check_file_exists("reports/trade_analysis_results.csv", "交易分析结果"):
            print("\n错误: 交易分析结果文件不存在，无法继续优化策略。")
            return
        
        # 步骤2: 运行策略优化
        if run_command("python scripts/optimize_strategy.py", "运行策略优化"):
            # 检查优化结果
            check_file_exists("reports/optimal_strategy_rules.txt", "优化策略规则")
            check_file_exists("reports/optimal_strategy_model.pkl", "优化策略模型")
            check_file_exists("reports/feature_importance.png", "特征重要性图")
            check_file_exists("reports/buy_points_distribution.png", "买入点分布图")
            
            print_header("分析完成")
            print("已生成以下分析结果文件:")
            print("1. trade_analysis_results.csv - 交易分析数据")
            print("2. optimal_strategy_rules.txt - 优化后的策略规则")
            print("3. optimal_strategy_model.pkl - 训练好的策略模型")
            print("4. feature_importance.png - 特征重要性可视化")
            print("5. buy_points_distribution.png - 买入点分布图")
            print("6. cumulative_profit_curve.png - 累积收益曲线")
            
            # 显示策略规则摘要
            if os.path.exists("reports/optimal_strategy_rules.txt"):
                print("\n策略规则摘要:")
                print("-" * 80)
                try:
                    with open("reports/optimal_strategy_rules.txt", "r") as f:
                        # 只读取前20行
                        for i, line in enumerate(f):
                            if i < 20:
                                print(line.strip())
                            else:
                                print("...")
                                print("(查看完整规则请打开 optimal_strategy_rules.txt)")
                                break
                except Exception as e:
                    print(f"读取策略规则文件时出错: {e}")
        else:
            print("\n策略优化失败，请检查错误信息。")
    else:
        print("\n交易分析失败，请检查错误信息。")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 