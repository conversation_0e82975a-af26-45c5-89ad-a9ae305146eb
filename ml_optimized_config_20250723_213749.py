# -*- coding: utf-8 -*-
"""
机器学习优化后的配置
生成时间: 2025-07-23 21:37:49
基于机器学习因子重要性分析的权重优化
"""

ML_OPTIMIZED_FACTORS_CONFIG = {
    'cci_14': {
        'weight': 0.106,
        'ml_importance': 0.01,
        'optimization_note': 'ML优化: 重要性0.0100, 权重调整0.100→0.098',
    },
    'rsi_14': {
        'weight': 0.085,
        'ml_importance': 0.0329,
        'optimization_note': 'ML优化: 重要性0.0329, 权重调整0.080→0.079',
    },
    'atr_pct': {
        'weight': 0.108,
        'ml_importance': 0.5038,
        'optimization_note': 'ML优化: 重要性0.5038, 权重调整0.100→0.100',
    },
    'adx_14': {
        'weight': 0.085,
        'ml_importance': 0.2788,
        'optimization_note': 'ML优化: 重要性0.2788, 权重调整0.080→0.079',
    },
    'overall_score': {
        'weight': 0.222,
        'ml_importance': 8.8913,
        'optimization_note': 'ML优化: 重要性8.8913, 权重调整0.150→0.205',
    },
    'technical_score': {
        'weight': 0.162,
        'ml_importance': 6.1953,
        'optimization_note': 'ML优化: 重要性6.1953, 权重调整0.120→0.150',
    },
    'fundamental_score': {
        'weight': 0.125,
        'ml_importance': 4.2341,
        'optimization_note': 'ML优化: 重要性4.2341, 权重调整0.100→0.116',
    },
    'sentiment_score': {
        'weight': 0.106,
        'ml_importance': 5.5099,
        'optimization_note': 'ML优化: 重要性5.5099, 权重调整0.080→0.098',
    },
}
