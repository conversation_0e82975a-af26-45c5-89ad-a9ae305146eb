# coding=utf-8
"""
TRIX预筛选器
基于买入条件"TRIX昨日<前日"进行预筛选，大幅减少分析范围
"""

import pandas as pd
import numpy as np
import time
import json
import os
from datetime import datetime

class TRIXPreFilter:
    """TRIX预筛选器"""
    
    def __init__(self, context):
        self.context = context
        self.pre_filtered_stocks = set()
        self.last_filter_date = None
        self.cache_dir = "cache"
        
        # 确保缓存目录存在
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        self.context.log.info("TRIX预筛选器初始化完成")
    
    def daily_pre_filter(self, all_symbols):
        """每日预筛选：找出TRIX昨日<前日的股票（大幅减少分析数量）"""
        current_date = self.context.now.strftime('%Y-%m-%d')

        # 如果今天已经筛选过，直接返回缓存结果
        if self.last_filter_date == current_date and self.pre_filtered_stocks:
            self.context.log.info(f"💾 使用TRIX预筛选缓存，股票数: {len(self.pre_filtered_stocks)}")
            self.context.log.info(f"💡 这些股票昨日TRIX < 前日TRIX，今日只需检查实时TRIX")
            return self.pre_filtered_stocks

        # 尝试从文件缓存加载
        cached_result = self._load_cached_result(current_date)
        if cached_result is not None:
            self.pre_filtered_stocks = cached_result
            self.last_filter_date = current_date
            self.context.log.info(f"📁 从文件加载TRIX预筛选结果，股票数: {len(cached_result)}")
            self.context.log.info(f"💡 这些股票昨日TRIX < 前日TRIX，今日只需检查实时TRIX")
            return cached_result

        # 执行新的预筛选
        self.context.log.info(f"🔍 开始TRIX预筛选，总股票数: {len(all_symbols)}")
        self.context.log.info(f"🎯 目标：找出昨日TRIX < 前日TRIX的股票，大幅减少今日分析量")
        start_time = time.time()
        
        pre_filtered = self._execute_prefilter(all_symbols)
        
        # 保存结果
        self.pre_filtered_stocks = pre_filtered
        self.last_filter_date = current_date
        self._save_cached_result(pre_filtered, current_date)
        
        # 统计信息
        filter_time = time.time() - start_time
        filter_ratio = len(pre_filtered) / len(all_symbols) * 100 if all_symbols else 0
        reduction_ratio = 100 - filter_ratio

        self.context.log.info(f"🎉 TRIX预筛选完成:")
        self.context.log.info(f"  📊 原始股票数: {len(all_symbols)}")
        self.context.log.info(f"  ✅ 筛选后数量: {len(pre_filtered)}")
        self.context.log.info(f"  📈 筛选比例: {filter_ratio:.1f}%")
        self.context.log.info(f"  🚀 减少分析量: {reduction_ratio:.1f}% (节省{len(all_symbols)-len(pre_filtered)}只股票的分析)")
        self.context.log.info(f"  ⏱️ 耗时: {filter_time:.2f}秒")
        self.context.log.info(f"  💡 今日只需分析{len(pre_filtered)}只股票的实时TRIX条件")
        
        return pre_filtered
    
    def _execute_prefilter(self, all_symbols):
        """执行预筛选逻辑"""
        pre_filtered = set()
        
        # 优先使用批量获取
        if hasattr(self.context, 'history_data_manager') and len(all_symbols) > 50:
            pre_filtered = self._batch_prefilter(all_symbols)
        else:
            pre_filtered = self._sequential_prefilter(all_symbols)
        
        return pre_filtered
    
    def _batch_prefilter(self, all_symbols):
        """批量预筛选"""
        pre_filtered = set()
        
        try:
            self.context.log.info("使用批量数据获取进行TRIX预筛选")
            
            # 批量获取历史数据
            batch_data = self.context.history_data_manager.get_history_data_batch(
                symbol_list=all_symbols,
                frequency='1d',
                count=25,  # 获取25天数据计算TRIX
                fields=['close']
            )
            
            # 分析每只股票
            for symbol in all_symbols:
                if symbol in batch_data and batch_data[symbol] is not None:
                    data = batch_data[symbol]
                    if self._check_trix_pre_condition(symbol, data):
                        pre_filtered.add(symbol)
                        
        except Exception as e:
            self.context.log.warning(f"批量预筛选失败，回退到逐个处理: {e}")
            pre_filtered = self._sequential_prefilter(all_symbols)
        
        return pre_filtered
    
    def _sequential_prefilter(self, all_symbols):
        """逐个预筛选"""
        pre_filtered = set()
        
        self.context.log.info("使用逐个数据获取进行TRIX预筛选")
        
        for i, symbol in enumerate(all_symbols):
            try:
                # 获取历史数据
                from main import history_n
                data = history_n(symbol, 25, '1d', ['close'])
                
                if data is not None and self._check_trix_pre_condition(symbol, data):
                    pre_filtered.add(symbol)
                
                # 每100只股票显示进度
                if (i + 1) % 100 == 0:
                    progress = (i + 1) / len(all_symbols) * 100
                    self.context.log.debug(f"TRIX预筛选进度: {progress:.1f}% ({i+1}/{len(all_symbols)})")
                    
            except Exception as e:
                # 单只股票失败不影响整体
                continue
        
        return pre_filtered
    
    def _check_trix_pre_condition(self, symbol, data):
        """检查TRIX昨日<前日的条件"""
        try:
            if data is None or len(data) < 20:
                return False
            
            close_prices = data['close'].values
            
            # 计算TRIX
            trix = self._calculate_trix(close_prices)
            
            if len(trix) < 3:
                return False
            
            # 检查条件：TRIX昨日 < TRIX前日
            trix_yesterday = trix[-2]  # 昨日
            trix_day_before = trix[-3]  # 前日
            
            # 确保数值有效
            if np.isnan(trix_yesterday) or np.isnan(trix_day_before):
                return False
            
            result = trix_yesterday < trix_day_before
            
            if result:
                self.context.log.debug(f"{symbol} 通过TRIX预筛选: 昨日{trix_yesterday:.6f} < 前日{trix_day_before:.6f}")
            
            return result
            
        except Exception as e:
            self.context.log.debug(f"{symbol} TRIX预筛选计算失败: {e}")
            return False
    
    def _calculate_trix(self, close_prices):
        """计算TRIX指标"""
        try:
            # 优先使用talib
            import talib
            return talib.TRIX(close_prices, timeperiod=14)
        except ImportError:
            # 回退到手动计算
            return self._manual_trix_calculation(close_prices)
    
    def _manual_trix_calculation(self, close_prices):
        """手动计算TRIX"""
        try:
            # 三重指数移动平均
            series = pd.Series(close_prices)
            ema1 = series.ewm(span=14, adjust=False).mean()
            ema2 = ema1.ewm(span=14, adjust=False).mean()
            ema3 = ema2.ewm(span=14, adjust=False).mean()
            
            # TRIX = (EMA3今日 - EMA3昨日) / EMA3昨日 * 10000
            trix = ema3.pct_change() * 10000
            
            return trix.values
            
        except Exception as e:
            # 如果计算失败，返回空数组
            return np.array([])
    
    def _save_cached_result(self, result, date):
        """保存预筛选结果到缓存文件"""
        try:
            cache_file = os.path.join(self.cache_dir, f"trix_prefilter_{date}.json")
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(list(result), f, ensure_ascii=False, indent=2)
            self.context.log.debug(f"TRIX预筛选结果已缓存到: {cache_file}")
        except Exception as e:
            self.context.log.warning(f"保存TRIX预筛选缓存失败: {e}")
    
    def _load_cached_result(self, date):
        """从缓存文件加载预筛选结果"""
        try:
            cache_file = os.path.join(self.cache_dir, f"trix_prefilter_{date}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                return set(result)
        except Exception as e:
            self.context.log.warning(f"加载TRIX预筛选缓存失败: {e}")
        return None
    
    def get_filter_stats(self):
        """获取筛选统计信息"""
        return {
            'filtered_count': len(self.pre_filtered_stocks),
            'last_filter_date': self.last_filter_date,
            'cache_available': self.last_filter_date is not None
        }

def quick_trix_prefilter(symbols, context):
    """快速TRIX预筛选函数 - 简化版本"""
    filtered = []
    
    context.log.info(f"开始快速TRIX预筛选，股票数: {len(symbols)}")
    start_time = time.time()
    
    for symbol in symbols:
        try:
            # 获取最近20天的收盘价
            from main import history_n
            data = history_n(symbol, 20, '1d', ['close'])
            
            if data is None or len(data) < 15:
                continue
            
            close_prices = data['close'].values
            
            # 简化的TRIX计算
            series = pd.Series(close_prices)
            ema1 = series.ewm(span=14, adjust=False).mean()
            ema2 = ema1.ewm(span=14, adjust=False).mean()
            ema3 = ema2.ewm(span=14, adjust=False).mean()
            trix = ema3.pct_change() * 10000
            
            if len(trix) >= 3:
                # 检查昨日<前日
                trix_yesterday = trix.iloc[-2]
                trix_day_before = trix.iloc[-3]
                
                if not (np.isnan(trix_yesterday) or np.isnan(trix_day_before)):
                    if trix_yesterday < trix_day_before:
                        filtered.append(symbol)
                        
        except Exception:
            continue
    
    filter_time = time.time() - start_time
    filter_ratio = len(filtered) / len(symbols) * 100 if symbols else 0
    
    context.log.info(f"快速TRIX预筛选完成:")
    context.log.info(f"  {len(symbols)} -> {len(filtered)} ({filter_ratio:.1f}%)")
    context.log.info(f"  减少分析量: {100-filter_ratio:.1f}%")
    context.log.info(f"  耗时: {filter_time:.2f}秒")
    
    return filtered

# 全局预筛选器实例
_trix_prefilter = None

def get_trix_prefilter(context=None):
    """获取TRIX预筛选器实例"""
    global _trix_prefilter
    if _trix_prefilter is None and context is not None:
        _trix_prefilter = TRIXPreFilter(context)
    return _trix_prefilter
