# coding=utf-8
"""
详细胜率分析器
基于数据库中的买入指标进行详细的胜率分析
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def detailed_win_rate_analysis():
    """详细胜率分析"""
    print('📊 详细胜率分析器')
    print('=' * 80)
    
    try:
        # 1. 加载数据
        conn = sqlite3.connect('data/trades.db')
        
        # 查询买入记录
        buy_query = '''
        SELECT * FROM trades 
        WHERE action = 'BUY' 
        ORDER BY timestamp DESC
        '''
        
        buy_data = pd.read_sql_query(buy_query, conn)
        
        # 查询卖出记录
        sell_query = '''
        SELECT symbol, timestamp, price, net_profit_pct_sell, sell_reason
        FROM trades 
        WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC
        '''
        
        sell_data = pd.read_sql_query(sell_query, conn)
        conn.close()
        
        print(f'📈 买入记录: {len(buy_data)} 条')
        print(f'📉 卖出记录: {len(sell_data)} 条')
        
        # 2. 匹配买卖记录
        matched_data = match_trades(buy_data, sell_data)
        
        if len(matched_data) == 0:
            print('❌ 没有匹配的交易记录')
            return
        
        print(f'✅ 匹配成功: {len(matched_data)} 笔完整交易')
        
        # 3. 基础统计
        overall_stats = calculate_basic_stats(matched_data)
        print_basic_stats(overall_stats)
        
        # 4. 详细指标分析
        indicator_analysis = analyze_all_indicators(matched_data)
        
        # 5. 生成策略建议
        strategies = generate_strategies(indicator_analysis, overall_stats['win_rate'])
        
        # 6. 保存详细报告
        save_detailed_report(overall_stats, indicator_analysis, strategies)
        
        return {
            'overall_stats': overall_stats,
            'indicator_analysis': indicator_analysis,
            'strategies': strategies
        }
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def match_trades(buy_data, sell_data):
    """匹配买卖记录"""
    try:
        # 转换时间格式
        buy_data['timestamp'] = pd.to_datetime(buy_data['timestamp']).dt.tz_localize(None)
        sell_data['timestamp'] = pd.to_datetime(sell_data['timestamp']).dt.tz_localize(None)
        
        # 匹配记录
        buy_data['profit_pct'] = np.nan
        buy_data['is_win'] = np.nan
        buy_data['sell_reason'] = ''
        
        matched_count = 0
        for idx, buy_row in buy_data.iterrows():
            matching_sells = sell_data[
                (sell_data['symbol'] == buy_row['symbol']) & 
                (sell_data['timestamp'] > buy_row['timestamp'])
            ].sort_values('timestamp')
            
            if len(matching_sells) > 0:
                sell_row = matching_sells.iloc[0]
                profit_pct = sell_row['net_profit_pct_sell']
                
                buy_data.at[idx, 'profit_pct'] = profit_pct
                buy_data.at[idx, 'is_win'] = 1 if profit_pct > 0 else 0
                buy_data.at[idx, 'sell_reason'] = sell_row['sell_reason']
                matched_count += 1
        
        return buy_data.dropna(subset=['profit_pct'])
        
    except Exception as e:
        print(f'❌ 匹配失败: {e}')
        return pd.DataFrame()

def calculate_basic_stats(data):
    """计算基础统计"""
    stats = {
        'total_trades': len(data),
        'win_trades': int(data['is_win'].sum()),
        'lose_trades': int(len(data) - data['is_win'].sum()),
        'win_rate': data['is_win'].mean(),
        'avg_profit': data['profit_pct'].mean(),
        'avg_win_profit': data[data['is_win'] == 1]['profit_pct'].mean(),
        'avg_lose_profit': data[data['is_win'] == 0]['profit_pct'].mean(),
        'max_profit': data['profit_pct'].max(),
        'max_loss': data['profit_pct'].min()
    }
    return stats

def print_basic_stats(stats):
    """打印基础统计"""
    print(f'\n📊 基础统计信息:')
    print(f'  总交易数: {stats["total_trades"]}')
    print(f'  盈利交易: {stats["win_trades"]} ({stats["win_rate"]:.2%})')
    print(f'  亏损交易: {stats["lose_trades"]} ({1-stats["win_rate"]:.2%})')
    print(f'  平均收益: {stats["avg_profit"]:.2%}')
    print(f'  平均盈利: {stats["avg_win_profit"]:.2%}')
    print(f'  平均亏损: {stats["avg_lose_profit"]:.2%}')
    print(f'  最大盈利: {stats["max_profit"]:.2%}')
    print(f'  最大亏损: {stats["max_loss"]:.2%}')

def analyze_all_indicators(data):
    """分析所有指标"""
    print(f'\n🔍 分析各类指标...')

    # 获取所有数值型列
    numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()

    # 排除非指标列
    exclude_columns = ['profit_pct', 'is_win', 'price', 'volume', 'timestamp']
    indicator_columns = [col for col in numeric_columns if col not in exclude_columns and not col.endswith('_sell')]

    # 添加之前遗漏的重要指标
    important_missing_indicators = [
        'gap_pct', 'volume_price_correlation', 'bb_squeeze', 'ma_alignment_score',
        'pattern_score_total', 'sharpe_ratio_5d', 'max_drawdown_3d', 'max_drawdown_5d',
        'max_drawdown_10d', 'volatility_ratio_5_20', 'volatility_score_enhanced',
        'ma5_slope', 'ma10_slope', 'ma20_slope', 'volume_momentum_3d', 'volume_momentum_5d'
    ]

    # 确保重要指标被包含
    for indicator in important_missing_indicators:
        if indicator in numeric_columns and indicator not in indicator_columns:
            indicator_columns.append(indicator)

    print(f'📈 找到 {len(indicator_columns)} 个指标')

    indicator_results = {}

    for indicator in indicator_columns:
        try:
            result = analyze_single_indicator(data, indicator)
            if result:
                indicator_results[indicator] = result
        except Exception as e:
            continue

    # 按胜率改善排序
    sorted_indicators = sorted(indicator_results.items(),
                             key=lambda x: x[1]['best_win_rate'],
                             reverse=True)

    print(f'\n🏆 Top 10 最佳指标:')
    for i, (indicator, result) in enumerate(sorted_indicators[:10], 1):
        improvement = result['best_win_rate'] - data['is_win'].mean()
        print(f'{i:2d}. {indicator:30} 最佳胜率: {result["best_win_rate"]:.2%} (提升: {improvement:+.2%})')

    # 分析多重指标叠加效果
    print(f'\n🔗 分析多重指标叠加效果...')
    multi_indicator_results = analyze_multi_indicator_combinations(data, sorted_indicators[:20])

    # 分析单种指标辅助效果
    print(f'\n📊 分析单种指标辅助效果...')
    single_indicator_assist = analyze_single_indicator_assist(data, sorted_indicators[:15])

    return {
        'single_indicators': dict(sorted_indicators),
        'multi_combinations': multi_indicator_results,
        'single_assist': single_indicator_assist
    }

def analyze_single_indicator(data, indicator):
    """分析单个指标"""
    try:
        indicator_data = data[indicator].dropna()
        if len(indicator_data) < 20:
            return None
        
        # 计算分位数
        q25 = indicator_data.quantile(0.25)
        q50 = indicator_data.quantile(0.50)
        q75 = indicator_data.quantile(0.75)
        
        # 分组分析
        ranges = {
            'low': data[data[indicator] <= q25],
            'medium_low': data[(data[indicator] > q25) & (data[indicator] <= q50)],
            'medium_high': data[(data[indicator] > q50) & (data[indicator] <= q75)],
            'high': data[data[indicator] > q75]
        }
        
        range_results = {}
        best_win_rate = 0
        best_range = None
        
        for range_name, range_data in ranges.items():
            if len(range_data) > 5:
                win_rate = range_data['is_win'].mean()
                range_results[range_name] = {
                    'win_rate': win_rate,
                    'count': len(range_data),
                    'avg_profit': range_data['profit_pct'].mean()
                }
                
                if win_rate > best_win_rate:
                    best_win_rate = win_rate
                    best_range = range_name
        
        if best_range:
            return {
                'best_win_rate': best_win_rate,
                'best_range': best_range,
                'range_results': range_results,
                'correlation': indicator_data.corr(data.loc[indicator_data.index, 'is_win']),
                'thresholds': {'q25': q25, 'q50': q50, 'q75': q75}
            }
        
        return None

    except Exception as e:
        return None

def analyze_multi_indicator_combinations(data, top_indicators):
    """分析多重指标叠加效果"""
    try:
        overall_win_rate = data['is_win'].mean()
        combinations = []

        # 2指标组合
        print('  🔗 分析2指标组合...')
        for i in range(min(10, len(top_indicators))):
            for j in range(i+1, min(10, len(top_indicators))):
                indicator1_name, indicator1_result = top_indicators[i]
                indicator2_name, indicator2_result = top_indicators[j]

                combo_result = test_two_indicator_combination(data,
                    indicator1_name, indicator1_result,
                    indicator2_name, indicator2_result)

                if combo_result:
                    combinations.append(combo_result)

        # 3指标组合
        print('  🔗 分析3指标组合...')
        for i in range(min(6, len(top_indicators))):
            for j in range(i+1, min(6, len(top_indicators))):
                for k in range(j+1, min(6, len(top_indicators))):
                    indicator1_name, indicator1_result = top_indicators[i]
                    indicator2_name, indicator2_result = top_indicators[j]
                    indicator3_name, indicator3_result = top_indicators[k]

                    combo_result = test_three_indicator_combination(data,
                        indicator1_name, indicator1_result,
                        indicator2_name, indicator2_result,
                        indicator3_name, indicator3_result)

                    if combo_result:
                        combinations.append(combo_result)

        # 按胜率排序
        combinations.sort(key=lambda x: x['win_rate'], reverse=True)

        print(f'  ✅ 找到 {len(combinations)} 个有效组合')

        # 显示最佳组合
        if combinations:
            print(f'\n🏆 Top 5 最佳指标组合:')
            for i, combo in enumerate(combinations[:5], 1):
                improvement = combo['win_rate'] - overall_win_rate
                print(f'{i}. {combo["description"]}')
                print(f'   胜率: {combo["win_rate"]:.2%} (提升: {improvement:+.2%}) 样本: {combo["sample_count"]}')

        return combinations

    except Exception as e:
        print(f'❌ 多重指标分析失败: {e}')
        return []

def test_two_indicator_combination(data, ind1_name, ind1_result, ind2_name, ind2_result):
    """测试两个指标的组合效果"""
    try:
        # 获取最佳范围条件
        ind1_condition = get_best_range_condition(data, ind1_name, ind1_result)
        ind2_condition = get_best_range_condition(data, ind2_name, ind2_result)

        if ind1_condition is None or ind2_condition is None:
            return None

        # 应用组合条件
        combined_condition = ind1_condition & ind2_condition
        filtered_data = data[combined_condition]

        if len(filtered_data) < 10:  # 样本太少
            return None

        win_rate = filtered_data['is_win'].mean()

        return {
            'type': '2指标组合',
            'indicators': [ind1_name, ind2_name],
            'description': f'{ind1_name} + {ind2_name}',
            'win_rate': win_rate,
            'sample_count': len(filtered_data),
            'avg_profit': filtered_data['profit_pct'].mean()
        }

    except Exception as e:
        return None

def test_three_indicator_combination(data, ind1_name, ind1_result, ind2_name, ind2_result, ind3_name, ind3_result):
    """测试三个指标的组合效果"""
    try:
        # 获取最佳范围条件
        ind1_condition = get_best_range_condition(data, ind1_name, ind1_result)
        ind2_condition = get_best_range_condition(data, ind2_name, ind2_result)
        ind3_condition = get_best_range_condition(data, ind3_name, ind3_result)

        if any(cond is None for cond in [ind1_condition, ind2_condition, ind3_condition]):
            return None

        # 应用组合条件
        combined_condition = ind1_condition & ind2_condition & ind3_condition
        filtered_data = data[combined_condition]

        if len(filtered_data) < 5:  # 样本太少
            return None

        win_rate = filtered_data['is_win'].mean()

        return {
            'type': '3指标组合',
            'indicators': [ind1_name, ind2_name, ind3_name],
            'description': f'{ind1_name} + {ind2_name} + {ind3_name}',
            'win_rate': win_rate,
            'sample_count': len(filtered_data),
            'avg_profit': filtered_data['profit_pct'].mean()
        }

    except Exception as e:
        return None

def get_best_range_condition(data, indicator_name, indicator_result):
    """获取指标的最佳范围条件"""
    try:
        if indicator_name not in data.columns:
            return None

        best_range = indicator_result['best_range']
        thresholds = indicator_result['thresholds']

        if best_range == 'low':
            return data[indicator_name] <= thresholds['q25']
        elif best_range == 'medium_low':
            return (data[indicator_name] > thresholds['q25']) & (data[indicator_name] <= thresholds['q50'])
        elif best_range == 'medium_high':
            return (data[indicator_name] > thresholds['q50']) & (data[indicator_name] <= thresholds['q75'])
        else:  # high
            return data[indicator_name] > thresholds['q75']

    except Exception as e:
        return None

def analyze_single_indicator_assist(data, top_indicators):
    """分析单种指标辅助效果"""
    try:
        overall_win_rate = data['is_win'].mean()
        assist_results = []

        print('  📊 测试单指标辅助效果...')

        for indicator_name, indicator_result in top_indicators:
            # 测试该指标作为主要条件的效果
            main_condition_result = test_as_main_condition(data, indicator_name, indicator_result, overall_win_rate)

            # 测试该指标作为辅助条件的效果
            assist_condition_result = test_as_assist_condition(data, indicator_name, indicator_result, top_indicators[0], overall_win_rate)

            if main_condition_result and assist_condition_result:
                assist_results.append({
                    'indicator': indicator_name,
                    'main_condition': main_condition_result,
                    'assist_condition': assist_condition_result,
                    'assist_improvement': assist_condition_result['win_rate'] - main_condition_result['win_rate']
                })

        # 按辅助改善效果排序
        assist_results.sort(key=lambda x: x['assist_improvement'], reverse=True)

        print(f'  ✅ 分析了 {len(assist_results)} 个指标的辅助效果')

        # 显示最佳辅助效果
        if assist_results:
            print(f'\n📈 Top 5 最佳辅助指标:')
            for i, result in enumerate(assist_results[:5], 1):
                print(f'{i}. {result["indicator"]}')
                print(f'   单独使用: {result["main_condition"]["win_rate"]:.2%} (样本: {result["main_condition"]["sample_count"]})')
                print(f'   辅助使用: {result["assist_condition"]["win_rate"]:.2%} (样本: {result["assist_condition"]["sample_count"]})')
                print(f'   辅助提升: {result["assist_improvement"]:+.2%}')

        return assist_results

    except Exception as e:
        print(f'❌ 单指标辅助分析失败: {e}')
        return []

def test_as_main_condition(data, indicator_name, indicator_result, overall_win_rate):
    """测试指标作为主要条件的效果"""
    try:
        condition = get_best_range_condition(data, indicator_name, indicator_result)
        if condition is None:
            return None

        filtered_data = data[condition]
        if len(filtered_data) < 10:
            return None

        return {
            'win_rate': filtered_data['is_win'].mean(),
            'sample_count': len(filtered_data),
            'improvement': filtered_data['is_win'].mean() - overall_win_rate
        }

    except Exception as e:
        return None

def test_as_assist_condition(data, indicator_name, indicator_result, best_indicator, overall_win_rate):
    """测试指标作为辅助条件的效果"""
    try:
        # 主要条件：使用最佳指标
        best_indicator_name, best_indicator_result = best_indicator
        main_condition = get_best_range_condition(data, best_indicator_name, best_indicator_result)

        # 辅助条件：使用当前指标
        assist_condition = get_best_range_condition(data, indicator_name, indicator_result)

        if main_condition is None or assist_condition is None:
            return None

        # 组合条件
        combined_condition = main_condition & assist_condition
        filtered_data = data[combined_condition]

        if len(filtered_data) < 5:
            return None

        return {
            'win_rate': filtered_data['is_win'].mean(),
            'sample_count': len(filtered_data),
            'improvement': filtered_data['is_win'].mean() - overall_win_rate
        }

    except Exception as e:
        return None

def generate_strategies(indicator_analysis, overall_win_rate):
    """生成策略建议"""
    strategies = []

    # 单指标策略
    single_indicators = indicator_analysis.get('single_indicators', {})
    for indicator, result in single_indicators.items():
        improvement = result['best_win_rate'] - overall_win_rate

        if improvement > 0.03:  # 胜率提升3%以上
            best_range = result['best_range']
            thresholds = result['thresholds']

            # 生成条件描述
            if best_range == 'low':
                condition = f'{indicator} <= {thresholds["q25"]:.3f}'
            elif best_range == 'medium_low':
                condition = f'{thresholds["q25"]:.3f} < {indicator} <= {thresholds["q50"]:.3f}'
            elif best_range == 'medium_high':
                condition = f'{thresholds["q50"]:.3f} < {indicator} <= {thresholds["q75"]:.3f}'
            else:  # high
                condition = f'{indicator} > {thresholds["q75"]:.3f}'

            strategies.append({
                'type': '单指标策略',
                'indicator': indicator,
                'condition': condition,
                'win_rate': result['best_win_rate'],
                'improvement': improvement,
                'sample_count': result['range_results'][best_range]['count'],
                'avg_profit': result['range_results'][best_range]['avg_profit']
            })

    # 多指标组合策略
    multi_combinations = indicator_analysis.get('multi_combinations', [])
    for combo in multi_combinations:
        improvement = combo['win_rate'] - overall_win_rate
        if improvement > 0.05:  # 组合策略要求更高的提升
            strategies.append({
                'type': combo['type'],
                'indicator': combo['description'],
                'condition': combo['description'],
                'win_rate': combo['win_rate'],
                'improvement': improvement,
                'sample_count': combo['sample_count'],
                'avg_profit': combo['avg_profit']
            })

    # 按改善程度排序
    strategies.sort(key=lambda x: x['improvement'], reverse=True)

    return strategies

def save_detailed_report(overall_stats, indicator_analysis, strategies):
    """保存详细报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'详细胜率分析报告_{timestamp}.txt'
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('详细胜率分析报告\n')
        f.write('=' * 60 + '\n\n')
        f.write(f'分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
        
        # 基础统计
        f.write('📊 基础统计:\n')
        f.write(f'  总交易数: {overall_stats["total_trades"]}\n')
        f.write(f'  整体胜率: {overall_stats["win_rate"]:.2%}\n')
        f.write(f'  平均收益: {overall_stats["avg_profit"]:.2%}\n')
        f.write(f'  平均盈利: {overall_stats["avg_win_profit"]:.2%}\n')
        f.write(f'  平均亏损: {overall_stats["avg_lose_profit"]:.2%}\n\n')
        
        # 策略建议
        f.write('🎯 高胜率策略建议:\n')
        f.write('-' * 40 + '\n')

        for i, strategy in enumerate(strategies[:15], 1):
            f.write(f'{i:2d}. [{strategy["type"]}] {strategy["condition"]}\n')
            f.write(f'    胜率: {strategy["win_rate"]:.2%} (提升: {strategy["improvement"]:+.2%})\n')
            f.write(f'    样本: {strategy["sample_count"]} 笔\n')
            f.write(f'    平均收益: {strategy["avg_profit"]:.2%}\n\n')

        # 多指标组合分析
        multi_combinations = indicator_analysis.get('multi_combinations', [])
        if multi_combinations:
            f.write('\n🔗 多指标组合分析:\n')
            f.write('-' * 40 + '\n')
            for i, combo in enumerate(multi_combinations[:10], 1):
                improvement = combo['win_rate'] - overall_stats['win_rate']
                f.write(f'{i:2d}. {combo["description"]}\n')
                f.write(f'    胜率: {combo["win_rate"]:.2%} (提升: {improvement:+.2%})\n')
                f.write(f'    样本: {combo["sample_count"]} 笔\n\n')

        # 单指标辅助分析
        single_assist = indicator_analysis.get('single_assist', [])
        if single_assist:
            f.write('\n📊 单指标辅助效果分析:\n')
            f.write('-' * 40 + '\n')
            for i, assist in enumerate(single_assist[:10], 1):
                f.write(f'{i:2d}. {assist["indicator"]}\n')
                f.write(f'    单独使用: {assist["main_condition"]["win_rate"]:.2%} (样本: {assist["main_condition"]["sample_count"]})\n')
                f.write(f'    辅助使用: {assist["assist_condition"]["win_rate"]:.2%} (样本: {assist["assist_condition"]["sample_count"]})\n')
                f.write(f'    辅助提升: {assist["assist_improvement"]:+.2%}\n\n')
        
        # 详细指标分析
        f.write('\n📈 详细单指标分析:\n')
        f.write('-' * 40 + '\n')

        single_indicators = indicator_analysis.get('single_indicators', {})
        for indicator, result in list(single_indicators.items())[:20]:
            f.write(f'\n{indicator}:\n')
            f.write(f'  最佳胜率: {result["best_win_rate"]:.2%} (范围: {result["best_range"]})\n')
            f.write(f'  相关性: {result["correlation"]:.3f}\n')

            for range_name, range_result in result['range_results'].items():
                f.write(f'  {range_name:12}: 胜率 {range_result["win_rate"]:.2%}, 样本 {range_result["count"]}\n')
    
    print(f'✅ 详细报告已保存: {filename}')
    return filename

def main():
    """主函数"""
    print('📊 详细胜率分析器')
    print('=' * 80)
    
    results = detailed_win_rate_analysis()
    
    if results:
        print(f'\n🎉 分析完成!')
        print(f'📊 总交易数: {results["overall_stats"]["total_trades"]}')
        print(f'🎯 整体胜率: {results["overall_stats"]["win_rate"]:.2%}')
        print(f'📈 发现 {len(results["strategies"])} 个有效策略')
        
        if len(results["strategies"]) > 0:
            best_strategy = results["strategies"][0]
            print(f'\n🏆 最佳策略:')
            print(f'  条件: {best_strategy["condition"]}')
            print(f'  胜率: {best_strategy["win_rate"]:.2%} (提升: {best_strategy["improvement"]:+.2%})')
            print(f'  样本: {best_strategy["sample_count"]} 笔')
    else:
        print('❌ 分析失败')

if __name__ == "__main__":
    main()
