# coding=utf-8
"""
分析性能优化后的回测数据
评估速度提升和胜率变化
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def analyze_current_backtest_performance():
    """分析当前回测表现"""
    print('📊 分析性能优化后的回测数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有交易数据
        query = """
        SELECT 
            timestamp, symbol, action, price,
            sell_reason, net_profit_pct_sell, holding_hours,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy
        FROM trades 
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 总交易记录: {len(df)} 条')
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        
        # 分析时间分布
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        latest_time = df['timestamp'].max()
        earliest_time = df['timestamp'].min()
        
        print(f'   时间范围: {earliest_time.strftime("%Y-%m-%d %H:%M")} 到 {latest_time.strftime("%Y-%m-%d %H:%M")}')
        print(f'   数据跨度: {(latest_time - earliest_time).total_seconds() / 3600:.1f} 小时')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, None, None

def evaluate_performance_optimization_impact(sell_records):
    """评估性能优化的影响"""
    print(f'\n🎯 性能优化影响评估')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录')
        return None
    
    # 计算当前胜率
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return None
    
    current_wins = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    current_total = len(completed_trades)
    current_win_rate = current_wins / current_total * 100
    
    current_avg_profit = completed_trades['net_profit_pct_sell'].mean()
    current_median_profit = completed_trades['net_profit_pct_sell'].median()
    
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
    
    current_avg_win = winning_trades['net_profit_pct_sell'].mean() if len(winning_trades) > 0 else 0
    current_avg_loss = abs(losing_trades['net_profit_pct_sell'].mean()) if len(losing_trades) > 0 else 0
    
    print(f'📈 当前策略表现:')
    print(f'   总胜率: {current_win_rate:.1f}% ({current_wins}/{current_total})')
    print(f'   平均收益: {current_avg_profit:.2f}%')
    print(f'   中位数收益: {current_median_profit:.2f}%')
    print(f'   平均盈利: {current_avg_win:.2f}%')
    print(f'   平均亏损: {current_avg_loss:.2f}%')
    print(f'   盈亏比: {current_avg_win/current_avg_loss:.2f}' if current_avg_loss > 0 else '   盈亏比: N/A')
    
    # 与历史胜率对比
    historical_rates = [24.7, 40.8, 43.6]  # 历史胜率记录
    
    print(f'\n📊 胜率变化历程:')
    print(f'   初始胜率: 24.7%')
    print(f'   第一轮优化: 40.8% (+16.1%)')
    print(f'   第二轮优化: 43.6% (+2.8%)')
    print(f'   性能优化后: {current_win_rate:.1f}%')
    
    if len(historical_rates) > 0:
        latest_change = current_win_rate - historical_rates[-1]
        total_improvement = current_win_rate - historical_rates[0]
        
        print(f'   最新变化: {latest_change:+.1f}%')
        print(f'   总体提升: {total_improvement:+.1f}%')
        
        # 评估性能优化效果
        if latest_change >= -1:
            print(f'   ✅ 性能优化成功: 胜率影响很小')
        elif latest_change >= -3:
            print(f'   📊 性能优化良好: 胜率影响在预期范围')
        elif latest_change >= -5:
            print(f'   ⚠️ 性能优化代价较高: 胜率下降较多')
        else:
            print(f'   ❌ 性能优化代价过高: 胜率下降过多')
        
        # 评估是否达到预期
        expected_range = (55, 57)
        if expected_range[0] <= current_win_rate <= expected_range[1]:
            print(f'   🎯 胜率在预期范围内 ({expected_range[0]}-{expected_range[1]}%)')
        elif current_win_rate > expected_range[1]:
            print(f'   🚀 胜率超出预期 (>{expected_range[1]}%)')
        else:
            print(f'   📉 胜率低于预期 (<{expected_range[0]}%)')
    
    return current_win_rate

def analyze_sell_reasons_after_optimization(sell_records):
    """分析优化后的卖出原因分布"""
    print(f'\n📋 优化后卖出原因分析')
    print('=' * 50)
    
    completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return
    
    # 统计各卖出原因
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', 'median', lambda x: (x > 0).mean() * 100],
        'holding_hours': ['mean', 'median']
    }).round(2)
    
    sell_reason_stats.columns = ['交易数', '平均收益%', '中位收益%', '胜率%', '平均持仓h', '中位持仓h']
    sell_reason_stats = sell_reason_stats.sort_values('胜率%', ascending=False)
    
    print(f'📊 各卖出原因表现 (按胜率排序):')
    print(sell_reason_stats.to_string())
    
    # 分析结构变化
    total_trades = len(completed_trades)
    
    print(f'\n🎯 交易结构分析:')
    for reason, stats in sell_reason_stats.iterrows():
        count = stats['交易数']
        percentage = count / total_trades * 100
        win_rate = stats['胜率%']
        avg_profit = stats['平均收益%']
        
        print(f'   {reason}: {count}笔 ({percentage:.1f}%), 胜率{win_rate:.1f}%, 平均{avg_profit:.2f}%')
    
    # 重点分析关键卖出方式
    print(f'\n🔍 关键卖出方式分析:')
    
    # 跟踪止盈
    trailing_stop = completed_trades[completed_trades['sell_reason'] == '跟踪止盈']
    if len(trailing_stop) > 0:
        trailing_rate = (trailing_stop['net_profit_pct_sell'] > 0).mean() * 100
        trailing_avg = trailing_stop['net_profit_pct_sell'].mean()
        trailing_count = len(trailing_stop)
        trailing_pct = trailing_count / total_trades * 100
        
        print(f'   跟踪止盈: {trailing_count}笔 ({trailing_pct:.1f}%), 胜率{trailing_rate:.1f}%, 平均{trailing_avg:.2f}%')
        
        if trailing_rate >= 45:
            print(f'     ✅ 跟踪止盈表现良好')
        else:
            print(f'     💡 跟踪止盈仍需优化')
    
    # 最大持仓天数
    max_holding = completed_trades[completed_trades['sell_reason'] == '最大持仓天数']
    if len(max_holding) > 0:
        max_rate = (max_holding['net_profit_pct_sell'] > 0).mean() * 100
        max_avg = max_holding['net_profit_pct_sell'].mean()
        max_count = len(max_holding)
        max_pct = max_count / total_trades * 100
        
        print(f'   最大持仓天数: {max_count}笔 ({max_pct:.1f}%), 胜率{max_rate:.1f}%, 平均{max_avg:.2f}%')
        
        if max_rate >= 80:
            print(f'     ✅ 最大持仓天数表现优秀')
        else:
            print(f'     📉 最大持仓天数胜率下降')
    
    # 固定止盈
    fixed_profit = completed_trades[completed_trades['sell_reason'] == '固定止盈']
    if len(fixed_profit) > 0:
        fixed_rate = (fixed_profit['net_profit_pct_sell'] > 0).mean() * 100
        fixed_avg = fixed_profit['net_profit_pct_sell'].mean()
        fixed_count = len(fixed_profit)
        fixed_pct = fixed_count / total_trades * 100
        
        print(f'   固定止盈: {fixed_count}笔 ({fixed_pct:.1f}%), 胜率{fixed_rate:.1f}%, 平均{fixed_avg:.2f}%')
        
        if fixed_rate >= 60:
            print(f'     ✅ 固定止盈表现良好')
        else:
            print(f'     💡 固定止盈需要调整')

def analyze_buy_signal_changes(buy_records):
    """分析买入信号变化"""
    print(f'\n🎯 买入信号变化分析')
    print('=' * 50)
    
    if len(buy_records) == 0:
        print('⚠️ 没有买入记录')
        return
    
    print(f'📊 买入信号统计 (基于{len(buy_records)}条记录):')
    
    # 分析多因子评分分布
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    for col in score_columns:
        if col in buy_records.columns:
            values = buy_records[col].dropna()
            if len(values) > 0:
                mean_val = values.mean()
                median_val = values.median()
                std_val = values.std()
                
                print(f'   {col}: 均值{mean_val:.3f}, 中位{median_val:.3f}, 标准差{std_val:.3f}')
    
    # 分析买入信号质量变化
    print(f'\n📈 买入信号质量分析:')
    
    # 检查是否有明显的质量提升
    overall_scores = buy_records['overall_score'].dropna()
    if len(overall_scores) > 0:
        high_quality_signals = len(overall_scores[overall_scores >= 0.2])
        quality_rate = high_quality_signals / len(overall_scores) * 100
        
        print(f'   高质量信号 (>=0.2): {high_quality_signals}/{len(overall_scores)} ({quality_rate:.1f}%)')
        
        if quality_rate >= 70:
            print(f'   ✅ 买入信号质量优秀')
        elif quality_rate >= 50:
            print(f'   📊 买入信号质量良好')
        else:
            print(f'   💡 买入信号质量需要提升')

def evaluate_speed_vs_performance_tradeoff(current_win_rate):
    """评估速度与性能的权衡"""
    print(f'\n⚖️ 速度与性能权衡评估')
    print('=' * 50)
    
    print(f'📊 权衡分析:')
    
    # 预期的性能优化效果
    expected_speed_improvement = 70  # 70%速度提升
    expected_win_rate_impact = -2.5  # 平均-2.5%胜率影响
    
    # 实际效果评估
    baseline_win_rate = 43.6  # 优化前胜率
    actual_win_rate_change = current_win_rate - baseline_win_rate
    
    print(f'   预期速度提升: +{expected_speed_improvement}%')
    print(f'   预期胜率影响: {expected_win_rate_impact}%')
    print(f'   实际胜率变化: {actual_win_rate_change:+.1f}%')
    
    # 评估权衡效果
    if actual_win_rate_change >= -1:
        tradeoff_rating = "优秀"
        tradeoff_icon = "🏆"
    elif actual_win_rate_change >= -3:
        tradeoff_rating = "良好"
        tradeoff_icon = "✅"
    elif actual_win_rate_change >= -5:
        tradeoff_rating = "可接受"
        tradeoff_icon = "📊"
    else:
        tradeoff_rating = "需要调整"
        tradeoff_icon = "⚠️"
    
    print(f'\n{tradeoff_icon} 权衡效果评估: {tradeoff_rating}')
    
    # 性价比分析
    if current_win_rate >= 50:
        performance_level = "优秀"
    elif current_win_rate >= 40:
        performance_level = "良好"
    elif current_win_rate >= 30:
        performance_level = "一般"
    else:
        performance_level = "需要改进"
    
    print(f'   当前胜率水平: {performance_level} ({current_win_rate:.1f}%)')
    print(f'   速度提升效果: 预期1.7倍')
    print(f'   综合性价比: {"高" if actual_win_rate_change >= -3 else "中等"}')

def generate_next_optimization_suggestions(current_win_rate, sell_reason_stats=None):
    """生成下一步优化建议"""
    print(f'\n💡 下一步优化建议')
    print('=' * 50)
    
    suggestions = []
    
    if current_win_rate < 50:
        suggestions.append({
            'priority': 'high',
            'action': '微调多因子阈值',
            'reason': f'当前胜率{current_win_rate:.1f}%，可适当降低阈值增加买入机会',
            'config': 'min_overall_score: 0.15 → 0.12'
        })
    
    if current_win_rate < 45:
        suggestions.append({
            'priority': 'high',
            'action': '重新启用部分增强功能',
            'reason': '胜率下降较多，考虑重新启用智能评分或时序分析',
            'config': 'enable_smart_scoring: False → True'
        })
    
    suggestions.append({
        'priority': 'medium',
        'action': '优化跟踪止盈参数',
        'reason': '继续优化跟踪止盈表现',
        'config': 'TRAILING_STOP: 0.01 → 0.008'
    })
    
    suggestions.append({
        'priority': 'low',
        'action': '监控长期表现',
        'reason': '观察策略在不同市场环境下的稳定性',
        'config': '无需配置更改'
    })
    
    print(f'🎯 建议优先级排序:')
    for i, suggestion in enumerate(suggestions, 1):
        priority_icon = '🔥' if suggestion['priority'] == 'high' else '📊' if suggestion['priority'] == 'medium' else '💡'
        print(f'\n   {i}. {priority_icon} {suggestion["action"]} ({suggestion["priority"]}优先级)')
        print(f'      原因: {suggestion["reason"]}')
        print(f'      配置: {suggestion["config"]}')

def main():
    """主函数"""
    print('🚀 性能优化后回测数据分析')
    print('=' * 60)
    
    # 分析当前回测表现
    df, buy_records, sell_records = analyze_current_backtest_performance()
    
    if df is not None:
        # 评估性能优化影响
        current_win_rate = evaluate_performance_optimization_impact(sell_records)
        
        if current_win_rate is not None:
            # 分析卖出原因变化
            analyze_sell_reasons_after_optimization(sell_records)
            
            # 分析买入信号变化
            analyze_buy_signal_changes(buy_records)
            
            # 评估速度与性能权衡
            evaluate_speed_vs_performance_tradeoff(current_win_rate)
            
            # 生成下一步建议
            generate_next_optimization_suggestions(current_win_rate)
            
            print(f'\n🎯 分析总结')
            print('=' * 40)
            print('✅ 性能优化后数据分析完成')
            print('📊 已评估速度与胜率权衡效果')
            print('💡 已生成下一步优化建议')
            
            if current_win_rate >= 50:
                print('🏆 策略表现优秀，性能优化成功!')
            elif current_win_rate >= 45:
                print('📈 策略表现良好，权衡效果不错!')
            elif current_win_rate >= 40:
                print('📊 策略表现可接受，可考虑微调!')
            else:
                print('⚠️ 策略需要进一步优化!')
        else:
            print('⚠️ 无法获取胜率数据')
    else:
        print('❌ 无法获取回测数据')

if __name__ == '__main__':
    main()
