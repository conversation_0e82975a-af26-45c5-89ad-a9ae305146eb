# coding=utf-8
"""
修复版因子分析
解决数据处理问题，深度挖掘133个因子的预测能力
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy import stats

def get_matched_trading_data():
    """获取匹配的交易数据"""
    print('🔍 获取匹配的交易数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 简化查询，直接获取买入记录和对应的收益
        query = """
        SELECT * FROM trades 
        WHERE action = 'BUY' 
        AND symbol IN (
            SELECT DISTINCT symbol FROM trades 
            WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
        )
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        buy_df = pd.read_sql_query(query, conn)
        
        # 获取对应的卖出记录
        sell_query = """
        SELECT symbol, net_profit_pct_sell, sell_reason, holding_hours, timestamp as sell_time
        FROM trades 
        WHERE action = 'SELL' AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC
        """
        
        sell_df = pd.read_sql_query(sell_query, conn)
        conn.close()
        
        print(f'📊 买入记录: {len(buy_df)} 条')
        print(f'📊 卖出记录: {len(sell_df)} 条')
        
        # 为每个买入记录匹配最近的卖出记录
        matched_data = []
        
        for _, buy_row in buy_df.iterrows():
            symbol = buy_row['symbol']
            buy_time = buy_row['timestamp']
            
            # 找到该股票在买入时间之后的卖出记录
            symbol_sells = sell_df[sell_df['symbol'] == symbol]
            future_sells = symbol_sells[symbol_sells['sell_time'] > buy_time]
            
            if len(future_sells) > 0:
                # 取最近的一次卖出
                nearest_sell = future_sells.iloc[0]
                
                # 合并买入和卖出信息
                matched_row = buy_row.copy()
                matched_row['net_profit_pct_sell'] = nearest_sell['net_profit_pct_sell']
                matched_row['sell_reason'] = nearest_sell['sell_reason']
                matched_row['holding_hours'] = nearest_sell['holding_hours']
                
                matched_data.append(matched_row)
        
        if len(matched_data) == 0:
            print('⚠️ 没有匹配的交易记录')
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(matched_data)
        
        # 添加盈利标识
        df['is_profitable'] = df['net_profit_pct_sell'] > 0
        
        print(f'📈 成功匹配: {len(df)} 条交易记录')
        
        profitable_count = df['is_profitable'].sum()
        win_rate = profitable_count / len(df) * 100
        avg_profit = df['net_profit_pct_sell'].mean()
        
        print(f'   盈利交易: {profitable_count}/{len(df)} ({win_rate:.1f}%)')
        print(f'   平均收益: {avg_profit:.2f}%')
        
        return df
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None

def analyze_factor_effectiveness(df):
    """分析因子有效性"""
    print(f'\n🎯 因子有效性分析')
    print('=' * 50)
    
    # 排除非因子列
    exclude_columns = [
        'id', 'timestamp', 'symbol', 'action', 'price', 'volume', 'cost_price_sell',
        'confirmed_high_sell', 'confirmed_high_time', 'max_profit_pct', 'final_drawdown_pct',
        'status', 'sell_reason', 'net_profit_pct_sell', 'holding_hours', 'is_profitable'
    ]
    
    factor_columns = [col for col in df.columns if col not in exclude_columns]
    
    print(f'📊 分析因子数量: {len(factor_columns)}个')
    
    factor_results = []
    
    for factor in factor_columns:
        try:
            # 获取有效数据
            valid_mask = df[factor].notna() & df['net_profit_pct_sell'].notna()
            factor_values = df.loc[valid_mask, factor]
            profit_values = df.loc[valid_mask, 'net_profit_pct_sell']
            
            if len(factor_values) < 50:  # 样本量太小
                continue
            
            # 检查因子是否有变化
            if factor_values.nunique() < 3:  # 唯一值太少
                continue
            
            # 计算相关系数
            try:
                ic, ic_pvalue = stats.pearsonr(factor_values, profit_values)
                spearman_ic, spearman_pvalue = stats.spearmanr(factor_values, profit_values)
            except:
                continue
            
            # 分组分析
            try:
                q75 = factor_values.quantile(0.75)
                q25 = factor_values.quantile(0.25)
                
                high_mask = valid_mask & (df[factor] >= q75)
                low_mask = valid_mask & (df[factor] <= q25)
                
                high_group = df[high_mask]
                low_group = df[low_mask]
                
                if len(high_group) < 10 or len(low_group) < 10:
                    continue
                
                high_win_rate = high_group['is_profitable'].mean() * 100
                low_win_rate = low_group['is_profitable'].mean() * 100
                win_rate_diff = high_win_rate - low_win_rate
                
                high_avg_profit = high_group['net_profit_pct_sell'].mean()
                low_avg_profit = low_group['net_profit_pct_sell'].mean()
                profit_diff = high_avg_profit - low_avg_profit
                
                # t检验
                t_stat, t_pvalue = stats.ttest_ind(
                    high_group['net_profit_pct_sell'].dropna(),
                    low_group['net_profit_pct_sell'].dropna()
                )
                
                factor_results.append({
                    'factor': factor,
                    'ic': ic,
                    'ic_pvalue': ic_pvalue,
                    'spearman_ic': spearman_ic,
                    'spearman_pvalue': spearman_pvalue,
                    'ic_abs': abs(ic),
                    'spearman_abs': abs(spearman_ic),
                    'win_rate_diff': win_rate_diff,
                    'profit_diff': profit_diff,
                    'high_win_rate': high_win_rate,
                    'low_win_rate': low_win_rate,
                    'high_avg_profit': high_avg_profit,
                    'low_avg_profit': low_avg_profit,
                    't_pvalue': t_pvalue,
                    'sample_size': len(factor_values),
                    'unique_values': factor_values.nunique()
                })
                
            except Exception as e:
                continue
                
        except Exception as e:
            continue
    
    print(f'✅ 成功分析: {len(factor_results)}个因子')
    
    # 按Spearman IC绝对值排序
    factor_results.sort(key=lambda x: x['spearman_abs'], reverse=True)
    
    return factor_results

def display_top_factors(factor_results):
    """显示最有效的因子"""
    print(f'\n🏆 最有效因子排序 (前20名)')
    print('=' * 80)
    
    print(f'   排名  因子名称                      Spearman IC  胜率差异   收益差异   显著性')
    print(f'   ' + '-' * 85)
    
    top_factors = []
    
    for i, result in enumerate(factor_results[:20], 1):
        ic_str = f"{result['spearman_ic']:+.4f}"
        win_diff_str = f"{result['win_rate_diff']:+.1f}%"
        profit_diff_str = f"{result['profit_diff']:+.2f}%"
        
        # 显著性判断
        if result['spearman_pvalue'] < 0.001:
            significance = "🔥极显著"
        elif result['spearman_pvalue'] < 0.01:
            significance = "🚀显著"
        elif result['spearman_pvalue'] < 0.05:
            significance = "📊一般"
        else:
            significance = "🔹微弱"
        
        print(f'   {i:2d}.  {result["factor"]:<28} {ic_str:>10} {win_diff_str:>9} {profit_diff_str:>9} {significance}')
        
        if i <= 15:
            top_factors.append(result)
    
    return top_factors

def analyze_factor_categories(factor_results):
    """分析因子类别"""
    print(f'\n📊 因子类别效果分析')
    print('=' * 50)
    
    # 定义因子类别
    categories = {
        '价格动量': ['price_momentum_3d', 'price_momentum_5d', 'price_momentum_10d', 'price_change_pct'],
        '成交量': ['volume_ma5_ratio', 'volume_ma10_ratio', 'volume_ma20_ratio', 'volume_change_pct', 
                 'volume_momentum_3d', 'volume_momentum_5d', 'relative_volume', 'volume_change_rate'],
        '技术指标': ['rsi', 'macd_hist', 'adx', 'cci', 'williams_r', 'stoch_k', 'stoch_d'],
        '布林带': ['bb_width', 'bb_position', 'bb_upper_20', 'bb_middle_20', 'bb_lower_20', 'bb_width_20', 'bb_position_20'],
        '移动平均': ['ma5', 'ma10', 'ma20', 'ma60', 'ma120', 'ma5_distance_pct', 'ma10_distance_pct', 'ma20_distance_pct'],
        '波动率': ['atr_pct', 'volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d'],
        '时间因子': ['hour_of_day', 'minute_of_hour', 'day_of_week', 'day_of_month'],
        '评分因子': ['overall_score', 'technical_score', 'momentum_score', 'volume_score', 'volatility_score', 'trend_score']
    }
    
    category_stats = {}
    
    for category, factors in categories.items():
        category_factors = [f for f in factor_results if f['factor'] in factors]
        
        if category_factors:
            avg_ic = np.mean([f['spearman_abs'] for f in category_factors])
            max_ic = max([f['spearman_abs'] for f in category_factors])
            significant_count = len([f for f in category_factors if f['spearman_pvalue'] < 0.05])
            
            category_stats[category] = {
                'avg_ic': avg_ic,
                'max_ic': max_ic,
                'significant_count': significant_count,
                'total_count': len(category_factors),
                'best_factor': max(category_factors, key=lambda x: x['spearman_abs'])['factor']
            }
    
    # 按平均IC排序
    sorted_categories = sorted(category_stats.items(), key=lambda x: x[1]['avg_ic'], reverse=True)
    
    print(f'📈 因子类别效果排序:')
    print(f'   类别         平均IC    最大IC    显著因子  最佳因子')
    print(f'   ' + '-' * 65)
    
    for category, stats in sorted_categories:
        avg_ic_str = f"{stats['avg_ic']:.4f}"
        max_ic_str = f"{stats['max_ic']:.4f}"
        significant_str = f"{stats['significant_count']}/{stats['total_count']}"
        
        effectiveness = "🔥" if stats['avg_ic'] > 0.02 else "📊" if stats['avg_ic'] > 0.01 else "🔹"
        
        print(f'   {category:<12} {avg_ic_str:>8} {max_ic_str:>8} {significant_str:>8} {stats["best_factor"]:<20} {effectiveness}')

def generate_factor_recommendations(top_factors):
    """生成因子使用建议"""
    print(f'\n💡 因子使用建议')
    print('=' * 50)
    
    # 分级因子
    tier1 = [f for f in top_factors if f['spearman_abs'] > 0.03 and f['spearman_pvalue'] < 0.01]
    tier2 = [f for f in top_factors if 0.02 < f['spearman_abs'] <= 0.03 and f['spearman_pvalue'] < 0.05]
    tier3 = [f for f in top_factors if 0.01 < f['spearman_abs'] <= 0.02 and f['spearman_pvalue'] < 0.05]
    
    print(f'🔥 一级因子 (IC>0.03, 极显著): {len(tier1)}个')
    for factor in tier1:
        print(f'   - {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    print(f'\n🚀 二级因子 (IC>0.02, 显著): {len(tier2)}个')
    for factor in tier2:
        print(f'   - {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    print(f'\n📊 三级因子 (IC>0.01, 一般): {len(tier3)}个')
    for factor in tier3:
        print(f'   - {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    # 生成配置建议
    all_effective = tier1 + tier2 + tier3
    
    if len(all_effective) > 0:
        print(f'\n⚙️ 策略配置建议:')
        
        config_suggestion = '''
# 基于因子分析的策略配置建议
EFFECTIVE_FACTORS_CONFIG = {
    'enable': True,
    'factors': {'''
        
        total_weight = sum([abs(f['spearman_ic']) for f in all_effective])
        
        for factor in all_effective[:10]:  # 取前10个最有效的
            weight = abs(factor['spearman_ic']) / total_weight
            direction = "positive" if factor['spearman_ic'] > 0 else "negative"
            
            config_suggestion += f'''
        '{factor["factor"]}': {{
            'weight': {weight:.3f},
            'direction': '{direction}',
            'ic': {factor["spearman_ic"]:.4f},
            'significance': {factor["spearman_pvalue"]:.4f}
        }},'''
        
        config_suggestion += '''
    }
}'''
        
        print(config_suggestion)
    
    return tier1, tier2, tier3

def main():
    """主函数"""
    print('🚀 修复版因子分析')
    print('=' * 60)
    
    # 获取数据
    df = get_matched_trading_data()
    
    if df is not None:
        # 分析因子有效性
        factor_results = analyze_factor_effectiveness(df)
        
        if len(factor_results) > 0:
            # 显示最有效因子
            top_factors = display_top_factors(factor_results)
            
            # 分析因子类别
            analyze_factor_categories(factor_results)
            
            # 生成建议
            tier1, tier2, tier3 = generate_factor_recommendations(top_factors)
            
            print(f'\n🎯 分析总结')
            print('=' * 40)
            print(f'✅ 成功分析{len(factor_results)}个因子')
            print(f'🔥 一级因子: {len(tier1)}个')
            print(f'🚀 二级因子: {len(tier2)}个')
            print(f'📊 三级因子: {len(tier3)}个')
            
            total_effective = len(tier1) + len(tier2) + len(tier3)
            print(f'💎 总有效因子: {total_effective}个')
            
            if total_effective > 0:
                print(f'\n🚀 建议立即应用前{min(10, total_effective)}个最有效因子到策略中!')
            else:
                print(f'\n⚠️ 未发现显著有效的因子，需要进一步优化')
        else:
            print('❌ 因子分析失败')
    else:
        print('❌ 数据获取失败')

if __name__ == '__main__':
    main()
