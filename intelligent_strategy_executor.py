# coding=utf-8
"""
智能策略执行器
基于46个多维因子的完整策略执行系统
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from enhanced_multi_factor_engine import EnhancedMultiFactorEngine
from config import EFFECTIVE_FACTORS_CONFIG, MARKET_ENVIRONMENT_CONFIG, TIME_BASED_CONFIG

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentStrategyExecutor:
    """智能策略执行器"""
    
    def __init__(self, context=None):
        self.context = context
        self.factor_engine = EnhancedMultiFactorEngine(context)
        self.config = EFFECTIVE_FACTORS_CONFIG
        
    def detect_market_environment(self):
        """检测市场环境"""
        try:
            # 简化的市场环境检测 (实际应该基于指数数据)
            # 这里使用随机模拟，实际使用时应该替换为真实市场数据分析
            
            market_states = ['bull_market', 'bear_market', 'sideways_market']
            # 模拟市场环境检测
            current_state = np.random.choice(market_states)
            
            logger.info(f"🌊 检测到市场环境: {current_state}")
            return current_state
            
        except Exception as e:
            logger.error(f"市场环境检测失败: {e}")
            return 'sideways_market'  # 默认震荡市
    
    def get_time_based_config(self):
        """获取基于时间的配置"""
        try:
            current_time = datetime.now()
            hour = current_time.hour
            
            if 9 <= hour <= 11:
                return TIME_BASED_CONFIG['morning_session']
            elif 13 <= hour <= 15:
                return TIME_BASED_CONFIG['afternoon_session']
            else:
                return {}  # 非交易时间
                
        except Exception as e:
            logger.error(f"时间配置获取失败: {e}")
            return {}
    
    def apply_dynamic_adjustments(self, base_config, market_env, time_config):
        """应用动态调整"""
        try:
            adjusted_config = base_config.copy()
            
            # 应用市场环境调整
            if market_env in MARKET_ENVIRONMENT_CONFIG:
                env_config = MARKET_ENVIRONMENT_CONFIG[market_env]
                
                # 调整CCI权重
                if 'cci' in adjusted_config and 'cci_weight_multiplier' in env_config:
                    adjusted_config['cci']['weight'] *= env_config['cci_weight_multiplier']
                
                # 调整ATR阈值
                if 'atr_pct' in adjusted_config and 'atr_threshold_multiplier' in env_config:
                    adjusted_config['atr_pct']['min_threshold'] *= env_config['atr_threshold_multiplier']
                
                # 调整ADX阈值
                if 'adx' in adjusted_config and 'adx_threshold_multiplier' in env_config:
                    adjusted_config['adx']['min_threshold'] *= env_config['adx_threshold_multiplier']
                
                # 调整综合评分阈值
                if 'buy_conditions' in adjusted_config and 'min_combined_score' in env_config:
                    adjusted_config['buy_conditions']['min_combined_score'] = env_config['min_combined_score']
            
            # 应用时间段调整
            if time_config:
                # 调整成交量相关权重
                if 'volume_weight_boost' in time_config:
                    boost = time_config['volume_weight_boost']
                    if 'volume_breakthrough' in adjusted_config:
                        adjusted_config['volume_breakthrough']['weight'] *= boost
                
                # 调整动量相关权重
                if 'momentum_weight_boost' in time_config:
                    boost = time_config['momentum_weight_boost']
                    if 'opening_momentum' in adjusted_config:
                        adjusted_config['opening_momentum']['weight'] *= boost
            
            logger.info(f"✅ 动态调整完成: 市场环境={market_env}")
            return adjusted_config
            
        except Exception as e:
            logger.error(f"动态调整失败: {e}")
            return base_config
    
    def calculate_stock_factors(self, symbol, hist_data):
        """计算股票因子"""
        try:
            factors = self.factor_engine.calculate_all_enhanced_factors(hist_data, symbol)
            return factors
            
        except Exception as e:
            logger.error(f"计算股票因子失败 {symbol}: {e}")
            return {}
    
    def apply_multi_dimensional_filters(self, factors, config):
        """应用多维度筛选"""
        try:
            if not factors:
                return False, "无因子数据"
            
            passed_filters = []
            filter_details = []
            
            # 1. 技术面筛选
            tech_passed, tech_details = self._apply_technical_filters(factors, config)
            passed_filters.append(tech_passed)
            filter_details.extend(tech_details)
            
            # 2. 基本面筛选
            fund_passed, fund_details = self._apply_fundamental_filters(factors, config)
            passed_filters.append(fund_passed)
            filter_details.extend(fund_details)
            
            # 3. 情绪面筛选
            sent_passed, sent_details = self._apply_sentiment_filters(factors, config)
            passed_filters.append(sent_passed)
            filter_details.extend(sent_details)
            
            # 4. 跨市场筛选
            cross_passed, cross_details = self._apply_cross_market_filters(factors, config)
            passed_filters.append(cross_passed)
            filter_details.extend(cross_details)
            
            # 5. 综合评分筛选
            overall_passed, overall_details = self._apply_overall_score_filter(factors, config)
            passed_filters.append(overall_passed)
            filter_details.extend(overall_details)
            
            # 计算通过的筛选数量
            passed_count = sum(passed_filters)
            min_required = config.get('buy_conditions', {}).get('min_factors_count', 6)

            # 综合评分必须通过 + 其他维度达到最低要求
            final_passed = overall_passed and (passed_count >= min_required)
            
            return final_passed, {
                'passed_count': passed_count,
                'min_required': min_required,
                'details': filter_details,
                'technical_passed': tech_passed,
                'fundamental_passed': fund_passed,
                'sentiment_passed': sent_passed,
                'cross_market_passed': cross_passed,
                'overall_passed': overall_passed
            }
            
        except Exception as e:
            logger.error(f"多维度筛选失败: {e}")
            return False, str(e)
    
    def _apply_technical_filters(self, factors, config):
        """应用技术面筛选"""
        passed_filters = []
        details = []
        
        try:
            # CCI筛选
            if 'cci_14' in factors:
                cci_config = config.get('cci', {})
                cci_value = factors['cci_14']
                min_threshold = cci_config.get('min_threshold', -40)
                max_threshold = cci_config.get('max_threshold', 120)
                
                cci_passed = min_threshold <= cci_value <= max_threshold
                passed_filters.append(cci_passed)
                details.append(f"CCI: {cci_value:.2f} ({'✅' if cci_passed else '❌'}) [{min_threshold}, {max_threshold}]")
            
            # ATR筛选
            if 'atr_pct' in factors:
                atr_config = config.get('atr_pct', {})
                atr_value = factors['atr_pct']
                min_threshold = atr_config.get('min_threshold', 3.0)
                max_threshold = atr_config.get('max_threshold', 6.0)
                
                atr_passed = min_threshold <= atr_value <= max_threshold
                passed_filters.append(atr_passed)
                details.append(f"ATR: {atr_value:.2f}% ({'✅' if atr_passed else '❌'}) [≥{min_threshold}%]")
            
            # ADX筛选
            if 'adx_14' in factors:
                adx_config = config.get('adx', {})
                adx_value = factors['adx_14']
                min_threshold = adx_config.get('min_threshold', 28)
                
                adx_passed = adx_value >= min_threshold
                passed_filters.append(adx_passed)
                details.append(f"ADX: {adx_value:.2f} ({'✅' if adx_passed else '❌'}) [≥{min_threshold}]")
            
            # RSI筛选
            if 'rsi_14' in factors:
                rsi_value = factors['rsi_14']
                rsi_passed = 30 <= rsi_value <= 70
                passed_filters.append(rsi_passed)
                details.append(f"RSI: {rsi_value:.2f} ({'✅' if rsi_passed else '❌'}) [30-70]")
            
            # 布林带位置筛选
            if 'bb_position' in factors:
                bb_value = factors['bb_position']
                bb_passed = 20 <= bb_value <= 80
                passed_filters.append(bb_passed)
                details.append(f"BB位置: {bb_value:.2f}% ({'✅' if bb_passed else '❌'}) [20-80%]")
            
            tech_passed = sum(passed_filters) >= 3  # 技术面至少通过3个
            
            return tech_passed, details
            
        except Exception as e:
            logger.error(f"技术面筛选失败: {e}")
            return False, [f"技术面筛选错误: {e}"]
    
    def _apply_fundamental_filters(self, factors, config):
        """应用基本面筛选"""
        passed_filters = []
        details = []
        
        try:
            # PE相对值筛选
            if 'pe_relative' in factors:
                pe_config = config.get('pe_relative', {})
                pe_value = factors['pe_relative']
                max_threshold = pe_config.get('max_threshold', 1.5)
                
                pe_passed = pe_value <= max_threshold
                passed_filters.append(pe_passed)
                details.append(f"PE相对值: {pe_value:.2f} ({'✅' if pe_passed else '❌'}) [≤{max_threshold}]")
            
            # ROE质量筛选
            if 'roe_quality' in factors:
                roe_config = config.get('roe_quality', {})
                roe_value = factors['roe_quality']
                min_threshold = roe_config.get('min_threshold', 8)
                
                roe_passed = roe_value >= min_threshold
                passed_filters.append(roe_passed)
                details.append(f"ROE质量: {roe_value:.2f}% ({'✅' if roe_passed else '❌'}) [≥{min_threshold}%]")
            
            # 营收增长筛选
            if 'revenue_growth' in factors:
                revenue_config = config.get('revenue_growth', {})
                revenue_value = factors['revenue_growth']
                min_threshold = revenue_config.get('min_threshold', -10)
                
                revenue_passed = revenue_value >= min_threshold
                passed_filters.append(revenue_passed)
                details.append(f"营收增长: {revenue_value:.2f}% ({'✅' if revenue_passed else '❌'}) [≥{min_threshold}%]")
            
            fund_passed = sum(passed_filters) >= 2  # 基本面至少通过2个
            
            return fund_passed, details
            
        except Exception as e:
            logger.error(f"基本面筛选失败: {e}")
            return False, [f"基本面筛选错误: {e}"]
    
    def _apply_sentiment_filters(self, factors, config):
        """应用情绪面筛选"""
        passed_filters = []
        details = []
        
        try:
            # 主力资金持续性筛选
            if 'main_fund_persistence' in factors:
                fund_config = config.get('main_fund_persistence', {})
                fund_value = factors['main_fund_persistence']
                min_threshold = fund_config.get('min_threshold', 0.4)
                
                fund_passed = fund_value >= min_threshold
                passed_filters.append(fund_passed)
                details.append(f"主力资金持续性: {fund_value:.2f} ({'✅' if fund_passed else '❌'}) [≥{min_threshold}]")
            
            # 市场关注度筛选
            if 'market_attention' in factors:
                attention_config = config.get('market_attention', {})
                attention_value = factors['market_attention']
                min_threshold = attention_config.get('min_threshold', 1.2)
                
                attention_passed = attention_value >= min_threshold
                passed_filters.append(attention_passed)
                details.append(f"市场关注度: {attention_value:.2f} ({'✅' if attention_passed else '❌'}) [≥{min_threshold}]")
            
            # 成交量突破筛选
            if 'volume_breakthrough' in factors:
                volume_config = config.get('volume_breakthrough', {})
                volume_value = factors['volume_breakthrough']
                min_threshold = volume_config.get('min_threshold', 1.5)
                
                volume_passed = volume_value >= min_threshold
                passed_filters.append(volume_passed)
                details.append(f"成交量突破: {volume_value:.2f} ({'✅' if volume_passed else '❌'}) [≥{min_threshold}]")
            
            sent_passed = sum(passed_filters) >= 2  # 情绪面至少通过2个
            
            return sent_passed, details
            
        except Exception as e:
            logger.error(f"情绪面筛选失败: {e}")
            return False, [f"情绪面筛选错误: {e}"]
    
    def _apply_cross_market_filters(self, factors, config):
        """应用跨市场筛选"""
        passed_filters = []
        details = []
        
        try:
            # 行业相对强度筛选
            if 'industry_relative_strength' in factors:
                industry_config = config.get('industry_relative_strength', {})
                industry_value = factors['industry_relative_strength']
                min_threshold = industry_config.get('min_threshold', 0)
                
                industry_passed = industry_value >= min_threshold
                passed_filters.append(industry_passed)
                details.append(f"行业相对强度: {industry_value:.4f} ({'✅' if industry_passed else '❌'}) [≥{min_threshold}]")
            
            # 市场Beta筛选
            if 'market_beta' in factors:
                beta_config = config.get('market_beta', {})
                beta_value = factors['market_beta']
                min_threshold = beta_config.get('min_threshold', 0.8)
                max_threshold = beta_config.get('max_threshold', 1.5)
                
                beta_passed = min_threshold <= beta_value <= max_threshold
                passed_filters.append(beta_passed)
                details.append(f"市场Beta: {beta_value:.2f} ({'✅' if beta_passed else '❌'}) [{min_threshold}-{max_threshold}]")
            
            # 概念热度筛选
            if 'concept_heat' in factors:
                concept_config = config.get('concept_heat', {})
                concept_value = factors['concept_heat']
                min_threshold = concept_config.get('min_threshold', 0.3)
                
                concept_passed = concept_value >= min_threshold
                passed_filters.append(concept_passed)
                details.append(f"概念热度: {concept_value:.2f} ({'✅' if concept_passed else '❌'}) [≥{min_threshold}]")
            
            cross_passed = sum(passed_filters) >= 1  # 跨市场至少通过1个
            
            return cross_passed, details
            
        except Exception as e:
            logger.error(f"跨市场筛选失败: {e}")
            return False, [f"跨市场筛选错误: {e}"]
    
    def _apply_overall_score_filter(self, factors, config):
        """应用综合评分筛选"""
        try:
            overall_score = factors.get('overall_score', 0)
            min_score = config.get('buy_conditions', {}).get('min_combined_score', 0.55)
            
            overall_passed = overall_score >= min_score
            details = [f"综合评分: {overall_score:.4f} ({'✅' if overall_passed else '❌'}) [≥{min_score}]"]
            
            return overall_passed, details
            
        except Exception as e:
            logger.error(f"综合评分筛选失败: {e}")
            return False, [f"综合评分筛选错误: {e}"]
    
    def execute_strategy(self, stock_list):
        """执行完整策略"""
        logger.info("🚀 开始执行智能多因子策略")
        
        # 1. 检测市场环境
        market_env = self.detect_market_environment()
        
        # 2. 获取时间配置
        time_config = self.get_time_based_config()
        
        # 3. 应用动态调整
        adjusted_config = self.apply_dynamic_adjustments(self.config, market_env, time_config)
        
        # 4. 处理股票列表
        selected_stocks = []
        
        for symbol in stock_list:
            try:
                logger.info(f"📊 分析股票: {symbol}")
                
                # 模拟获取历史数据
                hist_data = self._get_mock_data(symbol)
                
                # 计算因子
                factors = self.calculate_stock_factors(symbol, hist_data)
                
                if not factors:
                    logger.warning(f"❌ {symbol} 因子计算失败")
                    continue
                
                # 应用筛选
                passed, filter_result = self.apply_multi_dimensional_filters(factors, adjusted_config)
                
                if passed:
                    selected_stocks.append({
                        'symbol': symbol,
                        'overall_score': factors.get('overall_score', 0),
                        'technical_score': factors.get('technical_score', 0),
                        'fundamental_score': factors.get('fundamental_score', 0),
                        'sentiment_score': factors.get('sentiment_score', 0),
                        'cross_market_score': factors.get('cross_market_score', 0),
                        'filter_result': filter_result,
                        'factors': factors
                    })
                    logger.info(f"✅ {symbol} 通过筛选，综合评分: {factors.get('overall_score', 0):.4f}")
                else:
                    logger.info(f"❌ {symbol} 未通过筛选: {filter_result['passed_count']}/{filter_result['min_required']}")
                
            except Exception as e:
                logger.error(f"❌ 处理股票 {symbol} 失败: {e}")
                continue
        
        # 5. 按综合评分排序
        selected_stocks.sort(key=lambda x: x['overall_score'], reverse=True)
        
        # 6. 限制数量
        max_signals = adjusted_config.get('buy_conditions', {}).get('max_signals_per_stock', 10)
        final_selection = selected_stocks[:max_signals]
        
        logger.info(f"🎯 策略执行完成: 选择了 {len(final_selection)} 只股票")
        
        return {
            'selected_stocks': final_selection,
            'market_environment': market_env,
            'time_config': time_config,
            'total_analyzed': len(stock_list),
            'total_selected': len(final_selection)
        }
    
    def _get_mock_data(self, symbol):
        """获取模拟数据"""
        # 生成模拟历史数据
        dates = pd.date_range(end=datetime.now(), periods=60, freq='D')
        
        np.random.seed(hash(symbol) % 2**32)
        base_price = 10 + np.random.uniform(5, 50)
        
        data = []
        current_price = base_price
        
        for date in dates:
            change_pct = np.random.normal(0, 0.02)
            current_price *= (1 + change_pct)
            
            open_price = current_price * (1 + np.random.uniform(-0.01, 0.01))
            high_price = max(open_price, current_price) * (1 + np.random.uniform(0, 0.03))
            low_price = min(open_price, current_price) * (1 - np.random.uniform(0, 0.03))
            close_price = current_price
            volume = int(np.random.uniform(1000000, 10000000))
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        return pd.DataFrame(data, index=dates)

def main():
    """主函数"""
    print("🚀 智能策略执行器测试")
    print("=" * 60)
    
    # 创建执行器
    executor = IntelligentStrategyExecutor()
    
    # 测试股票列表
    test_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '300015.SZ']
    
    # 执行策略
    result = executor.execute_strategy(test_stocks)
    
    # 显示结果
    print(f"\n📊 策略执行结果:")
    print(f"   市场环境: {result['market_environment']}")
    print(f"   分析股票: {result['total_analyzed']} 只")
    print(f"   选择股票: {result['total_selected']} 只")
    
    print(f"\n🎯 选择的股票:")
    for i, stock in enumerate(result['selected_stocks'], 1):
        print(f"   {i}. {stock['symbol']}")
        print(f"      综合评分: {stock['overall_score']:.4f}")
        print(f"      技术评分: {stock['technical_score']:.4f}")
        print(f"      基本面评分: {stock['fundamental_score']:.4f}")
        print(f"      情绪评分: {stock['sentiment_score']:.4f}")
        print(f"      跨市场评分: {stock['cross_market_score']:.4f}")
    
    return result

if __name__ == '__main__':
    main()
