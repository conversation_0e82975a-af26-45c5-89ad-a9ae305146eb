# coding=utf-8
"""
回测后问题诊断脚本
分析为什么回测后仍然没有买入记录
"""

import sqlite3
import re
import os

def analyze_backtest_results():
    """分析回测结果"""
    print('🔍 回测后问题诊断')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 统计回测前后的变化
        print('📊 回测前后数据变化:')
        cursor.execute("SELECT COUNT(*) FROM trades")
        total_count = cursor.fetchone()[0]
        print(f'  当前总记录数: {total_count}条')
        print(f'  回测前记录数: 49条')
        print(f'  新增记录数: {total_count - 49}条')
        
        # 2. 检查新增记录的时间范围
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM trades")
        time_range = cursor.fetchone()
        print(f'  数据时间范围: {time_range[0]} 到 {time_range[1]}')
        
        # 3. 检查新增记录的action分布
        cursor.execute("SELECT action, COUNT(*) FROM trades GROUP BY action")
        action_stats = cursor.fetchall()
        print(f'  action分布:')
        for action, count in action_stats:
            print(f'    {action}: {count}条')
        
        # 4. 检查是否有任何BUY相关的痕迹
        buy_patterns = ['BUY', 'buy', 'Buy', 'PURCHASE', 'purchase', 'OPEN', 'open']
        for pattern in buy_patterns:
            cursor.execute("SELECT COUNT(*) FROM trades WHERE action = ?", (pattern,))
            count = cursor.fetchone()[0]
            if count > 0:
                print(f'  发现 {pattern} 记录: {count}条')
        
        # 5. 检查最新的几条记录
        cursor.execute("SELECT timestamp, symbol, action, price, volume FROM trades ORDER BY timestamp DESC LIMIT 5")
        recent_records = cursor.fetchall()
        print(f'\n📅 最新5条记录:')
        for i, record in enumerate(recent_records, 1):
            timestamp, symbol, action, price, volume = record
            print(f'  {i}. {timestamp} | {symbol} | {action} | ¥{price} | {volume}股')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_buy_execution_in_code():
    """检查代码中的买入执行逻辑"""
    print('\n🔍 买入执行逻辑检查')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 买入执行路径分析:')
        
        # 1. 检查买入策略调用
        buy_strategy_pattern = r'def buy_strategy\(.*?\):(.*?)(?=def|\Z)'
        buy_strategy_match = re.search(buy_strategy_pattern, content, re.DOTALL)
        
        if buy_strategy_match:
            buy_strategy_body = buy_strategy_match.group(1)
            
            # 检查是否调用了analyze_single_symbol
            if 'analyze_single_symbol' in buy_strategy_body:
                print('  ✅ buy_strategy调用analyze_single_symbol')
            else:
                print('  ❌ buy_strategy未调用analyze_single_symbol')
            
            # 检查是否有买入执行逻辑
            if 'execute_backup_buy_logic' in buy_strategy_body:
                print('  ✅ buy_strategy调用execute_backup_buy_logic')
            else:
                print('  ❌ buy_strategy未调用execute_backup_buy_logic')
        
        # 2. 检查买入执行函数
        execute_buy_pattern = r'def execute_backup_buy_logic\(.*?\):(.*?)(?=def|\Z)'
        execute_buy_match = re.search(execute_buy_pattern, content, re.DOTALL)
        
        if execute_buy_match:
            execute_buy_body = execute_buy_match.group(1)
            
            # 检查是否有order_volume调用
            if 'order_volume' in execute_buy_body:
                print('  ✅ execute_backup_buy_logic调用order_volume')
            else:
                print('  ❌ execute_backup_buy_logic未调用order_volume')
            
            # 检查是否有记录保存
            if 'save_original_buy_record' in execute_buy_body:
                print('  ✅ execute_backup_buy_logic调用save_original_buy_record')
            else:
                print('  ❌ execute_backup_buy_logic未调用save_original_buy_record')
        
        # 3. 检查记录保存函数
        save_record_pattern = r'def save_original_buy_record\(.*?\):(.*?)(?=def|\Z)'
        save_record_match = re.search(save_record_pattern, content, re.DOTALL)
        
        if save_record_match:
            save_record_body = save_record_match.group(1)
            
            # 检查数据管理器调用
            if 'context.data_manager.save_trade' in save_record_body:
                print('  ✅ save_original_buy_record调用data_manager.save_trade')
            else:
                print('  ❌ save_original_buy_record未调用data_manager.save_trade')
            
            # 检查备用保存
            if 'save_analysis' in save_record_body:
                print('  ✅ save_original_buy_record有备用保存save_analysis')
            else:
                print('  ❌ save_original_buy_record无备用保存')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def check_possible_blocking_conditions():
    """检查可能的阻塞条件"""
    print('\n🔍 可能的阻塞条件检查')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 可能阻塞买入的条件:')
        
        # 检查常见的阻塞条件
        blocking_patterns = [
            (r'if not signal_analysis_results:', '没有买入信号'),
            (r'if len\(signal_analysis_results\) == 0:', '信号结果为空'),
            (r'if max_buy_stocks <= 0:', '达到最大持仓'),
            (r'if available_cash <', '资金不足'),
            (r'if not is_trading', '非交易时间'),
            (r'if context\.run_mode.*backtest.*return', '回测模式返回'),
            (r'return False', '条件不满足返回False'),
            (r'return None', '条件不满足返回None')
        ]
        
        for pattern, description in blocking_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  ⚠️ {description}: {len(matches)}处')
        
        # 检查回测模式特殊处理
        backtest_patterns = [
            r'if.*run_mode.*backtest',
            r'if.*backtest.*mode',
            r'回测模式'
        ]
        
        print(f'\n📊 回测模式相关检查:')
        for pattern in backtest_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f'  🔍 回测模式处理: {len(matches)}处')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def diagnose_root_cause():
    """诊断根本原因"""
    print('\n💡 根本原因诊断')
    print('=' * 50)
    
    print('📊 基于当前证据的分析:')
    
    evidence = [
        '✅ 回测确实运行了（记录从49条增加到88条）',
        '✅ 数据管理器初始化已修复',
        '✅ 字段映射已修复为小写',
        '❌ 仍然没有BUY记录，只有SELL记录',
        '❌ 所有新增记录都是SELL类型'
    ]
    
    for item in evidence:
        print(f'  {item}')
    
    print(f'\n🔍 可能的原因:')
    
    possible_causes = [
        {
            'cause': '买入条件从未满足',
            'probability': '高',
            'evidence': '回测期间没有触发买入信号',
            'check': '检查买入信号生成逻辑和条件'
        },
        {
            'cause': '买入执行被阻塞',
            'probability': '中',
            'evidence': '有买入信号但被某些条件阻塞',
            'check': '检查资金、持仓、时间等限制条件'
        },
        {
            'cause': 'order_volume调用失败',
            'probability': '中',
            'evidence': '买入逻辑执行但下单失败',
            'check': '检查order_volume的返回值处理'
        },
        {
            'cause': '记录保存逻辑仍有问题',
            'probability': '低',
            'evidence': '买入成功但记录保存失败',
            'check': '检查save_original_buy_record的执行'
        },
        {
            'cause': '回测模式特殊处理',
            'probability': '中',
            'evidence': '回测模式下买入逻辑被跳过',
            'check': '检查回测模式的特殊处理逻辑'
        }
    ]
    
    for i, cause in enumerate(possible_causes, 1):
        print(f'\n{i}. {cause["cause"]} (概率: {cause["probability"]})')
        print(f'   证据: {cause["evidence"]}')
        print(f'   检查: {cause["check"]}')

def suggest_debugging_steps():
    """建议调试步骤"""
    print('\n📋 建议调试步骤')
    print('=' * 50)
    
    steps = [
        {
            'step': '1. 检查买入信号生成',
            'action': '在analyze_single_symbol中添加详细日志',
            'purpose': '确认是否生成了买入信号'
        },
        {
            'step': '2. 检查买入策略调用',
            'action': '在buy_strategy中添加调试日志',
            'purpose': '确认买入策略是否被调用'
        },
        {
            'step': '3. 检查买入执行逻辑',
            'action': '在execute_backup_buy_logic中添加日志',
            'purpose': '确认买入执行是否被调用'
        },
        {
            'step': '4. 检查order_volume调用',
            'action': '记录order_volume的参数和返回值',
            'purpose': '确认下单是否成功'
        },
        {
            'step': '5. 检查记录保存',
            'action': '在save_original_buy_record中添加详细日志',
            'purpose': '确认记录保存是否执行'
        }
    ]
    
    for step in steps:
        print(f'{step["step"]}: {step["action"]}')
        print(f'   目的: {step["purpose"]}')

def main():
    """主函数"""
    print('🔍 回测后问题诊断报告')
    print('=' * 60)
    
    # 分析回测结果
    analyze_backtest_results()
    
    # 检查买入执行逻辑
    check_buy_execution_in_code()
    
    # 检查可能的阻塞条件
    check_possible_blocking_conditions()
    
    # 诊断根本原因
    diagnose_root_cause()
    
    # 建议调试步骤
    suggest_debugging_steps()
    
    print(f'\n🎯 诊断结论:')
    print('=' * 40)
    print('✅ 回测确实运行了，新增了39条记录')
    print('❌ 但仍然没有买入记录，问题可能在买入逻辑执行环节')
    print('🔍 最可能的原因是买入条件从未满足或被阻塞')
    print('🔧 建议添加详细的调试日志来定位具体问题')

if __name__ == '__main__':
    main()
