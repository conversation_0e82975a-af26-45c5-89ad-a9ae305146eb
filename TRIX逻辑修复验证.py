#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TRIX买入逻辑修复验证脚本
验证修复后的TRIX逻辑是否符合用户期望
"""

import numpy as np
import pandas as pd
import talib

def test_trix_calculation():
    """测试TRIX计算的一致性"""
    print("🔍 测试TRIX计算一致性")
    print("=" * 50)
    
    # 模拟价格数据
    prices = np.array([100, 101, 99, 102, 98, 103, 97, 104, 96, 105, 95, 106, 94, 107, 93, 108, 92, 109, 91, 110])
    
    # 测试不同周期的TRIX计算
    periods = [3, 4, 14]
    
    for period in periods:
        print(f"\n📊 TRIX周期: {period}日")
        
        # 使用talib计算
        trix_talib = talib.TRIX(prices, timeperiod=period)
        
        # 手动计算
        series = pd.Series(prices)
        ema1 = series.ewm(span=period, adjust=False).mean()
        ema2 = ema1.ewm(span=period, adjust=False).mean()
        ema3 = ema2.ewm(span=period, adjust=False).mean()
        trix_manual = ema3.pct_change() * 10000
        
        print(f"  talib TRIX最后3个值: {trix_talib[-3:]}")
        print(f"  手动TRIX最后3个值: {trix_manual.values[-3:]}")
        
        # 检查是否有有效值
        valid_talib = ~np.isnan(trix_talib[-3:])
        valid_manual = ~np.isnan(trix_manual.values[-3:])
        print(f"  talib有效值: {valid_talib}")
        print(f"  手动有效值: {valid_manual}")

def test_trix_logic():
    """测试TRIX买入逻辑"""
    print("\n🎯 测试TRIX买入逻辑")
    print("=" * 50)
    
    # 模拟不同的价格场景
    scenarios = [
        {
            'name': '下降后反转',
            'prices': [100, 99, 98, 97, 96, 95, 94, 93, 94, 95, 96, 97, 98, 99, 100],
            'expected_prefilter': True,
            'expected_reversal': True
        },
        {
            'name': '持续上升',
            'prices': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114],
            'expected_prefilter': False,
            'expected_reversal': False
        },
        {
            'name': '持续下降',
            'prices': [100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86],
            'expected_prefilter': True,
            'expected_reversal': False
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📈 场景: {scenario['name']}")
        prices = np.array(scenario['prices'], dtype=float)
        
        # 计算3日TRIX
        trix = talib.TRIX(prices, timeperiod=3)
        
        if len(trix) >= 3 and not np.isnan(trix[-3:]).any():
            current_trix = trix[-1]   # 今日
            prev_trix = trix[-2]      # 昨日  
            prev2_trix = trix[-3]     # 前日
            
            # 预筛选条件：昨日 < 前日
            prefilter_pass = prev_trix < prev2_trix
            
            # 反转确认：今日 > 昨日
            reversal_pass = current_trix > prev_trix
            
            print(f"  TRIX序列: 前日({prev2_trix:.6f}) -> 昨日({prev_trix:.6f}) -> 今日({current_trix:.6f})")
            print(f"  预筛选(昨日<前日): {prefilter_pass} (期望: {scenario['expected_prefilter']})")
            print(f"  反转确认(今日>昨日): {reversal_pass} (期望: {scenario['expected_reversal']})")
            print(f"  最终通过: {prefilter_pass and reversal_pass}")
            
            # 验证结果
            if prefilter_pass == scenario['expected_prefilter']:
                print("  ✅ 预筛选结果正确")
            else:
                print("  ❌ 预筛选结果错误")
                
            if reversal_pass == scenario['expected_reversal']:
                print("  ✅ 反转确认结果正确")
            else:
                print("  ❌ 反转确认结果错误")
        else:
            print("  ❌ TRIX数据不足或包含NaN")

def test_config_consistency():
    """测试配置一致性"""
    print("\n⚙️ 测试配置一致性")
    print("=" * 50)
    
    try:
        from config import get_config_value
        
        # 检查TRIX相关配置
        trix_period = get_config_value('TRIX_EMA_PERIOD', 3)
        use_talib = get_config_value('USE_TALIB_TRIX', True)
        threshold = get_config_value('TRIX_REVERSAL_THRESHOLD', 0.0001)
        
        print(f"✅ TRIX_EMA_PERIOD: {trix_period}")
        print(f"✅ USE_TALIB_TRIX: {use_talib}")
        print(f"✅ TRIX_REVERSAL_THRESHOLD: {threshold}")
        
        # 验证期望值
        if trix_period == 3:
            print("✅ TRIX周期设置正确 (3日)")
        else:
            print(f"❌ TRIX周期设置错误，期望3日，实际{trix_period}日")
            
        if threshold <= 0.001:
            print("✅ TRIX阈值设置合理")
        else:
            print(f"❌ TRIX阈值过高: {threshold}")
            
        return True
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_import_consistency():
    """测试导入一致性"""
    print("\n📦 测试导入一致性")
    print("=" * 50)
    
    try:
        # 测试主要模块导入
        import main
        print("✅ main模块导入成功")
        
        # 测试TRIX预筛选器导入
        from trix_prefilter import get_trix_prefilter
        print("✅ trix_prefilter模块导入成功")
        
        # 测试关键函数存在性
        if hasattr(main, 'calculate_trix_unified'):
            print("✅ calculate_trix_unified函数存在")
        else:
            print("❌ calculate_trix_unified函数不存在")
            
        if hasattr(main, 'daily_trix_prefilter'):
            print("✅ daily_trix_prefilter函数存在")
        else:
            print("❌ daily_trix_prefilter函数不存在")
            
        return True
    except Exception as e:
        print(f"❌ 导入检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 TRIX买入逻辑修复验证")
    print("=" * 60)
    
    all_passed = True
    
    # 执行各项测试
    tests = [
        ("TRIX计算测试", test_trix_calculation),
        ("TRIX逻辑测试", test_trix_logic),
        ("配置一致性测试", test_config_consistency),
        ("导入一致性测试", test_import_consistency)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result is False:
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name}执行失败: {e}")
            all_passed = False
    
    # 总结
    print("\n" + "="*60)
    if all_passed:
        print("🎉 所有测试通过！TRIX买入逻辑修复验证成功")
        print("\n📋 修复总结:")
        print("✅ 统一TRIX周期为3日")
        print("✅ 统一TRIX计算方法")
        print("✅ 简化反转判断逻辑")
        print("✅ 增强日志记录")
        print("\n🎯 期望效果:")
        print("📈 预筛选应该能筛选出更多股票")
        print("🔄 反转确认逻辑更加敏感")
        print("📊 整体筛选效果应该显著改善")
    else:
        print("❌ 部分测试未通过，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    main()
