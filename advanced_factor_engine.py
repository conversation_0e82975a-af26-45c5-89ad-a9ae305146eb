# coding=utf-8
"""
高级因子计算引擎
基于完整数据的多维度因子计算
"""

import pandas as pd
import numpy as np
import sqlite3
import talib
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedFactorEngine:
    """高级因子计算引擎"""
    
    def __init__(self, db_path='data/enhanced_market_data.db'):
        self.db_path = db_path
        
    def get_stock_data(self, symbol, days=252):
        """获取股票数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取行情数据
            market_query = """
                SELECT trade_date, open_price, high_price, low_price, close_price, 
                       volume, amount, turnover_rate, pct_change, amplitude
                FROM daily_market_data 
                WHERE symbol = ? 
                ORDER BY trade_date DESC 
                LIMIT ?
            """
            
            market_data = pd.read_sql_query(market_query, conn, params=(symbol, days))
            market_data['trade_date'] = pd.to_datetime(market_data['trade_date'])
            market_data = market_data.sort_values('trade_date').reset_index(drop=True)
            
            # 获取财务数据
            financial_query = """
                SELECT report_date, pe_ttm, pb_lf, roe_ttm, roa_ttm, revenue, net_profit
                FROM financial_data 
                WHERE symbol = ? 
                ORDER BY report_date DESC 
                LIMIT 8
            """
            
            financial_data = pd.read_sql_query(financial_query, conn, params=(symbol,))
            
            # 获取资金流向数据
            money_flow_query = """
                SELECT trade_date, net_amount_main, net_pct_main, net_amount_xl, net_pct_xl
                FROM money_flow_data 
                WHERE symbol = ? 
                ORDER BY trade_date DESC 
                LIMIT ?
            """
            
            money_flow_data = pd.read_sql_query(money_flow_query, conn, params=(symbol, days))
            
            # 获取行业概念数据
            industry_query = """
                SELECT industry_sw1, concept_names, market_cap
                FROM industry_concept_mapping 
                WHERE symbol = ?
            """
            
            industry_data = pd.read_sql_query(industry_query, conn, params=(symbol,))
            
            conn.close()
            
            return {
                'market': market_data,
                'financial': financial_data,
                'money_flow': money_flow_data,
                'industry': industry_data
            }
            
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None
    
    def calculate_technical_factors(self, market_data):
        """计算技术因子"""
        factors = {}
        
        if len(market_data) < 20:
            return factors
        
        try:
            close = market_data['close_price'].values
            high = market_data['high_price'].values
            low = market_data['low_price'].values
            volume = market_data['volume'].values
            
            # 1. 多周期RSI
            factors['rsi_6'] = talib.RSI(close, timeperiod=6)[-1] if len(close) >= 6 else np.nan
            factors['rsi_14'] = talib.RSI(close, timeperiod=14)[-1] if len(close) >= 14 else np.nan
            factors['rsi_21'] = talib.RSI(close, timeperiod=21)[-1] if len(close) >= 21 else np.nan
            
            # 2. 自适应RSI (根据波动率调整周期)
            volatility = np.std(np.diff(close[-20:])) / np.mean(close[-20:])
            adaptive_period = max(6, min(30, int(14 * (1 + volatility))))
            factors['rsi_adaptive'] = talib.RSI(close, timeperiod=adaptive_period)[-1]
            
            # 3. CCI指标
            factors['cci_14'] = talib.CCI(high, low, close, timeperiod=14)[-1] if len(close) >= 14 else np.nan
            factors['cci_20'] = talib.CCI(high, low, close, timeperiod=20)[-1] if len(close) >= 20 else np.nan
            
            # 4. ADX趋势强度
            factors['adx_14'] = talib.ADX(high, low, close, timeperiod=14)[-1] if len(close) >= 14 else np.nan
            
            # 5. ATR波动率
            atr_14 = talib.ATR(high, low, close, timeperiod=14)[-1] if len(close) >= 14 else np.nan
            factors['atr_14'] = atr_14
            factors['atr_pct'] = (atr_14 / close[-1] * 100) if atr_14 and close[-1] else np.nan
            
            # 6. 布林带
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20)
            if len(bb_upper) > 0:
                factors['bb_upper'] = bb_upper[-1]
                factors['bb_middle'] = bb_middle[-1]
                factors['bb_lower'] = bb_lower[-1]
                factors['bb_width'] = (bb_upper[-1] - bb_lower[-1]) / bb_middle[-1] * 100
                factors['bb_position'] = (close[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) * 100
            
            # 7. MACD
            macd, macd_signal, macd_hist = talib.MACD(close)
            if len(macd) > 0:
                factors['macd'] = macd[-1]
                factors['macd_signal'] = macd_signal[-1]
                factors['macd_hist'] = macd_hist[-1]
            
            # 8. 多时间框架动量
            if len(close) >= 20:
                factors['momentum_5d'] = (close[-1] - close[-6]) / close[-6] * 100
                factors['momentum_10d'] = (close[-1] - close[-11]) / close[-11] * 100
                factors['momentum_20d'] = (close[-1] - close[-21]) / close[-21] * 100
                
                # 综合动量评分
                momentum_scores = [factors['momentum_5d'], factors['momentum_10d'], factors['momentum_20d']]
                factors['momentum_composite'] = np.mean([s for s in momentum_scores if not np.isnan(s)])
            
            # 9. 量价协同因子
            if len(close) >= 20:
                price_changes = np.diff(close[-20:])
                volume_changes = np.diff(volume[-20:])
                if len(price_changes) > 0 and len(volume_changes) > 0:
                    factors['volume_price_correlation'] = np.corrcoef(price_changes, volume_changes)[0, 1]
            
            # 10. 成交量因子
            if len(volume) >= 20:
                volume_ma_20 = np.mean(volume[-20:])
                factors['volume_ratio'] = volume[-1] / volume_ma_20 if volume_ma_20 > 0 else np.nan
                
                # 成交量趋势
                volume_trend = np.polyfit(range(10), volume[-10:], 1)[0] if len(volume) >= 10 else 0
                factors['volume_trend'] = volume_trend
            
        except Exception as e:
            logger.error(f"计算技术因子失败: {e}")
        
        return factors
    
    def calculate_fundamental_factors(self, financial_data, market_data):
        """计算基本面因子"""
        factors = {}
        
        if len(financial_data) == 0:
            return factors
        
        try:
            latest_financial = financial_data.iloc[0]
            
            # 1. 估值因子
            factors['pe_ttm'] = latest_financial['pe_ttm']
            factors['pb_lf'] = latest_financial['pb_lf']
            
            # 2. 盈利质量因子
            factors['roe_ttm'] = latest_financial['roe_ttm']
            factors['roa_ttm'] = latest_financial['roa_ttm']
            
            # 3. 成长性因子
            if len(financial_data) >= 4:  # 至少4个季度数据
                # 营收增长率
                revenue_current = financial_data.iloc[0]['revenue']
                revenue_yoy = financial_data.iloc[3]['revenue'] if len(financial_data) > 3 else revenue_current
                factors['revenue_growth'] = (revenue_current - revenue_yoy) / revenue_yoy * 100 if revenue_yoy > 0 else np.nan
                
                # 净利润增长率
                profit_current = financial_data.iloc[0]['net_profit']
                profit_yoy = financial_data.iloc[3]['net_profit'] if len(financial_data) > 3 else profit_current
                factors['profit_growth'] = (profit_current - profit_yoy) / profit_yoy * 100 if profit_yoy > 0 else np.nan
            
            # 4. ROE稳定性
            if len(financial_data) >= 4:
                roe_values = financial_data['roe_ttm'].dropna()
                if len(roe_values) >= 4:
                    factors['roe_stability'] = 1 / (1 + np.std(roe_values))  # 稳定性评分
            
            # 5. 相对估值因子 (需要行业数据，这里简化处理)
            if not np.isnan(factors.get('pe_ttm', np.nan)):
                # 假设市场平均PE为20
                market_avg_pe = 20
                factors['pe_relative'] = factors['pe_ttm'] / market_avg_pe
            
        except Exception as e:
            logger.error(f"计算基本面因子失败: {e}")
        
        return factors
    
    def calculate_sentiment_factors(self, money_flow_data, market_data):
        """计算市场情绪因子"""
        factors = {}
        
        if len(money_flow_data) == 0:
            return factors
        
        try:
            # 1. 主力资金因子
            if len(money_flow_data) >= 5:
                main_flows = money_flow_data['net_amount_main'].values[-5:]
                factors['main_fund_net'] = main_flows[-1]  # 最新主力净流入
                factors['main_fund_avg_5d'] = np.mean(main_flows)  # 5日平均
                
                # 主力资金持续性
                positive_days = np.sum(main_flows > 0)
                factors['main_fund_persistence'] = positive_days / len(main_flows)
            
            # 2. 超大单因子
            if len(money_flow_data) >= 5:
                xl_flows = money_flow_data['net_amount_xl'].values[-5:]
                factors['xl_fund_net'] = xl_flows[-1]
                factors['xl_fund_trend'] = np.polyfit(range(len(xl_flows)), xl_flows, 1)[0]
            
            # 3. 市场关注度因子
            if len(market_data) >= 20:
                volumes = market_data['volume'].values[-20:]
                avg_volume = np.mean(volumes)
                current_volume = volumes[-1]
                factors['market_attention'] = current_volume / avg_volume if avg_volume > 0 else np.nan
            
            # 4. 价格动量与资金流向协同
            if len(money_flow_data) >= 10 and len(market_data) >= 10:
                price_changes = market_data['pct_change'].values[-10:]
                main_flows = money_flow_data['net_pct_main'].values[-10:]
                
                # 去除NaN值
                valid_indices = ~(np.isnan(price_changes) | np.isnan(main_flows))
                if np.sum(valid_indices) >= 5:
                    factors['price_flow_correlation'] = np.corrcoef(
                        price_changes[valid_indices], 
                        main_flows[valid_indices]
                    )[0, 1]
            
        except Exception as e:
            logger.error(f"计算市场情绪因子失败: {e}")
        
        return factors
    
    def calculate_cross_market_factors(self, symbol, market_data, industry_data):
        """计算跨市场因子"""
        factors = {}
        
        try:
            # 1. 行业相对强度 (简化实现)
            if len(market_data) >= 20:
                stock_returns = market_data['pct_change'].values[-20:]
                # 这里应该获取行业指数收益率，简化为市场平均
                market_avg_return = 0.05  # 假设市场平均日收益率
                
                stock_avg_return = np.mean(stock_returns[~np.isnan(stock_returns)])
                factors['industry_relative_strength'] = stock_avg_return - market_avg_return
            
            # 2. 市场Beta
            if len(market_data) >= 60:
                stock_returns = market_data['pct_change'].values[-60:]
                # 这里应该使用市场指数收益率，简化处理
                market_returns = np.random.normal(0.05, 1.5, 60)  # 模拟市场收益率
                
                valid_indices = ~(np.isnan(stock_returns) | np.isnan(market_returns))
                if np.sum(valid_indices) >= 30:
                    covariance = np.cov(stock_returns[valid_indices], market_returns[valid_indices])[0, 1]
                    market_variance = np.var(market_returns[valid_indices])
                    factors['market_beta'] = covariance / market_variance if market_variance > 0 else np.nan
            
            # 3. 概念热度因子 (基于概念板块)
            if not industry_data.empty:
                concepts = industry_data.iloc[0]['concept_names']
                if pd.notna(concepts):
                    concept_list = concepts.split(',')
                    # 这里应该计算概念板块的热度，简化处理
                    hot_concepts = ['人工智能', '新能源汽车', '5G', '芯片']
                    concept_heat_score = sum(1 for concept in concept_list if concept in hot_concepts)
                    factors['concept_heat'] = concept_heat_score / len(concept_list) if concept_list else 0
            
        except Exception as e:
            logger.error(f"计算跨市场因子失败: {e}")
        
        return factors
    
    def calculate_composite_scores(self, all_factors):
        """计算综合评分"""
        scores = {}
        
        try:
            # 1. 技术面评分
            technical_factors = [
                'rsi_14', 'cci_14', 'adx_14', 'atr_pct', 'bb_position', 
                'macd_hist', 'momentum_composite', 'volume_ratio'
            ]
            
            technical_values = []
            for factor in technical_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    # 标准化到0-1区间
                    if factor == 'rsi_14':
                        score = 1 - abs(all_factors[factor] - 50) / 50  # RSI接近50分数高
                    elif factor == 'cci_14':
                        score = max(0, min(1, (all_factors[factor] + 100) / 200))  # CCI标准化
                    elif factor == 'adx_14':
                        score = min(1, all_factors[factor] / 50)  # ADX越高越好
                    elif factor == 'atr_pct':
                        score = min(1, all_factors[factor] / 5)  # ATR适中为好
                    elif factor == 'bb_position':
                        score = all_factors[factor] / 100  # BB位置标准化
                    elif factor == 'momentum_composite':
                        score = max(0, min(1, (all_factors[factor] + 10) / 20))  # 动量标准化
                    elif factor == 'volume_ratio':
                        score = min(1, all_factors[factor] / 3)  # 成交量比率
                    else:
                        score = 0.5  # 默认中性评分
                    
                    technical_values.append(score)
            
            scores['technical_score'] = np.mean(technical_values) if technical_values else 0.5
            
            # 2. 基本面评分
            fundamental_factors = ['pe_relative', 'roe_ttm', 'revenue_growth', 'profit_growth']
            fundamental_values = []
            
            for factor in fundamental_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    if factor == 'pe_relative':
                        score = max(0, min(1, 2 - all_factors[factor]))  # PE相对值越低越好
                    elif factor == 'roe_ttm':
                        score = min(1, all_factors[factor] / 30)  # ROE越高越好
                    elif factor in ['revenue_growth', 'profit_growth']:
                        score = max(0, min(1, (all_factors[factor] + 20) / 40))  # 增长率标准化
                    else:
                        score = 0.5
                    
                    fundamental_values.append(score)
            
            scores['fundamental_score'] = np.mean(fundamental_values) if fundamental_values else 0.5
            
            # 3. 情绪面评分
            sentiment_factors = ['main_fund_persistence', 'market_attention', 'price_flow_correlation']
            sentiment_values = []
            
            for factor in sentiment_factors:
                if factor in all_factors and not np.isnan(all_factors[factor]):
                    if factor == 'main_fund_persistence':
                        score = all_factors[factor]  # 已经是0-1区间
                    elif factor == 'market_attention':
                        score = min(1, all_factors[factor] / 3)  # 关注度标准化
                    elif factor == 'price_flow_correlation':
                        score = (all_factors[factor] + 1) / 2  # 相关性标准化到0-1
                    else:
                        score = 0.5
                    
                    sentiment_values.append(score)
            
            scores['sentiment_score'] = np.mean(sentiment_values) if sentiment_values else 0.5
            
            # 4. 综合评分
            weights = {
                'technical_score': 0.4,
                'fundamental_score': 0.3,
                'sentiment_score': 0.3
            }
            
            overall_score = sum(scores[key] * weights[key] for key in weights.keys())
            scores['overall_score'] = overall_score
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            scores = {
                'technical_score': 0.5,
                'fundamental_score': 0.5,
                'sentiment_score': 0.5,
                'overall_score': 0.5
            }
        
        return scores
    
    def calculate_all_factors(self, symbol):
        """计算所有因子"""
        logger.info(f"开始计算 {symbol} 的所有因子")
        
        # 获取数据
        data = self.get_stock_data(symbol)
        if not data:
            logger.error(f"无法获取 {symbol} 的数据")
            return None
        
        all_factors = {}
        
        # 计算各类因子
        technical_factors = self.calculate_technical_factors(data['market'])
        fundamental_factors = self.calculate_fundamental_factors(data['financial'], data['market'])
        sentiment_factors = self.calculate_sentiment_factors(data['money_flow'], data['market'])
        cross_market_factors = self.calculate_cross_market_factors(symbol, data['market'], data['industry'])
        
        # 合并所有因子
        all_factors.update(technical_factors)
        all_factors.update(fundamental_factors)
        all_factors.update(sentiment_factors)
        all_factors.update(cross_market_factors)
        
        # 计算综合评分
        composite_scores = self.calculate_composite_scores(all_factors)
        all_factors.update(composite_scores)
        
        logger.info(f"✅ {symbol} 因子计算完成，共 {len(all_factors)} 个因子")
        
        return all_factors
    
    def save_factors_to_db(self, symbol, factors, trade_date=None):
        """保存因子到数据库"""
        if not factors:
            return False
        
        if trade_date is None:
            trade_date = datetime.now().date()
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 准备插入数据
            factor_fields = [
                'rsi_6', 'rsi_14', 'rsi_21', 'cci_14', 'adx_14', 'atr_14', 'atr_pct',
                'bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_position',
                'macd', 'macd_signal', 'macd_hist', 'pe_relative', 'pb_relative',
                'roe_quality', 'revenue_growth', 'profit_growth', 'volume_ratio',
                'price_momentum_5d', 'price_momentum_20d', 'main_fund_persistence',
                'market_attention', 'industry_relative_strength', 'market_beta',
                'technical_score', 'fundamental_score', 'sentiment_score', 'overall_score'
            ]
            
            # 构建SQL
            placeholders = ', '.join(['?' for _ in range(len(factor_fields) + 3)])  # +3 for symbol, trade_date, created_time
            fields_str = ', '.join(['symbol', 'trade_date'] + factor_fields + ['created_time'])
            
            values = [symbol, trade_date]
            for field in factor_fields:
                values.append(factors.get(field, None))
            values.append(datetime.now())
            
            sql = f"""
                INSERT OR REPLACE INTO calculated_factors 
                ({fields_str})
                VALUES ({placeholders})
            """
            
            cursor.execute(sql, values)
            conn.commit()
            conn.close()
            
            logger.info(f"✅ {symbol} 因子数据保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存因子数据失败 {symbol}: {e}")
            return False

def main():
    """主函数"""
    print("🔬 高级因子计算引擎启动")
    print("=" * 60)
    
    engine = AdvancedFactorEngine()
    
    # 获取股票列表
    conn = sqlite3.connect('data/enhanced_market_data.db')
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT symbol FROM daily_market_data LIMIT 5")
    symbols = [row[0] for row in cursor.fetchall()]
    conn.close()
    
    print(f"开始计算 {len(symbols)} 只股票的因子...")
    
    success_count = 0
    for i, symbol in enumerate(symbols):
        print(f"\n计算 {symbol} ({i+1}/{len(symbols)})")
        
        factors = engine.calculate_all_factors(symbol)
        if factors:
            saved = engine.save_factors_to_db(symbol, factors)
            if saved:
                success_count += 1
                
                # 显示部分关键因子
                key_factors = ['rsi_14', 'cci_14', 'atr_pct', 'overall_score']
                print("关键因子:")
                for factor in key_factors:
                    if factor in factors:
                        print(f"  {factor}: {factors[factor]:.4f}")
    
    print(f"\n🎉 因子计算完成!")
    print(f"✅ 成功: {success_count}/{len(symbols)}")
    
    return success_count, len(symbols)

if __name__ == '__main__':
    main()
