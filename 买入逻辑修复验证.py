#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
买入逻辑修复验证脚本
检查修复后的买入逻辑是否正常工作
"""

import sys
import os
import importlib.util

def check_imports():
    """检查关键模块和函数的导入"""
    print("🔍 检查关键导入...")
    
    try:
        # 检查gm.api导入
        from gm.api import order_volume, OrderSide_Buy, OrderType_Market, PositionEffect_Open
        print("✅ gm.api导入成功")
        
        # 检查数据管理器导入
        from scripts.data_manager import get_data_manager, save_trade
        print("✅ 数据管理器导入成功")
        
        # 检查主要模块导入
        import main
        print("✅ main模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_functions():
    """检查关键函数是否存在"""
    print("\n🔍 检查关键函数...")
    
    try:
        import main
        
        # 检查买入相关函数
        functions_to_check = [
            'buy_strategy',
            'buy_strategy_main_logic', 
            'analyze_single_symbol_simple',
            'execute_backup_buy_logic',
            'save_original_buy_record'
        ]
        
        for func_name in functions_to_check:
            if hasattr(main, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 函数检查失败: {e}")
        return False

def check_data_manager():
    """检查数据管理器是否正常工作"""
    print("\n🔍 检查数据管理器...")
    
    try:
        from scripts.data_manager import get_data_manager
        
        # 获取数据管理器实例
        dm = get_data_manager()
        print(f"✅ 数据管理器实例创建成功: {type(dm)}")
        
        # 检查save_trade方法
        if hasattr(dm, 'save_trade'):
            print("✅ save_trade方法存在")
        else:
            print("❌ save_trade方法不存在")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 数据管理器检查失败: {e}")
        return False

def check_buy_logic_flow():
    """检查买入逻辑流程"""
    print("\n🔍 检查买入逻辑流程...")
    
    try:
        import main
        
        # 检查关键配置函数
        if hasattr(main, 'get_config_value'):
            print("✅ get_config_value函数存在")
        else:
            print("❌ get_config_value函数不存在")
            return False
        
        # 检查TRIX相关函数
        trix_functions = [
            'daily_trix_prefilter',
            'calculate_trix_unified'
        ]
        
        for func_name in trix_functions:
            if hasattr(main, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"⚠️ {func_name} 函数不存在（可能在其他模块）")
        
        return True
    except Exception as e:
        print(f"❌ 买入逻辑流程检查失败: {e}")
        return False

def analyze_buy_record_function():
    """分析买入记录保存函数"""
    print("\n🔍 分析买入记录保存函数...")
    
    try:
        import main
        import inspect
        
        # 获取save_original_buy_record函数的源码
        if hasattr(main, 'save_original_buy_record'):
            func = getattr(main, 'save_original_buy_record')
            source = inspect.getsource(func)
            
            # 检查关键修复点
            if 'context.data_manager' in source:
                print("✅ 包含context.data_manager检查")
            else:
                print("❌ 缺少context.data_manager检查")
            
            if 'save_trade' in source:
                print("✅ 包含save_trade调用")
            else:
                print("❌ 缺少save_trade调用")
            
            if 'Exception' in source:
                print("✅ 包含异常处理")
            else:
                print("❌ 缺少异常处理")
                
            return True
        else:
            print("❌ save_original_buy_record函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 买入记录函数分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 买入逻辑修复验证开始")
    print("=" * 50)
    
    all_passed = True
    
    # 执行各项检查
    checks = [
        ("导入检查", check_imports),
        ("函数检查", check_functions), 
        ("数据管理器检查", check_data_manager),
        ("买入逻辑流程检查", check_buy_logic_flow),
        ("买入记录函数分析", analyze_buy_record_function)
    ]
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        if not check_func():
            all_passed = False
    
    # 总结
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过！买入逻辑修复验证成功")
        print("\n📋 修复总结:")
        print("✅ 增强了API函数可用性检查")
        print("✅ 修复了数据管理器引用问题")
        print("✅ 增强了错误处理和日志记录")
        print("✅ 优化了买入记录保存逻辑")
        print("✅ 增加了详细的调试信息")
    else:
        print("❌ 部分检查未通过，需要进一步修复")
    
    return all_passed

if __name__ == "__main__":
    main()
