# coding=utf-8
"""
增强选股过滤模块
用于提升买入股票质量，降低风险
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# 尝试导入talib，如果失败则使用替代方案
try:
    import talib
    HAS_TALIB = True
except ImportError:
    HAS_TALIB = False

def calculate_simple_atr(high, low, close, period=14):
    """简化的ATR计算（当talib不可用时）"""
    if len(high) < period + 1:
        return np.array([np.mean(high - low)])
    
    # 计算真实范围
    tr1 = high - low
    tr2 = np.abs(high - np.roll(close, 1))
    tr3 = np.abs(low - np.roll(close, 1))
    
    # 取最大值作为真实范围
    tr = np.maximum(tr1, np.maximum(tr2, tr3))
    
    # 计算ATR（简单移动平均）
    atr = pd.Series(tr).rolling(window=period).mean()
    return atr.fillna(tr[0]).values

def calculate_volatility(close_prices, period=20):
    """计算价格波动率"""
    if len(close_prices) < period:
        return 0.0
    
    returns = pd.Series(close_prices).pct_change().dropna()
    volatility = returns.rolling(window=period).std().iloc[-1]
    return volatility * np.sqrt(252) if not pd.isna(volatility) else 0.0

class EnhancedStockFilter:
    def __init__(self, context):
        self.context = context

    def get_config_value(self, key, default=None):
        """安全地获取配置值"""
        try:
            if hasattr(self.context, 'get_config_value'):
                return self.context.get_config_value(key, default)
            elif hasattr(self.context, 'config') and isinstance(self.context.config, dict):
                return self.context.config.get(key, default)
            else:
                return default
        except Exception:
            return default

    def validate_data(self, symbol, data):
        """验证数据格式和完整性"""
        try:
            if data is None:
                return False, "Data is None"

            if not isinstance(data, pd.DataFrame):
                return False, "Data is not DataFrame"

            if len(data) < 10:
                return False, f"Insufficient data: {len(data)} rows"

            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                return False, f"Missing columns: {missing_columns}"

            # 检查数据是否包含NaN或无效值
            if data[required_columns].isnull().any().any():
                return False, "Data contains NaN values"

            # 检查价格数据是否有效
            for col in ['open', 'high', 'low', 'close']:
                if (data[col] <= 0).any():
                    return False, f"Invalid {col} prices (<=0)"

            # 检查成交量数据是否有效
            if (data['volume'] < 0).any():
                return False, "Invalid volume data (<0)"

            return True, "Data validation passed"

        except Exception as e:
            return False, f"Data validation error: {str(e)}"
        
    def amplitude_filter(self, symbol, data):
        """振幅过滤"""
        try:
            # 验证数据
            is_valid, msg = self.validate_data(symbol, data)
            if not is_valid:
                return False, msg

            if not self.get_config_value('AMPLITUDE_FILTER_ENABLED', True):
                return True, "振幅过滤已禁用"

            if len(data) < 5:
                return False, "数据不足"

            # 获取配置参数
            amplitude_days = self.get_config_value('AMPLITUDE_DAYS', 5)
            min_amplitude_pct = self.get_config_value('MIN_AMPLITUDE_PCT', 2.0)
            max_amplitude_pct = self.get_config_value('MAX_AMPLITUDE_PCT', 9.5)
            
            # 计算最近几天的振幅
            recent_data = data.tail(amplitude_days)
            high_prices = recent_data['high'].values
            low_prices = recent_data['low'].values
            
            # 计算平均振幅
            amplitudes = (high_prices - low_prices) / low_prices * 100
            avg_amplitude = np.mean(amplitudes)
            
            # 检查振幅是否在合理范围内
            if avg_amplitude < min_amplitude_pct:
                return False, f"振幅过低: {avg_amplitude:.2f}% < {min_amplitude_pct}%"
            
            if avg_amplitude > max_amplitude_pct:
                return False, f"振幅过高: {avg_amplitude:.2f}% > {max_amplitude_pct}%"
            
            return True, f"振幅合适: {avg_amplitude:.2f}%"
            
        except Exception as e:
            return False, f"Amplitude filter error: {str(e)}"
    
    def ma_trend_filter(self, symbol, data):
        """均线趋势过滤"""
        try:
            # 验证数据
            is_valid, msg = self.validate_data(symbol, data)
            if not is_valid:
                return False, msg

            if not self.get_config_value('MA_FILTER_ENABLED', True):
                return True, "均线过滤已禁用"

            if len(data) < 20:
                return False, "数据不足"

            # 获取配置参数
            ma_period = self.get_config_value('MA_FILTER_PERIOD', 5)
            ma_direction = self.get_config_value('MA_FILTER_DIRECTION', 'up')
            ma_days = self.get_config_value('MA_FILTER_DAYS', 3)
            
            close_prices = data['close'].values
            current_price = close_prices[-1]
            
            # 计算均线
            if HAS_TALIB:
                ma = talib.SMA(close_prices, timeperiod=ma_period)
            else:
                ma = pd.Series(close_prices).rolling(window=ma_period).mean().values
            
            if len(ma) < ma_days + 1:
                return False, "均线数据不足"
            
            current_ma = ma[-1]
            
            # 检查价格是否在均线之上
            if current_price <= current_ma:
                return False, f"价格({current_price:.3f})低于{ma_period}日均线({current_ma:.3f})"
            
            # 检查均线方向
            if ma_direction == 'up':
                # 检查均线是否连续上涨
                ma_trend_up = all(ma[-i] > ma[-i-1] for i in range(1, min(ma_days + 1, len(ma))))
                if not ma_trend_up:
                    return False, f"{ma_period}日均线未连续{ma_days}天上涨"
            
            return True, f"均线趋势良好: 价格{current_price:.3f} > {ma_period}日均线{current_ma:.3f}"
            
        except Exception as e:
            return False, f"MA trend filter error: {str(e)}"
    
    def volatility_filter(self, symbol, data):
        """波动性过滤"""
        try:
            # 验证数据
            is_valid, msg = self.validate_data(symbol, data)
            if not is_valid:
                return False, msg

            if not self.get_config_value('ENABLE_VOLATILITY_BUY_FILTER', True):
                return True, "波动性过滤已禁用"

            if len(data) < 30:
                return False, "数据不足"

            # 获取配置参数
            min_volatility_ratio = self.get_config_value('MIN_BUY_VOLATILITY_RATIO', 0.6)
            max_volatility_ratio = self.get_config_value('MAX_BUY_VOLATILITY_RATIO', 2.0)
            volatility_days = self.get_config_value('VOLATILITY_CALCULATION_DAYS', 20)
            
            close_prices = data['close'].values
            
            # 计算股票波动率
            stock_volatility = calculate_volatility(close_prices, volatility_days)
            
            # 获取市场基准波动率（简化处理，使用配置值）
            market_volatility = self.get_config_value('DEFAULT_MARKET_VOLATILITY', 2.0)
            
            # 计算相对波动率
            relative_volatility = stock_volatility / market_volatility if market_volatility > 0 else 1.0
            
            # 检查波动率是否在合理范围内
            if relative_volatility < min_volatility_ratio:
                return False, f"波动率过低: {relative_volatility:.2f} < {min_volatility_ratio}"
            
            if relative_volatility > max_volatility_ratio:
                return False, f"波动率过高: {relative_volatility:.2f} > {max_volatility_ratio}"
            
            return True, f"波动率合适: {relative_volatility:.2f}"
            
        except Exception as e:
            return False, f"Volatility filter error: {str(e)}"
    
    def atr_filter(self, symbol, data):
        """ATR过滤"""
        try:
            # 验证数据
            is_valid, msg = self.validate_data(symbol, data)
            if not is_valid:
                return False, msg

            if not self.get_config_value('ENABLE_ATR_BUY_FILTER', True):
                return True, "ATR过滤已禁用"

            if len(data) < 20:
                return False, "数据不足"

            # 获取配置参数
            min_atr_percent = self.get_config_value('MIN_BUY_ATR_PERCENT', 1.5)
            max_atr_percent = self.get_config_value('MAX_BUY_ATR_PERCENT', 4.0)
            
            high_prices = data['high'].values
            low_prices = data['low'].values
            close_prices = data['close'].values
            current_price = close_prices[-1]
            
            # 计算ATR
            if HAS_TALIB:
                atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)
                current_atr = atr[-1]
            else:
                atr_values = calculate_simple_atr(high_prices, low_prices, close_prices, 14)
                current_atr = atr_values[-1]
            
            # 计算ATR占价格的百分比
            atr_percent = (current_atr / current_price) * 100
            
            # 检查ATR是否在合理范围内
            if atr_percent < min_atr_percent:
                return False, f"ATR过低: {atr_percent:.2f}% < {min_atr_percent}%"
            
            if atr_percent > max_atr_percent:
                return False, f"ATR过高: {atr_percent:.2f}% > {max_atr_percent}%"
            
            return True, f"ATR合适: {atr_percent:.2f}%"
            
        except Exception as e:
            return False, f"ATR filter error: {str(e)}"
    
    def technical_indicators_filter(self, symbol, data):
        """技术指标综合过滤"""
        try:
            if len(data) < 30:
                return False, "数据不足"
            
            close_prices = data['close'].values
            
            # 计算RSI
            if HAS_TALIB:
                rsi = talib.RSI(close_prices, timeperiod=14)
                current_rsi = rsi[-1]
            else:
                # 简化RSI计算
                delta = pd.Series(close_prices).diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi_series = 100 - (100 / (1 + rs))
                current_rsi = rsi_series.iloc[-1] if not pd.isna(rsi_series.iloc[-1]) else 50
            
            # RSI过滤：避免超买超卖
            if current_rsi < 30:
                return False, f"RSI过低(超卖): {current_rsi:.2f}"
            
            if current_rsi > 75:
                return False, f"RSI过高(超买): {current_rsi:.2f}"
            
            # 计算MACD
            if HAS_TALIB:
                macd, macd_signal, macd_hist = talib.MACD(close_prices)
                current_macd_hist = macd_hist[-1]
            else:
                # 简化MACD计算
                ema12 = pd.Series(close_prices).ewm(span=12).mean()
                ema26 = pd.Series(close_prices).ewm(span=26).mean()
                macd_line = ema12 - ema26
                macd_signal_line = macd_line.ewm(span=9).mean()
                macd_hist_series = macd_line - macd_signal_line
                current_macd_hist = macd_hist_series.iloc[-1] if not pd.isna(macd_hist_series.iloc[-1]) else 0
            
            # MACD过滤：要求MACD柱状图为正
            if current_macd_hist <= 0:
                return False, f"MACD柱状图为负: {current_macd_hist:.6f}"
            
            return True, f"技术指标良好: RSI={current_rsi:.2f}, MACD_HIST={current_macd_hist:.6f}"
            
        except Exception as e:
            return False, f"Technical indicators filter error: {str(e)}"
    
    def comprehensive_filter(self, symbol, data):
        """综合过滤检查"""
        try:
            filters = []
            reasons = []
            
            # 1. 振幅过滤
            amplitude_result, amplitude_reason = self.amplitude_filter(symbol, data)
            filters.append(amplitude_result)
            reasons.append(f"振幅: {amplitude_reason}")
            
            # 2. 均线趋势过滤
            ma_result, ma_reason = self.ma_trend_filter(symbol, data)
            filters.append(ma_result)
            reasons.append(f"均线: {ma_reason}")
            
            # 3. 波动性过滤
            volatility_result, volatility_reason = self.volatility_filter(symbol, data)
            filters.append(volatility_result)
            reasons.append(f"波动性: {volatility_reason}")
            
            # 4. ATR过滤
            atr_result, atr_reason = self.atr_filter(symbol, data)
            filters.append(atr_result)
            reasons.append(f"ATR: {atr_reason}")
            
            # 5. 技术指标过滤
            tech_result, tech_reason = self.technical_indicators_filter(symbol, data)
            filters.append(tech_result)
            reasons.append(f"技术指标: {tech_reason}")
            
            # 综合判断：所有过滤条件都必须通过
            all_passed = all(filters)
            
            return all_passed, reasons, {
                'amplitude': amplitude_result,
                'ma_trend': ma_result,
                'volatility': volatility_result,
                'atr': atr_result,
                'technical': tech_result
            }
            
        except Exception as e:
            return False, [f"Comprehensive filter error: {str(e)}"], {}

def create_enhanced_stock_filter(context):
    """创建增强选股过滤器实例"""
    return EnhancedStockFilter(context)
