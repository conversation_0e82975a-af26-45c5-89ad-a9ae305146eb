# 万和策略分析系统

这个目录包含了万和策略分析系统的所有组件，用于分析交易数据、优化交易策略并生成分析报告。

## 系统结构

- `analysis_manager.py` - 分析系统主入口
- `run_analysis.py` - 运行完整分析流程
- `analyze_trades.py` - 分析交易数据
- `optimize_strategy.py` - 优化交易策略
- `templates/` - 报告模板目录
  - `reports_guide_template.html` - 报告指南模板
  - `high_performance_template.html` - 高性能交易分析模板
- `start_analysis.bat` - 启动脚本（Windows）

## 依赖项

- Python 3.6+
- 必要的Python包：
  - pandas
  - numpy
  - matplotlib
  - seaborn
  - scikit-learn
  - jinja2

## 使用方法

### 快速启动（Windows）

1. 双击运行 `start_analysis.bat`
2. 按照提示选择要执行的操作

### 命令行使用

```bash
# 运行完整分析流程
python analysis_manager.py --all

# 仅运行交易分析
python analysis_manager.py --analyze

# 仅运行策略优化
python analysis_manager.py --optimize

# 仅生成HTML报告
python analysis_manager.py --html
```

## 输入文件

系统需要以下输入文件：

- `data/trade_log.csv` - 交易日志文件（必需）
- `data/analysis_log.csv` - 分析日志文件（可选）

这些文件应位于项目根目录下的`data`文件夹中。

## 输出文件

系统生成以下输出文件，全部存放在项目根目录下的`reports`文件夹中：

### 分析结果文件
- `reports/trade_analysis_results.csv` - 交易分析结果
- `reports/optimal_rules.csv` - 最优规则数据

### 策略优化文件
- `reports/optimal_strategy_rules.txt` - 优化策略规则
- `reports/optimal_strategy_model.pkl` - 训练好的策略模型

### 可视化图表
- `reports/feature_importance.png` - 特征重要性可视化
- `reports/buy_points_distribution.png` - 买入点分布图
- `reports/cumulative_profit_curve.png` - 累积收益曲线
- `reports/profit_distribution.png` - 收益分布图
- `reports/volatility_vs_profit.png` - 波动性与收益关系图
- `reports/trix_vs_profit.png` - TRIX指标与收益关系图
- `reports/holding_time_vs_profit.png` - 持仓时间与收益关系图

### HTML报告
- `reports/reports_guide.html` - 报告指南
- `reports/high_performance_trades.html` - 高性能交易模式分析

## 注意事项

1. 请确保在运行分析前已生成交易日志文件
2. 分析结果的可靠性依赖于交易样本的数量，样本越多结果越可靠
3. 如果遇到问题，请检查日志文件和错误信息
4. 建议定期重新运行分析，以适应市场变化 