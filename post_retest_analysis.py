# coding=utf-8
"""
重新回测后的深度数据挖掘
分析高效因子策略的实际效果，挖掘新的有效策略和因子
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

def analyze_latest_backtest_results():
    """分析最新回测结果"""
    print('🔍 重新回测后的数据分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp DESC 
        LIMIT 5000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 最新交易记录: {len(df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
        
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        print(f'   已完成交易: {len(completed_trades)} 条')
        
        # 分析时间分布
        latest_time = df['timestamp'].max()
        earliest_time = df['timestamp'].min()
        time_span = (latest_time - earliest_time).total_seconds() / 3600
        
        print(f'   时间范围: {earliest_time.strftime("%Y-%m-%d %H:%M")} 到 {latest_time.strftime("%Y-%m-%d %H:%M")}')
        print(f'   数据跨度: {time_span:.1f} 小时')
        
        return df, buy_records, sell_records, completed_trades
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None, None, None, None

def evaluate_strategy_performance(completed_trades):
    """评估策略表现"""
    print(f'\n📈 策略表现评估')
    print('=' * 50)
    
    if len(completed_trades) == 0:
        print('⚠️ 没有已完成的交易')
        return None
    
    # 基础统计
    total_trades = len(completed_trades)
    profitable_trades = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
    win_rate = profitable_trades / total_trades * 100
    
    avg_profit = completed_trades['net_profit_pct_sell'].mean()
    median_profit = completed_trades['net_profit_pct_sell'].median()
    std_profit = completed_trades['net_profit_pct_sell'].std()
    
    max_profit = completed_trades['net_profit_pct_sell'].max()
    max_loss = completed_trades['net_profit_pct_sell'].min()
    
    print(f'🎯 核心指标:')
    print(f'   总交易数: {total_trades}')
    print(f'   胜率: {win_rate:.2f}% ({profitable_trades}/{total_trades})')
    print(f'   平均收益: {avg_profit:.2f}%')
    print(f'   中位数收益: {median_profit:.2f}%')
    print(f'   收益标准差: {std_profit:.2f}%')
    print(f'   最大盈利: {max_profit:.2f}%')
    print(f'   最大亏损: {max_loss:.2f}%')
    
    # 盈亏比分析
    winning_trades = completed_trades[completed_trades['net_profit_pct_sell'] > 0]
    losing_trades = completed_trades[completed_trades['net_profit_pct_sell'] <= 0]
    
    if len(winning_trades) > 0 and len(losing_trades) > 0:
        avg_win = winning_trades['net_profit_pct_sell'].mean()
        avg_loss = abs(losing_trades['net_profit_pct_sell'].mean())
        profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf')
        
        print(f'\n💰 盈亏分析:')
        print(f'   平均盈利: {avg_win:.2f}%')
        print(f'   平均亏损: {avg_loss:.2f}%')
        print(f'   盈亏比: {profit_loss_ratio:.2f}')
    
    # 收益分布分析
    print(f'\n📊 收益分布:')
    profit_ranges = [
        ('大亏 (<-5%)', completed_trades['net_profit_pct_sell'] < -5),
        ('中亏 (-5% to -2%)', (completed_trades['net_profit_pct_sell'] >= -5) & (completed_trades['net_profit_pct_sell'] < -2)),
        ('小亏 (-2% to 0%)', (completed_trades['net_profit_pct_sell'] >= -2) & (completed_trades['net_profit_pct_sell'] < 0)),
        ('小盈 (0% to 2%)', (completed_trades['net_profit_pct_sell'] >= 0) & (completed_trades['net_profit_pct_sell'] < 2)),
        ('中盈 (2% to 5%)', (completed_trades['net_profit_pct_sell'] >= 2) & (completed_trades['net_profit_pct_sell'] < 5)),
        ('大盈 (>5%)', completed_trades['net_profit_pct_sell'] >= 5)
    ]
    
    for range_name, condition in profit_ranges:
        count = condition.sum()
        percentage = count / len(completed_trades) * 100
        print(f'   {range_name}: {count}笔 ({percentage:.1f}%)')
    
    # 卖出原因分析
    print(f'\n📋 卖出原因分析:')
    sell_reason_stats = completed_trades.groupby('sell_reason').agg({
        'net_profit_pct_sell': ['count', 'mean', lambda x: (x > 0).mean() * 100]
    }).round(2)
    sell_reason_stats.columns = ['交易数', '平均收益%', '胜率%']
    sell_reason_stats = sell_reason_stats.sort_values('胜率%', ascending=False)
    
    for reason, stats in sell_reason_stats.iterrows():
        count = stats['交易数']
        avg_profit = stats['平均收益%']
        win_rate_reason = stats['胜率%']
        percentage = count / total_trades * 100
        
        print(f'   {reason}: {count}笔 ({percentage:.1f}%), 胜率{win_rate_reason:.1f}%, 平均{avg_profit:.2f}%')
    
    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'profit_loss_ratio': profit_loss_ratio if 'profit_loss_ratio' in locals() else 0,
        'sell_reason_stats': sell_reason_stats
    }

def analyze_time_distribution_improvement(buy_records):
    """分析时间分布改善情况"""
    print(f'\n🕐 时间分布改善分析')
    print('=' * 50)
    
    if len(buy_records) == 0:
        print('⚠️ 没有买入记录')
        return
    
    # 按小时分析
    buy_records['hour'] = buy_records['timestamp'].dt.hour
    hourly_distribution = buy_records.groupby('hour').size()
    
    total_signals = len(buy_records)
    
    print(f'📊 按小时买入信号分布:')
    for hour in sorted(hourly_distribution.index):
        count = hourly_distribution[hour]
        percentage = count / total_signals * 100
        print(f'   {hour:02d}:00: {count}笔 ({percentage:.1f}%)')
    
    # 重点分析开盘时段
    opening_signals = buy_records[buy_records['hour'] == 9]
    opening_percentage = len(opening_signals) / total_signals * 100
    
    print(f'\n🎯 开盘时段(09:00)分析:')
    print(f'   买入信号: {len(opening_signals)}笔 ({opening_percentage:.1f}%)')
    
    if opening_percentage > 50:
        print(f'   ⚠️ 仍然存在开盘信号集中问题')
    elif opening_percentage > 30:
        print(f'   📊 开盘信号有所改善但仍偏高')
    else:
        print(f'   ✅ 开盘信号分布已显著改善')
    
    # 分析信号分散度
    distribution_entropy = -sum([(count/total_signals) * np.log2(count/total_signals) 
                                for count in hourly_distribution.values if count > 0])
    
    print(f'   信号分散度: {distribution_entropy:.2f} (越高越分散)')
    
    return opening_percentage, distribution_entropy

def deep_factor_mining_v2(df):
    """深度因子挖掘 V2.0"""
    print(f'\n🔬 深度因子挖掘 V2.0')
    print('=' * 50)
    
    # 获取买入-卖出匹配数据
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 改进的匹配查询
        query = """
        WITH ranked_sells AS (
            SELECT 
                s.*,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades s
            WHERE s.action = 'SELL' AND s.net_profit_pct_sell IS NOT NULL
        ),
        ranked_buys AS (
            SELECT 
                b.*,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank
            FROM trades b
            WHERE b.action = 'BUY'
        )
        SELECT 
            b.*,
            s.net_profit_pct_sell,
            s.sell_reason,
            s.holding_hours
        FROM ranked_buys b
        JOIN ranked_sells s ON b.symbol = s.symbol AND b.buy_rank = s.sell_rank
        ORDER BY b.timestamp DESC
        LIMIT 3000
        """
        
        matched_df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 成功匹配: {len(matched_df)} 条交易')
        
        if len(matched_df) == 0:
            print('⚠️ 没有匹配的交易数据')
            return None
        
        # 创建盈利标识
        matched_df['is_profitable'] = matched_df['net_profit_pct_sell'] > 0
        
        profitable_count = matched_df['is_profitable'].sum()
        win_rate = profitable_count / len(matched_df) * 100
        
        print(f'   盈利交易: {profitable_count}/{len(matched_df)} ({win_rate:.1f}%)')
        
        return matched_df
        
    except Exception as e:
        print(f'❌ 因子挖掘失败: {e}')
        return None

def advanced_factor_effectiveness_analysis(matched_df):
    """高级因子有效性分析"""
    print(f'\n🎯 高级因子有效性分析')
    print('=' * 50)
    
    # 排除非因子列
    exclude_columns = [
        'id', 'timestamp', 'symbol', 'action', 'price', 'volume', 'cost_price_sell',
        'confirmed_high_sell', 'confirmed_high_time', 'max_profit_pct', 'final_drawdown_pct',
        'status', 'sell_reason', 'net_profit_pct_sell', 'holding_hours', 'is_profitable'
    ]
    
    factor_columns = [col for col in matched_df.columns if col not in exclude_columns]
    
    print(f'📊 分析因子数量: {len(factor_columns)}个')
    
    factor_results = []
    
    for factor in factor_columns:
        try:
            # 获取有效数据
            valid_mask = matched_df[factor].notna() & matched_df['net_profit_pct_sell'].notna()
            factor_values = matched_df.loc[valid_mask, factor]
            profit_values = matched_df.loc[valid_mask, 'net_profit_pct_sell']
            
            if len(factor_values) < 100:  # 提高样本量要求
                continue
            
            # 检查因子变化
            if factor_values.nunique() < 5:
                continue
            
            # 计算相关系数
            try:
                ic, ic_pvalue = stats.pearsonr(factor_values, profit_values)
                spearman_ic, spearman_pvalue = stats.spearmanr(factor_values, profit_values)
            except:
                continue
            
            # 多分位数分析 (更精细)
            try:
                q90 = factor_values.quantile(0.9)
                q75 = factor_values.quantile(0.75)
                q50 = factor_values.quantile(0.5)
                q25 = factor_values.quantile(0.25)
                q10 = factor_values.quantile(0.1)
                
                # 分析极值组合
                top10_mask = valid_mask & (matched_df[factor] >= q90)
                bottom10_mask = valid_mask & (matched_df[factor] <= q10)
                
                top10_group = matched_df[top10_mask]
                bottom10_group = matched_df[bottom10_mask]
                
                if len(top10_group) < 20 or len(bottom10_group) < 20:
                    continue
                
                top10_win_rate = top10_group['is_profitable'].mean() * 100
                bottom10_win_rate = bottom10_group['is_profitable'].mean() * 100
                extreme_win_diff = top10_win_rate - bottom10_win_rate
                
                top10_avg_profit = top10_group['net_profit_pct_sell'].mean()
                bottom10_avg_profit = bottom10_group['net_profit_pct_sell'].mean()
                extreme_profit_diff = top10_avg_profit - bottom10_avg_profit
                
                # 稳定性分析 - 分段IC
                segment_ics = []
                segment_size = len(factor_values) // 5
                
                for i in range(5):
                    start_idx = i * segment_size
                    end_idx = (i + 1) * segment_size if i < 4 else len(factor_values)
                    
                    segment_factor = factor_values.iloc[start_idx:end_idx]
                    segment_profit = profit_values.iloc[start_idx:end_idx]
                    
                    if len(segment_factor) > 10:
                        seg_ic, _ = stats.spearmanr(segment_factor, segment_profit)
                        segment_ics.append(seg_ic)
                
                ic_stability = np.std(segment_ics) if len(segment_ics) > 0 else 999
                
                factor_results.append({
                    'factor': factor,
                    'ic': ic,
                    'ic_pvalue': ic_pvalue,
                    'spearman_ic': spearman_ic,
                    'spearman_pvalue': spearman_pvalue,
                    'ic_abs': abs(ic),
                    'spearman_abs': abs(spearman_ic),
                    'extreme_win_diff': extreme_win_diff,
                    'extreme_profit_diff': extreme_profit_diff,
                    'top10_win_rate': top10_win_rate,
                    'bottom10_win_rate': bottom10_win_rate,
                    'top10_avg_profit': top10_avg_profit,
                    'bottom10_avg_profit': bottom10_avg_profit,
                    'ic_stability': ic_stability,
                    'sample_size': len(factor_values),
                    'unique_values': factor_values.nunique()
                })
                
            except Exception as e:
                continue
                
        except Exception as e:
            continue
    
    print(f'✅ 成功分析: {len(factor_results)}个因子')
    
    # 按Spearman IC绝对值排序
    factor_results.sort(key=lambda x: x['spearman_abs'], reverse=True)
    
    return factor_results

def display_enhanced_factor_ranking(factor_results):
    """显示增强的因子排序"""
    print(f'\n🏆 增强因子排序 (前25名)')
    print('=' * 100)
    
    print(f'   排名  因子名称                      Spearman IC  极值胜率差  极值收益差  稳定性   显著性')
    print(f'   ' + '-' * 95)
    
    top_factors = []
    
    for i, result in enumerate(factor_results[:25], 1):
        ic_str = f"{result['spearman_ic']:+.4f}"
        win_diff_str = f"{result['extreme_win_diff']:+.1f}%"
        profit_diff_str = f"{result['extreme_profit_diff']:+.2f}%"
        stability_str = f"{result['ic_stability']:.3f}"
        
        # 显著性判断
        if result['spearman_pvalue'] < 0.001:
            significance = "🔥极显著"
        elif result['spearman_pvalue'] < 0.01:
            significance = "🚀显著"
        elif result['spearman_pvalue'] < 0.05:
            significance = "📊一般"
        else:
            significance = "🔹微弱"
        
        # 稳定性评级
        if result['ic_stability'] < 0.02:
            stability_rating = "🏆极稳定"
        elif result['ic_stability'] < 0.05:
            stability_rating = "✅稳定"
        elif result['ic_stability'] < 0.1:
            stability_rating = "📊一般"
        else:
            stability_rating = "⚠️不稳定"
        
        print(f'   {i:2d}.  {result["factor"]:<28} {ic_str:>10} {win_diff_str:>10} {profit_diff_str:>10} {stability_str:>8} {significance}')
        
        if i <= 15:
            top_factors.append(result)
    
    return top_factors

def identify_factor_combinations(factor_results):
    """识别因子组合"""
    print(f'\n🔗 因子组合分析')
    print('=' * 50)
    
    # 按类别分组最优因子
    factor_categories = {
        '技术指标': ['rsi', 'macd', 'macd_hist', 'adx', 'cci', 'williams_r', 'stoch_k', 'stoch_d'],
        '布林带': ['bb_width', 'bb_position', 'bb_upper_20', 'bb_middle_20', 'bb_lower_20'],
        '移动平均': ['ma5', 'ma10', 'ma20', 'ma60', 'ma5_distance_pct', 'ma10_distance_pct'],
        '波动率': ['atr_pct', 'volatility_3d', 'volatility_5d', 'volatility_10d'],
        '成交量': ['volume_ma5_ratio', 'volume_ma10_ratio', 'volume_change_pct', 'relative_volume'],
        '价格动量': ['price_momentum_3d', 'price_momentum_5d', 'price_change_pct'],
        '评分因子': ['overall_score', 'technical_score', 'momentum_score', 'volatility_score']
    }
    
    category_best = {}
    
    for category, factors in factor_categories.items():
        category_factors = [f for f in factor_results if f['factor'] in factors]
        
        if category_factors:
            # 选择该类别中最好的因子
            best_factor = max(category_factors, key=lambda x: x['spearman_abs'])
            category_best[category] = best_factor
    
    print(f'📊 各类别最优因子:')
    for category, best_factor in category_best.items():
        ic = best_factor['spearman_ic']
        win_diff = best_factor['extreme_win_diff']
        stability = best_factor['ic_stability']
        
        print(f'   {category}: {best_factor["factor"]} (IC={ic:+.4f}, 胜率差={win_diff:+.1f}%, 稳定性={stability:.3f})')
    
    return category_best

def generate_next_generation_strategy(top_factors, category_best):
    """生成下一代策略"""
    print(f'\n🚀 下一代策略生成')
    print('=' * 50)
    
    # 选择最优因子组合
    tier1_factors = [f for f in top_factors if f['spearman_abs'] > 0.05 and f['spearman_pvalue'] < 0.001 and f['ic_stability'] < 0.05]
    tier2_factors = [f for f in top_factors if 0.03 < f['spearman_abs'] <= 0.05 and f['spearman_pvalue'] < 0.01]
    
    print(f'🔥 超级因子 (IC>0.05, 极显著, 高稳定): {len(tier1_factors)}个')
    for factor in tier1_factors:
        print(f'   - {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差={factor["extreme_win_diff"]:+.1f}%')
    
    print(f'\n🚀 优质因子 (IC>0.03, 显著): {len(tier2_factors)}个')
    for factor in tier2_factors:
        print(f'   - {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差={factor["extreme_win_diff"]:+.1f}%')
    
    # 生成策略配置
    all_effective = tier1_factors + tier2_factors
    
    if len(all_effective) > 0:
        print(f'\n⚙️ 下一代策略配置:')
        
        total_weight = sum([abs(f['spearman_ic']) for f in all_effective])
        
        config_text = '''
# 下一代高效因子策略配置
NEXT_GEN_STRATEGY_CONFIG = {
    'enable': True,
    'version': '2.0',
    'factors': {'''
        
        for factor in all_effective[:12]:  # 取前12个最有效的
            weight = abs(factor['spearman_ic']) / total_weight
            direction = "positive" if factor['spearman_ic'] > 0 else "negative"
            
            config_text += f'''
        '{factor["factor"]}': {{
            'weight': {weight:.3f},
            'direction': '{direction}',
            'ic': {factor["spearman_ic"]:.4f},
            'stability': {factor["ic_stability"]:.3f},
            'extreme_win_diff': {factor["extreme_win_diff"]:.1f}
        }},'''
        
        config_text += '''
    },
    'selection_criteria': {
        'min_ic_threshold': 0.03,
        'max_stability_threshold': 0.1,
        'min_sample_size': 100,
        'require_significance': True
    }
}'''
        
        print(config_text)
    
    return tier1_factors, tier2_factors

def main():
    """主函数"""
    print('🚀 重新回测后的深度数据挖掘')
    print('=' * 60)
    
    # 分析最新回测结果
    df, buy_records, sell_records, completed_trades = analyze_latest_backtest_results()
    
    if df is not None:
        # 评估策略表现
        performance = evaluate_strategy_performance(completed_trades)
        
        # 分析时间分布改善
        if buy_records is not None:
            opening_pct, entropy = analyze_time_distribution_improvement(buy_records)
        
        # 深度因子挖掘
        matched_df = deep_factor_mining_v2(df)
        
        if matched_df is not None:
            # 高级因子有效性分析
            factor_results = advanced_factor_effectiveness_analysis(matched_df)
            
            if len(factor_results) > 0:
                # 显示增强因子排序
                top_factors = display_enhanced_factor_ranking(factor_results)
                
                # 识别因子组合
                category_best = identify_factor_combinations(factor_results)
                
                # 生成下一代策略
                tier1, tier2 = generate_next_generation_strategy(top_factors, category_best)
                
                print(f'\n🎯 挖掘总结')
                print('=' * 40)
                print(f'✅ 分析了{len(factor_results)}个因子')
                print(f'🔥 发现{len(tier1)}个超级因子')
                print(f'🚀 发现{len(tier2)}个优质因子')
                
                if performance:
                    print(f'📈 当前胜率: {performance["win_rate"]:.1f}%')
                    print(f'💰 当前盈亏比: {performance["profit_loss_ratio"]:.2f}')
                
                total_effective = len(tier1) + len(tier2)
                if total_effective > 0:
                    print(f'\n🚀 建议: 基于{total_effective}个有效因子升级策略!')
                else:
                    print(f'\n💡 建议: 继续优化现有策略配置')
            else:
                print('❌ 因子分析失败')
        else:
            print('❌ 因子挖掘失败')
    else:
        print('❌ 数据获取失败')

if __name__ == '__main__':
    main()
