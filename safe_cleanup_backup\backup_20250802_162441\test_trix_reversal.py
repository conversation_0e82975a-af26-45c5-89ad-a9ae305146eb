#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TRIX反转参数测试脚本
用于验证TRIX反转筛选的改进效果
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, timed<PERSON><PERSON>

def test_trix_calculation():
    """测试TRIX计算和反转判断"""
    
    print("🔍 TRIX反转参数测试")
    print("=" * 50)
    
    # 模拟股票价格数据（包含反转信号）
    close_prices = np.array([
        10.0, 10.1, 10.2, 10.1, 10.0,  # 下降趋势
        9.9,  9.8,  9.7,  9.6,  9.5,   # 继续下降
        9.4,  9.3,  9.2,  9.1,  9.0,   # 继续下降
        9.1,  9.2,  9.3,  9.4,  9.5,   # 开始反转
        9.6,  9.7,  9.8,  9.9,  10.0   # 反转确认
    ])
    
    print(f"📊 模拟价格数据长度: {len(close_prices)}")
    print(f"📈 价格变化: {close_prices[0]:.2f} -> {close_prices[-1]:.2f}")
    
    # 测试不同的TRIX参数
    test_configs = [
        {'period': 3, 'use_talib': False, 'threshold': 0.0001, 'name': '原配置'},
        {'period': 4, 'use_talib': True, 'threshold': 0.001, 'name': '新配置'},
        {'period': 5, 'use_talib': True, 'threshold': 0.001, 'name': '保守配置'},
    ]
    
    for config in test_configs:
        print(f"\n🧪 测试配置: {config['name']}")
        print(f"  TRIX周期: {config['period']}")
        print(f"  使用talib: {config['use_talib']}")
        print(f"  反转阈值: {config['threshold']}")
        
        # 计算TRIX
        if config['use_talib']:
            trix = talib.TRIX(close_prices, timeperiod=config['period'])
        else:
            # 自定义TRIX计算
            ema1 = talib.EMA(close_prices, timeperiod=config['period'])
            ema2 = talib.EMA(ema1, timeperiod=config['period'])
            ema3 = talib.EMA(ema2, timeperiod=config['period'])
            
            trix = np.zeros_like(close_prices)
            for i in range(1, len(ema3)):
                if ema3[i-1] != 0:
                    trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
        
        # 检查反转信号
        if len(trix) >= 3:
            current_trix = trix[-1]
            prev_trix = trix[-2]
            prev2_trix = trix[-3]
            
            # 反转判断
            trix_reversal_signal = current_trix > prev_trix + config['threshold']
            trix_trend_down = prev_trix <= prev2_trix + config['threshold']
            
            print(f"  📊 TRIX序列: 前日({prev2_trix:.6f}) -> 昨日({prev_trix:.6f}) -> 当日({current_trix:.6f})")
            print(f"  📈 下降趋势: {trix_trend_down} (昨日 <= 前日 + 阈值)")
            print(f"  🔄 反转信号: {trix_reversal_signal} (当日 > 昨日 + 阈值)")
            print(f"  ✅ 通过筛选: {trix_trend_down and trix_reversal_signal}")
        else:
            print(f"  ❌ TRIX数据不足: {len(trix)} < 3")

def test_real_market_data():
    """测试真实市场数据的TRIX反转"""
    
    print("\n📈 真实市场数据测试")
    print("=" * 50)
    
    # 模拟真实市场数据（包含多个反转点）
    market_data = {
        'SHSE.000001': [10.0, 10.1, 10.0, 9.9, 9.8, 9.7, 9.6, 9.5, 9.4, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8],
        'SHSE.000002': [20.0, 20.2, 20.1, 20.0, 19.9, 19.8, 19.7, 19.6, 19.5, 19.4, 19.5, 19.6, 19.7, 19.8, 19.9],
        'SHSE.000858': [15.0, 15.1, 15.0, 14.9, 14.8, 14.7, 14.6, 14.5, 14.4, 14.3, 14.4, 14.5, 14.6, 14.7, 14.8],
    }
    
    test_configs = [
        {'period': 3, 'use_talib': False, 'threshold': 0.0001},
        {'period': 4, 'use_talib': True, 'threshold': 0.001},
    ]
    
    for symbol, prices in market_data.items():
        print(f"\n📊 股票: {symbol}")
        print(f"价格变化: {prices[0]:.2f} -> {prices[-1]:.2f}")
        
        for config in test_configs:
            # 计算TRIX
            if config['use_talib']:
                trix = talib.TRIX(np.array(prices), timeperiod=config['period'])
            else:
                ema1 = talib.EMA(np.array(prices), timeperiod=config['period'])
                ema2 = talib.EMA(ema1, timeperiod=config['period'])
                ema3 = talib.EMA(ema2, timeperiod=config['period'])
                
                trix = np.zeros_like(prices)
                for i in range(1, len(ema3)):
                    if ema3[i-1] != 0:
                        trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
            
            if len(trix) >= 3:
                current_trix = trix[-1]
                prev_trix = trix[-2]
                prev2_trix = trix[-3]
                
                trix_reversal_signal = current_trix > prev_trix + config['threshold']
                trix_trend_down = prev_trix <= prev2_trix + config['threshold']
                
                print(f"  周期{config['period']}, talib{config['use_talib']}, 阈值{config['threshold']}:")
                print(f"    TRIX: {prev2_trix:.6f} -> {prev_trix:.6f} -> {current_trix:.6f}")
                print(f"    反转: {trix_reversal_signal}, 下降: {trix_trend_down}, 通过: {trix_trend_down and trix_reversal_signal}")

if __name__ == "__main__":
    test_trix_calculation()
    test_real_market_data()
    
    print("\n🎯 测试总结:")
    print("1. 新配置使用TRIX_EMA_PERIOD=4，减少假信号")
    print("2. 使用talib直接计算TRIX，确保计算准确性")
    print("3. 反转阈值调整为0.001，提高筛选精度")
    print("4. 预筛选条件更严格，必须同时满足下降趋势和反转信号") 