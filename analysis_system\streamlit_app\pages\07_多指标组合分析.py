import streamlit as st
import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from itertools import combinations

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置文件，用于获取买入指标参数
try:
    import config as strategy_config
except ImportError:
    strategy_config = None

# 导入工具函数
from utils.data_loader import load_analysis_results

# 检查必要的依赖项
missing_deps = []
try:
    import plotly.express as px
except ImportError:
    missing_deps.append("plotly")

try:
    import pandas as pd
except ImportError:
    missing_deps.append("pandas")

try:
    import numpy as np
except ImportError:
    missing_deps.append("numpy")

st.set_page_config(page_title="多指标组合分析", page_icon="🔍", layout="wide")

st.title("多指标组合分析")
st.markdown("分析多个技术指标在不同范围内的交易表现，帮助您找出最优和最差的指标组合。")

# 如果缺少依赖项，显示安装指南
if missing_deps:
    st.error(f"缺少必要的依赖项: {', '.join(missing_deps)}")
    st.markdown("""
    请运行以下命令安装缺失的依赖项:
    ```
    pip install {0}
    ```
    """.format(' '.join(missing_deps)))
    st.stop()

# 检查分析结果是否存在
analysis_file = "reports/trade_analysis_results.csv"
if not os.path.exists(analysis_file):
    st.warning("未找到分析结果文件。请先在主页运行交易分析。")
    st.stop()

# 加载分析结果
try:
    df = load_analysis_results()
    if df is None:
        st.error("加载分析结果失败")
        st.stop()
    st.success(f"成功加载分析数据: {len(df)}条交易记录")
except Exception as e:
    st.error(f"加载分析数据失败: {str(e)}")
    st.stop()

# 确保有Actual_Profit_Pct列
if 'Actual_Profit_Pct' not in df.columns:
    # 计算买入金额和卖出金额以及实际收益率
    df['Buy_Amount'] = df['Buy_Price'] * df['Volume']
    df['Sell_Amount'] = df['Sell_Price'] * df['Volume']
    df['Profit_Amount'] = df['Sell_Amount'] - df['Buy_Amount']
    df['Actual_Profit_Pct'] = (df['Profit_Amount'] / df['Buy_Amount'] * 100)

# 为特征名称创建中文映射字典
feature_names_cn = {
    # 基础指标
    'Actual_Profit_Pct': '收益率',
    'Holding_Hours': '持仓时间',
    'Holding_Days': '持仓天数',
    'Holding_Periods': '持仓周期',
    
    # 价格和成交量基础指标
    'Open': '开盘价',
    'High': '最高价',
    'Low': '最低价',
    'Close': '收盘价',
    'Volume': '成交量',
    'Adj_Close': '调整收盘价',
    'Price_Change_Pct': '价格变化率',
    'Volume_Change_Pct': '成交量变化率',
    'Volume_Ratio': '成交量比率',
    
    # 波动性指标
    'ATR': '平均真实波幅',
    'ATR_Pct': 'ATR百分比',
    'Volatility': '波动率',
    'Volatility_Score': '波动分数',
    'NATR': '归一化ATR',
    'True_Range': '真实波幅',
    'Bollinger_Width': '布林带宽度',
    'BOLL_Width': '布林带宽度',
    'BOLL_Upper': '布林上轨',
    'BOLL_Middle': '布林中轨',
    'BOLL_Lower': '布林下轨',
    'Keltner_Upper': 'Keltner通道上轨',
    'Keltner_Middle': 'Keltner通道中轨',
    'Keltner_Lower': 'Keltner通道下轨',
    
    # 移动平均线
    'MA3': '3日均线',
    'MA5': '5日均线',
    'MA7': '7日均线',
    'MA10': '10日均线',
    'MA20': '20日均线',
    'MA30': '30日均线',
    'MA60': '60日均线',
    'MA120': '120日均线',
    'MA200': '200日均线',
    'EMA5': '5日指数均线',
    'EMA10': '10日指数均线',
    'EMA20': '20日指数均线',
    'EMA30': '30日指数均线',
    'EMA60': '60日指数均线',
    'WMA5': '5日加权均线',
    'WMA10': '10日加权均线',
    'WMA20': '20日加权均线',
    'HMA10': '10日赫尔均线',
    'HMA20': '20日赫尔均线',
    
    # 动量指标
    'RSI': 'RSI指标',
    'MACD': 'MACD指标',
    'MACD_Signal': 'MACD信号线',
    'MACD_Hist': 'MACD柱状图',
    'KDJ_K': 'KDJ_K值',
    'KDJ_D': 'KDJ_D值',
    'KDJ_J': 'KDJ_J值',
    'Stoch_K': '随机指标K值',
    'Stoch_D': '随机指标D值',
    'CCI': '顺势指标',
    'ROC': '变动率指标',
    'Momentum': '动量指标',
    'Williams_R': '威廉指标',
    'WR': '威廉指标',
    'TSI': '真实强度指数',
    'UO': '终极震荡指标',
    'AO': '动量震荡指标',
    'PPO': '百分比价格震荡指标',
    'PVO': '百分比成交量震荡指标',
    
    # 趋势指标
    'ADX': 'ADX指标',
    'DMI_Plus': 'DMI+指标',
    'DMI_Minus': 'DMI-指标',
    'Trend_Strength': '趋势强度',
    'Aroon_Up': 'Aroon上升指标',
    'Aroon_Down': 'Aroon下降指标',
    'Aroon_Oscillator': 'Aroon震荡指标',
    'Ichimoku_A': '一目均衡图A',
    'Ichimoku_B': '一目均衡图B',
    'Ichimoku_Base': '一目均衡图基准线',
    'Ichimoku_Conversion': '一目均衡图转换线',
    'PSAR': '抛物线SAR',
    'Supertrend': '超级趋势',
    
    # 成交量指标
    'OBV': '能量潮指标',
    'MFI': '资金流量指标',
    'ADL': '累积/派发线',
    'CMF': '蔡金货币流量',
    'Force_Index': '强力指数',
    'EMV': '简易波动指标',
    'VPT': '成交量价格趋势',
    'NVI': '负成交量指标',
    'PVI': '正成交量指标',
    
    # 振荡器
    'Stochastic_RSI': '随机RSI',
    'TRIX': '三重指数平滑移动平均线',
    'APO': '绝对价格震荡器',
    'CMO': '钱德动量震荡器',
    'MACD_Histogram': 'MACD直方图',
    
    # 其他指标
    'PSY': '心理线指标',
    'VR': '成交量比率',
    'CR': '能量指标',
    'BR': '买卖意愿指标',
    'AR': '人气指标',
    'BIAS': '乖离率',
    'DPO': '区间震荡线',
    'KST': '了解顺势指标',
    'Ichimoku': '一目均衡图',
    'Parabolic_SAR': '抛物线转向系统',
    'Coppock_Curve': '科普克曲线',
    'Chande_Momentum_Oscillator': '钱德动量震荡器',
    
    # 资金管理指标
    'Allocation_Factor': '资金分配系数',
    'Risk_Ratio': '风险比率',
    'Kelly_Criterion': '凯利准则',
    
    # 收益相关指标
    'Annualized_Return': '年化收益率',
    'Sharpe_Ratio': '夏普比率',
    'Sortino_Ratio': '索提诺比率',
    'Max_Drawdown': '最大回撤',
    'Win_Rate': '胜率',
    'Profit_Factor': '盈亏比',
    'Expectancy': '期望值',
    
    # 买入时指标（已有部分）
    'TRIX_Buy': 'TRIX指标',
    'Volatility_Buy': '波动率',
    'ATR_Pct_Buy': '买入时ATR',
    'Volatility_Score_Buy': '波动分数',
    'Allocation_Factor_Buy': '资金分配系数',
    'RSI_Buy': 'RSI指标',
    'MACD_Buy': 'MACD指标',
    'MACD_Signal_Buy': 'MACD信号线',
    'MACD_Hist_Buy': 'MACD柱状图',
    'KDJ_K_Buy': 'KDJ_K值',
    'KDJ_D_Buy': 'KDJ_D值',
    'KDJ_J_Buy': 'KDJ_J值',
    'BOLL_Upper_Buy': '布林上轨',
    'BOLL_Middle_Buy': '布林中轨',
    'BOLL_Lower_Buy': '布林下轨',
    'BOLL_Width_Buy': '布林带宽度',
    'MA3_Buy': '3日均线',
    'MA7_Buy': '7日均线',
    'MA20_Buy': '20日均线',
    'Volume_Ratio_Buy': '成交量比率',
    'Trend_Strength_Buy': '趋势强度',
    'Price_Change_Pct_Buy': '价格变化率',
    'OBV_Buy': '能量潮指标',
    'DMI_Plus_Buy': 'DMI+指标',
    'DMI_Minus_Buy': 'DMI-指标',
    'ADX_Buy': 'ADX指标',
    'CCI_Buy': '顺势指标',
    'WR_Buy': '威廉指标',
    'ROC_Buy': '变动率指标',
    'Momentum_Buy': '动量指标',
    'EMV_Buy': '简易波动指标',
    'PSY_Buy': '心理线指标'
}

# 添加指标说明字典
indicator_descriptions = {
    # 价格趋势指标
    'TRIX_Buy': '三重指数平滑移动平均线，是一种中长期技术分析工具，可以滤除价格波动中的噪音，识别主要趋势。',
    'TRIX': '三重指数平滑移动平均线，是一种中长期技术分析工具，可以滤除价格波动中的噪音，识别主要趋势。',
    'Trend_Strength_Buy': '趋势强度指标，衡量价格趋势的强弱程度，高值表示强趋势。',
    'Trend_Strength': '趋势强度指标，衡量价格趋势的强弱程度，高值表示强趋势。',
    'Price_Change_Pct_Buy': '价格变化率，衡量近期价格变动的百分比，用于判断短期动量。',
    'Price_Change_Pct': '价格变化率，衡量近期价格变动的百分比，用于判断短期动量。',
    'MA3_Buy': '3日简单移动平均线，反映短期价格趋势。',
    'MA5_Buy': '5日简单移动平均线，反映短期价格趋势。',
    'MA7_Buy': '7日简单移动平均线，反映中短期价格趋势。',
    'MA10_Buy': '10日简单移动平均线，反映中短期价格趋势。',
    'MA20_Buy': '20日简单移动平均线，反映中期价格趋势。',
    'MA30_Buy': '30日简单移动平均线，反映中长期价格趋势。',
    'MA60_Buy': '60日简单移动平均线，反映长期价格趋势。',
    'MA3': '3日简单移动平均线，反映短期价格趋势。',
    'MA5': '5日简单移动平均线，反映短期价格趋势。',
    'MA7': '7日简单移动平均线，反映中短期价格趋势。',
    'MA10': '10日简单移动平均线，反映中短期价格趋势。',
    'MA20': '20日简单移动平均线，反映中期价格趋势。',
    'MA30': '30日简单移动平均线，反映中长期价格趋势。',
    'MA60': '60日简单移动平均线，反映长期价格趋势。',
    'MA120': '120日简单移动平均线，反映长期价格趋势。',
    'MA200': '200日简单移动平均线，反映超长期价格趋势。',
    'EMA5_Buy': '5日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA10_Buy': '10日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA20_Buy': '20日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA30_Buy': '30日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA5': '5日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA10': '10日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA20': '20日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    'EMA30': '30日指数移动平均线，对近期价格赋予更高权重，反应更敏感。',
    
    # 波动性指标
    'Volatility_Buy': '波动率指标衡量价格波动的剧烈程度，高波动率表示价格变化大，低波动率表示价格相对稳定。',
    'Volatility': '波动率指标衡量价格波动的剧烈程度，高波动率表示价格变化大，低波动率表示价格相对稳定。',
    'ATR_Buy': '平均真实波幅，衡量市场波动性的指标，常用于设置止损点和判断市场波动状态。',
    'ATR': '平均真实波幅，衡量市场波动性的指标，常用于设置止损点和判断市场波动状态。',
    'ATR_Pct_Buy': '平均真实波幅百分比，衡量价格波动的幅度相对于价格的比例，常用于设置止损点。',
    'ATR_Pct': '平均真实波幅百分比，衡量价格波动的幅度相对于价格的比例，常用于设置止损点。',
    'Volatility_Score_Buy': '综合波动性评分，结合了波动率和ATR等因素，用于评估股票的整体波动特性。',
    'Volatility_Score': '综合波动性评分，结合了波动率和ATR等因素，用于评估股票的整体波动特性。',
    'True_Range_Buy': '真实波幅，衡量当前交易日的最高价与最低价之间的波动范围，包含了跳空缺口。',
    'True_Range': '真实波幅，衡量当前交易日的最高价与最低价之间的波动范围，包含了跳空缺口。',
    'NATR_Buy': '归一化平均真实波幅，将ATR除以收盘价，便于不同价格水平的股票进行比较。',
    'NATR': '归一化平均真实波幅，将ATR除以收盘价，便于不同价格水平的股票进行比较。',
    
    # 布林带指标
    'BOLL_Upper_Buy': '布林带上轨，通常是中轨加上两倍标准差，价格接近上轨可能表示超买。',
    'BOLL_Middle_Buy': '布林带中轨，通常是20日移动平均线，代表价格的中期趋势。',
    'BOLL_Lower_Buy': '布林带下轨，通常是中轨减去两倍标准差，价格接近下轨可能表示超卖。',
    'BOLL_Width_Buy': '布林带宽度，上轨与下轨之间的距离，反映市场波动性。',
    'BOLL_Upper': '布林带上轨，通常是中轨加上两倍标准差，价格接近上轨可能表示超买。',
    'BOLL_Middle': '布林带中轨，通常是20日移动平均线，代表价格的中期趋势。',
    'BOLL_Lower': '布林带下轨，通常是中轨减去两倍标准差，价格接近下轨可能表示超卖。',
    'BOLL_Width': '布林带宽度，上轨与下轨之间的距离，反映市场波动性。',
    'Bollinger_Width_Buy': '布林带宽度，衡量市场波动性，宽度扩大可能预示行情变化。',
    'Bollinger_Width': '布林带宽度，衡量市场波动性，宽度扩大可能预示行情变化。',
    
    # 动量指标
    'RSI_Buy': '相对强弱指标，衡量价格变动的强度，通常用于判断市场是否超买或超卖。',
    'RSI': '相对强弱指标，衡量价格变动的强度，通常用于判断市场是否超买或超卖。',
    'CCI_Buy': '顺势指标，用于判断价格是否偏离其统计学平均水平，常用于识别超买超卖。',
    'CCI': '顺势指标，用于判断价格是否偏离其统计学平均水平，常用于识别超买超卖。',
    'WR_Buy': '威廉指标，衡量收盘价与最高价和最低价的关系，用于判断超买超卖。',
    'WR': '威廉指标，衡量收盘价与最高价和最低价的关系，用于判断超买超卖。',
    'ROC_Buy': '变动率指标，衡量当前价格与N日前价格的变化率，用于判断价格动量。',
    'ROC': '变动率指标，衡量当前价格与N日前价格的变化率，用于判断价格动量。',
    'Momentum_Buy': '动量指标，衡量价格变化速度，用于判断趋势强度和可能的转折点。',
    'Momentum': '动量指标，衡量价格变化速度，用于判断趋势强度和可能的转折点。',
    'Stoch_K_Buy': '随机指标K值，反映收盘价在最近N个交易日的最高价和最低价之间的位置。',
    'Stoch_K': '随机指标K值，反映收盘价在最近N个交易日的最高价和最低价之间的位置。',
    'Stoch_D_Buy': '随机指标D值，是K值的移动平均，变化较K值平缓，用于确认信号。',
    'Stoch_D': '随机指标D值，是K值的移动平均，变化较K值平缓，用于确认信号。',
    
    # 震荡指标
    'MACD_Buy': '平滑异同移动平均线，是一种趋势跟踪指标，用于判断价格趋势的强度和方向变化。',
    'MACD_Signal_Buy': 'MACD的信号线，通常是MACD的9日EMA，用于产生买卖信号。',
    'MACD_Hist_Buy': 'MACD柱状图，表示MACD线与信号线之间的差距，用于判断趋势强度。',
    'MACD': '平滑异同移动平均线，是一种趋势跟踪指标，用于判断价格趋势的强度和方向变化。',
    'MACD_Signal': 'MACD的信号线，通常是MACD的9日EMA，用于产生买卖信号。',
    'MACD_Hist': 'MACD柱状图，表示MACD线与信号线之间的差距，用于判断趋势强度。',
    'KDJ_K_Buy': 'KDJ指标中的K值，反映价格波动的速率，常用于判断超买超卖。',
    'KDJ_D_Buy': 'KDJ指标中的D值，是K值的移动平均，变化较K值平缓，用于确认信号。',
    'KDJ_J_Buy': 'KDJ指标中的J值，反映K值与D值的差距，用于判断趋势的强弱和可能的反转点。',
    'KDJ_K': 'KDJ指标中的K值，反映价格波动的速率，常用于判断超买超卖。',
    'KDJ_D': 'KDJ指标中的D值，是K值的移动平均，变化较K值平缓，用于确认信号。',
    'KDJ_J': 'KDJ指标中的J值，反映K值与D值的差距，用于判断趋势的强弱和可能的反转点。',
    
    # 趋势指标
    'DMI_Plus_Buy': '趋向指标中的+DI，表示上升趋势的强度。',
    'DMI_Minus_Buy': '趋向指标中的-DI，表示下降趋势的强度。',
    'ADX_Buy': '平均趋向指标，衡量趋势的强度，不考虑趋势方向。',
    'DMI_Plus': '趋向指标中的+DI，表示上升趋势的强度。',
    'DMI_Minus': '趋向指标中的-DI，表示下降趋势的强度。',
    'ADX': '平均趋向指标，衡量趋势的强度，不考虑趋势方向。',
    
    # 成交量指标
    'Volume_Ratio_Buy': '成交量比率，当前成交量与过去平均成交量的比值，用于判断成交量异常。',
    'OBV_Buy': '能量潮指标，结合价格和成交量分析市场动量，用于确认价格趋势。',
    'EMV_Buy': '简易波动指标，结合价格和成交量分析市场力量对价格的推动作用。',
    'Volume_Ratio': '成交量比率，当前成交量与过去平均成交量的比值，用于判断成交量异常。',
    'OBV': '能量潮指标，结合价格和成交量分析市场动量，用于确认价格趋势。',
    'EMV': '简易波动指标，结合价格和成交量分析市场力量对价格的推动作用。',
    
    # 其他技术指标
    'PSY_Buy': '心理线指标，衡量一段时间内上涨天数的比例，用于判断市场情绪。',
    'PSY': '心理线指标，衡量一段时间内上涨天数的比例，用于判断市场情绪。',
    
    # 资金管理指标
    'Allocation_Factor_Buy': '资金分配系数，根据波动性等因素决定投入资金的比例，高波动性股票通常分配较少资金。',
    'Allocation_Factor': '资金分配系数，根据波动性等因素决定投入资金的比例，高波动性股票通常分配较少资金。',
    'Risk_Ratio_Buy': '风险比率，衡量潜在收益与潜在风险的比例，用于资金管理决策。',
    'Risk_Ratio': '风险比率，衡量潜在收益与潜在风险的比例，用于资金管理决策。',
    
    # 持仓相关指标
    'Holding_Hours': '持仓时间（小时），交易持仓的总小时数，反映交易的持续时间。',
    'Holding_Days': '持仓天数，交易持仓的总天数，反映交易的持续时间。',
    'Holding_Periods': '持仓周期，交易持仓的周期数，反映交易的持续时间。',
    
    # 收益指标
    'Actual_Profit_Pct': '实际收益率，交易产生的实际百分比收益。',
    'Max_Drawdown': '最大回撤，交易过程中最大的亏损百分比。',
    'Win_Rate': '胜率，盈利交易占总交易的百分比。',
    'Profit_Factor': '盈亏比，总盈利除以总亏损的比率。',
    
    # 卖出时指标
    'RSI_Sell': '卖出时的相对强弱指标，用于判断卖出时机是否处于超买或超卖状态。',
    'MACD_Sell': '卖出时的MACD指标，用于判断卖出时机的趋势强度和方向。',
    'Volume_Ratio_Sell': '卖出时的成交量比率，用于判断卖出时的成交量是否异常。',
    
    # 新增指标
    'Final_Drawdown_Pct': '最终回撤百分比，交易结束时的回撤水平，反映交易结束时的风险状态。',
    'Max_Profit_Pct': '最大收益百分比，交易过程中达到的最高收益水平。',
    'Profit_Pct': '收益百分比，交易产生的收益占投入资金的百分比。',
    'Sell_Reason': '卖出原因，记录交易卖出的具体原因或触发条件。',
    'Trade_Result': '交易结果，记录交易的最终结果（盈利/亏损）及相关信息。'
}

# 查找可用的技术指标
available_indicators = [col for col in df.columns if 
                       col not in ['Symbol', 'Buy_Time', 'Sell_Time', 'Buy_Price', 'Sell_Price', 
                                  'Volume', 'Buy_Amount', 'Sell_Amount', 'Profit_Amount', 
                                  'Strategy', 'Trade_ID', 'Combination_ID'] and
                       not col.startswith('Indicator_') and
                       not col.endswith('_Range')]

# 为了确保我们不会漏掉任何指标，我们也会检查一些常见的技术指标名称
common_indicators = [
    # 价格和成交量指标
    'Open', 'High', 'Low', 'Close', 'Volume', 'Adj_Close',
    'Price_Change_Pct', 'Volume_Change_Pct', 'Volume_Ratio',
    
    # 移动平均线
    'MA3', 'MA5', 'MA7', 'MA10', 'MA20', 'MA30', 'MA60', 'MA120', 'MA200',
    'EMA5', 'EMA10', 'EMA20', 'EMA30', 'EMA60',
    'WMA5', 'WMA10', 'WMA20',
    'HMA10', 'HMA20',
    
    # 波动性指标
    'ATR', 'ATR_Pct', 'Volatility', 'Volatility_Score', 'NATR', 'True_Range',
    'Bollinger_Width', 'BOLL_Width', 'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower',
    'Keltner_Upper', 'Keltner_Middle', 'Keltner_Lower',
    
    # 动量指标
    'RSI', 'MACD', 'MACD_Signal', 'MACD_Hist',
    'KDJ_K', 'KDJ_D', 'KDJ_J',
    'Stoch_K', 'Stoch_D',
    'CCI', 'ROC', 'Momentum', 'Williams_R', 'WR',
    'TSI', 'UO', 'AO', 'PPO', 'PVO',
    
    # 趋势指标
    'ADX', 'DMI_Plus', 'DMI_Minus', 'Trend_Strength',
    'Aroon_Up', 'Aroon_Down', 'Aroon_Oscillator',
    'Ichimoku_A', 'Ichimoku_B', 'Ichimoku_Base', 'Ichimoku_Conversion',
    'PSAR', 'Supertrend',
    
    # 成交量指标
    'OBV', 'MFI', 'ADL', 'CMF', 'Force_Index', 'EMV', 'VPT', 'NVI', 'PVI',
    
    # 振荡器
    'Stochastic_RSI', 'TRIX', 'APO', 'CMO', 'MACD_Histogram',
    
    # 其他指标
    'PSY', 'VR', 'CR', 'BR', 'AR', 'BIAS', 'DPO', 'KST', 'Ichimoku',
    'Parabolic_SAR', 'Coppock_Curve', 'Chande_Momentum_Oscillator',
    
    # 资金管理指标
    'Allocation_Factor', 'Risk_Ratio', 'Kelly_Criterion',
    
    # 买入/卖出时的指标（带后缀）
    'RSI_Buy', 'MACD_Buy', 'MACD_Signal_Buy', 'MACD_Hist_Buy',
    'KDJ_K_Buy', 'KDJ_D_Buy', 'KDJ_J_Buy',
    'BOLL_Upper_Buy', 'BOLL_Middle_Buy', 'BOLL_Lower_Buy', 'BOLL_Width_Buy',
    'MA3_Buy', 'MA5_Buy', 'MA7_Buy', 'MA10_Buy', 'MA20_Buy', 'MA30_Buy', 'MA60_Buy',
    'EMA5_Buy', 'EMA10_Buy', 'EMA20_Buy', 'EMA30_Buy',
    'Volume_Ratio_Buy', 'Trend_Strength_Buy', 'Price_Change_Pct_Buy',
    'OBV_Buy', 'DMI_Plus_Buy', 'DMI_Minus_Buy', 'ADX_Buy',
    'CCI_Buy', 'WR_Buy', 'ROC_Buy', 'Momentum_Buy', 'EMV_Buy', 'PSY_Buy',
    'Volatility_Buy', 'ATR_Pct_Buy', 'Volatility_Score_Buy', 'TRIX_Buy',
    'Allocation_Factor_Buy',
    
    # 卖出时的指标
    'RSI_Sell', 'MACD_Sell', 'KDJ_K_Sell', 'KDJ_D_Sell', 'KDJ_J_Sell',
    'BOLL_Upper_Sell', 'BOLL_Middle_Sell', 'BOLL_Lower_Sell',
    'MA5_Sell', 'MA10_Sell', 'MA20_Sell', 'MA30_Sell',
    'EMA5_Sell', 'EMA10_Sell', 'EMA20_Sell',
    'Volume_Ratio_Sell', 'Trend_Strength_Sell',
    
    # 持仓相关指标
    'Holding_Hours', 'Holding_Days', 'Holding_Periods',
    
    # 收益相关指标
    'Annualized_Return', 'Sharpe_Ratio', 'Sortino_Ratio',
    'Max_Drawdown', 'Win_Rate', 'Profit_Factor', 'Expectancy'
]

# 将常见指标添加到available_indicators中（如果它们存在于DataFrame中）
for indicator in common_indicators:
    if indicator in df.columns and indicator not in available_indicators:
        available_indicators.append(indicator)

# 确保没有重复
available_indicators = list(set(available_indicators))

# 按字母顺序排序，方便查找
available_indicators.sort()

if not available_indicators:
    st.warning("未找到可用的技术指标。请确保数据中包含技术指标信息。")
    st.stop()

# 为其他可能的特征添加默认中文名称
for col in available_indicators:
    if col not in feature_names_cn:
        if col.endswith('_Buy'):
            base_name = col[:-4]  # 移除 _Buy 后缀
            feature_names_cn[col] = f'买入时{base_name}'
        elif col.endswith('_Sell'):
            base_name = col[:-5]  # 移除 _Sell 后缀
            feature_names_cn[col] = f'卖出时{base_name}'
        else:
            feature_names_cn[col] = col

# 初始化选定的指标列表
if 'selected_indicators' not in st.session_state:
    st.session_state.selected_indicators = []

# 使用session_state中的selected_indicators
selected_indicators = st.session_state.selected_indicators

# 用户选择要分析的指标
st.header("指标选择")
st.markdown("请选择2-5个要组合分析的技术指标。系统将分析这些指标在不同范围内的组合表现。")

# 从配置文件中获取买入指标信息并显示
strategy_indicators = {}
if strategy_config:
    # 检查TRIX指标
    if hasattr(strategy_config, 'ENABLE_TRIX_BUY_SIGNAL') and strategy_config.ENABLE_TRIX_BUY_SIGNAL:
        strategy_indicators['TRIX'] = f'启用 (TRIX买入信号)'
        if hasattr(strategy_config, 'TRIX_EMA_PERIOD'):
            strategy_indicators['TRIX'] += f', 周期={strategy_config.TRIX_EMA_PERIOD}'
        strategy_indicators['TRIX_SIGNAL'] = '启用 (TRIX信号线)'
        strategy_indicators['TRIX_HIST'] = '启用 (TRIX柱状图)'
    
    # 检查均线交叉指标
    if hasattr(strategy_config, 'ENABLE_MA_CROSS_BUY_SIGNAL') and strategy_config.ENABLE_MA_CROSS_BUY_SIGNAL:
        ma_info = '启用 (均线交叉买入信号)'
        if hasattr(strategy_config, 'MA_SHORT_PERIOD') and hasattr(strategy_config, 'MA_MID_PERIOD') and hasattr(strategy_config, 'MA_LONG_PERIOD'):
            ma_info += f', 周期={strategy_config.MA_SHORT_PERIOD}/{strategy_config.MA_MID_PERIOD}/{strategy_config.MA_LONG_PERIOD}'
        strategy_indicators['MA_Cross'] = ma_info
        strategy_indicators[f'MA{strategy_config.MA_SHORT_PERIOD}'] = f'短期均线 (周期={strategy_config.MA_SHORT_PERIOD})'
        strategy_indicators[f'MA{strategy_config.MA_MID_PERIOD}'] = f'中期均线 (周期={strategy_config.MA_MID_PERIOD})'
        strategy_indicators[f'MA{strategy_config.MA_LONG_PERIOD}'] = f'长期均线 (周期={strategy_config.MA_LONG_PERIOD})'
    
    # 检查波动性指标
    if hasattr(strategy_config, 'VOLATILITY_PERIOD'):
        vol_info = f'启用, 周期={strategy_config.VOLATILITY_PERIOD}'
        if hasattr(strategy_config, 'VOLATILITY_THRESHOLD'):
            vol_info += f', 阈值={strategy_config.VOLATILITY_THRESHOLD}'
        strategy_indicators['Volatility'] = vol_info
    
    # 检查ATR指标
    if hasattr(strategy_config, 'ATR_THRESHOLD'):
        strategy_indicators['ATR'] = f'启用, 阈值={strategy_config.ATR_THRESHOLD}%'
        strategy_indicators['ATR_Pct'] = f'启用, 阈值={strategy_config.ATR_THRESHOLD}%'
    
    # 波动性评分
    if hasattr(strategy_config, 'VOLATILITY_WEIGHT') and hasattr(strategy_config, 'ATR_WEIGHT'):
        strategy_indicators['Volatility_Score'] = f'启用, 权重={strategy_config.VOLATILITY_WEIGHT}/{strategy_config.ATR_WEIGHT}'
    
    # 布林带指标
    if hasattr(strategy_config, 'BOLL_PERIOD'):
        strategy_indicators['BOLL_UPPER'] = f'布林带上轨 (周期={strategy_config.BOLL_PERIOD})'
        strategy_indicators['BOLL_MIDDLE'] = f'布林带中轨 (周期={strategy_config.BOLL_PERIOD})'
        strategy_indicators['BOLL_LOWER'] = f'布林带下轨 (周期={strategy_config.BOLL_PERIOD})'
        strategy_indicators['BOLL_WIDTH'] = f'布林带宽度 (周期={strategy_config.BOLL_PERIOD})'
    
    # RSI指标
    if hasattr(strategy_config, 'RSI_PERIOD'):
        strategy_indicators['RSI'] = f'相对强弱指标 (周期={strategy_config.RSI_PERIOD})'

# 显示策略中使用的买入指标信息
if strategy_indicators:
    with st.expander("策略中使用的买入指标", expanded=True):
        st.write("以下是策略配置文件中定义的买入指标：")
        for indicator, info in strategy_indicators.items():
            # 检查指标名称是否在交易数据中存在，或者是否有类似的指标名称
            found = False
            matched_indicators = []
            
            # 精确匹配
            if indicator in available_indicators:
                st.info(f"**{indicator}**: {info}")
                found = True
            else:
                # 模糊匹配 - 检查是否有包含该指标名称的列
                for col in available_indicators:
                    if indicator.lower() in col.lower() or col.lower() in indicator.lower():
                        matched_indicators.append(col)
                
                if matched_indicators:
                    st.info(f"**{indicator}**: {info} (可能对应的列: {', '.join(matched_indicators)})")
                    found = True
            
            if not found:
                st.warning(f"**{indicator}**: {info} (在交易数据中未找到此指标或相似指标)")

# 创建指标分类
indicator_categories = {
    "价格趋势": [
        "MA3_Buy", "MA5_Buy", "MA7_Buy", "MA10_Buy", "MA20_Buy", "MA30_Buy", "MA60_Buy",
        "EMA5_Buy", "EMA10_Buy", "EMA20_Buy", "EMA30_Buy", "EMA60_Buy",
        "WMA5_Buy", "WMA10_Buy", "WMA20_Buy", "HMA10_Buy", "HMA20_Buy",
        "TRIX_Buy", "Trend_Strength_Buy", "Price_Change_Pct_Buy", "Supertrend_Buy",
        "MA3", "MA5", "MA7", "MA10", "MA20", "MA30", "MA60", "MA120", "MA200",
        "EMA5", "EMA10", "EMA20", "EMA30", "EMA60",
        "WMA5", "WMA10", "WMA20", "HMA10", "HMA20",
        "TRIX", "Trend_Strength", "Price_Change_Pct", "Supertrend"
    ],
    
    "波动性指标": [
        "Volatility_Buy", "ATR_Buy", "ATR_Pct_Buy", "Volatility_Score_Buy", 
        "BOLL_Width_Buy", "True_Range_Buy", "NATR_Buy",
        "Keltner_Upper_Buy", "Keltner_Middle_Buy", "Keltner_Lower_Buy",
        "Volatility", "ATR", "ATR_Pct", "Volatility_Score", 
        "BOLL_Width", "True_Range", "NATR",
        "Keltner_Upper", "Keltner_Middle", "Keltner_Lower"
    ],
    
    "布林带指标": [
        "BOLL_Upper_Buy", "BOLL_Middle_Buy", "BOLL_Lower_Buy", "BOLL_Width_Buy",
        "BOLL_Upper", "BOLL_Middle", "BOLL_Lower", "BOLL_Width",
        "Bollinger_Width", "Bollinger_Width_Buy"
    ],
    
    "动量指标": [
        "RSI_Buy", "CCI_Buy", "WR_Buy", "Williams_R_Buy", "ROC_Buy", "Momentum_Buy",
        "Stoch_K_Buy", "Stoch_D_Buy", "TSI_Buy", "UO_Buy", "AO_Buy", "PPO_Buy", "PVO_Buy",
        "RSI", "CCI", "WR", "Williams_R", "ROC", "Momentum",
        "Stoch_K", "Stoch_D", "TSI", "UO", "AO", "PPO", "PVO",
        "Stochastic_RSI", "Stochastic_RSI_Buy",
        "CMO", "CMO_Buy", "DPO", "DPO_Buy"
    ],
    
    "震荡指标": [
        "MACD_Buy", "MACD_Signal_Buy", "MACD_Hist_Buy", "MACD_Histogram_Buy",
        "KDJ_K_Buy", "KDJ_D_Buy", "KDJ_J_Buy", "APO_Buy",
        "MACD", "MACD_Signal", "MACD_Hist", "MACD_Histogram",
        "KDJ_K", "KDJ_D", "KDJ_J", "APO"
    ],
    
    "趋势指标": [
        "DMI_Plus_Buy", "DMI_Minus_Buy", "ADX_Buy", "Aroon_Up_Buy", "Aroon_Down_Buy", 
        "Aroon_Oscillator_Buy", "PSAR_Buy", "Ichimoku_A_Buy", "Ichimoku_B_Buy",
        "DMI_Plus", "DMI_Minus", "ADX", "Aroon_Up", "Aroon_Down", 
        "Aroon_Oscillator", "PSAR", "Ichimoku_A", "Ichimoku_B",
        "Ichimoku_Base", "Ichimoku_Conversion", "Ichimoku",
        "Ichimoku_Base_Buy", "Ichimoku_Conversion_Buy", "Ichimoku_Buy"
    ],
    
    "成交量指标": [
        "Volume_Ratio_Buy", "OBV_Buy", "MFI_Buy", "ADL_Buy", "CMF_Buy", 
        "Force_Index_Buy", "EMV_Buy", "VPT_Buy", "NVI_Buy", "PVI_Buy",
        "Volume_Ratio", "OBV", "MFI", "ADL", "CMF", 
        "Force_Index", "EMV", "VPT", "NVI", "PVI",
        "Volume_Change_Pct", "Volume_Change_Pct_Buy"
    ],
    
    "其他技术指标": [
        "PSY_Buy", "VR_Buy", "CR_Buy", "BR_Buy", "AR_Buy", "BIAS_Buy", 
        "KST_Buy", "Coppock_Curve_Buy", "Chande_Momentum_Oscillator_Buy",
        "PSY", "VR", "CR", "BR", "AR", "BIAS", 
        "KST", "Coppock_Curve", "Chande_Momentum_Oscillator",
        "Parabolic_SAR", "Parabolic_SAR_Buy"
    ],
    
    "资金管理": [
        "Allocation_Factor_Buy", "Risk_Ratio_Buy", "Kelly_Criterion_Buy",
        "Allocation_Factor", "Risk_Ratio", "Kelly_Criterion"
    ],
    
    "持仓相关": [
        "Holding_Hours", "Holding_Days", "Holding_Periods"
    ],
    
    "收益指标": [
        "Actual_Profit_Pct", "Annualized_Return", "Sharpe_Ratio", 
        "Sortino_Ratio", "Max_Drawdown", "Win_Rate", "Profit_Factor", "Expectancy"
    ],
    
    "卖出时指标": [
        "RSI_Sell", "MACD_Sell", "KDJ_K_Sell", "KDJ_D_Sell", "KDJ_J_Sell",
        "BOLL_Upper_Sell", "BOLL_Middle_Sell", "BOLL_Lower_Sell",
        "MA5_Sell", "MA10_Sell", "MA20_Sell", "MA30_Sell",
        "EMA5_Sell", "EMA10_Sell", "EMA20_Sell",
        "Volume_Ratio_Sell", "Trend_Strength_Sell"
    ]
}

# 将所有可用指标按类别分组
categorized_indicators = {}
for category, indicators in indicator_categories.items():
    # 只保留实际存在于available_indicators中的指标
    available_in_category = [ind for ind in indicators if ind in available_indicators]
    if available_in_category:  # 只添加非空类别
        categorized_indicators[category] = available_in_category

# 找出未分类的指标
all_categorized = [ind for inds in indicator_categories.values() for ind in inds]
uncategorized = [ind for ind in available_indicators if ind not in all_categorized]
if uncategorized:
    categorized_indicators["其他指标"] = uncategorized

col1, col2 = st.columns([2, 1])

with col1:
    # 添加指标搜索功能
    search_term = st.text_input("搜索指标", placeholder="输入指标名称关键词...")
    
    # 添加推荐指标按钮
    if strategy_indicators and st.button("使用策略配置的买入指标", key="use_strategy_indicators"):
        # 找出所有在available_indicators中存在的策略指标
        available_strategy_indicators = []
        for indicator in strategy_indicators:
            if indicator in available_indicators:
                available_strategy_indicators.append(indicator)
            else:
                # 模糊匹配
                for col in available_indicators:
                    if indicator.lower() in col.lower() or col.lower() in indicator.lower():
                        if col not in available_strategy_indicators:
                            available_strategy_indicators.append(col)
                            break
        
        # 最多选择5个指标
        selected_strategy_indicators = available_strategy_indicators[:5]
        if selected_strategy_indicators:
            st.session_state.selected_indicators = selected_strategy_indicators
            st.success(f"已自动选择{len(selected_strategy_indicators)}个策略配置的买入指标")
            st.rerun()
        else:
            st.warning("未找到可用的策略配置买入指标")
    
    # 创建一个容器来显示已选指标
    selected_container = st.container()
    with selected_container:
        st.write("### 已选指标")
        if not selected_indicators:
            st.info("尚未选择任何指标，请从下方选择2-5个指标进行分析")
        else:
            selected_cols = st.columns(len(selected_indicators))
            for i, ind in enumerate(selected_indicators):
                with selected_cols[i]:
                    # 检查是否是策略中的指标
                    is_strategy_indicator = False
                    for strategy_ind in strategy_indicators:
                        if ind == strategy_ind or ind.lower() in strategy_ind.lower() or strategy_ind.lower() in ind.lower():
                            is_strategy_indicator = True
                            break
                    
                    # 为策略指标添加特殊样式
                    indicator_name = feature_names_cn.get(ind, ind)
                    if is_strategy_indicator:
                        st.write(f"**{i+1}. 📊 {indicator_name}** ({ind})")
                    else:
                        st.write(f"**{i+1}. {indicator_name}** ({ind})")
                        
                    if st.button("移除", key=f"remove_{ind}"):
                        selected_indicators.remove(ind)
                        st.session_state.selected_indicators = selected_indicators
                        st.rerun()

    # 添加重置按钮
    if selected_indicators and st.button("重置选择", type="primary"):
        selected_indicators = []
        st.session_state.selected_indicators = []
        st.rerun()

    # 显示所有可用指标的选择列表
    st.write("### 选择指标")
    
    # 根据搜索结果过滤指标
    filtered_indicators = available_indicators
    if search_term:
        search_term = search_term.lower()
        filtered_indicators = [ind for ind in available_indicators if 
                             search_term in ind.lower() or 
                             search_term in feature_names_cn.get(ind, "").lower()]
        
    # 如果有搜索结果或显示所有指标
    if filtered_indicators:
        # 将指标分成多列显示
        num_cols = 4
        indicator_cols = st.columns(num_cols)
        for i, ind in enumerate(filtered_indicators):
            with indicator_cols[i % num_cols]:
                # 检查是否是策略中的指标
                is_strategy_indicator = False
                for strategy_ind in strategy_indicators:
                    if ind == strategy_ind or ind.lower() in strategy_ind.lower() or strategy_ind.lower() in ind.lower():
                        is_strategy_indicator = True
                        break
                
                # 为策略指标添加特殊样式
                button_label = f"{feature_names_cn.get(ind, ind)}"
                if is_strategy_indicator:
                    button_label = f"📊 {button_label}"
                
                if st.button(button_label, key=f"ind_{ind}"):
                    if ind not in selected_indicators and len(selected_indicators) < 5:
                        selected_indicators.append(ind)
                        st.session_state.selected_indicators = selected_indicators
                        st.rerun()
                    elif ind not in selected_indicators:
                        st.warning("最多只能选择5个指标")
            
                # 添加指标的简短说明
                if ind in indicator_descriptions:
                    st.caption(indicator_descriptions.get(ind, "")[:50] + "..." if len(indicator_descriptions.get(ind, "")) > 50 else indicator_descriptions.get(ind, ""))
    else:
        st.warning("未找到匹配的指标")

with col2:
    if len(selected_indicators) >= 2:
        sort_by = st.radio(
            "排序依据",
            ["综合评分(胜率*收益率)", "胜率", "平均收益率", "交易次数"]
        )
        
        range_mode = st.radio(
            "范围划分模式",
            ["自定义范围", "自动划分"]
        )
        
        if range_mode == "自动划分":
            num_bins = st.slider("每个指标的范围划分数量", min_value=2, max_value=10, value=5, 
                                help="每个指标将被划分为多少个范围。注意：选择过多会导致组合数量激增，分析时间增加。")
    else:
        st.info("请至少选择2个指标进行组合分析")

# 删除重复的"再次显示已选指标"部分
# 如果选择了自定义范围模式，为每个指标创建自定义范围输入
if len(selected_indicators) >= 2 and range_mode == "自定义范围":
    st.header("自定义指标范围")
    
    # 创建一个字典来存储每个指标的自定义范围
    if 'custom_ranges' not in st.session_state:
        st.session_state.custom_ranges = {}
    
    custom_ranges = st.session_state.custom_ranges
    
    for idx, indicator in enumerate(selected_indicators):
        st.subheader(f"指标 {idx+1}: {feature_names_cn.get(indicator, indicator)} ({indicator})")
        
        # 显示指标的最小值和最大值
        min_val = df[indicator].min()
        max_val = df[indicator].max()
        st.write(f"指标范围: {min_val:.4f} 到 {max_val:.4f}")
        
        # 初始化该指标的范围列表
        if indicator not in custom_ranges:
            custom_ranges[indicator] = []
        
        # 创建一个容器来放置该指标的所有范围
        with st.container():
            # 允许用户添加多个范围
            range_count = st.number_input(f"为 {feature_names_cn.get(indicator, indicator)} 添加范围数量", 
                                        min_value=1, max_value=10, value=3, key=f"range_count_{idx}")
            
            # 清空之前的范围设置
            custom_ranges[indicator] = []
            
            # 为每个范围创建输入字段
            for i in range(range_count):
                col1, col2, col3 = st.columns([2, 2, 1])
                
                with col1:
                    min_range = st.number_input(f"范围 {i+1} 最小值", 
                                              min_value=float(min_val), 
                                              max_value=float(max_val),
                                              value=float(min_val + (max_val - min_val) * i / range_count),
                                              format="%.4f", 
                                              key=f"{indicator}_min_{i}")
                with col2:
                    max_range = st.number_input(f"范围 {i+1} 最大值", 
                                              min_value=float(min_val), 
                                              max_value=float(max_val),
                                              value=float(min_val + (max_val - min_val) * (i + 1) / range_count),
                                              format="%.4f", 
                                              key=f"{indicator}_max_{i}")
                with col3:
                    range_name = st.text_input(f"范围 {i+1} 名称", 
                                            value=f"范围{i+1}", 
                                            key=f"{indicator}_name_{i}")
                
                # 将范围添加到列表中
                custom_ranges[indicator].append((min_range, max_range, range_name))
            
        st.markdown("---")
    
    # 更新session_state
    st.session_state.custom_ranges = custom_ranges

# 添加一个开始详细分析的按钮
if len(selected_indicators) >= 2:
    if st.button("开始详细分析", type="primary"):
        st.info("正在进行详细分析，请稍候...")
        st.session_state.analysis_started = True
        
        with st.spinner("正在分析指标组合，请稍候..."):
            try:
                # 为每个指标创建范围划分
                for idx, indicator in enumerate(selected_indicators):
                    if range_mode == "自动划分":
                        # 使用分位数进行划分，确保每个区间有相近数量的样本
                        try:
                            bins = pd.qcut(df[indicator], q=num_bins, duplicates='drop')
                            df[f'Indicator_{idx+1}_Range'] = bins
                        except Exception as e:
                            st.error(f"划分指标 {feature_names_cn.get(indicator, indicator)} 范围失败: {str(e)}")
                            st.stop()
                    elif range_mode == "自定义范围":
                        # 创建一个新列来存储范围标签
                        df[f'Indicator_{idx+1}_Range'] = None
                        
                        # 应用自定义范围
                        custom_ranges = st.session_state.custom_ranges
                        if indicator in custom_ranges:
                            for i, (min_val, max_val, range_name) in enumerate(custom_ranges[indicator]):
                                # 对于第一个范围，包括最小值
                                if i == 0:
                                    mask = (df[indicator] >= min_val) & (df[indicator] <= max_val)
                                else:
                                    mask = (df[indicator] > min_val) & (df[indicator] <= max_val)
                                
                                df.loc[mask, f'Indicator_{idx+1}_Range'] = f"{range_name} ({min_val:.2f}-{max_val:.2f})"
                            
                            # 检查是否有未分类的数据
                            if df[f'Indicator_{idx+1}_Range'].isnull().any():
                                st.warning(f"指标 {feature_names_cn.get(indicator, indicator)} 有 {df[f'Indicator_{idx+1}_Range'].isnull().sum()} 条记录未被任何范围覆盖。")
                        else:
                            st.error(f"未找到指标 {feature_names_cn.get(indicator, indicator)} 的自定义范围设置。")
                            st.stop()
                
                # 创建组合标识
                df['Combination_ID'] = df[[f'Indicator_{idx+1}_Range' for idx in range(len(selected_indicators))]].apply(
                    lambda x: ' + '.join([str(val) for val in x]), axis=1
                )
                
                # 过滤掉组合ID中包含None的记录
                df_filtered = df.dropna(subset=['Combination_ID'])
                if len(df_filtered) < len(df):
                    st.warning(f"有 {len(df) - len(df_filtered)} 条记录因为不在任何指定范围内而被排除。")
                
                # 对每个组合进行分析
                combination_stats = []
                
                for combination_id in df_filtered['Combination_ID'].unique():
                    comb_df = df_filtered[df_filtered['Combination_ID'] == combination_id]
                    
                    # 计算统计数据
                    total_trades = len(comb_df)
                    winning_trades = len(comb_df[comb_df['Actual_Profit_Pct'] > 0])
                    losing_trades = total_trades - winning_trades
                    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
                    avg_profit = comb_df['Actual_Profit_Pct'].mean()
                    avg_win = comb_df[comb_df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if winning_trades > 0 else 0
                    avg_loss = comb_df[comb_df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if losing_trades > 0 else 0
                    max_profit = comb_df['Actual_Profit_Pct'].max()
                    min_profit = comb_df['Actual_Profit_Pct'].min()
                    profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 and avg_loss < 0 else float('inf')
                    
                    # 计算综合评分
                    composite_score = win_rate * avg_profit / 100
                    
                    # 计算累计收益率
                    cumulative_return = (1 + avg_profit / 100) ** total_trades - 1
                    cumulative_return_pct = cumulative_return * 100
                    
                    # 提取每个指标的范围
                    ranges = combination_id.split(' + ')
                    range_dict = {}
                    for idx, indicator in enumerate(selected_indicators):
                        if idx < len(ranges):
                            range_dict[f'{feature_names_cn.get(indicator, indicator)}_Range'] = ranges[idx]
                    
                    # 添加到统计列表
                    stat_entry = {
                        'Combination_ID': combination_id,
                        'Total_Trades': total_trades,
                        'Winning_Trades': winning_trades,
                        'Losing_Trades': losing_trades,
                        'Win_Rate': win_rate,
                        'Avg_Profit': avg_profit,
                        'Avg_Win': avg_win,
                        'Avg_Loss': avg_loss,
                        'Max_Profit': max_profit,
                        'Min_Profit': min_profit,
                        'Profit_Loss_Ratio': profit_loss_ratio,
                        'Composite_Score': composite_score,
                        'Cumulative_Return': cumulative_return_pct
                    }
                    stat_entry.update(range_dict)
                    
                    combination_stats.append(stat_entry)
                
                # 创建统计数据框
                stats_df = pd.DataFrame(combination_stats)
                
                if stats_df.empty:
                    st.error("没有找到符合条件的组合。请检查您的范围设置。")
                    st.stop()
                
                # 根据选择的指标排序
                if sort_by == "综合评分(胜率*收益率)":
                    stats_df = stats_df.sort_values('Composite_Score', ascending=False)
                elif sort_by == "胜率":
                    stats_df = stats_df.sort_values('Win_Rate', ascending=False)
                elif sort_by == "平均收益率":
                    stats_df = stats_df.sort_values('Avg_Profit', ascending=False)
                else:  # 交易次数
                    stats_df = stats_df.sort_values('Total_Trades', ascending=False)
                
                # 过滤掉交易次数过少的组合
                min_trades = max(5, int(len(df) * 0.01))  # 至少5笔交易或者总交易的1%
                filtered_stats_df = stats_df[stats_df['Total_Trades'] >= min_trades].copy()
                
                if len(filtered_stats_df) == 0:
                    st.warning(f"没有找到交易次数大于等于{min_trades}的组合。请尝试减少指标数量或调整范围设置。")
                    st.stop()
                
                # 显示分析结果
                st.header("指标范围分析结果")
                st.markdown(f"<a href='#' id='share-link'>🔗</a>", unsafe_allow_html=True)
                
                # 🏆 最优指标范围组合 (Top 10)
                st.subheader("🏆 最优指标范围组合 (Top 10)")
                
                # 创建用于显示的数据框
                display_df = filtered_stats_df.head(10).copy()
                
                # 格式化显示列
                display_df['指标范围'] = display_df['Combination_ID']
                display_df['交易次数'] = display_df['Total_Trades']
                display_df['盈利交易'] = display_df['Winning_Trades']
                display_df['亏损交易'] = display_df['Losing_Trades']
                display_df['胜率'] = display_df['Win_Rate'].map('{:.2f}%'.format)
                display_df['平均收益率'] = display_df['Avg_Profit'].map('{:.4f}%'.format)
                display_df['累计收益率'] = display_df['Cumulative_Return'].map('{:.2f}%'.format)
                display_df['平均盈利'] = display_df['Avg_Win'].map('{:.4f}%'.format)
                display_df['平均亏损'] = display_df['Avg_Loss'].map('{:.4f}%'.format)
                display_df['最大盈利'] = display_df['Max_Profit'].map('{:.4f}%'.format)
                display_df['最大亏损'] = display_df['Min_Profit'].map('{:.4f}%'.format)
                display_df['盈亏比'] = display_df['Profit_Loss_Ratio'].map(lambda x: f"{x:.2f}" if x != float('inf') else "∞")
                display_df['综合评分'] = display_df['Composite_Score'].map('{:.4f}'.format)
                
                # 选择要显示的列
                columns_to_display = ['指标范围', '交易次数', '盈利交易', '亏损交易', '胜率', 
                                    '平均收益率', '累计收益率', '平均盈利', '平均亏损', '盈亏比', '综合评分']
                
                st.dataframe(display_df[columns_to_display], use_container_width=True)
                
                # 显示最差组合
                st.subheader("⚠️ 最差指标范围组合 (Bottom 10)")
                
                # 创建用于显示的数据框
                worst_display_df = filtered_stats_df.tail(10).iloc[::-1].copy()  # 反转顺序，使最差的显示在最上面
                
                # 格式化显示列
                worst_display_df['指标范围'] = worst_display_df['Combination_ID']
                worst_display_df['交易次数'] = worst_display_df['Total_Trades']
                worst_display_df['盈利交易'] = worst_display_df['Winning_Trades']
                worst_display_df['亏损交易'] = worst_display_df['Losing_Trades']
                worst_display_df['胜率'] = worst_display_df['Win_Rate'].map('{:.2f}%'.format)
                worst_display_df['平均收益率'] = worst_display_df['Avg_Profit'].map('{:.4f}%'.format)
                worst_display_df['累计收益率'] = worst_display_df['Cumulative_Return'].map('{:.2f}%'.format)
                worst_display_df['平均盈利'] = worst_display_df['Avg_Win'].map('{:.4f}%'.format)
                worst_display_df['平均亏损'] = worst_display_df['Avg_Loss'].map('{:.4f}%'.format)
                worst_display_df['最大盈利'] = worst_display_df['Max_Profit'].map('{:.4f}%'.format)
                worst_display_df['最大亏损'] = worst_display_df['Min_Profit'].map('{:.4f}%'.format)
                worst_display_df['盈亏比'] = worst_display_df['Profit_Loss_Ratio'].map(lambda x: f"{x:.2f}" if x != float('inf') else "∞")
                worst_display_df['综合评分'] = worst_display_df['Composite_Score'].map('{:.4f}'.format)
                
                st.dataframe(worst_display_df[columns_to_display], use_container_width=True)
                
                # 可视化分析
                st.header("可视化分析")
                
                # 胜率和平均收益率的散点图
                st.subheader("组合表现散点图")
                
                # 使用Plotly创建交互式散点图
                fig = px.scatter(
                    filtered_stats_df,
                    x='Win_Rate',
                    y='Avg_Profit',
                    size='Total_Trades',
                    color='Composite_Score',
                    hover_name='Combination_ID',
                    color_continuous_scale='RdYlGn',
                    title=f'指标组合表现分析',
                    labels={
                        'Win_Rate': '胜率 (%)',
                        'Avg_Profit': '平均收益率 (%)',
                        'Total_Trades': '交易次数',
                        'Composite_Score': '综合评分'
                    },
                    size_max=30,
                )
                
                # 添加水平线表示收益率为0
                fig.add_shape(
                    type="line",
                    x0=0,
                    y0=0,
                    x1=100,
                    y1=0,
                    line=dict(color="red", width=1, dash="dash"),
                )
                
                # 添加垂直线表示胜率为50%
                fig.add_shape(
                    type="line",
                    x0=50,
                    y0=min(filtered_stats_df['Avg_Profit']),
                    x1=50,
                    y1=max(filtered_stats_df['Avg_Profit']),
                    line=dict(color="red", width=1, dash="dash"),
                )
                
                # 更新布局
                fig.update_layout(
                    height=600,
                    xaxis_title='胜率 (%)',
                    yaxis_title='平均收益率 (%)',
                    legend_title='交易次数',
                    coloraxis_colorbar=dict(title='综合评分'),
                )
                
                st.plotly_chart(fig, use_container_width=True)
                
                # 热力图分析
                if len(selected_indicators) == 2:
                    st.subheader("指标组合热力图")
                    
                    # 提取两个指标的范围值
                    indicator1_ranges = sorted(df[f'Indicator_1_Range'].unique(), key=lambda x: str(x))
                    indicator2_ranges = sorted(df[f'Indicator_2_Range'].unique(), key=lambda x: str(x))
                    
                    # 创建热力图数据
                    heatmap_data = pd.pivot_table(
                        filtered_stats_df,
                        values='Composite_Score',
                        index=[f'{feature_names_cn.get(selected_indicators[0], selected_indicators[0])}_Range'],
                        columns=[f'{feature_names_cn.get(selected_indicators[1], selected_indicators[1])}_Range'],
                        aggfunc='mean'
                    )
                    
                    # 创建热力图
                    fig = px.imshow(
                        heatmap_data,
                        labels=dict(
                            x=feature_names_cn.get(selected_indicators[1], selected_indicators[1]),
                            y=feature_names_cn.get(selected_indicators[0], selected_indicators[0]),
                            color="综合评分"
                        ),
                        x=[str(r) for r in heatmap_data.columns],
                        y=[str(r) for r in heatmap_data.index],
                        color_continuous_scale="RdBu_r",
                        aspect="auto",
                        title=f"{feature_names_cn.get(selected_indicators[0], selected_indicators[0])} 与 {feature_names_cn.get(selected_indicators[1], selected_indicators[1])} 组合表现热力图"
                    )
                    
                    fig.update_layout(height=600)
                    st.plotly_chart(fig, use_container_width=True)
                
                # 策略优化建议
                st.header("策略优化建议")
                
                # 获取最佳组合
                best_combination = filtered_stats_df.iloc[0]
                
                st.markdown(f"""
                ### 基于分析结果的建议
                
                #### 最佳指标组合
                
                分析显示，以下指标组合表现最佳：
                
                **{best_combination['Combination_ID']}**
                
                该组合的关键指标：
                - 胜率: {best_combination['Win_Rate']:.2f}%
                - 平均收益率: {best_combination['Avg_Profit']:.4f}%
                - 累计收益率: {best_combination['Cumulative_Return']:.2f}%
                - 交易次数: {best_combination['Total_Trades']}笔
                - 综合评分: {best_combination['Composite_Score']:.4f}
                
                #### 优化建议
                
                1. **交易条件筛选**：考虑将交易条件限制在上述最佳指标范围内，可能会提高策略的整体表现。
                
                2. **风险控制**：对于表现最差的指标组合，建议在实际交易中避免或设置更严格的止损。
                
                3. **资金分配**：可以考虑根据不同指标组合的表现调整资金分配比例，对表现更好的组合增加资金比例。
                
                4. **进一步分析**：建议对最佳组合进行更深入的分析，包括不同市场环境下的表现、与其他因素的相关性等。
                
                > 注意：分析结果仅供参考，实际交易中应结合市场环境、风险偏好等多种因素综合考虑。样本数量过少的组合可能存在统计偏差。
                """)
            except Exception as e:
                st.error(f"分析过程中发生错误: {str(e)}")
                st.info("请尝试减少指标数量或调整范围设置，或者检查数据格式是否正确。")
else:
    st.info("请选择至少2个指标开始分析") 