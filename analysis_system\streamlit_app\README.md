# 万和策略分析系统 - Streamlit应用

这是一个基于Streamlit的交互式分析应用程序，用于分析和优化交易策略。

## 快速开始

### Windows用户
双击 `run_app.bat` 文件启动应用程序。

## 功能特点

1. **数据分析**：上传和分析交易数据
2. **策略优化**：通过参数调整优化交易策略
3. **可视化图表**：直观展示交易表现和指标
4. **报告生成**：生成详细的HTML分析报告
5. **参数配置**：通过UI界面轻松调整分析参数

## 使用指南

1. **首页**：提供系统概述和基本导航
2. **数据上传**：上传您的交易数据进行分析
3. **策略分析**：查看详细的交易策略分析结果
4. **参数优化**：调整参数以优化交易策略
5. **报告生成**：生成并下载详细分析报告

## 系统要求

- Python 3.7+
- 依赖包：streamlit, pandas, numpy, matplotlib, seaborn, plotly, scikit-learn, jinja2

## 故障排除

如果应用程序无法启动：

1. 确保已安装所有必要的依赖包
2. 检查数据文件路径是否正确
3. 查看应用程序日志以获取详细错误信息

## 文件结构

```
analysis_system/streamlit_app/
├── app.py                   # 主应用程序文件
├── data_loader.py           # 数据加载工具
├── chart_generator.py       # 图表生成工具
├── pages/                   # 页面组件
│   ├── home.py              # 首页
│   ├── data_analysis.py     # 数据分析页面
│   ├── strategy_optimizer.py # 策略优化页面
│   └── report_generator.py  # 报告生成页面
├── config.toml              # 应用配置文件
└── run_app.bat              # Windows启动脚本
```

## 联系与支持

如有问题或建议，请联系[您的联系信息] 