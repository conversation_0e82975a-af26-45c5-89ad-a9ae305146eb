# coding=utf-8
"""
验证配置生效情况
检查因子计算和交易时间配置是否真正生效
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def check_current_configuration():
    """检查当前配置"""
    print('⚙️ 当前配置检查')
    print('=' * 60)
    
    try:
        # 读取配置文件
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        print('📋 关键配置项检查:')
        
        # 检查买入检查模式
        if 'BUY_CHECK_INTERVAL_MODE = True' in config_content:
            print('   ✅ BUY_CHECK_INTERVAL_MODE = True (使用间隔模式)')
        else:
            print('   ❌ BUY_CHECK_INTERVAL_MODE = False (使用固定时间点)')
        
        # 检查间隔时间
        import re
        interval_match = re.search(r'BUY_CHECK_INTERVAL_MINUTES\s*=\s*(\d+)', config_content)
        if interval_match:
            interval = int(interval_match.group(1))
            print(f'   📊 BUY_CHECK_INTERVAL_MINUTES = {interval}分钟')
        
        # 检查因子实时配置
        if 'FACTOR_REALTIME_CONFIG' in config_content:
            print('   ✅ FACTOR_REALTIME_CONFIG 配置存在')
            
            # 提取因子周期配置
            cci_match = re.search(r"'cci_period':\s*(\d+)", config_content)
            rsi_match = re.search(r"'rsi_period':\s*(\d+)", config_content)
            adx_match = re.search(r"'adx_period':\s*(\d+)", config_content)
            
            if cci_match:
                print(f'   📊 CCI周期: {cci_match.group(1)}天')
            if rsi_match:
                print(f'   📊 RSI周期: {rsi_match.group(1)}天')
            if adx_match:
                print(f'   📊 ADX周期: {adx_match.group(1)}天')
        
        # 检查BUY_CHECK_TIMES
        times_match = re.search(r'BUY_CHECK_TIMES\s*=\s*\[(.*?)\]', config_content, re.DOTALL)
        if times_match:
            times_content = times_match.group(1)
            times = re.findall(r"'(\d{2}:\d{2}:\d{2})'", times_content)
            print(f'   📅 BUY_CHECK_TIMES: {len(times)}个时间点')
            for time in times[:5]:  # 显示前5个
                print(f'      - {time}')
            if len(times) > 5:
                print(f'      ... 还有{len(times)-5}个时间点')
        
        return interval if interval_match else None
        
    except Exception as e:
        print(f'❌ 配置检查失败: {e}')
        return None

def analyze_actual_trading_times():
    """分析实际交易时间"""
    print(f'\n🕐 实际交易时间分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最近3天的买入记录
        query = """
        SELECT 
            timestamp,
            symbol,
            strftime('%H:%M', timestamp) as trade_time,
            strftime('%Y-%m-%d', timestamp) as trade_date,
            strftime('%H', timestamp) as trade_hour,
            strftime('%M', timestamp) as trade_minute
        FROM trades 
        WHERE action = 'BUY'
        AND timestamp >= datetime('now', '-3 days')
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) > 0:
            print(f'📊 最近3天买入记录: {len(df)}条')
            
            # 分析时间分布
            print(f'\n⏰ 交易时间分布:')
            time_dist = df['trade_time'].value_counts().sort_index()
            
            opening_count = 0
            total_count = len(df)
            
            for time, count in time_dist.items():
                hour, minute = map(int, time.split(':'))
                is_opening = (hour == 9 and minute >= 30) or (hour == 10 and minute == 0)
                
                if is_opening:
                    opening_count += count
                    print(f'   {time}: {count}条 🚨 (开盘时段)')
                else:
                    print(f'   {time}: {count}条')
            
            opening_pct = opening_count / total_count * 100 if total_count > 0 else 0
            
            print(f'\n📊 开盘时段统计:')
            print(f'   开盘时段买入: {opening_count}条')
            print(f'   总买入: {total_count}条')
            print(f'   开盘时段占比: {opening_pct:.1f}%')
            
            if opening_pct > 70:
                print(f'   🚨 严重问题: 开盘时段买入过多，配置可能未生效')
            elif opening_pct > 30:
                print(f'   ⚠️ 需要关注: 开盘时段买入仍较多')
            else:
                print(f'   ✅ 良好: 开盘时段买入比例合理')
            
            # 分析时间间隔模式是否生效
            print(f'\n🔍 时间间隔模式分析:')
            
            # 按日期分组分析
            for date in df['trade_date'].unique()[-3:]:  # 最近3天
                date_df = df[df['trade_date'] == date].copy()
                if len(date_df) > 0:
                    print(f'\n   📅 {date}:')
                    date_times = date_df['trade_time'].unique()
                    date_times.sort()
                    
                    print(f'      交易时间点: {list(date_times)}')
                    
                    # 检查是否符合15分钟间隔
                    if len(date_times) > 1:
                        intervals = []
                        for i in range(1, len(date_times)):
                            prev_time = datetime.strptime(date_times[i-1], '%H:%M')
                            curr_time = datetime.strptime(date_times[i], '%H:%M')
                            interval = (curr_time - prev_time).total_seconds() / 60
                            intervals.append(interval)
                        
                        avg_interval = np.mean(intervals)
                        print(f'      平均间隔: {avg_interval:.1f}分钟')
                        
                        if 10 <= avg_interval <= 20:
                            print(f'      ✅ 间隔合理 (接近15分钟)')
                        else:
                            print(f'      ⚠️ 间隔异常')
            
            return df
        else:
            print('❌ 没有找到最近的买入记录')
            return None
            
    except Exception as e:
        print(f'❌ 交易时间分析失败: {e}')
        return None

def test_factor_calculation_with_new_config():
    """测试因子计算是否使用了新配置"""
    print(f'\n🧪 因子计算配置测试')
    print('=' * 60)
    
    try:
        # 导入因子计算引擎
        from enhanced_factor_engine import EnhancedFactorEngine
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'close': [10, 10.5, 11, 10.8, 11.2, 11.5, 11.3, 11.8, 12, 11.9, 12.2, 12.5, 12.3, 12.8, 13],
            'high': [10.2, 10.7, 11.2, 11, 11.4, 11.7, 11.5, 12, 12.2, 12.1, 12.4, 12.7, 12.5, 13, 13.2],
            'low': [9.8, 10.3, 10.8, 10.6, 11, 11.3, 11.1, 11.6, 11.8, 11.7, 12, 12.3, 12.1, 12.6, 12.8],
            'volume': [1000] * 15
        })
        
        # 创建因子引擎
        engine = EnhancedFactorEngine(context=None)
        
        # 计算因子
        factors = engine.calculate_all_factors(test_data, 'TEST.000001')
        
        print(f'📊 因子计算结果:')
        
        # 检查关键因子
        key_factors = ['cci', 'rsi_7', 'rsi_14', 'adx']
        for factor in key_factors:
            if factor in factors:
                print(f'   {factor}: {factors[factor]:.3f}')
            else:
                print(f'   {factor}: 未计算')
        
        # 检查是否有7天周期的因子
        has_7_day_factors = any('rsi_7' in key for key in factors.keys())
        
        if has_7_day_factors:
            print(f'   ✅ 检测到7天周期因子，配置可能已生效')
        else:
            print(f'   ❌ 未检测到7天周期因子，配置可能未生效')
        
        return factors
        
    except Exception as e:
        print(f'❌ 因子计算测试失败: {e}')
        import traceback
        print(f'详细错误: {traceback.format_exc()}')
        return None

def check_database_factor_changes():
    """检查数据库中因子值的变化"""
    print(f'\n📊 数据库因子变化检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最近的因子数据
        query = """
        SELECT 
            timestamp,
            symbol,
            cci,
            rsi,
            adx,
            strftime('%H:%M', timestamp) as trade_time,
            strftime('%Y-%m-%d %H', timestamp) as trade_hour
        FROM trades 
        WHERE action = 'BUY'
        AND timestamp >= datetime('now', '-2 days')
        ORDER BY timestamp DESC
        LIMIT 100
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) > 0:
            print(f'📈 最近2天买入记录: {len(df)}条')
            
            # 分析因子值的变化
            print(f'\n🔍 因子值变化分析:')
            
            factor_cols = ['cci', 'rsi', 'adx']
            for col in factor_cols:
                if col in df.columns:
                    valid_data = df[col].dropna()
                    if len(valid_data) > 1:
                        # 计算变异系数
                        cv = valid_data.std() / valid_data.mean() if valid_data.mean() != 0 else 0
                        # 计算相邻值的平均变化
                        changes = valid_data.diff().abs().mean()
                        
                        print(f'   {col.upper()}:')
                        print(f'      均值: {valid_data.mean():.2f}')
                        print(f'      标准差: {valid_data.std():.2f}')
                        print(f'      变异系数: {cv:.3f}')
                        print(f'      平均变化: {changes:.3f}')
                        
                        if cv > 0.3:
                            print(f'      ✅ 变化较大，可能反映实时计算')
                        elif cv > 0.1:
                            print(f'      🟡 变化适中')
                        else:
                            print(f'      ❌ 变化很小，可能仍使用固定值')
            
            # 按时间段分析因子差异
            print(f'\n⏰ 不同时间段因子差异:')
            
            # 按小时分组
            df['hour'] = pd.to_datetime(df['timestamp']).dt.hour
            hour_stats = df.groupby('hour')[factor_cols].mean().round(2)
            
            print(f'   按小时平均值:')
            for hour in sorted(hour_stats.index):
                print(f'   {hour:02d}:00 - CCI:{hour_stats.loc[hour, "cci"]:.1f}, RSI:{hour_stats.loc[hour, "rsi"]:.1f}, ADX:{hour_stats.loc[hour, "adx"]:.1f}')
            
            # 检查不同时间段的差异
            hour_variance = {}
            for col in factor_cols:
                if col in hour_stats.columns:
                    variance = hour_stats[col].std()
                    hour_variance[col] = variance
                    
                    if variance > 5:
                        print(f'   ✅ {col.upper()}时间段差异较大({variance:.2f})，可能反映实时计算')
                    elif variance > 2:
                        print(f'   🟡 {col.upper()}时间段差异适中({variance:.2f})')
                    else:
                        print(f'   ❌ {col.upper()}时间段差异很小({variance:.2f})，可能仍使用固定值')
            
            return df
        else:
            print('❌ 没有找到最近的因子数据')
            return None
            
    except Exception as e:
        print(f'❌ 因子变化检查失败: {e}')
        return None

def generate_diagnosis_report():
    """生成诊断报告"""
    print(f'\n📋 诊断报告')
    print('=' * 60)
    
    report = '''
🔍 配置生效情况诊断:

问题1: 买入时间配置
   现状: 使用BUY_CHECK_INTERVAL_MODE = True (间隔模式)
   配置: BUY_CHECK_INTERVAL_MINUTES = 15分钟
   影响: BUY_CHECK_TIMES固定时间点配置被忽略
   
   解决方案:
   - 要么改为BUY_CHECK_INTERVAL_MODE = False使用固定时间点
   - 要么确认15分钟间隔是否真正避开了开盘时段

问题2: 因子计算周期
   修改: enhanced_factor_engine.py已修改为读取配置
   配置: cci_period=7, rsi_period=7, adx_period=7
   验证: 需要检查实际计算是否使用了7天周期
   
   验证方法:
   - 检查数据库中是否有rsi_7字段
   - 对比因子值的变化幅度
   - 测试因子计算函数

问题3: 实时价格替代
   配置: USE_REALTIME_PRICE = True
   问题: 可能只在部分模块生效
   验证: 检查因子值是否在不同时间段有差异

🎯 关键验证点:
   1. 交易时间是否真的分散了？
   2. 因子计算是否使用了7天周期？
   3. 因子值是否在不同时间段有变化？
   4. 胜率是否有实际改善？

💡 下一步行动:
   1. 如果开盘时段买入仍过多，调整间隔模式起始时间
   2. 如果因子周期未生效，检查代码实现
   3. 如果实时性不足，增强实时价格替代逻辑
   4. 持续监控胜率变化
'''
    
    print(report)

def main():
    """主函数"""
    print('🔍 验证配置生效情况')
    print('=' * 60)
    
    print('🎯 目标: 检查胜率未变化的配置原因')
    
    # 检查当前配置
    interval = check_current_configuration()
    
    # 分析实际交易时间
    trading_df = analyze_actual_trading_times()
    
    # 测试因子计算
    factors = test_factor_calculation_with_new_config()
    
    # 检查数据库因子变化
    factor_df = check_database_factor_changes()
    
    # 生成诊断报告
    generate_diagnosis_report()
    
    print(f'\n🎯 关键发现')
    print('=' * 40)
    
    if interval:
        print(f'⚙️ 当前使用间隔模式: {interval}分钟')
    
    if trading_df is not None and len(trading_df) > 0:
        opening_count = len(trading_df[trading_df['trade_time'].str.match(r'^(09:[3-5]\d|10:00)$')])
        opening_pct = opening_count / len(trading_df) * 100
        print(f'🕐 开盘时段买入占比: {opening_pct:.1f}%')
        
        if opening_pct > 50:
            print(f'🚨 主要问题: 交易时间仍集中在开盘')
        else:
            print(f'✅ 改善: 交易时间已分散')
    
    if factors:
        has_7_day = any('rsi_7' in str(key) for key in factors.keys())
        print(f'🔧 7天周期因子: {"✅ 已生效" if has_7_day else "❌ 未生效"}')
    
    print(f'\n🚀 建议行动:')
    print(f'   1. 如果开盘买入仍过多，调整间隔模式起始时间')
    print(f'   2. 验证因子计算是否真正使用新周期')
    print(f'   3. 检查实时价格替代的全面性')
    print(f'   4. 持续监控胜率变化趋势')

if __name__ == '__main__':
    main()
