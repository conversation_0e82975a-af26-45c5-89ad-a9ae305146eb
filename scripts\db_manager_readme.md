# 数据库管理工具使用说明

## 概述

数据库管理工具（`db_manager.py`）是一个用于检查、维护和管理万和策略分析系统数据库的命令行工具。它提供了多种功能，包括查看数据库信息、备份数据库、清空数据表、优化数据库性能、检查数据库完整性以及导出数据表等。

## 功能列表

1. **查看数据库信息** - 显示数据库文件大小、表结构、记录数量和时间范围
2. **备份数据库** - 在进行任何可能的破坏性操作前，创建数据库的安全备份
3. **清空数据表** - 删除指定表中的全部或部分数据（按日期筛选）
4. **优化数据库** - 通过VACUUM操作压缩数据库文件，提高性能
5. **检查完整性** - 验证数据库文件的完整性，确保没有损坏
6. **导出数据表** - 将数据表导出为CSV格式，便于外部分析

## 使用方法

### 基本语法

```bash
python scripts/db_manager.py [选项]
```

### 选项说明

| 选项 | 参数 | 说明 |
|------|------|------|
| `--info` | 无 | 显示数据库信息 |
| `--backup` | 无 | 备份数据库 |
| `--clear` | TABLE | 清空指定表的数据 |
| `--before` | DATE | 删除指定日期之前的数据 (格式: YYYY-MM-DD) |
| `--optimize` | 无 | 优化数据库 |
| `--check` | 无 | 检查数据库完整性 |
| `--export` | TABLE | 将表导出为CSV文件 |
| `--output` | FILE | 导出文件路径 |

### 使用示例

1. **显示帮助信息**
   ```bash
   python scripts/db_manager.py
   ```

2. **查看数据库信息**
   ```bash
   python scripts/db_manager.py --info
   ```

3. **备份数据库**
   ```bash
   python scripts/db_manager.py --backup
   ```

4. **清空trades表**
   ```bash
   python scripts/db_manager.py --backup --clear trades
   ```
   注意：添加`--backup`参数会在清空前先进行备份

5. **删除2023年1月1日之前的数据**
   ```bash
   python scripts/db_manager.py --clear trades --before 2023-01-01
   ```

6. **优化数据库**
   ```bash
   python scripts/db_manager.py --optimize
   ```

7. **检查数据库完整性**
   ```bash
   python scripts/db_manager.py --check
   ```

8. **导出trades表**
   ```bash
   python scripts/db_manager.py --export trades
   ```

9. **导出trades表到指定文件**
   ```bash
   python scripts/db_manager.py --export trades --output ./data/my_trades.csv
   ```

10. **组合使用多个功能**
    ```bash
    python scripts/db_manager.py --backup --clear trades --optimize
    ```
    这将先备份数据库，然后清空trades表，最后优化数据库

## 注意事项

1. 在执行清空表操作前，强烈建议先备份数据库（使用`--backup`参数）
2. 数据库备份文件存储在`backups`目录下，按时间命名
3. 导出的CSV文件默认存储在`data/exports`目录下
4. 所有操作日志会记录在`db_manager.log`文件中

## 常见问题

1. **如何查看数据库中有哪些表？**
   ```bash
   python scripts/db_manager.py --info
   ```

2. **如何只删除部分数据？**
   使用`--before`参数指定日期，例如：
   ```bash
   python scripts/db_manager.py --clear trades --before 2023-01-01
   ```

3. **如何恢复误删的数据？**
   从备份目录中找到最近的备份，然后手动复制到`data`目录：
   ```bash
   cp backups/db_backup_YYYYMMDD_HHMMSS/trades.db data/
   ```

4. **数据库文件变得很大怎么办？**
   使用优化功能压缩数据库：
   ```bash
   python scripts/db_manager.py --optimize
   ```

## 高级用法

1. **定期清理旧数据**
   
   可以创建定时任务，定期清理过旧的数据：
   ```bash
   # 每月1日清理90天前的数据
   0 0 1 * * python scripts/db_manager.py --backup --clear trades --before $(date -d "90 days ago" +\%Y-\%m-\%d)
   ```

2. **定期优化数据库**
   
   定期优化数据库可以保持系统性能：
   ```bash
   # 每周日凌晨3点优化数据库
   0 3 * * 0 python scripts/db_manager.py --backup --optimize
   ```

3. **导出数据进行外部分析**
   
   将数据导出为CSV后，可以使用Excel、Python等工具进行更深入的分析：
   ```bash
   python scripts/db_manager.py --export trades --output ./analysis/trade_data.csv
   ``` 