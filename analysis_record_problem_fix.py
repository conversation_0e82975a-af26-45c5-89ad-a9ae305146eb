# coding=utf-8
"""
分析记录问题确认和修复
确认分析记录是否被误保存到trades表，并提供修复方案
"""

import sqlite3
import os

def check_database_tables():
    """检查数据库表结构"""
    print('🔍 数据库表结构检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print('📊 数据库中的表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 2. 检查是否有analysis表
        table_names = [table[0] for table in tables]
        
        if 'analysis' in table_names:
            print('\n✅ analysis表存在')
            
            # 检查analysis表的记录数
            cursor.execute("SELECT COUNT(*) FROM analysis")
            analysis_count = cursor.fetchone()[0]
            print(f'  analysis表记录数: {analysis_count}')
            
        else:
            print('\n❌ analysis表不存在')
            print('  💡 这可能是问题的根源！')
            print('  💡 分析记录可能被保存到trades表')
        
        # 3. 检查trades表的记录分布
        print('\n📊 trades表记录分布:')
        cursor.execute("SELECT action, COUNT(*) FROM trades GROUP BY action")
        action_counts = cursor.fetchall()
        
        for action, count in action_counts:
            print(f'  {action}: {count}条')
        
        conn.close()
        return 'analysis' in table_names
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return False

def analyze_buy_record_authenticity():
    """分析买入记录的真实性"""
    print('\n🔍 买入记录真实性分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 检查买入记录的字段完整性
        print('📊 买入记录字段分析:')
        
        # 检查关键交易字段
        trade_fields = ['price', 'volume', 'timestamp', 'symbol']
        
        for field in trade_fields:
            cursor.execute(f"SELECT COUNT(*) FROM trades WHERE action = 'BUY' AND {field} IS NOT NULL")
            not_null_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
            total_count = cursor.fetchone()[0]
            
            percentage = (not_null_count / total_count * 100) if total_count > 0 else 0
            print(f'  {field}: {not_null_count}/{total_count} ({percentage:.1f}%)')
        
        # 2. 检查分析相关字段
        print('\n📊 分析字段存在性检查:')
        
        # 获取所有字段名
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        field_names = [col[1] for col in columns]
        
        analysis_indicators = [
            'trix_current', 'signal_type', 'final_buy_signal', 
            'signal_reason', 'buy_signal_type', 'smart_score'
        ]
        
        for field in analysis_indicators:
            if field in field_names:
                cursor.execute(f"SELECT COUNT(*) FROM trades WHERE action = 'BUY' AND {field} IS NOT NULL")
                count = cursor.fetchone()[0]
                print(f'  {field}: {count}条有数据')
            else:
                print(f'  {field}: 字段不存在')
        
        # 3. 检查时间分布异常
        print('\n📊 时间分布异常检查:')
        
        cursor.execute("""
            SELECT timestamp, COUNT(*) as count 
            FROM trades 
            WHERE action = 'BUY' 
            GROUP BY timestamp 
            HAVING COUNT(*) > 1 
            ORDER BY count DESC 
            LIMIT 10
        """)
        
        duplicate_times = cursor.fetchall()
        
        if duplicate_times:
            print(f'  发现{len(duplicate_times)}个时间点有多条买入记录:')
            for timestamp, count in duplicate_times[:5]:
                print(f'    {timestamp}: {count}条')
            
            total_duplicates = sum(count for _, count in duplicate_times)
            print(f'  总重复记录数: {total_duplicates}条')
        else:
            print('  ✅ 没有发现时间重复的买入记录')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def check_enable_analysis_log_status():
    """检查enable_analysis_log状态"""
    print('\n🔍 enable_analysis_log状态检查')
    print('=' * 50)
    
    # 1. 检查环境变量
    env_value = os.environ.get('ENABLE_ANALYSIS_LOG', '未设置')
    print(f'环境变量 ENABLE_ANALYSIS_LOG: {env_value}')
    
    # 2. 检查默认值
    print('代码中的默认设置:')
    print("  os.environ.get('ENABLE_ANALYSIS_LOG', '1')  # 默认启用")
    
    # 3. 分析影响
    if env_value == '1' or env_value == '未设置':
        print('\n⚠️ 分析日志已启用！')
        print('  这意味着signal_generator.py会调用save_analysis()')
        print('  如果analysis表不存在，数据可能被保存到trades表')
    else:
        print('\n✅ 分析日志已禁用')

def propose_fix_solutions():
    """提出修复方案"""
    print('\n🔧 修复方案建议')
    print('=' * 50)
    
    solutions = [
        {
            'solution': '方案1: 禁用分析日志',
            'description': '设置ENABLE_ANALYSIS_LOG=0，停止保存分析记录',
            'pros': ['立即停止问题', '简单快速'],
            'cons': ['失去分析数据', '可能影响其他功能'],
            'implementation': '在启动策略前设置环境变量或修改代码默认值'
        },
        {
            'solution': '方案2: 创建独立的analysis表',
            'description': '创建专门的analysis表存储分析数据',
            'pros': ['保留分析功能', '数据分离清晰'],
            'cons': ['需要修改数据库结构', '可能需要数据迁移'],
            'implementation': '修改data_manager.py创建analysis表'
        },
        {
            'solution': '方案3: 修复save_analysis逻辑',
            'description': '确保save_analysis不会保存到trades表',
            'pros': ['根本解决问题', '保持功能完整'],
            'cons': ['需要代码修改', '测试工作量大'],
            'implementation': '修改data_manager.py的_save_analysis_to_db方法'
        },
        {
            'solution': '方案4: 清理现有错误数据',
            'description': '识别并删除trades表中的分析记录',
            'pros': ['清理历史问题', '保留真实交易记录'],
            'cons': ['可能误删真实记录', '需要精确识别'],
            'implementation': '编写脚本识别和删除分析记录'
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f'\n{i}. {solution["solution"]}')
        print(f'   描述: {solution["description"]}')
        print(f'   优点: {", ".join(solution["pros"])}')
        print(f'   缺点: {", ".join(solution["cons"])}')
        print(f'   实现: {solution["implementation"]}')

def create_immediate_fix():
    """创建立即修复脚本"""
    print('\n📝 创建立即修复脚本')
    print('=' * 50)
    
    fix_script = '''# coding=utf-8
"""
立即修复脚本 - 禁用分析日志
"""

import os

def disable_analysis_log():
    """禁用分析日志"""
    print("🔧 禁用分析日志...")
    
    # 设置环境变量
    os.environ['ENABLE_ANALYSIS_LOG'] = '0'
    print("✅ 已设置 ENABLE_ANALYSIS_LOG=0")
    
    print("💡 请重新启动策略以使设置生效")
    print("💡 这将停止保存分析记录到数据库")

if __name__ == '__main__':
    disable_analysis_log()
'''
    
    with open('disable_analysis_log.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print('✅ 已创建 disable_analysis_log.py')
    print('💡 运行此脚本可立即禁用分析日志')

def main():
    """主函数"""
    print('🔍 分析记录问题确认和修复方案')
    print('=' * 60)
    
    # 检查数据库表结构
    has_analysis_table = check_database_tables()
    
    # 分析买入记录真实性
    analyze_buy_record_authenticity()
    
    # 检查enable_analysis_log状态
    check_enable_analysis_log_status()
    
    # 提出修复方案
    propose_fix_solutions()
    
    # 创建立即修复脚本
    create_immediate_fix()
    
    print(f'\n🎯 问题确认结果')
    print('=' * 40)
    
    if not has_analysis_table:
        print('❌ 确认问题：analysis表不存在')
        print('⚠️ 分析记录被保存到trades表')
        print('💡 这解释了为什么有大量"买入"记录')
        print('🔧 建议立即禁用分析日志或创建analysis表')
    else:
        print('✅ analysis表存在，需要进一步调查')
    
    print('\n📋 推荐行动:')
    print('1. 🚨 立即运行 python disable_analysis_log.py')
    print('2. 🔄 重新启动策略')
    print('3. 📊 观察新的记录数量变化')
    print('4. 🧹 考虑清理历史错误数据')

if __name__ == '__main__':
    main()
