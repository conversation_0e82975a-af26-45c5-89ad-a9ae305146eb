# coding=utf-8
"""
当前实际情况检查
重新分析真实的策略表现，找出分析错误
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def get_current_real_performance():
    """获取当前真实表现"""
    print('🔍 当前实际策略表现分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp DESC 
        LIMIT 5000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 最新交易数据: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有交易数据')
            return None
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 分析买入和卖出记录
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
        
        print(f'\n📈 交易记录统计:')
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        print(f'   完成交易: {len(completed_trades)} 条')
        
        if len(completed_trades) == 0:
            print('⚠️ 没有完成的交易记录')
            return None
        
        # 计算真实胜率
        profitable_trades = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
        total_trades = len(completed_trades)
        real_win_rate = profitable_trades / total_trades * 100
        
        # 计算平均收益
        avg_profit = completed_trades['net_profit_pct_sell'].mean()
        median_profit = completed_trades['net_profit_pct_sell'].median()
        
        print(f'\n🎯 当前真实表现:')
        print(f'   总交易数: {total_trades}')
        print(f'   盈利交易: {profitable_trades}')
        print(f'   真实胜率: {real_win_rate:.1f}%')
        print(f'   平均收益: {avg_profit:.2f}%')
        print(f'   中位收益: {median_profit:.2f}%')
        
        # 分析最近的表现趋势
        latest_time = df['timestamp'].max()
        recent_cutoff = latest_time - timedelta(hours=24)
        recent_trades = completed_trades[completed_trades['timestamp'] >= recent_cutoff]
        
        if len(recent_trades) > 0:
            recent_win_rate = (recent_trades['net_profit_pct_sell'] > 0).mean() * 100
            recent_avg_profit = recent_trades['net_profit_pct_sell'].mean()
            
            print(f'\n📊 最近24小时表现:')
            print(f'   交易数: {len(recent_trades)}')
            print(f'   胜率: {recent_win_rate:.1f}%')
            print(f'   平均收益: {recent_avg_profit:.2f}%')
        
        return {
            'total_trades': total_trades,
            'win_rate': real_win_rate,
            'avg_profit': avg_profit,
            'recent_trades': len(recent_trades) if len(recent_trades) > 0 else 0,
            'recent_win_rate': recent_win_rate if len(recent_trades) > 0 else 0,
            'data': completed_trades
        }
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None

def analyze_optimization_impact():
    """分析优化的实际影响"""
    print(f'\n🔍 优化影响分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的买入记录，检查CCI和RSI配置是否生效
        query = """
        SELECT 
            timestamp, symbol, cci, rsi, macd_hist, 
            bb_position, adx, atr_pct, bb_width
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 1000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 最新买入记录: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有买入记录')
            return
        
        # 检查CCI优化是否生效
        if 'cci' in df.columns:
            cci_data = df.dropna(subset=['cci'])
            if len(cci_data) > 0:
                cci_in_range = cci_data[(cci_data['cci'] >= 20) & (cci_data['cci'] <= 30)]
                cci_stats = cci_data['cci'].describe()
                
                print(f'\n📈 CCI配置检查:')
                print(f'   CCI数据: {len(cci_data)} 条')
                print(f'   CCI范围: [{cci_stats["min"]:.1f}, {cci_stats["max"]:.1f}]')
                print(f'   CCI均值: {cci_stats["mean"]:.1f}')
                print(f'   CCI[20,30]区间: {len(cci_in_range)} 条 ({len(cci_in_range)/len(cci_data)*100:.1f}%)')
                
                if len(cci_in_range) == 0:
                    print('   ⚠️ 没有CCI[20,30]区间的信号！优化可能过于严格')
                elif len(cci_in_range) < len(cci_data) * 0.1:
                    print('   ⚠️ CCI[20,30]区间信号极少，可能影响交易频率')
        
        # 检查RSI优化是否生效
        if 'rsi' in df.columns:
            rsi_data = df.dropna(subset=['rsi'])
            if len(rsi_data) > 0:
                rsi_in_range = rsi_data[(rsi_data['rsi'] >= 70) & (rsi_data['rsi'] <= 100)]
                rsi_stats = rsi_data['rsi'].describe()
                
                print(f'\n📈 RSI配置检查:')
                print(f'   RSI数据: {len(rsi_data)} 条')
                print(f'   RSI范围: [{rsi_stats["min"]:.1f}, {rsi_stats["max"]:.1f}]')
                print(f'   RSI均值: {rsi_stats["mean"]:.1f}')
                print(f'   RSI[70,100]区间: {len(rsi_in_range)} 条 ({len(rsi_in_range)/len(rsi_data)*100:.1f}%)')
                
                if len(rsi_in_range) == 0:
                    print('   ⚠️ 没有RSI[70,100]区间的信号！优化可能过于严格')
        
        # 检查MACD优化是否生效
        if 'macd_hist' in df.columns:
            macd_data = df.dropna(subset=['macd_hist'])
            if len(macd_data) > 0:
                macd_strong = macd_data[macd_data['macd_hist'] > 0.01]
                macd_stats = macd_data['macd_hist'].describe()
                
                print(f'\n📈 MACD配置检查:')
                print(f'   MACD数据: {len(macd_data)} 条')
                print(f'   MACD范围: [{macd_stats["min"]:.4f}, {macd_stats["max"]:.4f}]')
                print(f'   MACD均值: {macd_stats["mean"]:.4f}')
                print(f'   MACD>0.01: {len(macd_strong)} 条 ({len(macd_strong)/len(macd_data)*100:.1f}%)')
        
        return df
        
    except Exception as e:
        print(f'❌ 优化影响分析失败: {e}')
        return None

def identify_analysis_errors():
    """识别分析错误"""
    print(f'\n🚨 分析错误识别')
    print('=' * 50)
    
    potential_errors = '''
🔍 可能的分析错误:

1. 📊 数据匹配问题:
   - 买入-卖出记录匹配可能不准确
   - 时间戳对应关系可能有误
   - 净收益计算可能有问题

2. 🎯 样本偏差:
   - 分析的历史数据可能不代表当前市场
   - 优化后的参数可能过于严格
   - 信号数量大幅减少影响整体表现

3. ⚙️ 配置生效问题:
   - 新配置可能没有正确加载
   - 策略代码可能没有使用新参数
   - 多因子条件可能过于严格

4. 📈 过度优化:
   - 基于历史数据的优化可能过拟合
   - 多个因子同时收紧可能过于保守
   - 忽略了信号数量与质量的平衡

5. 🕐 时间窗口问题:
   - 分析的时间窗口可能不合适
   - 市场环境变化影响因子有效性
   - 短期波动影响长期趋势判断

💡 需要重新检查的方面:
   - 当前配置是否真的生效
   - 信号数量是否大幅减少
   - 因子条件是否过于严格
   - 数据分析方法是否正确
   - 优化目标是否合理
'''
    
    print(potential_errors)

def suggest_corrective_actions():
    """建议纠正措施"""
    print(f'\n🔧 纠正措施建议')
    print('=' * 50)
    
    actions = '''
🚀 立即纠正措施:

1. 📊 重新验证当前配置:
   - 检查config.py中的实际配置值
   - 确认策略是否使用了新配置
   - 验证多因子条件是否过严

2. 🎯 调整优化策略:
   - 如果信号数量过少，适当放宽条件
   - 考虑单因子优化而非多因子同时收紧
   - 平衡信号质量与数量

3. 📈 重新分析数据:
   - 使用更近期的数据进行分析
   - 检查数据匹配的准确性
   - 验证收益计算的正确性

4. ⚙️ 渐进式优化:
   - 回退到较宽松的配置
   - 逐步收紧条件并验证效果
   - 避免一次性大幅调整

5. 🔍 深度诊断:
   - 分析当前低胜率的具体原因
   - 检查是否有系统性问题
   - 重新评估优化方向

🎯 优先级排序:
   1. 立即检查当前配置是否生效
   2. 分析信号数量变化
   3. 如果过严，适当放宽条件
   4. 重新验证优化效果
   5. 调整优化策略
'''
    
    print(actions)

def main():
    """主函数"""
    print('🚨 当前实际情况重新分析')
    print('=' * 60)
    
    print('🎯 问题: 当前胜率只有40多，可能分析有误')
    print('🔍 目标: 找出分析错误，制定纠正措施')
    
    # 获取当前真实表现
    real_performance = get_current_real_performance()
    
    # 分析优化影响
    optimization_data = analyze_optimization_impact()
    
    # 识别分析错误
    identify_analysis_errors()
    
    # 建议纠正措施
    suggest_corrective_actions()
    
    if real_performance:
        print(f'\n🎯 关键发现')
        print('=' * 40)
        print(f'📊 当前真实胜率: {real_performance["win_rate"]:.1f}%')
        print(f'📈 总交易数: {real_performance["total_trades"]}')
        print(f'💰 平均收益: {real_performance["avg_profit"]:.2f}%')
        
        if real_performance["win_rate"] < 50:
            print(f'🚨 确认: 当前胜率确实偏低')
            print(f'💡 需要: 重新评估优化策略')
        
        print(f'\n🔧 立即行动建议:')
        print(f'   1. 检查配置是否过于严格')
        print(f'   2. 分析信号数量变化')
        print(f'   3. 考虑适当放宽条件')
        print(f'   4. 重新制定优化计划')
    
    print(f'\n💡 感谢您指出问题！让我们重新制定正确的优化策略。')

if __name__ == '__main__':
    main()
