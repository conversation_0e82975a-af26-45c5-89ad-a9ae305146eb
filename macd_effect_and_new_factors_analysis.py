# coding=utf-8
"""
MACD优化效果验证与新因子挖掘
分析MACD优化实际效果，如果提升微乎其微，挖掘其他指标组合
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import itertools

def analyze_macd_optimization_reality():
    """分析MACD优化的实际效果"""
    print('📊 MACD优化实际效果验证')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'SELL' 
        AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 获取对应的买入数据
        buy_query = """
        SELECT * FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        conn.close()
        
        print(f'📈 卖出记录: {len(df)} 条')
        print(f'📈 买入记录: {len(buy_df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        buy_df['timestamp'] = pd.to_datetime(buy_df['timestamp'])
        
        # 分析不同时期的表现
        latest_300 = df.head(300)  # MACD优化后
        middle_300 = df.iloc[300:600]  # CCI优化后
        older_300 = df.iloc[600:900]  # 原始基准
        
        latest_win_rate = (latest_300['net_profit_pct_sell'] > 0).mean() * 100
        latest_avg_profit = latest_300['net_profit_pct_sell'].mean()
        
        middle_win_rate = (middle_300['net_profit_pct_sell'] > 0).mean() * 100
        middle_avg_profit = middle_300['net_profit_pct_sell'].mean()
        
        older_win_rate = (older_300['net_profit_pct_sell'] > 0).mean() * 100
        older_avg_profit = older_300['net_profit_pct_sell'].mean()
        
        print(f'\n📊 优化效果对比:')
        print(f'   最新300条 (MACD优化后): 胜率{latest_win_rate:.1f}%, 平均收益{latest_avg_profit:.2f}%')
        print(f'   中期300条 (CCI优化后):  胜率{middle_win_rate:.1f}%, 平均收益{middle_avg_profit:.2f}%')
        print(f'   早期300条 (原始基准):   胜率{older_win_rate:.1f}%, 平均收益{older_avg_profit:.2f}%')
        
        macd_effect = latest_win_rate - middle_win_rate
        total_effect = latest_win_rate - older_win_rate
        
        print(f'\n📈 优化效果评估:')
        print(f'   MACD优化效果: {macd_effect:+.1f}%')
        print(f'   总体优化效果: {total_effect:+.1f}%')
        
        # 评估MACD优化是否有效
        if abs(macd_effect) < 1.5:
            macd_effectiveness = 'MINIMAL'
            print(f'   🟡 MACD优化效果: 微乎其微 (提升{macd_effect:.1f}%)')
        elif macd_effect >= 1.5:
            macd_effectiveness = 'POSITIVE'
            print(f'   ✅ MACD优化效果: 有效 (提升{macd_effect:.1f}%)')
        else:
            macd_effectiveness = 'NEGATIVE'
            print(f'   ❌ MACD优化效果: 负面 (下降{abs(macd_effect):.1f}%)')
        
        return latest_300, buy_df, macd_effectiveness, macd_effect, total_effect
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None, None, None

def deep_factor_mining(latest_trades, buy_df):
    """深度挖掘新的因子组合"""
    print(f'\n🔍 深度因子挖掘与组合分析')
    print('=' * 60)
    
    # 匹配买入数据获取完整因子信息
    matched_data = []
    
    for _, sell_row in latest_trades.iterrows():
        symbol = sell_row['symbol']
        sell_time = sell_row['timestamp']
        
        # 找到对应的买入记录
        symbol_buys = buy_df[buy_df['symbol'] == symbol].copy()
        if len(symbol_buys) > 0:
            symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
            recent_buy = symbol_buys[symbol_buys['timestamp'] < sell_time]
            
            if len(recent_buy) > 0:
                recent_buy = recent_buy.iloc[-1]
                
                combined_row = {
                    'symbol': symbol,
                    'net_profit_pct_sell': sell_row['net_profit_pct_sell'],
                    'sell_reason': sell_row.get('sell_reason', ''),
                    'cci': recent_buy.get('cci'),
                    'rsi': recent_buy.get('rsi'),
                    'adx': recent_buy.get('adx'),
                    'macd_hist': recent_buy.get('macd_hist'),
                    'macd': recent_buy.get('macd'),
                    'atr_pct': recent_buy.get('atr_pct'),
                    'bb_width': recent_buy.get('bb_width'),
                    'bb_position': recent_buy.get('bb_position'),
                    'overall_score': recent_buy.get('overall_score'),
                    'volume_ratio': recent_buy.get('volume_ratio'),
                    'price_change_pct': recent_buy.get('price_change_pct'),
                }
                matched_data.append(combined_row)
    
    matched_df = pd.DataFrame(matched_data)
    print(f'📊 成功匹配: {len(matched_df)} 条最新交易')
    
    if len(matched_df) == 0:
        print('⚠️ 没有匹配的数据')
        return None
    
    # 分析单个因子的表现
    profitable_trades = matched_df[matched_df['net_profit_pct_sell'] > 0]
    losing_trades = matched_df[matched_df['net_profit_pct_sell'] <= 0]
    
    print(f'\n📈 当前交易分布:')
    print(f'   盈利交易: {len(profitable_trades)} 条 ({len(profitable_trades)/len(matched_df)*100:.1f}%)')
    print(f'   亏损交易: {len(losing_trades)} 条 ({len(losing_trades)/len(matched_df)*100:.1f}%)')
    
    # 分析所有可用因子
    available_factors = []
    for col in matched_df.columns:
        if col not in ['symbol', 'net_profit_pct_sell', 'sell_reason'] and matched_df[col].notna().sum() > 50:
            available_factors.append(col)
    
    print(f'\n🔍 可用因子: {len(available_factors)} 个')
    print(f'   {", ".join(available_factors)}')
    
    return matched_df, available_factors

def analyze_factor_combinations(matched_df, available_factors):
    """分析因子组合效果"""
    print(f'\n🚀 因子组合效果分析')
    print('=' * 60)
    
    combination_results = []
    
    # 分析双因子组合
    print(f'📊 分析双因子组合...')
    
    for factor1, factor2 in itertools.combinations(available_factors, 2):
        try:
            # 获取两个因子的数据
            data = matched_df.dropna(subset=[factor1, factor2])
            if len(data) < 30:
                continue
            
            # 计算每个因子的分位数
            factor1_q75 = data[factor1].quantile(0.75)
            factor1_q25 = data[factor1].quantile(0.25)
            factor2_q75 = data[factor2].quantile(0.75)
            factor2_q25 = data[factor2].quantile(0.25)
            
            # 测试不同的组合条件
            combinations_to_test = [
                (f'{factor1}_high_{factor2}_high', 
                 (data[factor1] >= factor1_q75) & (data[factor2] >= factor2_q75)),
                (f'{factor1}_low_{factor2}_low', 
                 (data[factor1] <= factor1_q25) & (data[factor2] <= factor2_q25)),
                (f'{factor1}_high_{factor2}_low', 
                 (data[factor1] >= factor1_q75) & (data[factor2] <= factor2_q25)),
                (f'{factor1}_low_{factor2}_high', 
                 (data[factor1] <= factor1_q25) & (data[factor2] >= factor2_q75)),
            ]
            
            for combo_name, condition in combinations_to_test:
                combo_data = data[condition]
                
                if len(combo_data) >= 10:  # 确保样本量足够
                    win_rate = (combo_data['net_profit_pct_sell'] > 0).mean() * 100
                    avg_profit = combo_data['net_profit_pct_sell'].mean()
                    sample_count = len(combo_data)
                    
                    # 计算相对于整体的提升
                    overall_win_rate = (data['net_profit_pct_sell'] > 0).mean() * 100
                    win_rate_improvement = win_rate - overall_win_rate
                    
                    combination_results.append({
                        'combination': combo_name,
                        'factors': f'{factor1} + {factor2}',
                        'win_rate': win_rate,
                        'avg_profit': avg_profit,
                        'sample_count': sample_count,
                        'improvement': win_rate_improvement,
                        'score': win_rate_improvement * (sample_count / 100)  # 考虑样本量的综合评分
                    })
        
        except Exception as e:
            continue
    
    # 按综合评分排序
    combination_results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f'\n🏆 最佳因子组合 (前10名):')
    print(f'排名  组合名称                           胜率%   样本数  提升%   评分')
    print(f'-' * 80)
    
    top_combinations = combination_results[:10]
    for i, result in enumerate(top_combinations, 1):
        print(f'{i:2d}   {result["combination"][:30]:<30} {result["win_rate"]:6.1f} {result["sample_count"]:6d} {result["improvement"]:6.1f} {result["score"]:6.1f}')
    
    return top_combinations

def analyze_triple_factor_combinations(matched_df, available_factors, top_dual_factors):
    """分析三因子组合"""
    print(f'\n🔥 三因子组合深度挖掘')
    print('=' * 60)
    
    # 从最佳双因子组合中提取因子
    promising_factors = set()
    for combo in top_dual_factors[:5]:  # 取前5个最佳组合
        factors = combo['factors'].split(' + ')
        promising_factors.update(factors)
    
    promising_factors = list(promising_factors)
    print(f'🎯 重点分析因子: {", ".join(promising_factors)}')
    
    triple_results = []
    
    # 分析三因子组合
    for factor1, factor2, factor3 in itertools.combinations(promising_factors, 3):
        try:
            data = matched_df.dropna(subset=[factor1, factor2, factor3])
            if len(data) < 50:
                continue
            
            # 计算分位数
            f1_q75 = data[factor1].quantile(0.75)
            f2_q75 = data[factor2].quantile(0.75)
            f3_q75 = data[factor3].quantile(0.75)
            
            f1_q25 = data[factor1].quantile(0.25)
            f2_q25 = data[factor2].quantile(0.25)
            f3_q25 = data[factor3].quantile(0.25)
            
            # 测试几种有希望的组合
            test_combinations = [
                ('all_high', (data[factor1] >= f1_q75) & (data[factor2] >= f2_q75) & (data[factor3] >= f3_q75)),
                ('all_low', (data[factor1] <= f1_q25) & (data[factor2] <= f2_q25) & (data[factor3] <= f3_q25)),
                ('mixed_1', (data[factor1] >= f1_q75) & (data[factor2] <= f2_q25) & (data[factor3] >= f3_q75)),
                ('mixed_2', (data[factor1] <= f1_q25) & (data[factor2] >= f2_q75) & (data[factor3] <= f3_q25)),
            ]
            
            for combo_type, condition in test_combinations:
                combo_data = data[condition]
                
                if len(combo_data) >= 8:  # 三因子组合样本要求可以稍低
                    win_rate = (combo_data['net_profit_pct_sell'] > 0).mean() * 100
                    avg_profit = combo_data['net_profit_pct_sell'].mean()
                    sample_count = len(combo_data)
                    
                    overall_win_rate = (data['net_profit_pct_sell'] > 0).mean() * 100
                    improvement = win_rate - overall_win_rate
                    
                    if improvement > 5:  # 只保留提升明显的组合
                        triple_results.append({
                            'combination': f'{factor1}_{factor2}_{factor3}_{combo_type}',
                            'factors': f'{factor1} + {factor2} + {factor3}',
                            'type': combo_type,
                            'win_rate': win_rate,
                            'avg_profit': avg_profit,
                            'sample_count': sample_count,
                            'improvement': improvement,
                            'score': improvement * (sample_count / 50)
                        })
        
        except Exception as e:
            continue
    
    # 按评分排序
    triple_results.sort(key=lambda x: x['score'], reverse=True)
    
    if triple_results:
        print(f'\n🚀 最佳三因子组合 (前5名):')
        print(f'排名  因子组合                         类型      胜率%   样本数  提升%   评分')
        print(f'-' * 85)
        
        for i, result in enumerate(triple_results[:5], 1):
            print(f'{i:2d}   {result["factors"][:25]:<25} {result["type"]:<8} {result["win_rate"]:6.1f} {result["sample_count"]:6d} {result["improvement"]:6.1f} {result["score"]:6.1f}')
    else:
        print(f'\n📊 未发现显著的三因子组合')
    
    return triple_results

def generate_new_optimization_strategy(macd_effectiveness, top_combinations, triple_combinations):
    """生成新的优化策略"""
    print(f'\n🎯 新优化策略制定')
    print('=' * 60)
    
    if macd_effectiveness == 'MINIMAL':
        print(f'🟡 MACD优化效果微乎其微，需要新的优化方向')
        
        if top_combinations:
            best_combo = top_combinations[0]
            print(f'\n🚀 建议策略：因子组合优化')
            print(f'   最佳组合: {best_combo["factors"]}')
            print(f'   组合条件: {best_combo["combination"]}')
            print(f'   预期胜率: {best_combo["win_rate"]:.1f}%')
            print(f'   胜率提升: +{best_combo["improvement"]:.1f}%')
            print(f'   样本支撑: {best_combo["sample_count"]}条')
            
            # 解析组合条件并生成配置建议
            combo_parts = best_combo["combination"].split('_')
            factors = best_combo["factors"].split(' + ')
            
            print(f'\n⚙️ 配置建议:')
            for i, factor in enumerate(factors):
                if i < len(combo_parts) - 1:
                    condition = combo_parts[i*2 + 1] if i*2 + 1 < len(combo_parts) else 'unknown'
                    if condition == 'high':
                        print(f'   {factor}: 使用高值区间 (>75分位数)')
                    elif condition == 'low':
                        print(f'   {factor}: 使用低值区间 (<25分位数)')
        
        if triple_combinations:
            best_triple = triple_combinations[0]
            print(f'\n🔥 高级策略：三因子组合')
            print(f'   因子组合: {best_triple["factors"]}')
            print(f'   组合类型: {best_triple["type"]}')
            print(f'   预期胜率: {best_triple["win_rate"]:.1f}%')
            print(f'   胜率提升: +{best_triple["improvement"]:.1f}%')
            print(f'   样本支撑: {best_triple["sample_count"]}条')
    
    elif macd_effectiveness == 'POSITIVE':
        print(f'✅ MACD优化有效，可以继续渐进式优化')
        print(f'🔧 建议：在MACD基础上叠加最佳因子组合')
        
        if top_combinations:
            best_combo = top_combinations[0]
            print(f'   叠加组合: {best_combo["factors"]}')
            print(f'   额外提升: +{best_combo["improvement"]:.1f}%')
    
    else:
        print(f'❌ MACD优化有负面效果，建议回退并尝试新方向')
        print(f'🔄 建议：回退MACD配置，采用因子组合策略')
    
    return top_combinations[0] if top_combinations else None

def main():
    """主函数"""
    print('🔍 MACD优化效果验证与新因子挖掘')
    print('=' * 60)
    
    print('🎯 目标: 验证MACD优化效果，如果微乎其微则挖掘新的因子组合')
    
    # 分析MACD优化实际效果
    result = analyze_macd_optimization_reality()
    
    if result[0] is not None:
        latest_trades, buy_df, macd_effectiveness, macd_effect, total_effect = result
        
        # 深度因子挖掘
        mining_result = deep_factor_mining(latest_trades, buy_df)
        
        if mining_result and mining_result[0] is not None:
            matched_df, available_factors = mining_result
            
            # 分析因子组合
            top_combinations = analyze_factor_combinations(matched_df, available_factors)
            
            # 分析三因子组合
            triple_combinations = analyze_triple_factor_combinations(matched_df, available_factors, top_combinations)
            
            # 生成新优化策略
            best_strategy = generate_new_optimization_strategy(macd_effectiveness, top_combinations, triple_combinations)
            
            print(f'\n🎯 分析总结')
            print('=' * 40)
            print(f'📊 MACD优化效果: {macd_effectiveness}')
            print(f'📈 MACD胜率变化: {macd_effect:+.1f}%')
            print(f'🔍 发现 {len(top_combinations)} 个有效因子组合')
            print(f'🚀 发现 {len(triple_combinations)} 个三因子组合')
            
            if best_strategy:
                print(f'💡 推荐策略: {best_strategy["factors"]}组合')
                print(f'📊 预期提升: +{best_strategy["improvement"]:.1f}%胜率')
                return best_strategy
            else:
                print(f'📊 建议: 继续当前配置，寻找其他优化方向')
                return None
        
        else:
            print('❌ 因子挖掘失败')
            return None
    
    else:
        print('❌ 数据分析失败，请检查数据库')
        return None

if __name__ == '__main__':
    result = main()
    if result:
        print(f'\n💡 立即行动建议: 实施{result["factors"]}组合优化')
