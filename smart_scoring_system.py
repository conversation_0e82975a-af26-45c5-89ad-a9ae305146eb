# coding=utf-8
"""
智能评分系统
基于胜率分析的智能买入评分系统，支持动态仓位管理和自适应阈值优化
"""

import numpy as np
import pandas as pd
from datetime import datetime
import sqlite3
from config import get_config_value

class SmartBuyingScoreSystem:
    """智能买入评分系统"""
    
    def __init__(self, context=None):
        self.context = context
        self.score_history = []
        self.performance_history = []
        self.adaptive_threshold = None
        
        # 加载配置
        self.config = get_config_value('SMART_SCORING_CONFIG', {})
        self.weights = get_config_value('SCORING_WEIGHTS', {})
        self.thresholds = get_config_value('SCORING_THRESHOLDS', {})
        self.position_config = get_config_value('DYNAMIC_POSITION_CONFIG', {})
        
        # 默认配置
        self.enabled = self.config.get('enable_smart_scoring', True)
        self.log_details = self.config.get('log_scoring_details', True)
        
    def calculate_buy_score(self, data):
        """
        计算买入评分 (0-100分)
        
        参数:
        - data: 股票数据字典
        
        返回:
        - score: 总评分 (0-100)
        - details: 评分详情字典
        """
        if not self.enabled:
            return 0, {}
            
        score = 0
        details = {}
        
        try:
            # 1. 均线偏离评分 (0-35分)
            ma20_distance_score = self._calculate_ma20_distance_score(data)
            score += ma20_distance_score
            details['ma20_distance_score'] = ma20_distance_score
            
            # 2. 波动率评分 (0-25分)
            volatility_score = self._calculate_volatility_score(data)
            score += volatility_score
            details['volatility_score'] = volatility_score
            
            # 3. 价格动量评分 (0-20分)
            momentum_score = self._calculate_momentum_score(data)
            score += momentum_score
            details['momentum_score'] = momentum_score
            
            # 4. 技术确认评分 (0-20分)
            technical_score = self._calculate_technical_score(data)
            score += technical_score
            details['technical_score'] = technical_score
            
            # 总分限制在100分以内
            total_score = min(score, 100)
            details['total_score'] = total_score
            
            return total_score, details
            
        except Exception as e:
            if self.context:
                self.context.log.error(f"计算买入评分失败: {e}")
            return 0, {'error': str(e)}
    
    def _calculate_ma20_distance_score(self, data):
        """计算20日均线偏离评分"""
        ma20_distance = data.get('ma20_distance_pct', 0)
        
        if ma20_distance <= -10:      # 深度偏离 (超跌严重)
            return 35
        elif ma20_distance <= -7:     # 中度偏离 (超跌明显)
            return 25
        elif ma20_distance <= -5:     # 轻度偏离 (超跌轻微)
            return 15
        elif ma20_distance <= -3:     # 微度偏离 (接近均线)
            return 5
        else:                         # 均线之上
            return 0
    
    def _calculate_volatility_score(self, data):
        """计算波动率评分"""
        atr_normalized = data.get('atr_normalized', 0)
        
        if atr_normalized >= 3.5:     # 极高波动 (机会与风险并存)
            return 25
        elif atr_normalized >= 2.8:   # 高波动 (较好的反弹机会)
            return 20
        elif atr_normalized >= 2.0:   # 中等波动 (一般机会)
            return 10
        elif atr_normalized >= 1.5:   # 低波动 (机会较小)
            return 5
        else:                         # 极低波动
            return 0
    
    def _calculate_momentum_score(self, data):
        """计算价格动量评分"""
        price_change = data.get('price_change_pct', 0)
        
        if price_change <= -10:       # 大幅下跌 (强反弹机会)
            return 20
        elif price_change <= -8:      # 中度下跌 (较好反弹机会)
            return 15
        elif price_change <= -5:      # 轻度下跌 (一般反弹机会)
            return 10
        elif price_change <= -3:      # 微度下跌 (小幅反弹机会)
            return 5
        else:                         # 上涨或平盘
            return 0
    
    def _calculate_technical_score(self, data):
        """计算技术确认评分"""
        score = 0
        
        # RSI超卖确认 (0-10分)
        rsi_5d = data.get('rsi_5d', 50)
        if rsi_5d <= 20:              # 严重超卖
            score += 10
        elif rsi_5d <= 30:            # 超卖
            score += 7
        elif rsi_5d <= 40:            # 偏弱
            score += 3
        
        # 布林带位置确认 (0-10分)
        bb_position = data.get('bb_position_20', 50)
        if bb_position <= 10:         # 接近下轨
            score += 10
        elif bb_position <= 20:       # 偏向下轨
            score += 7
        elif bb_position <= 30:       # 下半区
            score += 3
        
        return score
    
    def get_buy_signal(self, data, custom_threshold=None):
        """
        获取买入信号
        
        参数:
        - data: 股票数据字典
        - custom_threshold: 自定义阈值
        
        返回:
        - buy_signal: 是否买入 (bool)
        - score: 评分
        - details: 评分详情
        """
        score, details = self.calculate_buy_score(data)
        
        # 确定阈值
        threshold = custom_threshold or self._get_current_threshold()
        
        # 买入信号
        buy_signal = score >= threshold
        
        # 记录详情
        if self.log_details and self.context:
            symbol = data.get('symbol', 'Unknown')
            self.context.log.info(
                f"📊 {symbol} 评分: {score:.1f} (阈值: {threshold}) "
                f"- MA20: {details.get('ma20_distance_score', 0):.1f}, "
                f"波动: {details.get('volatility_score', 0):.1f}, "
                f"动量: {details.get('momentum_score', 0):.1f}, "
                f"技术: {details.get('technical_score', 0):.1f}"
            )
        
        return buy_signal, score, details
    
    def get_position_size(self, score):
        """
        根据评分确定仓位大小
        
        参数:
        - score: 评分
        
        返回:
        - position_ratio: 仓位比例 (0-1)
        """
        if not self.config.get('enable_dynamic_position', True):
            return 1.0  # 固定满仓
        
        if score >= 90:
            return self.position_config.get('score_90_position', 1.0)
        elif score >= 80:
            return self.position_config.get('score_80_position', 0.8)
        elif score >= 70:
            return self.position_config.get('score_70_position', 0.6)
        elif score >= 60:
            return self.position_config.get('score_60_position', 0.4)
        else:
            return self.position_config.get('below_60_position', 0.0)
    
    def _get_current_threshold(self):
        """获取当前阈值"""
        if self.config.get('enable_adaptive_threshold', True) and self.adaptive_threshold:
            return self.adaptive_threshold
        else:
            return self.thresholds.get('min_buy_score', 70)
    
    def update_performance(self, score, actual_return):
        """
        更新历史表现数据
        
        参数:
        - score: 买入时的评分
        - actual_return: 实际收益率
        """
        self.score_history.append(score)
        self.performance_history.append(actual_return)
        
        # 定期优化阈值
        update_freq = self.thresholds.get('threshold_update_frequency', 50)
        if len(self.score_history) % update_freq == 0:
            self._optimize_threshold()
    
    def _optimize_threshold(self):
        """优化评分阈值"""
        if len(self.score_history) < 30:  # 样本不足
            return
            
        try:
            best_threshold = self.thresholds.get('min_buy_score', 70)
            best_sharpe = 0
            
            # 测试不同阈值的效果
            for threshold in range(60, 95, 5):
                mask = np.array(self.score_history) >= threshold
                if mask.sum() >= 10:  # 至少10个样本
                    returns = np.array(self.performance_history)[mask]
                    if len(returns) > 0 and returns.std() > 0:
                        sharpe = returns.mean() / returns.std()
                        if sharpe > best_sharpe:
                            best_sharpe = sharpe
                            best_threshold = threshold
            
            # 更新自适应阈值
            self.adaptive_threshold = best_threshold
            
            if self.context:
                self.context.log.info(
                    f"🎯 阈值优化: 新阈值 {best_threshold} (夏普比率: {best_sharpe:.3f})"
                )
                
        except Exception as e:
            if self.context:
                self.context.log.error(f"阈值优化失败: {e}")
    
    def get_system_status(self):
        """获取系统状态"""
        return {
            'enabled': self.enabled,
            'current_threshold': self._get_current_threshold(),
            'adaptive_threshold': self.adaptive_threshold,
            'history_count': len(self.score_history),
            'avg_score': np.mean(self.score_history) if self.score_history else 0,
            'avg_return': np.mean(self.performance_history) if self.performance_history else 0
        }

class LegacyStrategySystem:
    """传统策略系统 (作为备选)"""
    
    def __init__(self, context=None):
        self.context = context
        
    def get_buy_signal(self, data):
        """传统买入信号逻辑"""
        try:
            # 原有的最佳双指标组合逻辑
            ma20_distance = data.get('ma20_distance_pct', 0)
            atr_normalized = data.get('atr_normalized', 0)
            
            # 基于胜率分析的最佳条件
            if ma20_distance <= -5.0 and atr_normalized >= 2.5:
                return True, 100, {'strategy': 'legacy_best_combo'}
            
            return False, 0, {'strategy': 'legacy_no_signal'}
            
        except Exception as e:
            if self.context:
                self.context.log.error(f"传统策略信号计算失败: {e}")
            return False, 0, {'error': str(e)}

def get_scoring_system(context=None):
    """获取评分系统实例"""
    config = get_config_value('SMART_SCORING_CONFIG', {})
    
    if config.get('enable_smart_scoring', True):
        return SmartBuyingScoreSystem(context)
    elif config.get('enable_legacy_strategy', False):
        return LegacyStrategySystem(context)
    else:
        return SmartBuyingScoreSystem(context)  # 默认使用智能评分系统
