# coding=utf-8
"""
增强因子计算引擎
计算所有技术指标和自定义因子，为策略胜率提升提供数据支持
"""

import numpy as np
import pandas as pd
import talib
from typing import Dict, Any, Optional, Tuple
import warnings
import sqlite3
import os
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

class EnhancedFactorEngine:
    """增强因子计算引擎"""
    
    def __init__(self, context=None):
        self.context = context
        self.factor_cache = {}
        self.timeseries_db_path = 'data/timeseries_factors.db'
        # 延迟初始化时序数据库
        self._timeseries_db_initialized = False
        
    def calculate_all_factors(self, hist_data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """计算所有因子"""
        try:
            # 提取基础数据
            close_prices = hist_data['close'].values.astype(np.float64)
            high_prices = hist_data['high'].values.astype(np.float64)
            low_prices = hist_data['low'].values.astype(np.float64)
            volume_data = hist_data['volume'].values.astype(np.float64)
            open_prices = hist_data['open'].values.astype(np.float64)

            factors = {}

            # 1. 基础价格因子
            factors.update(self._calculate_price_factors(close_prices, high_prices, low_prices, open_prices))

            # 2. 移动平均线因子
            factors.update(self._calculate_ma_factors(close_prices))

            # 3. 趋势指标因子
            factors.update(self._calculate_trend_factors(close_prices, high_prices, low_prices))

            # 4. 超买超卖指标因子
            factors.update(self._calculate_oscillator_factors(close_prices, high_prices, low_prices))

            # 5. 成交量指标因子
            factors.update(self._calculate_volume_factors(close_prices, volume_data))

            # 6. 波动率指标因子
            factors.update(self._calculate_volatility_factors(close_prices, high_prices, low_prices))

            # 7. 形态识别因子
            factors.update(self._calculate_pattern_factors(close_prices, high_prices, low_prices, volume_data))

            # 8. 自定义策略因子
            factors.update(self._calculate_custom_factors(close_prices, high_prices, low_prices, volume_data))

            # 9. 实时因子计算 (新增)
            factors.update(self._calculate_realtime_factors(hist_data, symbol))

            # 🚀 多因子策略所需的关键评分计算
            try:
                self._calculate_multifactor_scores(factors, hist_data)
            except Exception as e:
                if self.context and hasattr(self.context, 'log'):
                    self.context.log.error(f"多因子评分计算异常: {e}")
                else:
                    print(f"多因子评分计算异常: {e}")
                import traceback
                print(f"多因子评分详细错误: {traceback.format_exc()}")

            # 🔧 将因子名映射为数据库字段名格式
            mapped_factors = self._map_factor_names_to_db_fields(factors)

            return mapped_factors

        except Exception as e:
            if self.context:
                self.context.log.error(f"计算因子异常 {symbol}: {str(e)}")
            return {}

    def _map_factor_names_to_db_fields(self, factors: Dict[str, Any]) -> Dict[str, Any]:
        """将因子名映射为数据库字段名格式"""
        # 定义因子名映射关系（增强因子名 -> 数据库实际字段名）
        factor_mapping = {
            # 基础价格因子
            'current_price': 'price',  # 使用基础price字段
            'price_change_pct': 'price_change_pct',
            'momentum_3d': 'price_momentum_3d',
            'momentum_5d': 'price_momentum_5d',
            'momentum_10d': 'price_momentum_10d',
            'Price_Change': 'price_change_pct',
            'Price_Position_20d': 'price_position_in_range',
            'Price_Momentum_3d': 'price_momentum_3d',
            'Price_Momentum_5d': 'price_momentum_5d',
            'Price_Momentum_10d': 'price_momentum_10d',

            # 移动平均线因子
            'ma3': 'ma3_buy',  # 映射到实际存在的字段
            'ma5': 'ma5',
            'ma7': 'ma7_buy',
            'ma10': 'ma10',
            'ma20': 'ma20',
            'ma60': 'ma60',
            'ma_system_bullish': 'ma_system_status',
            'Ma3_Distance_Pct': 'ma5_distance_pct',
            'Ma5_Distance_Pct': 'ma5_distance_pct',
            'Ma7_Distance_Pct': 'ma5_distance_pct',
            'Ma10_Distance_Pct': 'ma10_distance_pct',
            'Ma20_Distance_Pct': 'ma20_distance_pct',

            # 趋势指标因子
            'macd': 'macd',
            'macd_signal': 'macd_signal',
            'macd_hist': 'macd_hist',
            'adx': 'adx',
            'trix': 'trix_buy',
            'Macd_Cross_Signal': 'macd_signal',

            # 超买超卖指标因子
            'rsi_14': 'rsi',
            'cci': 'cci',
            'Rsi_6': 'rsi_3d',
            'Rsi_14': 'rsi',
            'Rsi_21': 'rsi_20d',

            # 成交量指标因子
            'volume_ratio_20': 'relative_volume',
            'volume_change_pct': 'volume_change_rate',
            'Volume_Ratio_5': 'volume_ma5_ratio',
            'Volume_Ratio_10': 'volume_ma10_ratio',
            'Volume_Ratio_20': 'volume_ma20_ratio',
            'Volume_Change_Rate': 'volume_change_rate',
            'Volume': 'volume',

            # 波动率指标因子
            'atr_14_pct': 'atr_pct',
            'bb_width': 'bb_width',
            'bb_position': 'bb_position',
            'Atr_7': 'atr_3d',
            'Atr_14': 'atr_5d',
            'Atr_21': 'atr_10d',
            'Atr_7_Pct': 'atr_pct',
            'Atr_14_Pct': 'atr_pct',
            'Atr_21_Pct': 'atr_pct',
            'Bb_Width': 'bb_width',
            'Bb_Position': 'bb_position',
            'Bb_Lower': 'bb_lower_20',
            'Bb_Middle': 'bb_middle_20',
            'Bb_Upper': 'bb_upper_20',
            'Bb_Squeeze': 'bb_squeeze',

            # 其他指标
            'ma_cross_buy_signal': 'ma_cross_buy_signal_buy',

            # 🚀 多因子策略评分 (保持原始字段名)
            'overall_score': 'overall_score',
            'technical_score': 'technical_score',
            'momentum_score': 'momentum_score',
            'volume_score': 'volume_score',
            'volatility_score': 'volatility_score',
            'trend_score': 'trend_score',
            'buy_signal_strength': 'buy_signal_strength',
            'risk_adjusted_score': 'risk_adjusted_score'
        }

        mapped_factors = {}

        # 映射已定义的因子
        for original_name, value in factors.items():
            if original_name in factor_mapping:
                db_field_name = factor_mapping[original_name]
                mapped_factors[db_field_name] = value
            else:
                # 对于未映射的因子，使用标准化命名
                db_field_name = self._standardize_field_name(original_name)
                mapped_factors[db_field_name] = value

        return mapped_factors

    def _standardize_field_name(self, field_name: str) -> str:
        """标准化字段名为数据库格式"""
        # 将下划线分隔的字段名转换为首字母大写格式
        parts = field_name.split('_')
        standardized = '_'.join(word.capitalize() for word in parts)
        return standardized
    
    def _calculate_price_factors(self, close, high, low, open_prices) -> Dict[str, float]:
        """计算价格相关因子"""
        factors = {}
        
        try:
            # 当前价格
            factors['current_price'] = float(close[-1])
            
            # 价格变化
            if len(close) >= 2:
                factors['price_change'] = float(close[-1] - close[-2])
                factors['price_change_pct'] = float((close[-1] - close[-2]) / close[-2] * 100)
            
            # 价格位置
            if len(close) >= 20:
                period_high = np.max(high[-20:])
                period_low = np.min(low[-20:])
                factors['price_position_20d'] = float((close[-1] - period_low) / (period_high - period_low) * 100)
                factors['distance_from_high'] = float((period_high - close[-1]) / period_high * 100)
                factors['distance_from_low'] = float((close[-1] - period_low) / period_low * 100)
            
            # 价格动量
            if len(close) >= 10:
                factors['momentum_5d'] = float((close[-1] - close[-6]) / close[-6] * 100)
                factors['momentum_10d'] = float((close[-1] - close[-11]) / close[-11] * 100)
            
            # 价格反转
            if len(close) >= 3:
                factors['price_reversal_3d'] = float((close[-3] - close[-1]) / close[-1] * 100)
            
        except Exception as e:
            pass

        return factors

    def _calculate_realtime_factors(self, hist_data: pd.DataFrame, symbol: str) -> Dict[str, float]:
        """计算实时因子"""
        factors = {}

        try:
            if len(hist_data) == 0:
                return factors

            # 获取当前价格和开盘价
            current_price = hist_data['close'].iloc[-1]
            open_price = hist_data['open'].iloc[-1]

            # 1. 开盘动量因子
            if open_price > 0:
                opening_momentum = (current_price - open_price) / open_price
                factors['opening_momentum'] = float(opening_momentum)

                # 开盘动量强度分类
                factors['opening_momentum_strong'] = 1 if abs(opening_momentum) > 0.02 else 0
                factors['opening_momentum_positive'] = 1 if opening_momentum > 0 else 0

            # 2. 成交量突破因子 (如果有成交量数据)
            if 'volume' in hist_data.columns and len(hist_data) >= 5:
                current_volume = hist_data['volume'].iloc[-1]
                avg_volume_5d = hist_data['volume'].iloc[-5:].mean()

                if avg_volume_5d > 0:
                    volume_ratio = current_volume / avg_volume_5d
                    factors['volume_breakthrough'] = float(volume_ratio)
                    factors['volume_breakthrough_strong'] = 1 if volume_ratio > 1.5 else 0

            # 3. 价格突破确认因子
            if len(hist_data) >= 20:
                # 20日高点突破
                high_20d = hist_data['high'].iloc[-20:].max()
                factors['price_breakthrough_20d'] = 1 if current_price > high_20d * 0.99 else 0

                # 价格相对位置
                low_20d = hist_data['low'].iloc[-20:].min()
                if high_20d > low_20d:
                    price_position = (current_price - low_20d) / (high_20d - low_20d)
                    factors['price_position_pct'] = float(price_position * 100)

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.error(f"实时因子计算异常 {symbol}: {e}")
            pass

        return factors
    
    def _calculate_ma_factors(self, close) -> Dict[str, float]:
        """计算移动平均线因子"""
        factors = {}
        
        try:
            # 各周期移动平均线
            ma_periods = [3, 5, 7, 10, 15, 20, 30, 60, 120]
            for period in ma_periods:
                if len(close) >= period:
                    ma_value = talib.SMA(close, timeperiod=period)[-1]
                    factors[f'ma{period}'] = float(ma_value)
                    factors[f'ma{period}_distance_pct'] = float((close[-1] - ma_value) / ma_value * 100)
            
            # 移动平均线交叉信号
            if len(close) >= 20:
                ma5 = talib.SMA(close, timeperiod=5)
                ma10 = talib.SMA(close, timeperiod=10)
                ma20 = talib.SMA(close, timeperiod=20)
                
                factors['ma5_ma10_cross'] = 1 if ma5[-1] > ma10[-1] and ma5[-2] <= ma10[-2] else 0
                factors['ma10_ma20_cross'] = 1 if ma10[-1] > ma20[-1] and ma10[-2] <= ma20[-2] else 0
                factors['ma_system_bullish'] = 1 if ma5[-1] > ma10[-1] > ma20[-1] else 0
            
            # EMA指数移动平均
            ema_periods = [12, 26]
            for period in ema_periods:
                if len(close) >= period:
                    ema_value = talib.EMA(close, timeperiod=period)[-1]
                    factors[f'ema{period}'] = float(ema_value)
                    
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_trend_factors(self, close, high, low) -> Dict[str, float]:
        """计算趋势指标因子"""
        factors = {}

        # 获取配置的因子周期参数
        try:
            from config import FACTOR_REALTIME_CONFIG
            cci_period = FACTOR_REALTIME_CONFIG.get('cci_period', 14)
            rsi_period = FACTOR_REALTIME_CONFIG.get('rsi_period', 14)
            adx_period = FACTOR_REALTIME_CONFIG.get('adx_period', 14)
        except:
            # 如果配置不存在，使用默认值
            cci_period = 14
            rsi_period = 14
            adx_period = 14

        try:
            # MACD
            if len(close) >= 34:
                macd, macd_signal, macd_hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
                factors['macd'] = float(macd[-1])
                factors['macd_signal'] = float(macd_signal[-1])
                factors['macd_hist'] = float(macd_hist[-1])
                factors['macd_cross_signal'] = 1 if macd[-1] > macd_signal[-1] and macd[-2] <= macd_signal[-2] else 0
            
            # ADX趋势强度
            if len(close) >= adx_period:
                adx = talib.ADX(high, low, close, timeperiod=adx_period)[-1]
                factors['adx'] = float(adx)
                factors['adx_strong_trend'] = 1 if adx > 25 else 0
            
            # DMI方向指标
            if len(close) >= adx_period:
                dmi_plus = talib.PLUS_DI(high, low, close, timeperiod=adx_period)[-1]
                dmi_minus = talib.MINUS_DI(high, low, close, timeperiod=adx_period)[-1]
                factors['dmi_plus'] = float(dmi_plus)
                factors['dmi_minus'] = float(dmi_minus)
                factors['dmi_cross_signal'] = 1 if dmi_plus > dmi_minus else 0
            
            # TRIX
            if len(close) >= 30:
                trix = talib.TRIX(close, timeperiod=14)[-1] * 100
                factors['trix'] = float(trix)
                if len(close) >= 31:
                    trix_prev = talib.TRIX(close, timeperiod=14)[-2] * 100
                    factors['trix_signal'] = 1 if trix > 0 and trix_prev <= 0 else 0
            
            # Aroon
            if len(close) >= 25:
                aroon_down, aroon_up = talib.AROON(high, low, timeperiod=25)
                factors['aroon_up'] = float(aroon_up[-1])
                factors['aroon_down'] = float(aroon_down[-1])
                factors['aroon_signal'] = 1 if aroon_up[-1] > aroon_down[-1] else 0
                
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_oscillator_factors(self, close, high, low) -> Dict[str, float]:
        """计算超买超卖指标因子"""
        factors = {}

        # 获取配置的因子周期参数
        try:
            from config import FACTOR_REALTIME_CONFIG
            cci_period = FACTOR_REALTIME_CONFIG.get('cci_period', 14)
            rsi_period = FACTOR_REALTIME_CONFIG.get('rsi_period', 14)
        except:
            # 如果配置不存在，使用默认值
            cci_period = 14
            rsi_period = 14

        try:
            # RSI
            rsi_periods = [6, rsi_period, 21]  # 使用配置的RSI周期
            for period in rsi_periods:
                if len(close) >= period + 1:
                    rsi = talib.RSI(close, timeperiod=period)[-1]
                    factors[f'rsi_{period}'] = float(rsi)
                    factors[f'rsi_{period}_oversold'] = 1 if rsi < 30 else 0
                    factors[f'rsi_{period}_overbought'] = 1 if rsi > 70 else 0
            
            # CCI
            if len(close) >= cci_period:
                cci = talib.CCI(high, low, close, timeperiod=cci_period)[-1]
                factors['cci'] = float(cci)
                factors['cci_oversold'] = 1 if cci < -100 else 0
                factors['cci_overbought'] = 1 if cci > 100 else 0
            
            # Williams %R
            if len(close) >= 14:
                willr = talib.WILLR(high, low, close, timeperiod=14)[-1]
                factors['willr'] = float(willr)
                factors['willr_oversold'] = 1 if willr < -80 else 0
                factors['willr_overbought'] = 1 if willr > -20 else 0
            
            # Stochastic
            if len(close) >= 14:
                slowk, slowd = talib.STOCH(high, low, close, fastk_period=14, slowk_period=3, slowd_period=3)
                factors['stoch_k'] = float(slowk[-1])
                factors['stoch_d'] = float(slowd[-1])
                factors['stoch_oversold'] = 1 if slowk[-1] < 20 and slowd[-1] < 20 else 0
                factors['stoch_overbought'] = 1 if slowk[-1] > 80 and slowd[-1] > 80 else 0
            
            # Ultimate Oscillator
            if len(close) >= 28:
                ultosc = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)[-1]
                factors['ultosc'] = float(ultosc)
                factors['ultosc_oversold'] = 1 if ultosc < 30 else 0
                factors['ultosc_overbought'] = 1 if ultosc > 70 else 0
                
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_volume_factors(self, close, volume) -> Dict[str, float]:
        """计算成交量指标因子"""
        factors = {}
        
        try:
            # 基础成交量指标
            factors['volume'] = float(volume[-1])
            
            # 成交量移动平均
            volume_ma_periods = [5, 10, 20]
            for period in volume_ma_periods:
                if len(volume) >= period:
                    volume_ma = talib.SMA(volume, timeperiod=period)[-1]
                    factors[f'volume_ma{period}'] = float(volume_ma)
                    factors[f'volume_ratio_{period}'] = float(volume[-1] / volume_ma)
            
            # 成交量变化率
            if len(volume) >= 2:
                factors['volume_change_pct'] = float((volume[-1] - volume[-2]) / volume[-2] * 100)
            
            # OBV能量潮
            if len(close) >= 2:
                obv = talib.OBV(close, volume)[-1]
                factors['obv'] = float(obv)
                if len(close) >= 10:
                    obv_ma = talib.SMA(talib.OBV(close, volume), timeperiod=10)[-1]
                    factors['obv_signal'] = 1 if obv > obv_ma else 0
            
            # 资金流量指标MFI
            if len(close) >= 14:
                mfi = talib.MFI(np.array([close[-1]]*len(close)), np.array([close[-1]]*len(close)), close, volume, timeperiod=14)[-1]
                factors['mfi'] = float(mfi)
                factors['mfi_oversold'] = 1 if mfi < 20 else 0
                factors['mfi_overbought'] = 1 if mfi > 80 else 0
            
            # 成交量异常检测
            if len(volume) >= 20:
                volume_mean = np.mean(volume[-20:])
                volume_std = np.std(volume[-20:])
                factors['volume_zscore'] = float((volume[-1] - volume_mean) / volume_std)
                factors['volume_anomaly'] = 1 if abs(factors['volume_zscore']) > 2 else 0
                
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_volatility_factors(self, close, high, low) -> Dict[str, float]:
        """计算波动率指标因子"""
        factors = {}
        
        try:
            # ATR真实波动幅度
            atr_periods = [7, 14, 21]
            for period in atr_periods:
                if len(close) >= period:
                    atr = talib.ATR(high, low, close, timeperiod=period)[-1]
                    factors[f'atr_{period}'] = float(atr)
                    factors[f'atr_{period}_pct'] = float(atr / close[-1] * 100)
            
            # 布林带
            if len(close) >= 20:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2)
                factors['bb_upper'] = float(bb_upper[-1])
                factors['bb_middle'] = float(bb_middle[-1])
                factors['bb_lower'] = float(bb_lower[-1])
                factors['bb_width'] = float((bb_upper[-1] - bb_lower[-1]) / bb_middle[-1] * 100)
                factors['bb_position'] = float((close[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) * 100)
                factors['bb_squeeze'] = 1 if factors['bb_width'] < 10 else 0
            
            # 历史波动率
            volatility_periods = [5, 10, 20]
            for period in volatility_periods:
                if len(close) >= period + 1:
                    returns = np.diff(np.log(close[-period-1:]))
                    volatility = np.std(returns) * np.sqrt(252) * 100
                    factors[f'volatility_{period}d'] = float(volatility)
            
            # Keltner通道
            if len(close) >= 20:
                kc_middle = talib.EMA(close, timeperiod=20)[-1]
                atr_20 = talib.ATR(high, low, close, timeperiod=20)[-1]
                kc_upper = kc_middle + 2 * atr_20
                kc_lower = kc_middle - 2 * atr_20
                factors['kc_position'] = float((close[-1] - kc_lower) / (kc_upper - kc_lower) * 100)
                
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_pattern_factors(self, close, high, low, volume) -> Dict[str, float]:
        """计算形态识别因子"""
        factors = {}
        
        try:
            # 支撑阻力位
            if len(close) >= 20:
                # 近期高点和低点
                period_high = np.max(high[-20:])
                period_low = np.min(low[-20:])
                factors['resistance_distance'] = float((period_high - close[-1]) / close[-1] * 100)
                factors['support_distance'] = float((close[-1] - period_low) / close[-1] * 100)
                
                # 突破信号
                factors['breakout_resistance'] = 1 if close[-1] > period_high * 0.99 else 0
                factors['breakdown_support'] = 1 if close[-1] < period_low * 1.01 else 0
            
            # 缺口检测
            if len(close) >= 2:
                gap_up = (low[-1] > high[-2])
                gap_down = (high[-1] < low[-2])
                factors['gap_up'] = 1 if gap_up else 0
                factors['gap_down'] = 1 if gap_down else 0
            
            # 连续上涨/下跌天数
            if len(close) >= 10:
                consecutive_up = 0
                consecutive_down = 0
                for i in range(1, min(10, len(close))):
                    if close[-i] > close[-i-1]:
                        consecutive_up += 1
                    else:
                        break
                for i in range(1, min(10, len(close))):
                    if close[-i] < close[-i-1]:
                        consecutive_down += 1
                    else:
                        break
                factors['consecutive_up_days'] = consecutive_up
                factors['consecutive_down_days'] = consecutive_down
            
            # 量价关系
            if len(close) >= 2 and len(volume) >= 2:
                price_up = close[-1] > close[-2]
                volume_up = volume[-1] > volume[-2]
                factors['price_volume_confirm'] = 1 if price_up == volume_up else 0
                
        except Exception as e:
            pass
            
        return factors
    
    def _calculate_custom_factors(self, close, high, low, volume) -> Dict[str, float]:
        """计算自定义策略因子"""
        factors = {}
        
        try:
            # 综合趋势强度
            if len(close) >= 20:
                # 多周期趋势一致性
                trend_5 = 1 if close[-1] > close[-6] else 0
                trend_10 = 1 if close[-1] > close[-11] else 0
                trend_20 = 1 if close[-1] > close[-21] else 0
                factors['trend_consistency'] = trend_5 + trend_10 + trend_20
                
                # 趋势强度评分
                momentum_score = 0
                if len(close) >= 5:
                    momentum_score += (close[-1] - close[-6]) / close[-6] * 100
                if len(close) >= 10:
                    momentum_score += (close[-1] - close[-11]) / close[-11] * 100
                factors['momentum_score'] = float(momentum_score)
            
            # 超买超卖综合评分
            oversold_signals = 0
            overbought_signals = 0
            
            # RSI信号
            if 'rsi_14' in factors:
                if factors['rsi_14'] < 30:
                    oversold_signals += 1
                elif factors['rsi_14'] > 70:
                    overbought_signals += 1
            
            # CCI信号
            if 'cci' in factors:
                if factors['cci'] < -100:
                    oversold_signals += 1
                elif factors['cci'] > 100:
                    overbought_signals += 1
            
            factors['oversold_score'] = oversold_signals
            factors['overbought_score'] = overbought_signals
            
            # 成交量确认评分
            volume_score = 0
            if 'volume_ratio_20' in factors:
                if factors['volume_ratio_20'] > 1.5:
                    volume_score += 2
                elif factors['volume_ratio_20'] > 1.2:
                    volume_score += 1
            
            factors['volume_confirmation_score'] = volume_score
            
            # 综合买入评分
            buy_score = 0
            
            # 趋势评分 (40%)
            if 'trend_consistency' in factors:
                buy_score += factors['trend_consistency'] / 3 * 0.4
            
            # 超买超卖评分 (30%)
            if oversold_signals > 0:
                buy_score += 0.3
            elif overbought_signals > 0:
                buy_score -= 0.3
            
            # 成交量评分 (30%)
            if volume_score > 0:
                buy_score += volume_score / 2 * 0.3
            
            factors['comprehensive_buy_score'] = float(max(0, min(1, buy_score)))

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.error(f"因子计算异常: {e}")
            else:
                print(f"因子计算异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")



        return factors

    def init_timeseries_db(self):
        """初始化时序数据库"""
        try:
            if not os.path.exists('data'):
                os.makedirs('data')

            conn = sqlite3.connect(self.timeseries_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS timeseries_factors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    symbol VARCHAR(20) NOT NULL,
                    atr_pct FLOAT, macd FLOAT, macd_signal FLOAT, macd_hist FLOAT,
                    bb_width FLOAT, bb_position FLOAT, rsi FLOAT, adx FLOAT,
                    cci FLOAT, trix_buy FLOAT, relative_volume FLOAT,
                    volume_change_rate FLOAT, ma20 FLOAT, distance_from_high FLOAT,
                    price FLOAT, volume FLOAT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol_timestamp ON timeseries_factors(symbol, timestamp)')
            conn.commit()
            conn.close()

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.warning(f"时序数据库初始化失败: {e}")

    def save_factors_to_timeseries(self, symbol: str, factors: Dict, price: float = None, volume: float = None):
        """保存因子到时序数据库"""
        try:
            # 确保数据库已初始化
            if not self._timeseries_db_initialized:
                self.init_timeseries_db()
                self._timeseries_db_initialized = True

            conn = sqlite3.connect(self.timeseries_db_path)
            cursor = conn.cursor()

            # 准备数据
            timestamp = datetime.now()
            data = {
                'timestamp': timestamp,
                'symbol': symbol,
                'price': price,
                'volume': volume
            }

            # 添加关键因子
            key_factors = ['atr_pct', 'macd', 'macd_signal', 'macd_hist', 'bb_width',
                          'bb_position', 'rsi', 'adx', 'cci', 'trix_buy',
                          'relative_volume', 'volume_change_rate', 'ma20', 'distance_from_high']

            for factor in key_factors:
                if factor in factors:
                    value = factors[factor]
                    data[factor] = None if pd.isna(value) else float(value)
                else:
                    data[factor] = None

            # 插入数据
            fields = list(data.keys())
            placeholders = ', '.join(['?' for _ in fields])
            sql = f"INSERT INTO timeseries_factors ({', '.join(fields)}) VALUES ({placeholders})"

            cursor.execute(sql, list(data.values()))
            conn.commit()
            conn.close()

            return True

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.warning(f"保存{symbol}时序因子失败: {e}")
            return False

    def get_timeseries_data(self, symbol: str, lookback_hours: int = 24) -> pd.DataFrame:
        """获取时序数据"""
        try:
            # 确保数据库已初始化
            if not self._timeseries_db_initialized:
                self.init_timeseries_db()
                self._timeseries_db_initialized = True

            conn = sqlite3.connect(self.timeseries_db_path)

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=lookback_hours)

            query = """
                SELECT * FROM timeseries_factors
                WHERE symbol = ? AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp ASC
            """

            df = pd.read_sql_query(query, conn, params=[symbol, start_time, end_time])
            conn.close()

            if len(df) > 0:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)

            return df

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.warning(f"获取{symbol}时序数据失败: {e}")
            return pd.DataFrame()

    def calculate_dynamic_atr_threshold(self, symbol: str, lookback_days: int = 30) -> float:
        """计算动态ATR阈值"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=lookback_days * 24)

            if len(data) < 10:
                return 2.7  # 默认阈值

            atr_values = data['atr_pct'].dropna()
            if len(atr_values) < 10:
                return 2.7

            # 计算动态阈值
            q75 = atr_values.quantile(0.75)
            q90 = atr_values.quantile(0.90)
            atr_std = atr_values.std()
            atr_mean = atr_values.mean()

            if atr_std < atr_mean * 0.3:  # 低波动
                threshold = q75
            elif atr_std > atr_mean * 0.8:  # 高波动
                threshold = q90
            else:  # 正常波动
                threshold = q75 + (q90 - q75) * 0.5

            return max(1.5, min(threshold, 8.0))

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.warning(f"计算{symbol}动态ATR阈值失败: {e}")
            return 2.7

    def detect_macd_golden_cross_trend(self, symbol: str) -> bool:
        """检测MACD金叉趋势"""
        try:
            data = self.get_timeseries_data(symbol, lookback_hours=20)

            if len(data) < 3:
                return False

            macd_hist = data['macd_hist'].dropna()
            if len(macd_hist) < 3:
                return False

            recent_hist = macd_hist.tail(3).values
            # 金叉：从负转正
            return recent_hist[-1] > 0 and recent_hist[-2] <= 0

        except Exception as e:
            return False

    def _calculate_multifactor_scores(self, factors, data):
        """计算多因子策略所需的关键评分"""
        try:
            # 技术指标评分 (technical_score)
            technical_score = 0.0
            technical_count = 0

            # MACD评分
            if 'macd_hist' in factors:
                if factors['macd_hist'] > 0:
                    technical_score += 0.25
                technical_count += 1

            # RSI评分
            if 'rsi' in factors:
                rsi = factors['rsi']
                if 30 <= rsi <= 70:  # 适中区间
                    technical_score += 0.25
                elif rsi < 30:  # 超卖
                    technical_score += 0.15
                technical_count += 1

            # 布林带评分
            if 'bb_position' in factors and 'bb_width' in factors:
                bb_pos = factors['bb_position']
                bb_width = factors['bb_width']
                if bb_width > 8 and 20 <= bb_pos <= 80:  # 宽带且不在极端位置
                    technical_score += 0.25
                technical_count += 1

            # ADX评分
            if 'adx' in factors:
                adx = factors['adx']
                if adx > 25:  # 强趋势
                    technical_score += 0.25
                technical_count += 1

            factors['technical_score'] = technical_score / max(1, technical_count) if technical_count > 0 else 0.0

            # 动量评分 (momentum_score)
            momentum_score = 0.0
            momentum_count = 0

            # TRIX评分
            if 'trix_buy' in factors:
                trix = factors['trix_buy']
                if trix > 0:
                    momentum_score += 0.3
                elif trix > -20:
                    momentum_score += 0.1
                momentum_count += 1

            # 价格动量评分
            if hasattr(data, 'columns') and len(data) >= 10:
                close_prices = data['close'].values
                price_momentum = (close_prices[-1] - close_prices[-6]) / close_prices[-6] * 100
                if price_momentum > 2:
                    momentum_score += 0.3
                elif price_momentum > 0:
                    momentum_score += 0.1
                momentum_count += 1

            # MACD动量
            if 'macd' in factors and 'macd_signal' in factors:
                if factors['macd'] > factors['macd_signal']:
                    momentum_score += 0.2
                momentum_count += 1

            factors['momentum_score'] = momentum_score / max(1, momentum_count) if momentum_count > 0 else 0.0

            # 成交量评分 (volume_score)
            volume_score = 0.0

            if 'relative_volume' in factors:
                rel_vol = factors['relative_volume']
                if rel_vol > 2.0:
                    volume_score += 0.4
                elif rel_vol > 1.5:
                    volume_score += 0.3
                elif rel_vol > 1.2:
                    volume_score += 0.2

            if 'volume_change_rate' in factors:
                vol_change = factors['volume_change_rate']
                if vol_change > 50:
                    volume_score += 0.3
                elif vol_change > 20:
                    volume_score += 0.2

            if 'volume_confirmation_score' in factors:
                volume_score += factors['volume_confirmation_score'] / 2 * 0.3

            factors['volume_score'] = min(1.0, volume_score)

            # 波动率评分 (volatility_score)
            volatility_score = 0.0

            if 'atr_pct' in factors:
                atr = factors['atr_pct']
                if 2.0 <= atr <= 6.0:  # 适中波动率
                    volatility_score += 0.5
                elif 1.5 <= atr <= 8.0:  # 可接受范围
                    volatility_score += 0.3

            if 'bb_width' in factors:
                bb_width = factors['bb_width']
                if bb_width > 10:
                    volatility_score += 0.3
                elif bb_width > 6:
                    volatility_score += 0.2

            factors['volatility_score'] = min(1.0, volatility_score)

            # 趋势评分 (trend_score)
            trend_score = 0.0

            if 'trend_consistency' in factors:
                trend_score += factors['trend_consistency'] / 3 * 0.4

            if 'ma20' in factors and hasattr(data, 'columns'):
                current_price = data['close'].iloc[-1]
                ma20 = factors['ma20']
                if current_price > ma20:
                    trend_score += 0.3

            if 'adx' in factors:
                adx = factors['adx']
                if adx > 25:
                    trend_score += 0.3

            factors['trend_score'] = min(1.0, trend_score)

            # 买入信号强度 (buy_signal_strength)
            buy_signal_strength = 0.0
            signal_count = 0

            # 技术信号强度
            if factors['technical_score'] > 0.6:
                buy_signal_strength += 0.25
                signal_count += 1

            # 动量信号强度
            if factors['momentum_score'] > 0.5:
                buy_signal_strength += 0.25
                signal_count += 1

            # 成交量信号强度
            if factors['volume_score'] > 0.5:
                buy_signal_strength += 0.25
                signal_count += 1

            # 综合买入评分
            if 'comprehensive_buy_score' in factors and factors['comprehensive_buy_score'] > 0.6:
                buy_signal_strength += 0.25
                signal_count += 1

            factors['buy_signal_strength'] = buy_signal_strength

            # 风险调整评分 (risk_adjusted_score)
            risk_adjusted_score = 0.0

            # 基础评分
            base_score = (factors['technical_score'] + factors['momentum_score'] + factors['volume_score']) / 3

            # 风险调整
            risk_penalty = 0.0

            # 波动率风险
            if 'atr_pct' in factors:
                atr = factors['atr_pct']
                if atr > 8.0:  # 过高波动率
                    risk_penalty += 0.2
                elif atr < 1.0:  # 过低波动率
                    risk_penalty += 0.1

            # RSI极端值风险
            if 'rsi' in factors:
                rsi = factors['rsi']
                if rsi > 80 or rsi < 20:
                    risk_penalty += 0.1

            risk_adjusted_score = max(0.0, base_score - risk_penalty)
            factors['risk_adjusted_score'] = risk_adjusted_score

            # 综合评分 (overall_score)
            weights = {
                'technical_score': 0.25,
                'momentum_score': 0.20,
                'volume_score': 0.15,
                'volatility_score': 0.10,
                'trend_score': 0.15,
                'buy_signal_strength': 0.15
            }

            overall_score = 0.0
            for score_name, weight in weights.items():
                if score_name in factors:
                    overall_score += factors[score_name] * weight

            factors['overall_score'] = overall_score

        except Exception as e:
            if self.context and hasattr(self.context, 'log'):
                self.context.log.warning(f"多因子评分计算失败: {e}")
            # 设置默认值
            factors.update({
                'technical_score': 0.0,
                'momentum_score': 0.0,
                'volume_score': 0.0,
                'volatility_score': 0.0,
                'trend_score': 0.0,
                'buy_signal_strength': 0.0,
                'risk_adjusted_score': 0.0,
                'overall_score': 0.0
            })

def test_factor_engine():
    """测试因子引擎"""
    print("🧪 测试增强因子引擎")
    
    # 创建测试数据
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    
    # 模拟股价数据
    base_price = 10.0
    returns = np.random.normal(0.001, 0.02, 100)
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    test_data = pd.DataFrame({
        'open': np.array(prices) * (1 + np.random.normal(0, 0.005, 100)),
        'high': np.array(prices) * (1 + np.abs(np.random.normal(0, 0.01, 100))),
        'low': np.array(prices) * (1 - np.abs(np.random.normal(0, 0.01, 100))),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 100)
    }, index=dates)
    
    # 测试因子计算
    engine = EnhancedFactorEngine()
    factors = engine.calculate_all_factors(test_data, 'TEST.000001')
    
    print(f"✅ 成功计算 {len(factors)} 个因子")
    
    # 显示部分因子
    factor_categories = {
        '价格因子': [k for k in factors.keys() if 'price' in k or 'momentum' in k],
        '移动平均': [k for k in factors.keys() if 'ma' in k and 'macd' not in k],
        '趋势指标': [k for k in factors.keys() if any(x in k for x in ['macd', 'adx', 'dmi', 'trix'])],
        '超买超卖': [k for k in factors.keys() if any(x in k for x in ['rsi', 'cci', 'willr', 'stoch'])],
        '成交量': [k for k in factors.keys() if 'volume' in k or 'obv' in k or 'mfi' in k],
        '波动率': [k for k in factors.keys() if any(x in k for x in ['atr', 'bb_', 'volatility'])],
        '自定义': [k for k in factors.keys() if any(x in k for x in ['score', 'consistency', 'confirmation'])]
    }
    
    for category, factor_list in factor_categories.items():
        if factor_list:
            print(f"\n📊 {category} ({len(factor_list)}个):")
            for factor in factor_list[:5]:  # 显示前5个
                print(f"  {factor}: {factors[factor]:.4f}")
            if len(factor_list) > 5:
                print(f"  ... 还有{len(factor_list)-5}个")

def test_factor_engine():
    """测试因子引擎"""
    print("🧪 测试增强因子引擎")
    print("=" * 50)

    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)

    test_data = pd.DataFrame({
        'open': 100 + np.random.randn(100).cumsum(),
        'high': 100 + np.random.randn(100).cumsum() + 2,
        'low': 100 + np.random.randn(100).cumsum() - 2,
        'close': 100 + np.random.randn(100).cumsum(),
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)

    # 创建因子引擎
    engine = EnhancedFactorEngine()

    # 计算因子
    factors = engine.calculate_all_factors(test_data, 'TEST.000001')

    print(f"✅ 计算了 {len(factors)} 个因子")

    # 显示部分因子
    factor_list = list(factors.keys())
    print("📊 因子列表 (前10个):")
    for i, factor in enumerate(factor_list[:10]):
        value = factors[factor]
        print(f"  {i+1:2d}. {factor}: {value}")

    if len(factor_list) > 10:
        print(f"  ... 还有{len(factor_list)-10}个")

    # 测试时序功能
    print("\n🔍 测试时序功能:")
    success = engine.save_factors_to_timeseries('TEST.000001', factors, 100.0, 50000)
    print(f"保存时序数据: {'✅ 成功' if success else '❌ 失败'}")

    # 测试动态阈值
    threshold = engine.calculate_dynamic_atr_threshold('TEST.000001')
    print(f"动态ATR阈值: {threshold:.2f}")

    # 测试MACD金叉检测
    golden_cross = engine.detect_macd_golden_cross_trend('TEST.000001')
    print(f"MACD金叉检测: {'✅ 检测到' if golden_cross else '❌ 未检测到'}")

if __name__ == '__main__':
    test_factor_engine()
