# coding=utf-8
"""
验证紧急修复配置
确认所有关键修复已正确应用
"""

from config import get_config_value

def verify_emergency_fix():
    """验证紧急修复配置"""
    print('✅ 验证紧急修复配置')
    print('=' * 60)
    
    print('🎯 修复目标:')
    print('   解决96.7%开盘信号集中问题')
    print('   修复因子阈值设置问题')
    print('   启用时间分散机制')
    print('   基于最有效因子重构策略')
    
    # 验证阈值修复
    print(f'\n🔧 阈值修复验证:')
    
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    expected_thresholds = {
        'min_overall_score': 0.05,
        'min_technical_score': 0.03,
        'min_momentum_score': 0.02,
        'min_volatility_score': 0.15,
        'min_trend_score': 0.05,
        'min_buy_signal_strength': 0.03,
        'min_risk_adjusted_score': 0.02
    }
    
    threshold_correct = True
    for key, expected in expected_thresholds.items():
        actual = thresholds.get(key, 'NOT_FOUND')
        if actual == expected:
            if key == 'min_volatility_score':
                print(f'   ✅ {key}: {actual} (从0.0提高到0.15，解决100%满足问题)')
            else:
                print(f'   ✅ {key}: {actual} (大幅降低，解决代码默认值过高问题)')
        else:
            print(f'   ❌ {key}: {actual} (期望: {expected})')
            threshold_correct = False
    
    # 验证确认条件修复
    print(f'\n📊 确认条件修复验证:')
    
    confirmations = get_config_value('MULTIFACTOR_CONFIRMATIONS', {})
    
    min_score_count = confirmations.get('min_score_count', 2)
    technical_confirmation = confirmations.get('require_technical_confirmation', True)
    momentum_confirmation = confirmations.get('require_momentum_confirmation', True)
    
    if min_score_count == 1:
        print(f'   ✅ min_score_count: {min_score_count} (从2降低到1，因为大部分因子失效)')
    else:
        print(f'   ❌ min_score_count: {min_score_count} (期望: 1)')
    
    if technical_confirmation == False:
        print(f'   ✅ technical_confirmation: 已禁用 (暂时禁用技术确认)')
    else:
        print(f'   ❌ technical_confirmation: {technical_confirmation} (期望: False)')
    
    if momentum_confirmation == False:
        print(f'   ✅ momentum_confirmation: 已禁用 (保持禁用动量确认)')
    else:
        print(f'   ❌ momentum_confirmation: {momentum_confirmation} (期望: False)')
    
    # 验证时间分散配置
    print(f'\n⏰ 时间分散配置验证:')
    
    enable_time_distribution = get_config_value('ENABLE_TIME_DISTRIBUTION', False)
    time_config = get_config_value('TIME_DISTRIBUTION_CONFIG', {})
    
    if enable_time_distribution == True:
        print(f'   ✅ 时间分散机制: 已启用')
    else:
        print(f'   ❌ 时间分散机制: {enable_time_distribution} (期望: True)')
    
    if time_config.get('enable', False):
        max_signals = time_config.get('max_signals_per_hour', {})
        opening_limit = max_signals.get('09:30-10:00', 0)
        if opening_limit == 10:
            print(f'   ✅ 开盘时段限制: {opening_limit}个信号/小时 (严格限制)')
        else:
            print(f'   ❌ 开盘时段限制: {opening_limit} (期望: 10)')
    else:
        print(f'   ❌ 时间分散配置未启用')
    
    # 验证开盘特殊处理
    print(f'\n🌅 开盘特殊处理验证:')
    
    opening_config = get_config_value('OPENING_SPECIAL_CONFIG', {})
    
    if opening_config.get('enable', False):
        special_req = opening_config.get('special_requirements', {})
        min_atr = special_req.get('min_atr_pct', 0)
        min_bb = special_req.get('min_bb_width', 0)
        factor_mult = special_req.get('factor_multiplier', 1.0)
        
        print(f'   ✅ 开盘特殊处理: 已启用')
        print(f'   ✅ 开盘ATR要求: {min_atr} (提高门槛)')
        print(f'   ✅ 开盘BB要求: {min_bb} (提高门槛)')
        print(f'   ✅ 因子倍数: {factor_mult} (要求提高50%)')
    else:
        print(f'   ❌ 开盘特殊处理未启用')
    
    # 验证简化策略
    print(f'\n🎯 简化策略验证:')
    
    enable_simplified = get_config_value('ENABLE_SIMPLIFIED_STRATEGY', False)
    simplified_config = get_config_value('SIMPLIFIED_STRATEGY_CONFIG', {})
    
    if enable_simplified == True:
        print(f'   ✅ 简化策略: 已启用')
        
        primary_factors = simplified_config.get('primary_factors', {})
        if 'atr_pct' in primary_factors and 'bb_width' in primary_factors and 'volatility_score' in primary_factors:
            print(f'   ✅ 基于3个最有效因子: ATR, BB宽度, 波动率评分')
            
            atr_weight = primary_factors.get('atr_pct', {}).get('weight', 0)
            bb_weight = primary_factors.get('bb_width', {}).get('weight', 0)
            vol_weight = primary_factors.get('volatility_score', {}).get('weight', 0)
            
            print(f'   ✅ 因子权重: ATR({atr_weight}), BB({bb_weight}), 波动率({vol_weight})')
        else:
            print(f'   ❌ 简化策略因子配置不完整')
    else:
        print(f'   ❌ 简化策略未启用')
    
    # 总体验证结果
    all_correct = (
        threshold_correct and
        min_score_count == 1 and
        technical_confirmation == False and
        enable_time_distribution == True and
        opening_config.get('enable', False) and
        enable_simplified == True
    )
    
    print(f'\n🎯 验证总结:')
    if all_correct:
        print('✅ 所有紧急修复配置已正确应用')
        print('🚀 策略已准备就绪，可以重启程序')
        return True
    else:
        print('❌ 部分修复配置未正确应用')
        print('💡 请检查config.py文件并手动修正')
        return False

def show_fix_summary():
    """显示修复总结"""
    print(f'\n📋 紧急修复总结')
    print('=' * 50)
    
    summary = '''
🎯 核心问题解决:
   1. ✅ 阈值过高问题: 从代码默认0.75降低到0.05
   2. ✅ 波动率阈值问题: 从0.0提高到0.15 (解决100%满足)
   3. ✅ 确认条件过严: 从2个降低到1个
   4. ✅ 开盘信号集中: 启用时间分散机制
   5. ✅ 因子失效问题: 启用基于3个有效因子的简化策略

📊 预期效果:
   - 开盘信号比例: 96.7% → 40%以下
   - 信号分布: 更均匀分布在全天
   - 因子有效性: 基于最有效的3个因子
   - 策略稳定性: 显著提升
   - 胜率提升: 预期+5-10%

🔍 监控重点:
   - 开盘时段信号数量是否大幅减少
   - 信号是否更均匀分布在全天
   - 因子计算是否正常工作
   - 整体胜率是否提升

⚠️ 注意事项:
   - 这是紧急修复，后续需要进一步优化
   - 需要监控新配置的实际效果
   - 如有问题可以快速回退
   - 建议渐进式观察效果
'''
    
    print(summary)

def create_monitoring_checklist():
    """创建监控检查清单"""
    print(f'\n📋 修复后监控检查清单')
    print('=' * 50)
    
    checklist = '''
🔍 重启后立即检查 (前30分钟):
   □ 策略是否正常启动
   □ 买入信号是否正常生成
   □ 开盘时段信号是否明显减少
   □ 是否出现错误日志

📊 2小时内数据检查:
   □ 各时段信号分布是否改善
   □ 开盘时段信号比例是否<40%
   □ 因子计算是否正常 (无大量NaN)
   □ 简化策略是否正常工作

📈 24小时内效果评估:
   □ 信号时间分布是否均匀
   □ 买入信号质量是否提升
   □ 策略稳定性是否改善
   □ 是否需要微调参数

🎯 一周内综合评估:
   □ 胜率是否有提升趋势
   □ 交易结构是否优化
   □ 策略表现是否稳定
   □ 是否可以进入第二阶段修复

🚨 异常情况处理:
   □ 如果信号过少: 适当降低阈值
   □ 如果仍集中开盘: 加强时间限制
   □ 如果出现错误: 检查配置语法
   □ 如果效果不佳: 考虑回退配置
'''
    
    print(checklist)

def main():
    """主函数"""
    print('🚀 紧急修复配置验证')
    print('=' * 60)
    
    # 验证紧急修复配置
    success = verify_emergency_fix()
    
    # 显示修复总结
    show_fix_summary()
    
    # 创建监控检查清单
    create_monitoring_checklist()
    
    if success:
        print(f'\n🏆 紧急修复配置验证成功!')
        print('🚀 策略已完全修复，准备重启程序!')
        print('')
        print('🎯 下一步: python main.py')
        print('📈 目标: 解决96.7%开盘信号集中问题')
        print('💎 预期: 信号均匀分布，胜率提升5-10%')
    else:
        print(f'\n⚠️ 紧急修复配置验证失败!')
        print('💡 请检查并修正配置文件')

if __name__ == '__main__':
    main()
