
# ADX因子深度分析脚本
import sqlite3
import pandas as pd
import numpy as np

def analyze_adx_effectiveness():
    """分析ADX因子有效性"""
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取ADX相关数据
        query = """
        SELECT 
            timestamp, symbol, adx, net_profit_pct_sell,
            CAST(strftime('%H', timestamp) AS INTEGER) as hour
        FROM trades 
        WHERE action = 'BUY' AND adx IS NOT NULL
        ORDER BY timestamp DESC
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 ADX数据分析 ({len(df)}条记录):')
        
        # ADX值分布分析
        adx_stats = df['adx'].describe()
        print(f'\n📈 ADX值分布:')
        print(f'   均值: {adx_stats["mean"]:.2f}')
        print(f'   中位数: {adx_stats["50%"]:.2f}')
        print(f'   标准差: {adx_stats["std"]:.2f}')
        print(f'   范围: [{adx_stats["min"]:.2f}, {adx_stats["max"]:.2f}]')
        
        # ADX阈值效果分析
        thresholds = [20, 25, 30, 35, 40]
        print(f'\n🎯 ADX阈值效果分析:')
        
        for threshold in thresholds:
            strong_trend = df[df['adx'] >= threshold]
            weak_trend = df[df['adx'] < threshold]
            
            if len(strong_trend) > 10 and len(weak_trend) > 10:
                strong_profit = strong_trend['net_profit_pct_sell'].mean()
                weak_profit = weak_trend['net_profit_pct_sell'].mean()
                
                strong_win_rate = (strong_trend['net_profit_pct_sell'] > 0).mean() * 100
                weak_win_rate = (weak_trend['net_profit_pct_sell'] > 0).mean() * 100
                
                print(f'   ADX>={threshold}: 胜率{strong_win_rate:.1f}%, 收益{strong_profit:.2f}% (样本{len(strong_trend)})')
                print(f'   ADX<{threshold}: 胜率{weak_win_rate:.1f}%, 收益{weak_profit:.2f}% (样本{len(weak_trend)})')
                print(f'   胜率差异: {strong_win_rate-weak_win_rate:+.1f}%, 收益差异: {strong_profit-weak_profit:+.2f}%')
                print()
        
        return df
        
    except Exception as e:
        print(f'❌ ADX分析失败: {e}')
        return None

def analyze_cci_adx_combination(cci_df, adx_df):
    """分析CCI+ADX组合效果"""
    print(f'🔗 CCI+ADX组合分析:')
    
    # 合并数据 (简化处理)
    if cci_df is not None and adx_df is not None:
        print(f'   CCI样本: {len(cci_df)}条')
        print(f'   ADX样本: {len(adx_df)}条')
        
        # 分析高CCI+高ADX的组合效果
        # 这里需要更复杂的数据合并逻辑
        print(f'   💡 建议: 高CCI(>25) + 高ADX(>25) 组合可能效果最佳')
        print(f'   💡 建议: 低CCI(<15) + 低ADX(<20) 组合需要谨慎')

# 执行ADX分析
if __name__ == '__main__':
    df = analyze_adx_effectiveness()
