# 更新记录

## 版本 1.0.2 (2025-05-30)

### 修复
- 修复了数据库文件路径配置，现在正确指向 `data/trades.db` 而不是 `data.db`
- 更新了配置加载器的默认路径设置

## 版本 1.0.1 (2025-05-30)

### 新增功能
- 添加数据分析直接启动脚本 (start_analysis.py/bat)
- 添加数据分析工具桌面快捷方式创建功能

### 改进
- 更新文档，添加数据分析直接启动的使用说明
- 优化启动脚本的错误处理

## 版本 1.0.0 (2025-05-29)

### 新增功能
- 初始版本发布
- 数据库信息查看功能
- 数据分析功能，支持生成统计图表
- 缺失数据修复功能，支持多种填充策略
- 批量处理数据功能，支持更新和删除操作
- 数据库合并功能，支持多种合并策略
- 数据库报告生成功能
- 数据库备份功能
- 数据库优化功能

### 改进
- 交互式命令行界面
- 彩色输出支持
- 详细的错误处理和用户提示

### 文件结构
- 统一管理所有数据库工具相关文件
- 添加配置文件支持
- 添加桌面快捷方式创建功能

### 依赖库
- 支持自动检查和安装依赖库
- 支持seaborn、pandas、numpy、matplotlib和colorama

## 计划功能 (未来版本)

### 版本 1.1.0 (计划)
- 添加图形用户界面 (GUI)
- 添加数据导入/导出功能
- 添加数据可视化高级选项
- 支持更多数据库格式

### 版本 1.2.0 (计划)
- 添加数据同步功能
- 添加自动备份计划
- 添加数据库结构比较工具
- 添加性能监控功能 