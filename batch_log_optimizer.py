# coding=utf-8
"""
批量日志优化脚本
自动化替换重复的时间戳格式化调用
"""

import re
import os
import shutil

def backup_file(file_path):
    """备份原文件"""
    backup_path = f"{file_path}.backup"
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已备份文件: {backup_path}")
    return backup_path

def optimize_log_calls(file_path):
    """优化日志调用"""
    print(f'🔧 开始优化文件: {file_path}')
    
    # 备份文件
    backup_path = backup_file(file_path)
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 定义替换模式
        patterns = [
            # context.log.info(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - message")
            # -> log_with_timestamp(context, 'info', "message")
            {
                'pattern': r'context\.log\.info\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
                'replacement': r'log_with_timestamp(context, \'info\', "\1")',
                'description': 'info日志优化'
            },
            # context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - message")
            # -> log_with_timestamp(context, 'warning', "message")
            {
                'pattern': r'context\.log\.warning\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
                'replacement': r'log_with_timestamp(context, \'warning\', "\1")',
                'description': 'warning日志优化'
            },
            # context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - message")
            # -> log_with_timestamp(context, 'error', "message")
            {
                'pattern': r'context\.log\.error\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]+)"\)',
                'replacement': r'log_with_timestamp(context, \'error\', "\1")',
                'description': 'error日志优化'
            }
        ]
        
        total_replacements = 0
        
        # 应用所有替换模式
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            replacement = pattern_info['replacement']
            description = pattern_info['description']
            
            # 查找匹配
            matches = re.findall(pattern, content)
            if matches:
                print(f'  🎯 {description}: 找到 {len(matches)} 处匹配')
                
                # 执行替换
                content = re.sub(pattern, replacement, content)
                total_replacements += len(matches)
        
        # 特殊处理：包含变量的复杂日志
        complex_patterns = [
            # 处理包含变量的日志
            {
                'pattern': r'context\.log\.info\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]*\{[^}]+\}[^"]*)"\)',
                'replacement': r'log_with_timestamp(context, \'info\', f"\1")',
                'description': '复杂info日志优化'
            },
            {
                'pattern': r'context\.log\.warning\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]*\{[^}]+\}[^"]*)"\)',
                'replacement': r'log_with_timestamp(context, \'warning\', f"\1")',
                'description': '复杂warning日志优化'
            },
            {
                'pattern': r'context\.log\.error\(f"\{context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)\} - ([^"]*\{[^}]+\}[^"]*)"\)',
                'replacement': r'log_with_timestamp(context, \'error\', f"\1")',
                'description': '复杂error日志优化'
            }
        ]
        
        # 应用复杂模式
        for pattern_info in complex_patterns:
            pattern = pattern_info['pattern']
            replacement = pattern_info['replacement']
            description = pattern_info['description']
            
            # 查找匹配
            matches = re.findall(pattern, content)
            if matches:
                print(f'  🎯 {description}: 找到 {len(matches)} 处匹配')
                
                # 执行替换
                content = re.sub(pattern, replacement, content)
                total_replacements += len(matches)
        
        # 写入优化后的内容
        if total_replacements > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f'✅ 优化完成: 总共替换了 {total_replacements} 处日志调用')
            return total_replacements
        else:
            print('ℹ️ 没有找到需要优化的日志调用')
            # 删除备份文件
            os.remove(backup_path)
            return 0
            
    except Exception as e:
        print(f'❌ 优化失败: {e}')
        # 恢复备份
        shutil.copy2(backup_path, file_path)
        print(f'🔄 已恢复原文件')
        return 0

def validate_optimization(file_path):
    """验证优化结果"""
    print(f'\n🔍 验证优化结果: {file_path}')
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计剩余的旧模式
        old_pattern = r'context\.now\.strftime\(\'%Y-%m-%d %H:%M:%S\'\)'
        old_matches = re.findall(old_pattern, content)
        
        # 统计新模式
        new_pattern = r'log_with_timestamp\('
        new_matches = re.findall(new_pattern, content)
        
        print(f'📊 验证结果:')
        print(f'  剩余旧模式: {len(old_matches)} 处')
        print(f'  新统一接口: {len(new_matches)} 处')
        
        if len(old_matches) > 0:
            print(f'⚠️ 仍有 {len(old_matches)} 处旧模式需要手动处理')
            # 显示前5个示例
            lines = content.split('\n')
            examples = []
            for i, line in enumerate(lines):
                if 'context.now.strftime' in line and len(examples) < 5:
                    examples.append(f'  第{i+1}行: {line.strip()}')
            
            if examples:
                print('  示例:')
                for example in examples:
                    print(example)
        else:
            print('✅ 所有日志调用已优化完成')
        
        return len(old_matches), len(new_matches)
        
    except Exception as e:
        print(f'❌ 验证失败: {e}')
        return -1, -1

def generate_optimization_report(file_path, replacements, old_count, new_count):
    """生成优化报告"""
    print(f'\n📊 优化报告')
    print('=' * 50)
    print(f'文件: {file_path}')
    print(f'优化数量: {replacements} 处')
    print(f'剩余旧模式: {old_count} 处')
    print(f'新统一接口: {new_count} 处')
    
    if old_count == 0:
        print('🎉 优化完成度: 100%')
    else:
        completion = (new_count / (old_count + new_count)) * 100 if (old_count + new_count) > 0 else 0
        print(f'📈 优化完成度: {completion:.1f}%')
    
    print('\n💡 优化收益:')
    print('  ✅ 减少重复的时间戳格式化代码')
    print('  ✅ 统一日志接口，提高一致性')
    print('  ✅ 简化日志调用，提高可读性')
    print('  ✅ 便于后续维护和修改')

def main():
    """主函数"""
    print('🚀 批量日志优化工具')
    print('=' * 60)
    
    file_path = 'main.py'
    
    if not os.path.exists(file_path):
        print(f'❌ 文件不存在: {file_path}')
        return
    
    # 执行优化
    replacements = optimize_log_calls(file_path)
    
    # 验证结果
    old_count, new_count = validate_optimization(file_path)
    
    # 生成报告
    generate_optimization_report(file_path, replacements, old_count, new_count)
    
    print(f'\n🎯 批量优化完成!')
    if old_count > 0:
        print(f'⚠️ 建议手动处理剩余的 {old_count} 处复杂日志调用')
    else:
        print('✅ 所有日志调用已成功优化')

if __name__ == '__main__':
    main()
