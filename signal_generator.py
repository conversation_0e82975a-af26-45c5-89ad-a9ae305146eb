# coding=utf-8
from __future__ import print_function, absolute_import, unicode_literals
import datetime
import numpy as np
import pandas as pd
import talib
from gm.api import *
from collections import defaultdict
import time
import math
from scripts.data_manager import save_analysis  # 导入save_analysis函数
from talib import abstract as ta

# 导入增强因子引擎
try:
    from enhanced_factor_engine import EnhancedFactorEngine
except ImportError:
    # 如果导入失败，创建一个简单的替代版本
    class EnhancedFactorEngine:
        def __init__(self, context=None):
            self.context = context
        def calculate_all_factors(self, hist_data, symbol):
            return {}

# 直接实现is_trading_hour函数，避免循环导入
def is_trading_hour(current_time=None):
    """
    判断当前是否在交易时间段内
    
    Args:
        current_time: 指定时间，如果为None则使用当前时间
        
    Returns:
        bool: 是否在交易时间段内
    """
    if current_time is None:
        current_time = datetime.datetime.now().time()
    elif isinstance(current_time, datetime.datetime):
        current_time = current_time.time()
        
    # 上午交易时段: 9:30 - 11:30
    morning_session = (
        datetime.time(9, 30) <= current_time <= datetime.time(11, 30)
    )
    
    # 下午交易时段: 13:00 - 15:00
    afternoon_session = (
        datetime.time(13, 0) <= current_time <= datetime.time(15, 0)
    )
    
    return morning_session or afternoon_session

class SignalGenerator:
    def __init__(self, context):
        self.context = context
        # 初始化增强因子引擎
        self.factor_engine = EnhancedFactorEngine(context)
        
    def get_config_value(self, param_name, default=None):
        """获取配置值的辅助方法"""
        try:
            # 优先使用context的get_config_value方法
            if hasattr(self.context, 'get_config_value'):
                return self.context.get_config_value(param_name, default)
            
            # 其次使用context的_get_config_value方法
            elif hasattr(self.context, '_get_config_value'):
                return self.context._get_config_value(self.context, param_name, default)
                
            # 再次尝试从context直接获取属性
            elif hasattr(self.context, param_name.lower()):
                return getattr(self.context, param_name.lower())
                
            # 最后尝试从context.config获取
            elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
                return getattr(self.context.config, param_name)
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取配置值异常 {param_name}: {str(e)}")
            
        return default
    
    def _calculate_trix(self, close_prices, period, use_talib=False):
        """
        计算TRIX指标（调用统一的TRIX计算函数）

        参数:
        - close_prices: 收盘价数组
        - period: TRIX的EMA周期
        - use_talib: 是否使用talib直接计算

        返回:
        - trix: TRIX指标数组
        """
        try:
            # 导入统一的TRIX计算函数
            from main import calculate_trix_unified
            return calculate_trix_unified(close_prices, period, use_talib)
        except Exception as e:
            self.context.log.error(f"计算TRIX指标异常: {str(e)}")
            # 如果导入失败，使用原来的计算方法作为备用
            try:
                if use_talib:
                    # 使用talib直接计算TRIX
                    trix = talib.TRIX(close_prices, timeperiod=period)
                else:
                    # 使用自定义计算方法
                    ema1 = talib.EMA(close_prices, timeperiod=period)
                    ema2 = talib.EMA(ema1, timeperiod=period)
                    ema3 = talib.EMA(ema2, timeperiod=period)

                    # 计算TRIX线
                    trix = np.zeros_like(close_prices)
                    for i in range(1, len(ema3)):
                        if ema3[i-1] != 0:  # 避免除以零
                            trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100

                return trix
            except Exception as backup_error:
                self.context.log.error(f"备用TRIX计算也失败: {str(backup_error)}")
                return None
        
    def analyze_signals(self, symbols):
        """分析股票信号强度，支持不同周期的TRIX指标"""
        signal_analysis = []
        
        # 检查买入信号是否启用
        if not self.get_config_value('ENABLE_BUY_SIGNALS', True):
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 买入信号已禁用，跳过信号分析")
            return []
        
        for symbol in symbols:
            try:
                # 获取历史数据（增加历史数据获取天数）
                days_to_fetch = self.get_config_value('HISTORY_DATA_DAYS', 50)  # 使用配置中的天数
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 尝试获取{days_to_fetch}天历史数据")
                
                hist_data = history(symbol=symbol, 
                                  frequency='1d',
                                  start_time=self.context.now - datetime.timedelta(days=days_to_fetch),
                                  end_time=self.context.now,
                                  fields='close,high,low,volume',
                                  df=True)
                
                if hist_data is None or hist_data.empty:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 未获取到历史数据，跳过")
                    continue
                    
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 实际获取到{len(hist_data)}天历史数据")
                
                if len(hist_data) < 10:  # 确保至少有10天数据
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 历史数据不足10天，跳过")
                    continue
                
                # 检查是否使用实时价格替代当日收盘价
                use_realtime_price = self.get_config_value('USE_REALTIME_PRICE', True)
                
                # 如果启用实时价格且在交易时间内，获取当前价格并添加到历史数据中
                if use_realtime_price and is_trading_hour(self.context.now.time()):
                    try:
                        # 获取当前价格
                        current_data = current(symbols=symbol)
                        if current_data and len(current_data) > 0:
                            current_price = current_data[0]['price']
                            
                            # 记录日志
                            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 使用实时价格 {current_price} 替代当日收盘价")
                            
                            # 确保数据类型为float64
                            close_prices = hist_data['close'].values.astype(np.float64)
                            # 替换最后一个收盘价为当前价格
                            if len(close_prices) > 0:
                                close_prices[-1] = current_price
                        else:
                            # 确保数据类型为float64
                            close_prices = hist_data['close'].values.astype(np.float64)
                            current_price = close_prices[-1]
                    except Exception as e:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取{symbol}实时价格异常: {str(e)}")
                        # 确保数据类型为float64
                        close_prices = hist_data['close'].values.astype(np.float64)
                        current_price = close_prices[-1]
                else:
                    # 确保数据类型为float64
                    close_prices = hist_data['close'].values.astype(np.float64)
                    current_price = close_prices[-1]
                
                # 初始化买入信号
                trix_buy_signal = False
                trix_reversal_signal = False
                ma_cross_buy_signal = False
                rebound_buy_signal = False  # 初始化反弹买入信号
                
                # 获取是否使用talib直接计算TRIX的参数
                use_talib_trix = self.get_config_value('USE_TALIB_TRIX', False)
                
                # 1. 计算TRIX买入信号（如果启用）
                trix_buy = None  # 重命名为trix_buy，避免与拐点计算冲突
                if self.get_config_value('ENABLE_TRIX_BUY_SIGNAL', True):
                    # 获取TRIX EMA周期参数
                    trix_period = self.get_config_value('TRIX_EMA_PERIOD', 3)
                    
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 开始计算TRIX买入信号，周期={trix_period}")
                    
                    # 计算TRIX指标
                    trix_buy = self._calculate_trix(close_prices, trix_period, use_talib_trix)
                    
                    # 确保有足够的有效数据
                    if trix_buy is not None and len(trix_buy) >= 3 and not np.isnan(trix_buy[-1]) and not np.isnan(trix_buy[-2]) and not np.isnan(trix_buy[-3]):
                        # TRIX买入信号：当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX
                        trix_buy_signal = (trix_buy[-1] > trix_buy[-2] and trix_buy[-2] < trix_buy[-3])
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX买入值(周期={trix_period}): 今日={trix_buy[-1]:.6f}, 昨日={trix_buy[-2]:.6f}, 前日={trix_buy[-3]:.6f}, 信号={trix_buy_signal}")
                    else:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX买入信号计算失败: 数据不足或存在NaN")
                
                # 2. 计算TRIX拐点信号（如果启用）- 使用独立周期和独立变量
                trix_reversal = None  # 独立变量，不依赖于trix_buy
                if self.get_config_value('ENABLE_TRIX_REVERSAL_SIGNAL', True):
                    # 获取TRIX拐点周期参数
                    trix_reversal_period = self.get_config_value('TRIX_REVERSAL_PERIOD', 9)
                    
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 开始计算TRIX拐点信号，周期={trix_reversal_period}")
                    
                    # 单独计算拐点TRIX指标，完全独立于买入信号
                    trix_reversal = self._calculate_trix(close_prices, trix_reversal_period, use_talib_trix)
                    
                    # 确保有足够的有效数据
                    if trix_reversal is not None and len(trix_reversal) >= 4 and not np.isnan(trix_reversal[-1]) and not np.isnan(trix_reversal[-2]) and not np.isnan(trix_reversal[-3]) and not np.isnan(trix_reversal[-4]):
                        # TRIX拐点买入信号：昨日TRIX > 前日TRIX 且 前日TRIX < 大前日TRIX
                        trix_reversal_signal = (trix_reversal[-2] > trix_reversal[-3]) and (trix_reversal[-3] < trix_reversal[-4])
                        
                        # 记录TRIX拐点值
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX拐点值(周期={trix_reversal_period}): 今日={trix_reversal[-1]:.6f}, 昨日={trix_reversal[-2]:.6f}, 前日={trix_reversal[-3]:.6f}, 大前日={trix_reversal[-4]:.6f}, 信号={trix_reversal_signal}")
                    else:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX拐点信号计算失败: 数据不足或存在NaN")
                
                # 3. 计算反弹买入信号
                if self.get_config_value('ENABLE_REBOUND_BUY', True):
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 开始计算反弹买入信号")
                    rebound_buy_signal = self.check_rebound_buy_signal(symbol, hist_data, current_price)
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 反弹买入信号: {rebound_buy_signal}")
                
                # 计算更多技术指标
                ma_short_period = self.get_config_value('MA_SHORT_PERIOD', 3)
                ma_mid_period = self.get_config_value('MA_MID_PERIOD', 7)
                ma_long_period = self.get_config_value('MA_LONG_PERIOD', 20)
                
                ma3 = talib.SMA(close_prices, timeperiod=ma_short_period)[-1]
                ma7 = talib.SMA(close_prices, timeperiod=ma_mid_period)[-1]
                ma20 = talib.SMA(close_prices, timeperiod=ma_long_period)[-1]
                
                # 计算均线交叉买入信号 - 如果启用了均线交叉买入信号
                if self.get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', False):
                    ma_cross_buy_signal = (ma3 > ma7) and (ma7 > ma20)
                
                # 计算RSI
                rsi_period = self.get_config_value('RSI_PERIOD', 14)
                rsi = talib.RSI(close_prices, timeperiod=rsi_period)[-1]
                
                # 计算MACD
                macd, macd_signal, macd_hist = talib.MACD(close_prices)
                macd_value = macd[-1]
                
                # 计算KDJ
                high_prices = hist_data['high'].values.astype(np.float64)
                low_prices = hist_data['low'].values.astype(np.float64)
                k, d = talib.STOCH(high_prices, low_prices, close_prices)
                kdj_k = k[-1]
                kdj_d = d[-1]
                
                # 计算布林带
                boll_period = self.get_config_value('BOLL_PERIOD', 20)
                upper, middle, lower = talib.BBANDS(close_prices, timeperiod=boll_period)
                boll_middle = middle[-1]
                
                # 计算成交量比率
                volume_data = hist_data['volume'].values.astype(np.float64)
                volume_ma5 = talib.SMA(volume_data, timeperiod=5)[-1]
                volume_ma20 = talib.SMA(volume_data, timeperiod=20)[-1]
                volume_ratio = volume_ma5 / volume_ma20 if volume_ma20 > 0 else 1.0
                
                # 计算趋势强度
                trend_strength = (close_prices[-1] - close_prices[-5]) / close_prices[-5] * 100
                
                # 综合买入信号 - 任一买入信号为True即可
                final_buy_signal = False
                if self.get_config_value('ENABLE_TRIX_BUY_SIGNAL', True) and trix_buy_signal:
                    final_buy_signal = True
                if self.get_config_value('ENABLE_TRIX_REVERSAL_SIGNAL', True) and trix_reversal_signal:
                    final_buy_signal = True
                if self.get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', False) and ma_cross_buy_signal:
                    final_buy_signal = True
                if self.get_config_value('ENABLE_REBOUND_BUY', True) and rebound_buy_signal:
                    final_buy_signal = True
                

                
                # 记录详细的分析日志
                trix_buy_log_info = f"TRIX买入指标: {trix_buy[-1]:.6f} (启用: {self.get_config_value('ENABLE_TRIX_BUY_SIGNAL', True)})" if trix_buy is not None and len(trix_buy) > 0 else "TRIX买入指标: 未计算"
                trix_reversal_log_info = f"TRIX拐点指标: {trix_reversal[-1]:.6f} (启用: {self.get_config_value('ENABLE_TRIX_REVERSAL_SIGNAL', True)})" if trix_reversal is not None and len(trix_reversal) > 0 else "TRIX拐点指标: 未计算"
                rebound_buy_log_info = f"反弹买入信号: {rebound_buy_signal} (启用: {self.get_config_value('ENABLE_REBOUND_BUY', True)})"

                
                self.context.log.info(f"""
                {self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 技术指标分析 - {symbol}:
                当前价格: {close_prices[-1]:.3f}
                {trix_buy_log_info}
                TRIX买入信号: {trix_buy_signal}
                {trix_reversal_log_info}
                TRIX拐点信号: {trix_reversal_signal}
                {rebound_buy_log_info}
                均线交叉买入信号: {ma_cross_buy_signal} (启用: {self.get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', False)})

                数据长度: {len(hist_data)}天
                """)
                
                signal_info = {
                    'symbol': symbol,
                    'current_price': float(close_prices[-1]),
                    'final_buy_signal': final_buy_signal,
                    'timestamp': self.context.now,
                    'ma3': float(ma3),
                    'ma7': float(ma7),
                    'ma20': float(ma20),
                    'rsi': float(rsi),
                    'macd': float(macd_value),
                    'kdj_k': float(kdj_k),
                    'kdj_d': float(kdj_d),
                    'boll_middle': float(boll_middle),
                    'volume_ratio': float(volume_ratio),
                    'trend_strength': float(trend_strength),
                    'ma_cross_buy_signal': ma_cross_buy_signal,
                    'rebound_buy_signal': rebound_buy_signal,  # 添加反弹买入信号

                }
                

                
                # 添加TRIX买入信号相关数据
                if trix_buy is not None and len(trix_buy) >= 3:
                    signal_info.update({
                        'trix_buy_signal': trix_buy_signal,
                        'trix_buy_current': float(trix_buy[-1]),
                        'trix_buy_prev': float(trix_buy[-2]),
                        'trix_buy_prev2': float(trix_buy[-3])
                    })
                
                # 添加TRIX拐点信号相关数据
                if trix_reversal is not None and len(trix_reversal) >= 4:
                    signal_info.update({
                        'trix_reversal_signal': trix_reversal_signal,
                        'trix_reversal_current': float(trix_reversal[-1]),
                        'trix_reversal_prev': float(trix_reversal[-2]),
                        'trix_reversal_prev2': float(trix_reversal[-3]),
                        'trix_reversal_prev3': float(trix_reversal[-4])
                    })
                
                # 添加反弹买入信号相关数据（如果启用反弹买入策略）
                if self.get_config_value('ENABLE_REBOUND_BUY', True):
                    # 计算反弹相关数据
                    rebound_period = self.get_config_value('REBOUND_PERIOD', 10)
                    low_prices = hist_data['low'].values.astype(np.float64)
                    period_low = np.min(low_prices[-rebound_period:])
                    rebound_ratio = (current_price - period_low) / period_low
                    
                    signal_info.update({
                        'rebound_buy_signal': rebound_buy_signal,
                        'rebound_period_low': float(period_low),
                        'rebound_ratio': float(rebound_ratio * 100)  # 存储为百分比值
                    })
                
                # 🚀 计算增强因子
                enhanced_factors = {}
                try:
                    enhanced_factors = self.factor_engine.calculate_all_factors(hist_data, symbol)
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 计算了{len(enhanced_factors)}个增强因子")
                except Exception as e:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 增强因子计算失败: {str(e)}")

                # 🚀 将增强因子添加到signal_info中，以便传递到买入记录
                signal_info.update(enhanced_factors)

                # 将分析数据保存到analysis_log.csv
                analysis_data = {
                    'Timestamp': self.context.now.strftime('%Y-%m-%d %H:%M:%S'),
                    'Symbol': symbol,
                    'Current_Price': float(current_price),
                    'MA3': float(ma3),
                    'MA7': float(ma7),
                    'MA20': float(ma20),
                    'RSI': float(rsi),
                    'MACD': float(macd_value),
                    'KDJ_K': float(kdj_k),
                    'KDJ_D': float(kdj_d),
                    'Boll_Middle': float(boll_middle),
                    'Volume_Ratio': float(volume_ratio),
                    'Trend_Strength': float(trend_strength),
                    'MA_Cross_Buy_Signal': int(ma_cross_buy_signal),
                    'Rebound_Buy_Signal': int(rebound_buy_signal),  # 添加反弹买入信号

                    'Final_Buy_Signal': int(final_buy_signal)
                }

                # 🚀 合并增强因子到分析数据
                analysis_data.update(enhanced_factors)
                

                
                # 添加TRIX买入信号相关数据
                if trix_buy is not None and len(trix_buy) >= 3:
                    analysis_data.update({
                        'TRIX_Buy_Signal': int(trix_buy_signal),
                        'TRIX_Buy_Current': float(trix_buy[-1]),
                        'TRIX_Buy_Prev': float(trix_buy[-2]),
                        'TRIX_Buy_Prev2': float(trix_buy[-3])
                    })
                
                # 添加TRIX拐点信号相关数据
                if trix_reversal is not None and len(trix_reversal) >= 4:
                    analysis_data.update({
                        'TRIX_Reversal_Signal': int(trix_reversal_signal),
                        'TRIX_Reversal_Current': float(trix_reversal[-1]),
                        'TRIX_Reversal_Prev': float(trix_reversal[-2]),
                        'TRIX_Reversal_Prev2': float(trix_reversal[-3]),
                        'TRIX_Reversal_Prev3': float(trix_reversal[-4])
                    })
                
                # 添加反弹买入信号相关数据
                if self.get_config_value('ENABLE_REBOUND_BUY', True):
                    # 只有在启用反弹买入策略时才添加相关分析数据
                    rebound_period = self.get_config_value('REBOUND_PERIOD', 10)
                    low_prices = hist_data['low'].values.astype(np.float64)
                    period_low = np.min(low_prices[-rebound_period:])
                    rebound_ratio = (current_price - period_low) / period_low
                    
                    analysis_data.update({
                        'Rebound_Period_Low': float(period_low),
                        'Rebound_Ratio': float(rebound_ratio * 100)  # 存储为百分比值
                    })
                
                # 根据开关决定是否保存分析数据
                if hasattr(self.context, 'enable_analysis_log') and self.context.enable_analysis_log:
                    # 直接保存到数据库，不考虑CSV日志
                    save_analysis(analysis_data)
                
                signal_analysis.append(signal_info)
                
            except Exception as e:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 生成信号异常 - {symbol}: {str(e)}")
                import traceback
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
    
        return signal_analysis
        
    def check_sell_conditions(self, position, cost_info):
        """检查卖出条件"""
        try:
            # 检查卖出信号是否启用
            if not self.get_config_value('ENABLE_SELL_SIGNALS', True):
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 卖出信号已禁用，跳过卖出检查")
                return {'trailing_stop': False, 'trix_death_cross': False, 'dynamic_stop_loss': False, 'fixed_profit_stop': False, 'fixed_stop_loss': False, 'time_stop_loss': False, 'max_holding_days': False}
                
            current_price = float(position['price'])
            cost_price = float(cost_info['cost_price'])
            symbol = position['symbol']
            
            # 检查持仓天数 - A股T+1交易规则
            buy_time = cost_info.get('buy_time')
            min_holding_days = self.get_config_value('MIN_HOLDING_DAYS', 1)  # 使用配置的最小持仓天数
            if hasattr(self.context, 't_plus_1') and self.context.t_plus_1 and buy_time and isinstance(buy_time, datetime.datetime):
                # 确保两个日期时间对象都没有时区信息或都有相同的时区信息
                buy_time_no_tz = buy_time.replace(tzinfo=None)
                now_no_tz = self.context.now.replace(tzinfo=None)
                holding_days = (now_no_tz - buy_time_no_tz).days
                if holding_days < min_holding_days:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 持仓未满{min_holding_days}天，根据A股T+1规则不能卖出")
                    return {'trailing_stop': False, 'trix_death_cross': False, 'dynamic_stop_loss': False, 'fixed_profit_stop': False, 'fixed_stop_loss': False, 'time_stop_loss': False, 'max_holding_days': False}
            
            # 检查是否超过最大持仓天数
            max_holding_days_signal = False
            time_stop_loss_signal = False
            if buy_time and isinstance(buy_time, datetime.datetime):
                # 确保两个日期时间对象都没有时区信息或都有相同的时区信息
                buy_time_no_tz = buy_time.replace(tzinfo=None)
                now_no_tz = self.context.now.replace(tzinfo=None)
                holding_days = (now_no_tz - buy_time_no_tz).days

                # 检查最大持仓天数
                max_holding_days = self.get_config_value('MAX_HOLDING_DAYS', 20)  # 使用配置的最大持仓天数
                max_holding_days_signal = holding_days >= max_holding_days
                if max_holding_days_signal:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 持仓已满{holding_days}天，超过最大持仓期限{max_holding_days}天，触发卖出信号")

                # 检查时间止损（新增）
                if self.get_config_value('ENABLE_TIME_STOP_LOSS', True):
                    time_stop_loss_days = self.get_config_value('TIME_STOP_LOSS_DAYS', 3)
                    current_profit_ratio = (current_price - cost_price) / cost_price

                    # 如果持仓超过指定天数且没有盈利，触发时间止损
                    if holding_days >= time_stop_loss_days and current_profit_ratio <= 0:
                        time_stop_loss_signal = True
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 持仓{holding_days}天无盈利（当前收益率: {current_profit_ratio*100:.2f}%），触发时间止损")
                    else:
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 时间止损检查: 持仓{holding_days}天，收益率{current_profit_ratio*100:.2f}%，时间止损阈值{time_stop_loss_days}天")
            
            # 初始化卖出信号
            trailing_stop_signal = False
            trix_sell_signal = False
            dynamic_stop_loss_signal = False
            fixed_profit_stop_signal = False
            fixed_stop_loss_signal = False
            
            # 检查跟踪止盈 - 如果启用了跟踪止盈
            if self.get_config_value('ENABLE_TRAILING_STOP', True):
                # 计算动态跟踪止盈阈值
                base_threshold = self.get_config_value('TRAILING_STOP', 0.055)  # 使用配置的跟踪止盈阈值
                
                # 计算回撤
                drawdown = (float(cost_info['confirmed_high']) - current_price)/float(cost_info['confirmed_high'])
                
                # 跟踪止盈信号
                trailing_stop_signal = drawdown >= base_threshold
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 跟踪止盈检测: 最高价={cost_info['confirmed_high']:.3f}, 当前价={current_price:.3f}, 回撤={drawdown*100:.2f}%, 阈值={base_threshold*100:.2f}%, 信号={trailing_stop_signal}")
            
            # 检查固定止盈 - 如果启用了固定止盈
            if self.get_config_value('ENABLE_FIXED_PROFIT_STOP', True):
                # 获取固定止盈比例
                fixed_profit_ratio = self.get_config_value('FIXED_PROFIT_RATIO', 0.10)  # 使用配置的固定止盈比例
                
                # 计算当前盈利比例
                current_profit_ratio = (current_price - cost_price) / cost_price
                
                # 固定止盈信号
                fixed_profit_stop_signal = current_profit_ratio >= fixed_profit_ratio
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 固定止盈检测: 买入价={cost_price:.3f}, 当前价={current_price:.3f}, 盈利比例={current_profit_ratio*100:.2f}%, 止盈阈值={fixed_profit_ratio*100:.2f}%, 信号={fixed_profit_stop_signal}")
            
            # 检查TRIX死叉 - 如果启用了TRIX死叉卖出信号
            if self.get_config_value('ENABLE_TRIX_SELL_SIGNAL', True):
                # 获取TRIX指标（增加历史数据获取天数）
                hist_data = history(symbol=position['symbol'], 
                                  frequency='1d',
                                  start_time=self.context.now - datetime.timedelta(days=21),
                                  end_time=self.context.now,
                                  fields='close',
                                  df=True)
                
                # 检查是否使用实时价格替代当日收盘价
                use_realtime_price = self.get_config_value('USE_REALTIME_PRICE', True)
                
                # 如果启用实时价格且在交易时间内，获取当前价格并添加到历史数据中
                if use_realtime_price and is_trading_hour(self.context.now.time()):
                    try:
                        # 获取当前价格已经在position中
                        # 记录日志
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 卖出检查使用实时价格 {current_price} 替代当日收盘价")
                        
                        # 确保数据类型为float64
                        close_prices = hist_data['close'].values.astype(np.float64)
                        # 替换最后一个收盘价为当前价格
                        if len(close_prices) > 0:
                            close_prices[-1] = current_price
                    except Exception as e:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 卖出检查替换{symbol}实时价格异常: {str(e)}")
                        # 确保数据类型为float64
                        close_prices = hist_data['close'].values.astype(np.float64)
                else:
                    # 确保数据类型为float64
                    close_prices = hist_data['close'].values.astype(np.float64)
                
                # 如果有足够的数据，计算TRIX指标
                if len(close_prices) >= 12:  # 需要至少12天的数据来计算TRIX
                    try:
                        # 获取TRIX周期参数
                        trix_period = self.get_config_value('TRIX_SELL_EMA_PERIOD', 7)
                        
                        # 计算TRIX指标
                        if self.get_config_value('USE_TALIB_TRIX', False):
                            # 使用talib直接计算TRIX
                            trix = talib.TRIX(close_prices, timeperiod=trix_period)
                        else:
                            # 手动计算TRIX
                            ema1 = talib.EMA(close_prices, timeperiod=trix_period)
                            ema2 = talib.EMA(ema1, timeperiod=trix_period)
                            ema3 = talib.EMA(ema2, timeperiod=trix_period)
                            trix = np.zeros_like(ema3)
                            trix[1:] = (ema3[1:] - ema3[:-1]) / ema3[:-1] * 100
                        
                        # TRIX卖出信号：当日TRIX < 昨日TRIX
                        if len(trix) >= 2 and not np.isnan(trix[-1]) and not np.isnan(trix[-2]):
                            trix_sell_signal = trix[-1] < trix[-2]
                            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX死叉检测: {trix[-1]:.6f} < {trix[-2]:.6f} = {trix_sell_signal}")
                        else:
                            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX数据不足或存在NaN值，无法生成死叉信号")
                    except Exception as e:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 计算TRIX异常: {str(e)}")
                else:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 历史数据不足，无法计算TRIX指标")
            
            # 计算净利润百分比
            net_profit_pct = (current_price - cost_price)/cost_price - 0.0002
            
            # 检查动态止损 - 如果启用了动态止损
            if self.get_config_value('ENABLE_DYNAMIC_STOP_LOSS', True):
                # 使用配置的动态止损比例
                dynamic_stop_loss_ratio = self.get_config_value('DYNAMIC_STOP_LOSS_RATIO', 0.05)
                
                # 计算当前亏损
                current_loss = (float(current_price) - float(cost_price)) / float(cost_price)
                
                # 如果已有动态止损值，使用已有值；否则使用配置的比例
                stop_loss_threshold = cost_info.get('dynamic_stop_loss', dynamic_stop_loss_ratio)
                
                # 动态止损信号
                dynamic_stop_loss_signal = current_loss <= -stop_loss_threshold
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 动态止损检测: 当前亏损={current_loss*100:.2f}%, 止损阈值={stop_loss_threshold*100:.2f}%, 信号={dynamic_stop_loss_signal}")
            
            # 检查固定止损 - 如果启用了固定止损
            if self.get_config_value('ENABLE_FIXED_STOP_LOSS', True):
                # 获取固定止损比例
                fixed_stop_loss_ratio = self.get_config_value('FIXED_STOP_LOSS_RATIO', 0.03)  # 使用配置的固定止损比例
                
                # 计算当前亏损比例
                current_loss = (current_price - cost_price) / cost_price
                
                # 固定止损信号
                fixed_stop_loss_signal = current_loss <= -fixed_stop_loss_ratio
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 固定止损检测: 买入价={cost_price:.3f}, 当前价={current_price:.3f}, 亏损比例={current_loss*100:.2f}%, 止损阈值={fixed_stop_loss_ratio*100:.2f}%, 信号={fixed_stop_loss_signal}")
            
            # 获取卖出信号组合模式
            sell_signal_mode = self.get_config_value('SELL_SIGNAL_MODE', 'any')
            
            # 获取卖出信号优先级
            sell_signal_priority = self.get_config_value('SELL_SIGNAL_PRIORITY', {
                'trailing_stop': 1,       # 跟踪止盈优先级最高
                'fixed_profit_stop': 1.5, # 固定止盈优先级次之
                'dynamic_stop_loss': 2,   # 动态止损再次之
                'trix_death_cross': 3     # TRIX死叉优先级最低
            })
            
            # 创建卖出信号字典
            sell_signals_dict = {
                'trailing_stop': trailing_stop_signal,
                'fixed_profit_stop': fixed_profit_stop_signal,
                'fixed_stop_loss': fixed_stop_loss_signal,
                'trix_death_cross': trix_sell_signal,
                'dynamic_stop_loss': dynamic_stop_loss_signal,
                'time_stop_loss': time_stop_loss_signal,
                'max_holding_days': max_holding_days_signal
            }
            
            # 根据卖出信号组合模式确定最终卖出信号
            final_signals = sell_signals_dict.copy()
            
            # 如果是'all'模式，只有所有信号都为True时才卖出
            if sell_signal_mode == 'all':
                # 检查是否所有启用的信号都为True
                enabled_signals = []
                if self.get_config_value('ENABLE_TRAILING_STOP', True):
                    enabled_signals.append(trailing_stop_signal)
                if self.get_config_value('ENABLE_FIXED_PROFIT_STOP', True):
                    enabled_signals.append(fixed_profit_stop_signal)
                if self.get_config_value('ENABLE_FIXED_STOP_LOSS', True):
                    enabled_signals.append(fixed_stop_loss_signal)
                if self.get_config_value('ENABLE_TRIX_SELL_SIGNAL', True):
                    enabled_signals.append(trix_sell_signal)
                if self.get_config_value('ENABLE_DYNAMIC_STOP_LOSS', True):
                    enabled_signals.append(dynamic_stop_loss_signal)
                if self.get_config_value('ENABLE_TIME_STOP_LOSS', True):
                    enabled_signals.append(time_stop_loss_signal)
                # 最大持仓天数信号总是启用的
                enabled_signals.append(max_holding_days_signal)
                
                # 如果没有启用任何信号，或者所有启用的信号都为True
                if not enabled_signals or all(enabled_signals):
                    # 保持原样
                    pass
                else:
                    # 所有信号设为False
                    for key in final_signals:
                        final_signals[key] = False
            
            # 记录卖出信号组合信息
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 卖出信号组合模式: {sell_signal_mode}, 跟踪止盈: {trailing_stop_signal}, 固定止盈: {fixed_profit_stop_signal}, 固定止损: {fixed_stop_loss_signal}, TRIX死叉: {trix_sell_signal}, 动态止损: {dynamic_stop_loss_signal}, 时间止损: {time_stop_loss_signal}, 最大持仓天数: {max_holding_days_signal}")
            
            # 返回所有卖出信号的字典
            return final_signals
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查卖出条件异常 - {position['symbol']}: {str(e)}")
            import traceback
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 异常堆栈: {traceback.format_exc()}")
            return {'trailing_stop': False, 'trix_death_cross': False, 'dynamic_stop_loss': False, 'fixed_profit_stop': False, 'fixed_stop_loss': False, 'time_stop_loss': False, 'max_holding_days': False}

    def check_rebound_buy_signal(self, symbol, hist_data, current_price):
        """
        检查股票是否满足反弹买入信号
        
        参数:
        hist_data: 包含OHLCV数据的DataFrame
        current_price: 当前价格
        symbol: 股票代码
        
        返回:
        bool: 是否满足反弹买入信号
        """
        if not self.get_config_value('ENABLE_REBOUND_BUY', True):
            return False
            
        try:
            # 确保有足够的数据
            if len(hist_data) < 5:  # 只需要少量数据用于其他条件检查
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 数据不足，无法计算反弹买入信号")
                return False
                
            # 获取价格数据
            close_prices = hist_data['close'].values.astype(np.float64)
            volumes = hist_data['volume'].values.astype(np.float64)
            
            # 优先使用确认低点计算反弹
            use_confirmed_low = False
            days_since_low = None
            
            # 检查是否有确认低点记录
            if hasattr(self.context, 'confirmed_lows') and symbol in self.context.confirmed_lows:
                confirmed_low = self.context.confirmed_lows[symbol]
                period_low = confirmed_low['price']
                low_time = confirmed_low['time']
                
                # 计算低点距今天数
                if isinstance(low_time, datetime.datetime) and isinstance(self.context.now, datetime.datetime):
                    days_since_low = (self.context.now.replace(tzinfo=None) - low_time.replace(tzinfo=None)).days
                    use_confirmed_low = True
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 使用确认低点: {period_low}, 出现于{days_since_low}天前")
            
            # 如果没有可用的确认低点，退回使用历史数据
            if not use_confirmed_low:
                # 获取监控期内的最低价
                rebound_period = self.get_config_value('REBOUND_PERIOD', 10)
                low_prices = hist_data['low'].values.astype(np.float64)
                period_low = np.min(low_prices[-rebound_period:])
                period_low_index = len(low_prices) - rebound_period + np.argmin(low_prices[-rebound_period:])
                
                # 计算最低价距今天数
                days_since_low = len(low_prices) - 1 - period_low_index
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 使用历史数据计算的最低价: {period_low}, 出现于{days_since_low}天前")
            
            # 检查最低价是否在最近几天内出现
            rebound_recent_low_days = self.get_config_value('REBOUND_RECENT_LOW_DAYS', 2)
            if days_since_low > rebound_recent_low_days:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 最低价出现在{days_since_low}天前，不符合最近{rebound_recent_low_days}天内的要求")
                return False
            
            # 计算从最低点到当前的涨幅
            rebound_ratio = (current_price - period_low) / period_low
            
            # 条件1: 反弹幅度达到下限阈值
            rebound_threshold = self.get_config_value('REBOUND_THRESHOLD', 0.025)
            if rebound_ratio < rebound_threshold:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 反弹幅度不足: {rebound_ratio*100:.2f}% < {rebound_threshold*100:.2f}%")
                return False
                
            # 新增条件: 反弹幅度不超过上限阈值
            rebound_max_threshold = self.get_config_value('REBOUND_MAX_THRESHOLD', 0.08)
            if rebound_ratio > rebound_max_threshold:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 反弹幅度过大: {rebound_ratio*100:.2f}% > {rebound_max_threshold*100:.2f}%，避免追高")
                return False
                
            # 条件2: 成交量确认
            if self.get_config_value('REBOUND_VOLUME_FILTER', True):
                avg_volume = np.mean(volumes[-5:])
                volume_ratio = self.get_config_value('REBOUND_VOLUME_RATIO', 1.5)
                if volumes[-1] < avg_volume * volume_ratio:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 成交量不足: {volumes[-1]:.0f} < {avg_volume*volume_ratio:.0f}")
                    return False
                    
            # 条件3: 均线过滤
            if self.get_config_value('REBOUND_MA_FILTER', True):
                ma_period = self.get_config_value('REBOUND_MA_PERIOD', 5)
                ma = np.mean(close_prices[-ma_period:])
                if current_price < ma:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 价格未站上均线: {current_price:.2f} < MA{ma_period}={ma:.2f}")
                    return False
                    
            # 条件4: ATR过滤
            min_atr_ratio = self.get_config_value('REBOUND_MIN_ATR_RATIO', 0.015)
            if min_atr_ratio > 0:
                tr_values = []
                for i in range(1, min(14, len(close_prices))):
                    high = hist_data['high'].values[-i]
                    low = hist_data['low'].values[-i]
                    prev_close = close_prices[-i-1]
                    tr = max(high - low, abs(high - prev_close), abs(low - prev_close))
                    tr_values.append(tr)
                atr = np.mean(tr_values)
                atr_ratio = atr / current_price
                if atr_ratio < min_atr_ratio:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 波动率不足: ATR比例={atr_ratio*100:.2f}% < {min_atr_ratio*100:.2f}%")
                    return False
                    
            # 条件5: 大盘过滤
            if self.get_config_value('REBOUND_WITH_MARKET_FILTER', True):
                # 获取大盘指数数据
                index_symbol = self.get_config_value('INDEX_SYMBOL', 'SHSE.000300')
                try:
                    market_data = history(symbol=index_symbol, 
                                        frequency='1d',
                                        start_time=self.context.now - datetime.timedelta(days=10),
                                        end_time=self.context.now,
                                        fields='close',
                                        df=True)
                    
                    if market_data is not None and len(market_data) >= 5:
                        market_ma5 = np.mean(market_data['close'][-5:])
                        market_current = market_data['close'][-1]
                        if market_current < market_ma5:
                            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 大盘未站上5日均线，不考虑反弹买入")
                            return False
                except Exception as e:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取大盘数据异常: {str(e)}")
            
            # 所有条件都满足，返回True
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 满足反弹买入信号: 从{period_low:.2f}反弹{rebound_ratio*100:.2f}%，最低价出现在{days_since_low}天前，在理想买入区间{rebound_threshold*100:.2f}%-{rebound_max_threshold*100:.2f}%内")
            return True
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算反弹买入信号出错: {str(e)}, symbol: {symbol}")
            import traceback
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 反弹买入异常堆栈: {traceback.format_exc()}")
            return False
    

        





