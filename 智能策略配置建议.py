# coding=utf-8
"""
智能策略配置建议
基于胜率分析结果的革命性改进方案
"""

# =============================================================================
# 智能策略模式系统 (革命性改进)
# =============================================================================

# 智能策略总开关
ENABLE_INTELLIGENT_STRATEGY = True  # 启用智能策略系统

# 策略模式配置 (基于胜率分析结果)
STRATEGY_MODES = {
    'conservative': {
        'name': '保守模式',
        'description': '单指标筛选，稳定收益',
        'expected_win_rate': 0.40,
        'conditions': {
            'day_of_month_max': 10,  # 月初10天买入
        },
        'risk_level': 'low',
        'position_ratio': 0.8,  # 降低仓位
        'max_positions': 30,    # 减少持仓数量
    },
    'balanced': {
        'name': '平衡模式', 
        'description': '双指标组合，平衡风险收益',
        'expected_win_rate': 0.56,
        'conditions': {
            'day_of_month_max': 10,
            'ma5_distance_pct_max': -9.584,  # 远离5日均线
        },
        'risk_level': 'medium',
        'position_ratio': 0.9,
        'max_positions': 40,
    },
    'aggressive': {
        'name': '激进模式',
        'description': '三指标组合，最大化收益',
        'expected_win_rate': 0.57,
        'conditions': {
            'day_of_month_max': 10,
            'price_change_pct_max': -9.366,  # 大跌后买入
            'ma5_distance_pct_max': -9.584,  # 远离5日均线
        },
        'risk_level': 'high',
        'position_ratio': 0.95,
        'max_positions': 50,
    },
    'adaptive': {
        'name': '自适应模式',
        'description': '根据市场环境动态调整',
        'expected_win_rate': 0.60,  # 预期更高
        'conditions': 'dynamic',  # 动态条件
        'risk_level': 'adaptive',
        'position_ratio': 'dynamic',
        'max_positions': 'dynamic',
    }
}

# 当前策略模式
CURRENT_STRATEGY_MODE = 'balanced'  # 推荐从平衡模式开始

# =============================================================================
# 智能时间因子系统 (基于胜率分析发现)
# =============================================================================

# 时间因子配置 (基于day_of_month胜率分析)
INTELLIGENT_TIME_FACTORS = {
    'monthly_pattern': {
        'enabled': True,
        'best_days': list(range(1, 11)),  # 月初1-10天，胜率40.22%
        'avoid_days': list(range(25, 32)), # 月末避免买入
        'weight': 1.5,  # 时间因子权重
    },
    'weekly_pattern': {
        'enabled': True,
        'best_weekdays': [1, 2],  # 周二、周三，胜率32.82%
        'avoid_weekdays': [0, 4], # 周一、周五避免
        'weight': 1.2,
    },
    'seasonal_pattern': {
        'enabled': True,
        'best_months': [3, 4],  # 3-4月，胜率31.94%
        'weight': 1.1,
    }
}

# =============================================================================
# 智能技术指标系统 (基于胜率分析结果)
# =============================================================================

# 技术指标权重配置 (基于胜率分析排名)
INTELLIGENT_INDICATORS = {
    'price_momentum': {
        'price_change_pct': {
            'enabled': True,
            'threshold': -9.366,  # 基于胜率分析的最优阈值
            'weight': 1.4,       # 高权重，胜率37.41%
            'win_rate': 0.3741,
        },
        'price_momentum_5d': {
            'enabled': True,
            'threshold': -9.481,
            'weight': 1.3,
            'win_rate': 0.3621,
        },
        'price_momentum_3d': {
            'enabled': True,
            'threshold': -9.947,
            'weight': 1.2,
            'win_rate': 0.3549,
        }
    },
    'ma_distance': {
        'ma5_distance_pct': {
            'enabled': True,
            'threshold': -9.584,
            'weight': 1.4,
            'win_rate': 0.3693,
        },
        'ma10_distance_pct': {
            'enabled': True,
            'threshold': -9.315,
            'weight': 1.3,
            'win_rate': 0.3573,
        },
        'ma20_distance_pct': {
            'enabled': True,
            'threshold': -7.852,
            'weight': 1.2,
            'win_rate': 0.3621,
        }
    },
    'volatility': {
        'volatility_20d': {
            'enabled': True,
            'threshold': 1.974,
            'direction': 'greater',  # 高波动率买入
            'weight': 1.1,
            'win_rate': 0.3374,
        },
        'atr_normalized': {
            'enabled': True,
            'threshold': 2.694,
            'direction': 'greater',
            'weight': 1.1,
            'win_rate': 0.3333,
        }
    }
}

# =============================================================================
# 智能组合策略系统
# =============================================================================

# 预定义的高胜率组合 (基于分析结果)
HIGH_WIN_RATE_COMBINATIONS = [
    {
        'name': '最优三指标组合',
        'indicators': ['day_of_month', 'price_change_pct', 'ma5_distance_pct'],
        'win_rate': 0.5714,
        'sample_count': 119,
        'avg_profit': 2.4812,
        'conditions': {
            'day_of_month': {'operator': '<=', 'value': 10},
            'price_change_pct': {'operator': '<=', 'value': -9.366},
            'ma5_distance_pct': {'operator': '<=', 'value': -9.584},
        }
    },
    {
        'name': '最优二指标组合',
        'indicators': ['day_of_month', 'ma5_distance_pct'],
        'win_rate': 0.5591,
        'sample_count': 127,
        'avg_profit': 2.2865,
        'conditions': {
            'day_of_month': {'operator': '<=', 'value': 10},
            'ma5_distance_pct': {'operator': '<=', 'value': -9.584},
        }
    }
]

# =============================================================================
# 智能自适应参数系统
# =============================================================================

# 自适应参数配置
ADAPTIVE_PARAMETERS = {
    'win_rate_monitoring': {
        'enabled': True,
        'target_win_rate': 0.35,  # 目标胜率
        'monitoring_period': 30,  # 监控周期(天)
        'adjustment_threshold': 0.05,  # 调整阈值
    },
    'parameter_optimization': {
        'enabled': True,
        'optimization_cycle': 7,  # 优化周期(天)
        'learning_rate': 0.1,     # 学习率
        'max_adjustment': 0.2,    # 最大调整幅度
    },
    'market_regime_detection': {
        'enabled': True,
        'bull_market_threshold': 0.02,   # 牛市阈值
        'bear_market_threshold': -0.02,  # 熊市阈值
        'sideways_threshold': 0.01,      # 震荡市阈值
    }
}

# =============================================================================
# 智能风险管理系统
# =============================================================================

# 基于胜率的动态风险管理
INTELLIGENT_RISK_MANAGEMENT = {
    'position_sizing': {
        'base_position_ratio': 0.02,  # 基础仓位比例
        'win_rate_multiplier': {
            'high_confidence': 1.5,    # 高胜率时增加仓位
            'medium_confidence': 1.0,  # 中等胜率正常仓位
            'low_confidence': 0.5,     # 低胜率减少仓位
        },
        'max_single_position': 0.15,  # 单只股票最大仓位
    },
    'stop_loss_optimization': {
        'dynamic_stop_loss': True,
        'win_rate_based_stop': {
            'high_win_rate': 0.005,   # 高胜率股票更紧止损
            'medium_win_rate': 0.01,  # 中等胜率正常止损
            'low_win_rate': 0.015,    # 低胜率更宽止损
        }
    }
}

# =============================================================================
# 智能缓存和性能优化
# =============================================================================

# 智能缓存策略
INTELLIGENT_CACHE_STRATEGY = {
    'indicator_cache': {
        'enabled': True,
        'priority_indicators': [  # 优先缓存高胜率指标
            'day_of_month', 'price_change_pct', 'ma5_distance_pct',
            'price_momentum_5d', 'ma10_distance_pct', 'volatility_20d'
        ],
        'cache_duration': {
            'high_priority': 3600,    # 1小时
            'medium_priority': 1800,  # 30分钟
            'low_priority': 900,      # 15分钟
        }
    },
    'precompute_strategy': {
        'enabled': True,
        'precompute_combinations': True,  # 预计算高胜率组合
        'precompute_schedule': '09:00',   # 每日9点预计算
    }
}

# =============================================================================
# 智能监控和报告系统
# =============================================================================

# 智能监控配置
INTELLIGENT_MONITORING = {
    'real_time_win_rate': {
        'enabled': True,
        'update_interval': 300,  # 5分钟更新一次
        'alert_threshold': 0.05, # 胜率偏差5%时报警
    },
    'strategy_performance': {
        'enabled': True,
        'daily_report': True,
        'weekly_analysis': True,
        'auto_optimization_suggestion': True,
    },
    'market_condition_analysis': {
        'enabled': True,
        'condition_detection': True,
        'strategy_recommendation': True,
    }
}

# =============================================================================
# 实施建议
# =============================================================================

IMPLEMENTATION_PLAN = {
    'phase_1': {
        'duration': '1-2周',
        'tasks': [
            '实施时间因子筛选 (day_of_month <= 10)',
            '添加实时胜率监控',
            '配置保守模式和平衡模式',
        ],
        'expected_improvement': '胜率从25%提升到40-56%'
    },
    'phase_2': {
        'duration': '3-4周', 
        'tasks': [
            '实施多指标组合策略',
            '添加自适应参数调整',
            '优化缓存和性能',
        ],
        'expected_improvement': '胜率提升到57%，性能提升50%'
    },
    'phase_3': {
        'duration': '1-2个月',
        'tasks': [
            '实施完整智能自适应系统',
            '添加机器学习优化',
            '完善监控和报告系统',
        ],
        'expected_improvement': '胜率提升到60%+，完全自动化'
    }
}

# =============================================================================
# 配置验证和安全机制
# =============================================================================

# 配置验证
CONFIG_VALIDATION = {
    'enabled': True,
    'validate_win_rate_targets': True,
    'validate_risk_parameters': True,
    'validate_indicator_thresholds': True,
    'fallback_to_safe_mode': True,  # 验证失败时回退到安全模式
}

# 安全机制
SAFETY_MECHANISMS = {
    'max_daily_loss': 0.02,      # 日最大亏损2%
    'emergency_stop': True,       # 紧急停止机制
    'position_limit_override': True,  # 仓位限制覆盖
    'win_rate_floor': 0.20,      # 胜率下限20%
}
