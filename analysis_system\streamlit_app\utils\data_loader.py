import pandas as pd
import sqlite3
import os
import streamlit as st
import json
import time
import random
from datetime import datetime

def load_config():
    """加载配置文件"""
    config_file = "analysis_system/streamlit_app/config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except:
            return {
                "data_path": "data",
                "reports_path": "reports"
            }
    else:
        return {
            "data_path": "data",
            "reports_path": "reports"
        }

@st.cache_data(ttl=3600)
def load_trade_log(use_cache=True):
    """加载交易日志数据"""
    config = load_config()
    data_path = config["data_path"]
    file_path = os.path.join(data_path, "trade_log.csv")
    
    if not os.path.exists(file_path):
        st.error(f"交易日志文件不存在: {file_path}")
        return None
    
    try:
        df = pd.read_csv(file_path)
        return df
    except Exception as e:
        st.error(f"加载交易日志失败: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def load_analysis_results():
    """加载分析结果数据"""
    config = load_config()
    reports_path = config["reports_path"]
    file_path = os.path.join(reports_path, "trade_analysis_results.csv")
    
    if not os.path.exists(file_path):
        st.error(f"分析结果文件不存在: {file_path}")
        return None
    
    try:
        df = pd.read_csv(file_path)
        return df
    except Exception as e:
        st.error(f"加载分析结果失败: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def load_trades_from_db():
    """从SQLite数据库加载交易数据"""
    config = load_config()
    data_path = config["data_path"]
    db_path = os.path.join(data_path, "trades.db")
    
    # 检查是否存在刷新标记文件
    refresh_tokens = [f for f in os.listdir(data_path) if f.startswith("refresh_token_")]
    refresh_token = refresh_tokens[0] if refresh_tokens else ""
    
    # 添加随机参数，用于在清空数据库后强制重新加载
    cache_buster = ""
    if "db_cleared" in st.session_state:
        cache_buster = f"?v={st.session_state.db_cleared}"
    
    # 添加刷新标记文件的时间戳作为缓存破坏参数
    if refresh_token:
        cache_buster += f"&refresh={refresh_token}"
    
    if not os.path.exists(db_path):
        st.error(f"交易数据库不存在: {db_path}")
        return None
    
    try:
        # 每次都重新连接数据库，确保获取最新数据
        conn = sqlite3.connect(db_path)
        query = "SELECT * FROM trades"
        df = pd.read_sql_query(query, conn)
        conn.close()
        return df
    except Exception as e:
        st.error(f"从数据库加载交易数据失败: {str(e)}")
        return None

def save_analysis_results(df):
    """保存分析结果"""
    config = load_config()
    reports_path = config["reports_path"]
    os.makedirs(reports_path, exist_ok=True)
    file_path = os.path.join(reports_path, "trade_analysis_results.csv")
    
    try:
        df.to_csv(file_path, index=False)
        return True
    except Exception as e:
        st.error(f"保存分析结果失败: {str(e)}")
        return False

def clear_database():
    """清空数据库中的所有数据但保留表结构"""
    config = load_config()
    data_path = config["data_path"]
    db_path = os.path.join(data_path, "trades.db")
    
    if not os.path.exists(db_path):
        return False, "数据库文件不存在"
    
    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        # 禁用外键约束（如果有）
        cursor.execute("PRAGMA foreign_keys = OFF;")
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION;")
        
        # 保存每个表的结构
        table_structures = {}
        for table in tables:
            table_name = table[0]
            # 获取表结构
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}';")
            create_stmt = cursor.fetchone()[0]
            table_structures[table_name] = create_stmt
            
            # 删除表并重新创建（更彻底的清空方式）
            try:
                cursor.execute(f"DROP TABLE {table_name};")
                cursor.execute(create_stmt)
            except Exception as e:
                # 如果删除失败，尝试使用DELETE FROM
                cursor.execute(f"DELETE FROM {table_name};")
                # 重置自增ID（如果有）
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}';")
        
        # 提交事务
        cursor.execute("COMMIT;")
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON;")
        
        # 获取清空后的记录数（重新连接以确保获取最新数据）
        conn.close()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        record_count = 0
        if "trades" in [t[0] for t in tables]:
            cursor.execute("SELECT COUNT(*) FROM trades;")
            record_count = cursor.fetchone()[0]
        conn.close()
        
        # 创建一个刷新标记文件
        refresh_token = datetime.now().strftime("%Y%m%d%H%M%S")
        with open(os.path.join(data_path, f"refresh_token_{refresh_token}.tmp"), "w") as f:
            f.write("refresh")
        
        # 清理旧的刷新标记文件
        for file in os.listdir(data_path):
            if file.startswith("refresh_token_") and file != f"refresh_token_{refresh_token}.tmp":
                try:
                    os.remove(os.path.join(data_path, file))
                except:
                    pass
        
        # 强制清除所有缓存
        if hasattr(st, 'cache_data'):
            st.cache_data.clear()
        if hasattr(st, 'cache_resource'):
            st.cache_resource.clear()
        
        # 清除load_trades_from_db函数的缓存
        if hasattr(load_trades_from_db, 'clear'):
            load_trades_from_db.clear()
        
        # 添加随机参数到数据库路径，以确保下次加载时不使用缓存
        db_path_with_timestamp = f"{db_path}?v={random.randint(1, 10000)}&refresh={refresh_token}"
        
        # 返回成功信息
        return True, f"数据库已彻底清空，当前记录数: {record_count}"
    except Exception as e:
        # 如果发生错误，尝试回滚事务
        try:
            conn.execute("ROLLBACK;")
        except:
            pass
        return False, f"清空数据库失败: {str(e)}"

def clear_csv_data():
    """清空CSV交易日志数据"""
    config = load_config()
    data_path = config["data_path"]
    trade_log_path = os.path.join(data_path, "trade_log.csv")
    analysis_log_path = os.path.join(data_path, "analysis_log.csv")
    
    files_cleared = []
    errors = []
    
    # 清空交易日志
    if os.path.exists(trade_log_path):
        try:
            # 创建一个空的DataFrame并保留列名
            if os.path.getsize(trade_log_path) > 0:
                df = pd.read_csv(trade_log_path, nrows=0)
                df.to_csv(trade_log_path, index=False)
                files_cleared.append("交易日志")
            else:
                # 如果文件为空，则创建一个空文件
                open(trade_log_path, 'w').close()
                files_cleared.append("交易日志")
        except Exception as e:
            errors.append(f"清空交易日志失败: {str(e)}")
    
    # 清空分析日志
    if os.path.exists(analysis_log_path):
        try:
            # 创建一个空的DataFrame并保留列名
            if os.path.getsize(analysis_log_path) > 0:
                df = pd.read_csv(analysis_log_path, nrows=0)
                df.to_csv(analysis_log_path, index=False)
                files_cleared.append("分析日志")
            else:
                # 如果文件为空，则创建一个空文件
                open(analysis_log_path, 'w').close()
                files_cleared.append("分析日志")
        except Exception as e:
            errors.append(f"清空分析日志失败: {str(e)}")
    
    # 清空分析结果
    reports_path = config["reports_path"]
    analysis_results_path = os.path.join(reports_path, "trade_analysis_results.csv")
    if os.path.exists(analysis_results_path):
        try:
            # 创建一个空的DataFrame并保留列名
            if os.path.getsize(analysis_results_path) > 0:
                df = pd.read_csv(analysis_results_path, nrows=0)
                df.to_csv(analysis_results_path, index=False)
                files_cleared.append("分析结果")
            else:
                # 如果文件为空，则创建一个空文件
                open(analysis_results_path, 'w').close()
                files_cleared.append("分析结果")
        except Exception as e:
            errors.append(f"清空分析结果失败: {str(e)}")
    
    # 清除缓存的数据
    if hasattr(st, 'cache_data'):
        st.cache_data.clear()
    
    if errors:
        return False, f"部分文件清空失败: {', '.join(errors)}"
    elif files_cleared:
        return True, f"已清空文件: {', '.join(files_cleared)}"
    else:
        return False, "未找到任何可清空的文件"

def rebuild_database():
    """通过重建数据库文件来彻底清空数据库"""
    config = load_config()
    data_path = config["data_path"]
    db_path = os.path.join(data_path, "trades.db")
    
    if not os.path.exists(db_path):
        return False, "数据库文件不存在"
    
    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名和创建语句
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='table';")
        tables_data = cursor.fetchall()
        
        # 创建一个临时数据库
        temp_db_path = os.path.join(data_path, "trades_temp.db")
        if os.path.exists(temp_db_path):
            os.remove(temp_db_path)
        
        temp_conn = sqlite3.connect(temp_db_path)
        temp_cursor = temp_conn.cursor()
        
        # 在临时数据库中重新创建所有表
        for table_name, create_stmt in tables_data:
            if create_stmt is not None:  # 跳过sqlite_sequence等系统表
                temp_cursor.execute(create_stmt)
        
        # 提交更改并关闭连接
        temp_conn.commit()
        temp_conn.close()
        conn.close()
        
        # 备份原始数据库（以防万一）
        backup_db_path = os.path.join(data_path, f"trades_backup_{datetime.now().strftime('%Y%m%d%H%M%S')}.db")
        import shutil
        shutil.copy2(db_path, backup_db_path)
        
        # 用临时数据库替换原始数据库
        os.remove(db_path)
        os.rename(temp_db_path, db_path)
        
        # 创建一个刷新标记文件
        refresh_token = datetime.now().strftime("%Y%m%d%H%M%S")
        with open(os.path.join(data_path, f"refresh_token_{refresh_token}.tmp"), "w") as f:
            f.write("refresh")
        
        # 清理旧的刷新标记文件
        for file in os.listdir(data_path):
            if file.startswith("refresh_token_") and file != f"refresh_token_{refresh_token}.tmp":
                try:
                    os.remove(os.path.join(data_path, file))
                except:
                    pass
        
        # 强制清除所有缓存
        if hasattr(st, 'cache_data'):
            st.cache_data.clear()
        if hasattr(st, 'cache_resource'):
            st.cache_resource.clear()
        
        # 清除load_trades_from_db函数的缓存
        if hasattr(load_trades_from_db, 'clear'):
            load_trades_from_db.clear()
        
        # 验证数据库是否真的被清空
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        record_count = 0
        if "trades" in [t[0] for t in tables]:
            cursor.execute("SELECT COUNT(*) FROM trades;")
            record_count = cursor.fetchone()[0]
        conn.close()
        
        return True, f"数据库已完全重建，当前记录数: {record_count}"
    except Exception as e:
        return False, f"重建数据库失败: {str(e)}"

def clear_analysis_results():
    """清空所有分析结果文件，但保留原始数据"""
    config = load_config()
    reports_path = config["reports_path"]
    
    if not os.path.exists(reports_path):
        return False, "报告目录不存在"
    
    files_cleared = []
    errors = []
    
    # 清空分析结果文件
    analysis_results_path = os.path.join(reports_path, "trade_analysis_results.csv")
    if os.path.exists(analysis_results_path):
        try:
            # 创建一个空的DataFrame并保留列名
            if os.path.getsize(analysis_results_path) > 0:
                df = pd.read_csv(analysis_results_path, nrows=0)
                df.to_csv(analysis_results_path, index=False)
                files_cleared.append("交易分析结果")
            else:
                # 如果文件为空，则创建一个空文件
                open(analysis_results_path, 'w').close()
                files_cleared.append("交易分析结果")
        except Exception as e:
            errors.append(f"清空交易分析结果失败: {str(e)}")
    
    # 清空策略优化规则文件
    strategy_rules_path = os.path.join(reports_path, "optimal_strategy_rules.txt")
    if os.path.exists(strategy_rules_path):
        try:
            open(strategy_rules_path, 'w').close()
            files_cleared.append("策略优化规则")
        except Exception as e:
            errors.append(f"清空策略优化规则失败: {str(e)}")
    
    # 清空特征重要性图表
    feature_importance_path = os.path.join(reports_path, "feature_importance.png")
    if os.path.exists(feature_importance_path):
        try:
            os.remove(feature_importance_path)
            files_cleared.append("特征重要性图表")
        except Exception as e:
            errors.append(f"删除特征重要性图表失败: {str(e)}")
    
    # 清空模型文件
    model_path = os.path.join(reports_path, "optimal_strategy_model.pkl")
    if os.path.exists(model_path):
        try:
            os.remove(model_path)
            files_cleared.append("优化模型文件")
        except Exception as e:
            errors.append(f"删除优化模型文件失败: {str(e)}")
    
    # 清空HTML报告
    html_reports_path = os.path.join(reports_path, "reports_guide.html")
    if os.path.exists(html_reports_path):
        try:
            os.remove(html_reports_path)
            files_cleared.append("HTML报告")
        except Exception as e:
            errors.append(f"删除HTML报告失败: {str(e)}")
    
    # 清除缓存的数据
    if hasattr(st, 'cache_data'):
        st.cache_data.clear()
    
    # 创建一个刷新标记文件
    refresh_token = datetime.now().strftime("%Y%m%d%H%M%S")
    data_path = config["data_path"]
    with open(os.path.join(data_path, f"refresh_token_{refresh_token}.tmp"), "w") as f:
        f.write("refresh")
    
    if errors:
        return False, f"部分文件清空失败: {', '.join(errors)}"
    elif files_cleared:
        return True, f"已清空分析结果: {', '.join(files_cleared)}"
    else:
        return False, "未找到任何分析结果文件" 