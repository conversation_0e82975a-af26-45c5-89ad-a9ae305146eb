# coding=utf-8
"""
数据去重和版本管理方案
解决频繁回测导致的数据重复问题
"""

import sqlite3
import pandas as pd
import hashlib
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class BacktestDataManager:
    """回测数据管理器 - 解决数据重复问题"""
    
    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        self.setup_data_management_tables()
    
    def setup_data_management_tables(self):
        """设置数据管理表"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建回测版本管理表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS backtest_versions (
                    version_id TEXT PRIMARY KEY,
                    start_date TEXT,
                    end_date TEXT,
                    strategy_config TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    record_count INTEGER,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # 创建交易记录版本关联表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS trade_versions (
                    trade_id INTEGER,
                    version_id TEXT,
                    record_hash TEXT,
                    PRIMARY KEY (trade_id, version_id)
                )
            ''')
            
            # 为trades表添加唯一标识字段（如果不存在）
            try:
                conn.execute('ALTER TABLE trades ADD COLUMN record_hash TEXT')
                conn.execute('ALTER TABLE trades ADD COLUMN version_id TEXT')
            except:
                pass  # 字段已存在
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f'❌ 设置数据管理表失败: {e}')
    
    def generate_record_hash(self, record):
        """生成记录哈希值"""
        try:
            # 使用关键字段生成唯一哈希
            key_fields = [
                str(record.get('symbol', '')),
                str(record.get('timestamp', '')),
                str(record.get('action', '')),
                str(record.get('price', '')),
                str(record.get('volume', ''))
            ]
            
            hash_string = '|'.join(key_fields)
            return hashlib.md5(hash_string.encode()).hexdigest()
            
        except Exception as e:
            return None
    
    def create_backtest_version(self, start_date, end_date, strategy_config=None):
        """创建新的回测版本"""
        try:
            version_id = f"v_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            conn = sqlite3.connect(self.db_path)
            conn.execute('''
                INSERT INTO backtest_versions 
                (version_id, start_date, end_date, strategy_config)
                VALUES (?, ?, ?, ?)
            ''', (version_id, start_date, end_date, str(strategy_config)))
            
            conn.commit()
            conn.close()
            
            print(f'✅ 创建回测版本: {version_id}')
            return version_id
            
        except Exception as e:
            print(f'❌ 创建回测版本失败: {e}')
            return None
    
    def clean_duplicate_trades(self, version_id=None):
        """清理重复交易记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            print('🧹 开始清理重复交易记录...')
            
            # 1. 为现有记录生成哈希值
            trades_query = "SELECT * FROM trades WHERE record_hash IS NULL"
            trades = pd.read_sql_query(trades_query, conn)
            
            print(f'📊 需要处理的记录数: {len(trades)}')
            
            updated_count = 0
            for idx, trade in trades.iterrows():
                record_hash = self.generate_record_hash(trade.to_dict())
                if record_hash:
                    conn.execute(
                        'UPDATE trades SET record_hash = ?, version_id = ? WHERE id = ?',
                        (record_hash, version_id, trade.get('id'))
                    )
                    updated_count += 1
            
            # 2. 识别重复记录
            duplicate_query = '''
                SELECT record_hash, COUNT(*) as count, MIN(id) as keep_id
                FROM trades 
                WHERE record_hash IS NOT NULL
                GROUP BY record_hash
                HAVING COUNT(*) > 1
            '''
            
            duplicates = pd.read_sql_query(duplicate_query, conn)
            print(f'🔍 发现重复记录组: {len(duplicates)}')
            
            # 3. 删除重复记录（保留最早的）
            deleted_count = 0
            for _, dup in duplicates.iterrows():
                delete_query = '''
                    DELETE FROM trades 
                    WHERE record_hash = ? AND id != ?
                '''
                cursor = conn.execute(delete_query, (dup['record_hash'], dup['keep_id']))
                deleted_count += cursor.rowcount
            
            conn.commit()
            conn.close()
            
            print(f'✅ 清理完成:')
            print(f'  更新哈希值: {updated_count} 条')
            print(f'  删除重复记录: {deleted_count} 条')
            
            return {
                'updated_count': updated_count,
                'deleted_count': deleted_count,
                'duplicate_groups': len(duplicates)
            }
            
        except Exception as e:
            print(f'❌ 清理重复记录失败: {e}')
            return None
    
    def get_clean_analysis_data(self, start_date=None, end_date=None):
        """获取去重后的分析数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 构建查询条件
            where_conditions = ["record_hash IS NOT NULL"]  # 只要有哈希值的记录
            params = []
            
            if start_date:
                where_conditions.append("timestamp >= ?")
                params.append(start_date)
            
            if end_date:
                where_conditions.append("timestamp <= ?")
                params.append(end_date)
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询去重后的数据
            query = f'''
                SELECT DISTINCT * FROM trades 
                WHERE {where_clause}
                ORDER BY timestamp DESC
            '''
            
            data = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            print(f'📊 获取去重数据: {len(data)} 条记录')
            return data
            
        except Exception as e:
            print(f'❌ 获取分析数据失败: {e}')
            return None
    
    def analyze_data_quality(self):
        """分析数据质量"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 总记录数
            total_query = "SELECT COUNT(*) as total FROM trades"
            total_result = pd.read_sql_query(total_query, conn)
            total_count = total_result['total'].iloc[0]
            
            # 有哈希值的记录数
            hashed_query = "SELECT COUNT(*) as hashed FROM trades WHERE record_hash IS NOT NULL"
            hashed_result = pd.read_sql_query(hashed_query, conn)
            hashed_count = hashed_result['hashed'].iloc[0]
            
            # 唯一记录数
            unique_query = "SELECT COUNT(DISTINCT record_hash) as unique_count FROM trades WHERE record_hash IS NOT NULL"
            unique_result = pd.read_sql_query(unique_query, conn)
            unique_count = unique_result['unique_count'].iloc[0]
            
            # 重复记录统计
            duplicate_query = '''
                SELECT record_hash, COUNT(*) as count
                FROM trades 
                WHERE record_hash IS NOT NULL
                GROUP BY record_hash
                HAVING COUNT(*) > 1
                ORDER BY count DESC
            '''
            duplicates = pd.read_sql_query(duplicate_query, conn)
            
            # 时间分布分析
            time_dist_query = '''
                SELECT DATE(timestamp) as date, COUNT(*) as count
                FROM trades
                WHERE record_hash IS NOT NULL
                GROUP BY DATE(timestamp)
                ORDER BY date
            '''
            time_dist = pd.read_sql_query(time_dist_query, conn)
            
            conn.close()
            
            # 生成报告
            print('📊 数据质量分析报告')
            print('=' * 50)
            print(f'总记录数: {total_count:,}')
            print(f'已处理记录数: {hashed_count:,}')
            print(f'唯一记录数: {unique_count:,}')
            print(f'重复记录数: {hashed_count - unique_count:,}')
            print(f'重复率: {((hashed_count - unique_count) / hashed_count * 100):.2f}%')
            
            if len(duplicates) > 0:
                print(f'\n🔍 重复记录分布:')
                print(f'重复组数: {len(duplicates)}')
                print(f'最大重复次数: {duplicates["count"].max()}')
                print(f'平均重复次数: {duplicates["count"].mean():.2f}')
            
            if len(time_dist) > 0:
                print(f'\n📅 时间分布:')
                print(f'数据时间跨度: {time_dist["date"].min()} 到 {time_dist["date"].max()}')
                print(f'平均每日记录数: {time_dist["count"].mean():.1f}')
            
            return {
                'total_count': total_count,
                'hashed_count': hashed_count,
                'unique_count': unique_count,
                'duplicate_count': hashed_count - unique_count,
                'duplicate_rate': (hashed_count - unique_count) / hashed_count if hashed_count > 0 else 0,
                'duplicate_groups': len(duplicates),
                'time_span_days': len(time_dist)
            }
            
        except Exception as e:
            print(f'❌ 数据质量分析失败: {e}')
            return None

def clean_and_analyze_database():
    """清理数据库并进行分析"""
    print('🧹 数据库清理和分析工具')
    print('=' * 60)
    
    # 创建数据管理器
    manager = BacktestDataManager()
    
    # 1. 分析当前数据质量
    print('\n📊 Step 1: 分析当前数据质量')
    quality_before = manager.analyze_data_quality()
    
    # 2. 创建新版本
    print('\n📝 Step 2: 创建数据版本')
    version_id = manager.create_backtest_version(
        start_date='2025-01-01',
        end_date='2025-12-31',
        strategy_config='current_strategy'
    )
    
    # 3. 清理重复数据
    print('\n🧹 Step 3: 清理重复数据')
    clean_result = manager.clean_duplicate_trades(version_id)
    
    # 4. 分析清理后的数据质量
    print('\n📊 Step 4: 分析清理后数据质量')
    quality_after = manager.analyze_data_quality()
    
    # 5. 生成清理报告
    if quality_before and quality_after and clean_result:
        print('\n📋 清理效果总结')
        print('=' * 40)
        print(f'清理前总记录: {quality_before["total_count"]:,}')
        print(f'清理后唯一记录: {quality_after["unique_count"]:,}')
        print(f'删除重复记录: {clean_result["deleted_count"]:,}')
        print(f'数据压缩率: {(clean_result["deleted_count"] / quality_before["total_count"] * 100):.2f}%')
        
        # 6. 获取清理后的数据用于分析
        print('\n📈 Step 5: 准备分析数据')
        clean_data = manager.get_clean_analysis_data()
        
        if clean_data is not None:
            print(f'✅ 清理完成！可用于分析的记录数: {len(clean_data):,}')
            return clean_data
        else:
            print('❌ 获取清理数据失败')
            return None
    else:
        print('❌ 清理过程出现问题')
        return None

def main():
    """主函数"""
    print('🧹 数据库去重和版本管理工具')
    print('=' * 60)
    
    # 执行清理和分析
    clean_data = clean_and_analyze_database()
    
    if clean_data is not None:
        print('\n💡 建议:')
        print('1. 使用清理后的数据重新运行胜率分析')
        print('2. 在未来的回测中使用版本管理机制')
        print('3. 定期运行数据质量检查')
        print('4. 考虑实施增量更新策略')
    else:
        print('\n❌ 清理失败，请检查数据库状态')

if __name__ == "__main__":
    main()
