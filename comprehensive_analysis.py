# coding=utf-8
"""
综合分析脚本
整合所有分析器，提供完整的策略分析
"""

import os
import sys
from datetime import datetime

def check_data_availability():
    """检查数据可用性"""
    print('🔍 检查数据可用性')
    print('=' * 50)
    
    try:
        import sqlite3
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 检查买入记录
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'BUY'")
        buy_count = cursor.fetchone()[0]
        
        # 检查卖出记录
        cursor.execute("SELECT COUNT(*) FROM trades WHERE action = 'SELL'")
        sell_count = cursor.fetchone()[0]
        
        # 检查有盈亏数据的记录
        cursor.execute("SELECT COUNT(*) FROM trades WHERE net_profit_pct_sell IS NOT NULL")
        profit_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f'📈 买入记录: {buy_count:,} 条')
        print(f'📉 卖出记录: {sell_count:,} 条')
        print(f'💰 有盈亏数据: {profit_count:,} 条')
        
        # 判断可用的分析方法
        available_analyses = []
        
        if buy_count > 0 and sell_count > 0:
            available_analyses.append('胜率分析器 (买入-卖出匹配)')
        
        if buy_count > 0:
            available_analyses.append('因子有效性分析器 (买入因子)')
            
        if profit_count > 0:
            available_analyses.append('卖出记录分析器 (历史胜率)')
        
        print(f'\n✅ 可用分析方法: {len(available_analyses)} 种')
        for i, analysis in enumerate(available_analyses, 1):
            print(f'  {i}. {analysis}')
        
        return {
            'buy_count': buy_count,
            'sell_count': sell_count,
            'profit_count': profit_count,
            'available_analyses': available_analyses
        }
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return None

def run_comprehensive_analysis():
    """运行综合分析"""
    print('🚀 策略综合分析工具')
    print('=' * 60)
    
    # 1. 检查数据可用性
    data_status = check_data_availability()
    if not data_status:
        print('❌ 数据检查失败，无法进行分析')
        return
    
    # 2. 根据数据情况选择分析方法
    if data_status['buy_count'] == 0:
        print('\n⚠️ 没有买入记录，只能分析历史卖出数据')
        print('💡 建议：运行策略生成买入记录后再进行完整分析')
        
        if data_status['profit_count'] > 0:
            print('\n🔄 运行卖出记录分析...')
            run_sell_records_analysis()
        else:
            print('❌ 没有可分析的数据')
        return
    
    # 3. 有买入记录的情况下，运行完整分析
    print('\n🎯 开始综合分析...')
    
    analyses_to_run = []
    
    # 选择分析方法
    print('\n📋 可用分析方法:')
    print('  1. 胜率分析器 (买入-卖出匹配分析)')
    print('  2. 因子有效性分析器 (买入因子分析)')
    print('  3. 卖出记录分析器 (历史胜率分析)')
    print('  4. 全部运行')
    
    choice = input('\n选择分析方法 (1-4): ').strip()
    
    if choice == '1':
        analyses_to_run = ['胜率分析器']
    elif choice == '2':
        analyses_to_run = ['因子有效性分析器']
    elif choice == '3':
        analyses_to_run = ['卖出记录分析器']
    elif choice == '4':
        analyses_to_run = ['胜率分析器', '因子有效性分析器', '卖出记录分析器']
    else:
        print('❌ 无效选择')
        return
    
    # 4. 执行选定的分析
    results = {}
    for analysis in analyses_to_run:
        print(f'\n{"="*60}')
        print(f'🔄 运行 {analysis}...')
        print(f'{"="*60}')
        
        try:
            if analysis == '胜率分析器':
                result = run_win_rate_analysis()
                results['win_rate'] = result
            elif analysis == '因子有效性分析器':
                result = run_factor_analysis()
                results['factor'] = result
            elif analysis == '卖出记录分析器':
                result = run_sell_records_analysis()
                results['sell_records'] = result
                
        except Exception as e:
            print(f'❌ {analysis} 运行失败: {e}')
            continue
    
    # 5. 生成综合报告
    generate_comprehensive_report(results)

def run_win_rate_analysis():
    """运行胜率分析器"""
    try:
        print('📊 正在运行胜率分析器...')
        os.system('python 胜率分析器.py')
        return True
    except Exception as e:
        print(f'❌ 胜率分析器运行失败: {e}')
        return False

def run_factor_analysis():
    """运行因子有效性分析器"""
    try:
        print('🔍 正在运行因子有效性分析器...')
        os.system('python 因子有效性分析器.py')
        return True
    except Exception as e:
        print(f'❌ 因子有效性分析器运行失败: {e}')
        return False

def run_sell_records_analysis():
    """运行卖出记录分析器"""
    try:
        print('📈 正在运行卖出记录分析器...')
        os.system('python sell_records_analyzer.py')
        return True
    except Exception as e:
        print(f'❌ 卖出记录分析器运行失败: {e}')
        return False

def generate_comprehensive_report(results):
    """生成综合报告"""
    print('\n' + '='*80)
    print('📊 综合分析报告')
    print('='*80)
    
    print(f'📅 分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print(f'🔍 完成分析: {len(results)} 项')
    
    for analysis_type, result in results.items():
        if result:
            print(f'  ✅ {analysis_type}: 成功')
        else:
            print(f'  ❌ {analysis_type}: 失败')
    
    print('\n💡 分析建议:')
    if len(results) >= 2:
        print('  🎯 多维度分析已完成，可以综合考虑各项指标')
        print('  📈 建议重点关注胜率最高的因子组合')
        print('  🔄 定期重新分析以验证因子有效性')
    else:
        print('  ⚠️ 建议运行完整的多维度分析')
        print('  📊 收集更多交易数据以提高分析准确性')
    
    print('\n📋 后续行动:')
    print('  1. 根据分析结果优化买入条件')
    print('  2. 调整技术指标权重')
    print('  3. 设置动态仓位管理')
    print('  4. 建立因子监控机制')

def show_usage():
    """显示使用说明"""
    print('📖 使用说明')
    print('=' * 40)
    print('🎯 功能: 整合所有分析器，提供完整的策略分析')
    print('')
    print('📊 包含分析:')
    print('  • 胜率分析器: 买入-卖出记录匹配，计算真实胜率')
    print('  • 因子有效性分析器: 分析买入时各技术指标的有效性')
    print('  • 卖出记录分析器: 基于历史卖出数据的胜率分析')
    print('')
    print('🔧 使用方法:')
    print('  python comprehensive_analysis.py')
    print('')
    print('📋 前置条件:')
    print('  • 数据库中有交易记录')
    print('  • 买入记录保存功能已修复')
    print('  • 相关分析脚本文件存在')

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        show_usage()
        return
    
    run_comprehensive_analysis()

if __name__ == '__main__':
    main()
