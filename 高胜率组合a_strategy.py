
# 基于因子分析的高胜率组合A策略实现
# 高波动 + 宽布林带 + MACD金叉
# 预期胜率: 35%+, 实际胜率: 25.7%

def apply_高胜率组合a_strategy(data, context):
    """
    应用高胜率组合A策略
    高波动 + 宽布林带 + MACD金叉
    """
    # 初始化信号
    buy_signal = False
    signal_reason = ""
    
    # 检查数据有效性
    if data is None or len(data) < 30:
        return False, "数据不足"
    
    try:
        # 计算技术指标
        from enhanced_factor_engine import EnhancedFactorEngine
        factor_engine = EnhancedFactorEngine(context)
        factors = factor_engine.calculate_all_factors(data, data.index[-1])
        
        # 应用高胜率组合A条件
        # 检查atr_pct > 2.7条件
        if "atr_pct" not in factors:
            return False, "atr_pct指标缺失"
        
        if not (factors["atr_pct"] > 2.7):
            return False, "atr_pct = {:.2f} 不满足 > 2.7".format(factors["atr_pct"])
        
        # 检查bb_width > 10.8条件
        if "bb_width" not in factors:
            return False, "bb_width指标缺失"
        
        if not (factors["bb_width"] > 10.8):
            return False, "bb_width = {:.2f} 不满足 > 10.8".format(factors["bb_width"])
        
        # 检查macd_hist > 0条件
        if "macd_hist" not in factors:
            return False, "macd_hist指标缺失"
        
        if not (factors["macd_hist"] > 0):
            return False, "macd_hist = {:.2f} 不满足 > 0".format(factors["macd_hist"])
        
        # 所有条件满足，生成买入信号
        buy_signal = True
        signal_reason = "高波动 + 宽布林带 + MACD金叉信号触发"
        
        # 记录关键指标值
        signal_reason += ", atr_pct={:.2f}".format(factors["atr_pct"])
        
        signal_reason += ", bb_width={:.2f}".format(factors["bb_width"])
        
        signal_reason += ", macd_hist={:.2f}".format(factors["macd_hist"])
        
        return buy_signal, signal_reason
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 应用高胜率组合A策略失败: {str(e)}")
        return False, f"策略应用异常: {str(e)}"
