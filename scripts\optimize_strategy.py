# coding=utf-8
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import joblib
import os

# 文件路径
TRADE_ANALYSIS_FILE = 'reports/trade_analysis_results.csv'
MODEL_OUTPUT_FILE = 'reports/optimal_strategy_model.pkl'
STRATEGY_RULES_FILE = 'reports/optimal_strategy_rules.txt'

def optimize_strategy():
    """使用机器学习方法优化交易策略"""
    print("开始优化交易策略...")
    
    # 读取交易分析结果
    try:
        df = pd.read_csv(TRADE_ANALYSIS_FILE)
        print(f"成功读取交易分析数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取交易分析数据失败: {e}")
        print("请先运行 analyze_trades.py 生成交易分析结果")
        return
    
    # 数据预处理
    print("数据预处理...")
    
    # 创建目标变量 - 是否盈利
    df['Profitable'] = (df['Profit_Pct'] > 0).astype(int)
    
    # 选择特征
    features = []
    for feature in ['TRIX_Buy', 'Volatility_Buy', 'ATR_Pct_Buy', 'Volatility_Score_Buy', 'Allocation_Factor_Buy']:
        if feature in df.columns and not df[feature].isna().all():
            features.append(feature)
    
    if not features:
        print("没有足够的特征进行模型训练")
        return
        
    print(f"使用特征: {', '.join(features)}")
    
    # 准备训练数据
    X = df[features].copy()
    y = df['Profitable']
    
    # 处理缺失值
    X = X.fillna(X.mean())
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    print(f"训练集: {len(X_train)}条记录")
    print(f"测试集: {len(X_test)}条记录")
    
    # 训练模型
    print("\n训练随机森林模型...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    
    # 评估模型
    y_pred = rf_model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"模型准确率: {accuracy:.2f}")
    
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    # 特征重要性
    feature_importance = pd.DataFrame({
        'Feature': features,
        'Importance': rf_model.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    print("\n特征重要性:")
    print(feature_importance)
    
    # 可视化特征重要性
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feature_importance)
    plt.title('特征重要性')
    plt.tight_layout()
    plt.savefig('reports/feature_importance.png')
    
    # 保存模型
    joblib.dump(rf_model, MODEL_OUTPUT_FILE)
    print(f"\n模型已保存到 {MODEL_OUTPUT_FILE}")
    
    # 生成最优参数组合
    generate_optimal_rules(rf_model, features, df)
    
    # 梯度提升模型（更高级的模型）
    print("\n训练梯度提升模型...")
    gb_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
    gb_model.fit(X_train, y_train)
    
    # 评估梯度提升模型
    gb_y_pred = gb_model.predict(X_test)
    gb_accuracy = accuracy_score(y_test, gb_y_pred)
    print(f"梯度提升模型准确率: {gb_accuracy:.2f}")
    
    # 如果梯度提升模型更好，则使用它
    if gb_accuracy > accuracy:
        print("梯度提升模型表现更好，使用它生成规则")
        joblib.dump(gb_model, 'reports/gb_' + os.path.basename(MODEL_OUTPUT_FILE))
        generate_optimal_rules(gb_model, features, df, prefix='gb_')
    
    # 生成策略绩效曲线
    generate_strategy_performance_curve(df)
    
    # 生成买卖点分布图
    generate_trade_distribution(df)
    
    print("\n策略优化完成!")

def generate_optimal_rules(model, features, df, prefix=''):
    """生成最优交易规则"""
    print("\n生成最优交易规则...")
    
    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': features,
        'Importance': model.feature_importances_
    }).sort_values('Importance', ascending=False)
    
    # 针对每个特征，找出最佳阈值
    rules = []
    for feature in feature_importance['Feature']:
        if feature not in df.columns:
            continue
            
        # 创建不同阈值
        thresholds = np.linspace(df[feature].min(), df[feature].max(), 20)
        best_score = 0
        best_threshold = None
        best_direction = None
        
        for threshold in thresholds:
            # 大于阈值
            subset_gt = df[df[feature] >= threshold]
            if len(subset_gt) >= 10:
                win_rate_gt = (subset_gt['Profit_Pct'] > 0).mean()
                avg_profit_gt = subset_gt['Profit_Pct'].mean()
                score_gt = win_rate_gt * avg_profit_gt
                
                if score_gt > best_score:
                    best_score = score_gt
                    best_threshold = threshold
                    best_direction = '>='
            
            # 小于阈值
            subset_lt = df[df[feature] <= threshold]
            if len(subset_lt) >= 10:
                win_rate_lt = (subset_lt['Profit_Pct'] > 0).mean()
                avg_profit_lt = subset_lt['Profit_Pct'].mean()
                score_lt = win_rate_lt * avg_profit_lt
                
                if score_lt > best_score:
                    best_score = score_lt
                    best_threshold = threshold
                    best_direction = '<='
        
        if best_threshold is not None:
            subset = df[eval(f"df['{feature}'] {best_direction} {best_threshold}")]
            win_rate = (subset['Profit_Pct'] > 0).mean() * 100
            avg_profit = subset['Profit_Pct'].mean()
            count = len(subset)
            
            rules.append({
                'Feature': feature,
                'Direction': best_direction,
                'Threshold': best_threshold,
                'Count': count,
                'Win_Rate': win_rate,
                'Avg_Profit': avg_profit,
                'Score': win_rate * avg_profit / 100
            })
    
    # 保存规则
    if rules:
        rules_df = pd.DataFrame(rules)
        rules_df.to_csv('reports/' + prefix + 'optimal_rules.csv', index=False)
        
        # 生成规则文本
        with open('reports/' + prefix + os.path.basename(STRATEGY_RULES_FILE), 'w') as f:
            f.write("# 最优交易规则\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## 单因子规则\n\n")
            
            for _, rule in rules_df.iterrows():
                f.write(f"- 当 {rule['Feature']} {rule['Direction']} {rule['Threshold']:.6f} 时:\n")
                f.write(f"  - 样本数: {rule['Count']}\n")
                f.write(f"  - 胜率: {rule['Win_Rate']:.2f}%\n")
                f.write(f"  - 平均收益: {rule['Avg_Profit']:.2f}%\n")
                f.write(f"  - 综合得分: {rule['Score']:.2f}\n\n")
            
            # 添加多因子组合规则
            f.write("## 推荐多因子组合规则\n\n")
            
            # 选择前3个最重要的特征
            top_features = feature_importance.head(min(3, len(feature_importance)))['Feature'].tolist()
            top_rules = [rule for rule in rules if rule['Feature'] in top_features]
            
            if len(top_rules) >= 2:
                f.write("推荐同时满足以下条件:\n\n")
                for rule in top_rules:
                    f.write(f"- {rule['Feature']} {rule['Direction']} {rule['Threshold']:.6f}\n")
                
                # 计算组合规则的预期表现
                combined_condition = "df"
                for rule in top_rules:
                    combined_condition += f" & (df['{rule['Feature']}'] {rule['Direction']} {rule['Threshold']})"
                
                try:
                    subset = eval(combined_condition)
                    if len(subset) >= 5:
                        win_rate = (subset['Profit_Pct'] > 0).mean() * 100
                        avg_profit = subset['Profit_Pct'].mean()
                        
                        f.write(f"\n组合规则表现:\n")
                        f.write(f"- 样本数: {len(subset)}\n")
                        f.write(f"- 胜率: {win_rate:.2f}%\n")
                        f.write(f"- 平均收益: {avg_profit:.2f}%\n")
                        f.write(f"- 综合得分: {win_rate * avg_profit / 100:.2f}\n")
                except Exception as e:
                    f.write(f"\n计算组合规则表现时出错: {e}\n")
            
        print(f"最优规则已保存到 {prefix + STRATEGY_RULES_FILE} 和 {prefix + 'optimal_rules.csv'}")

def generate_strategy_performance_curve(df):
    """生成策略绩效曲线"""
    print("\n生成策略绩效曲线...")
    
    # 确保时间戳是日期时间格式
    df['Buy_Time'] = pd.to_datetime(df['Buy_Time'])
    df['Sell_Time'] = pd.to_datetime(df['Sell_Time'])
    
    # 按买入时间排序
    df = df.sort_values('Buy_Time')
    
    # 计算累积收益
    df['Cumulative_Profit'] = df['Profit_Pct'].cumsum()
    
    # 绘制累积收益曲线
    plt.figure(figsize=(12, 6))
    plt.plot(df['Buy_Time'], df['Cumulative_Profit'], marker='o', linestyle='-')
    plt.title('策略累积收益曲线')
    plt.xlabel('买入时间')
    plt.ylabel('累积收益率 (%)')
    plt.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('reports/cumulative_profit_curve.png')
    
    # 计算滚动胜率
    window_size = min(20, len(df) // 2)  # 使用适当的窗口大小
    if window_size > 0:
        df['Rolling_Win_Rate'] = df['Profit_Pct'].apply(lambda x: 1 if x > 0 else 0).rolling(window=window_size).mean() * 100
        
        # 绘制滚动胜率曲线
        plt.figure(figsize=(12, 6))
        plt.plot(df['Buy_Time'].iloc[window_size-1:], df['Rolling_Win_Rate'].iloc[window_size-1:], marker='o', linestyle='-')
        plt.title(f'策略滚动胜率曲线 (窗口大小: {window_size})')
        plt.xlabel('买入时间')
        plt.ylabel('胜率 (%)')
        plt.grid(True)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('reports/rolling_win_rate.png')

def generate_trade_distribution(df):
    """生成买卖点分布图"""
    print("\n生成买卖点分布图...")
    
    # 设置绘图风格
    plt.style.use('ggplot')
    
    # 买入点分布 - 按波动性和TRIX
    if 'Volatility_Buy' in df.columns and 'TRIX_Buy' in df.columns:
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(df['Volatility_Buy'], df['TRIX_Buy'], 
                  c=df['Profit_Pct'], cmap='RdYlGn', alpha=0.7,
                  s=50, edgecolors='k')
        
        plt.colorbar(scatter, label='收益率 (%)')
        plt.title('买入点分布 - 波动性 vs TRIX')
        plt.xlabel('波动性')
        plt.ylabel('TRIX值')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('reports/buy_points_distribution.png')
    
    # 买入点分布 - 按波动性得分和ATR
    if 'Volatility_Score_Buy' in df.columns and 'ATR_Pct_Buy' in df.columns:
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(df['Volatility_Score_Buy'], df['ATR_Pct_Buy'], 
                  c=df['Profit_Pct'], cmap='RdYlGn', alpha=0.7,
                  s=50, edgecolors='k')
        
        plt.colorbar(scatter, label='收益率 (%)')
        plt.title('买入点分布 - 波动性得分 vs ATR')
        plt.xlabel('波动性得分')
        plt.ylabel('ATR百分比')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('reports/buy_points_distribution_alt.png')
    
    # 3D可视化 - 如果有三个关键特征
    try:
        from mpl_toolkits.mplot3d import Axes3D
        
        if all(x in df.columns for x in ['Volatility_Buy', 'TRIX_Buy', 'ATR_Pct_Buy']):
            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')
            
            scatter = ax.scatter(df['Volatility_Buy'], df['TRIX_Buy'], df['ATR_Pct_Buy'],
                      c=df['Profit_Pct'], cmap='RdYlGn', alpha=0.7,
                      s=50, edgecolors='k')
            
            plt.colorbar(scatter, label='收益率 (%)')
            ax.set_title('买入点3D分布')
            ax.set_xlabel('波动性')
            ax.set_ylabel('TRIX值')
            ax.set_zlabel('ATR百分比')
            plt.tight_layout()
            plt.savefig('reports/buy_points_3d.png')
    except Exception as e:
        print(f"生成3D可视化时出错: {e}")

if __name__ == "__main__":
    optimize_strategy() 