# coding=utf-8
"""
TRIX策略实际表现分析
"""

import sqlite3
import pandas as pd
import numpy as np

def analyze_current_performance():
    """分析当前策略表现"""
    print('📊 当前策略表现分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 总体胜率分析
        query = """
        SELECT 
            COUNT(*) as total_trades,
            SUM(CASE WHEN net_profit_pct_sell > 0 THEN 1 ELSE 0 END) as wins,
            AVG(net_profit_pct_sell) as avg_profit,
            AVG(CASE WHEN net_profit_pct_sell > 0 THEN net_profit_pct_sell END) as avg_win,
            AVG(CASE WHEN net_profit_pct_sell <= 0 THEN net_profit_pct_sell END) as avg_loss
        FROM trades 
        WHERE action = 'BUY' AND net_profit_pct_sell IS NOT NULL
        """
        
        result = pd.read_sql_query(query, conn).iloc[0]
        
        total_trades = result['total_trades']
        wins = result['wins']
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        avg_profit = result['avg_profit']
        avg_win = result['avg_win']
        avg_loss = abs(result['avg_loss']) if result['avg_loss'] else 0
        
        print(f'📈 总体表现:')
        print(f'   总交易数: {total_trades}')
        print(f'   胜率: {win_rate:.1f}% ({wins}/{total_trades})')
        print(f'   平均收益: {avg_profit:.2f}%')
        print(f'   平均盈利: {avg_win:.2f}%')
        print(f'   平均亏损: {avg_loss:.2f}%')
        print(f'   盈亏比: {avg_win/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
        
        return conn, win_rate
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None, 0

def analyze_trix_signals(conn):
    """分析TRIX信号表现"""
    print(f'\n🎯 TRIX信号分析')
    print('=' * 40)
    
    try:
        # 分析TRIX买入信号的分布和表现
        query = """
        SELECT 
            trix_buy,
            atr_pct,
            bb_width,
            macd_hist,
            rsi,
            net_profit_pct_sell,
            CASE 
                WHEN net_profit_pct_sell > 0 THEN 'WIN'
                WHEN net_profit_pct_sell <= 0 THEN 'LOSS'
                ELSE 'PENDING'
            END as result
        FROM trades 
        WHERE action = 'BUY' AND net_profit_pct_sell IS NOT NULL
        AND trix_buy IS NOT NULL
        """
        
        df = pd.read_sql_query(query, conn)
        
        if len(df) == 0:
            print('⚠️ 没有找到TRIX信号数据')
            return
        
        print(f'📊 TRIX信号样本: {len(df)} 条')
        
        # 分析TRIX值的分布
        print(f'\n📈 TRIX买入信号分布:')
        trix_ranges = [
            ('强负值 (<-50)', df['trix_buy'] < -50),
            ('负值 (-50~0)', (df['trix_buy'] >= -50) & (df['trix_buy'] < 0)),
            ('正值 (0~50)', (df['trix_buy'] >= 0) & (df['trix_buy'] < 50)),
            ('强正值 (>50)', df['trix_buy'] >= 50)
        ]
        
        for range_name, condition in trix_ranges:
            subset = df[condition]
            if len(subset) > 0:
                wins = len(subset[subset['result'] == 'WIN'])
                win_rate = wins / len(subset) * 100
                avg_profit = subset['net_profit_pct_sell'].mean()
                print(f'   {range_name}: {len(subset)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # 分析TRIX配合其他指标的效果
        print(f'\n🔍 TRIX配合其他指标的效果:')
        
        # 高ATR配合
        high_atr = df['atr_pct'] > 2.5
        trix_high_atr = df[high_atr]
        if len(trix_high_atr) > 0:
            wins = len(trix_high_atr[trix_high_atr['result'] == 'WIN'])
            win_rate = wins / len(trix_high_atr) * 100
            avg_profit = trix_high_atr['net_profit_pct_sell'].mean()
            print(f'   TRIX + 高ATR(>2.5): {len(trix_high_atr)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # 宽布林带配合
        wide_bb = df['bb_width'] > 10
        trix_wide_bb = df[wide_bb]
        if len(trix_wide_bb) > 0:
            wins = len(trix_wide_bb[trix_wide_bb['result'] == 'WIN'])
            win_rate = wins / len(trix_wide_bb) * 100
            avg_profit = trix_wide_bb['net_profit_pct_sell'].mean()
            print(f'   TRIX + 宽布林带(>10): {len(trix_wide_bb)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # MACD金叉配合
        macd_golden = df['macd_hist'] > 0
        trix_macd = df[macd_golden]
        if len(trix_macd) > 0:
            wins = len(trix_macd[trix_macd['result'] == 'WIN'])
            win_rate = wins / len(trix_macd) * 100
            avg_profit = trix_macd['net_profit_pct_sell'].mean()
            print(f'   TRIX + MACD金叉: {len(trix_macd)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # RSI超卖配合
        rsi_oversold = df['rsi'] < 40
        trix_rsi = df[rsi_oversold]
        if len(trix_rsi) > 0:
            wins = len(trix_rsi[trix_rsi['result'] == 'WIN'])
            win_rate = wins / len(trix_rsi) * 100
            avg_profit = trix_rsi['net_profit_pct_sell'].mean()
            print(f'   TRIX + RSI超卖(<40): {len(trix_rsi)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # 多重条件组合
        multi_condition = high_atr & wide_bb & macd_golden
        trix_multi = df[multi_condition]
        if len(trix_multi) > 0:
            wins = len(trix_multi[trix_multi['result'] == 'WIN'])
            win_rate = wins / len(trix_multi) * 100
            avg_profit = trix_multi['net_profit_pct_sell'].mean()
            print(f'   TRIX + 多重条件: {len(trix_multi)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        return df
        
    except Exception as e:
        print(f'❌ TRIX信号分析失败: {e}')
        return None

def analyze_best_performers(conn):
    """分析表现最好的条件组合"""
    print(f'\n🏆 最佳表现条件组合')
    print('=' * 40)
    
    try:
        query = """
        SELECT 
            overall_score,
            technical_score,
            momentum_score,
            volume_score,
            volatility_score,
            trend_score,
            buy_signal_strength,
            risk_adjusted_score,
            trix_buy,
            atr_pct,
            bb_width,
            macd_hist,
            rsi,
            net_profit_pct_sell
        FROM trades 
        WHERE action = 'BUY' AND net_profit_pct_sell IS NOT NULL
        """
        
        df = pd.read_sql_query(query, conn)
        
        if len(df) == 0:
            print('⚠️ 没有数据')
            return
        
        # 分析不同评分区间的表现
        print(f'📊 不同评分区间的表现:')
        
        score_ranges = [
            ('低分 (<0.3)', df['overall_score'] < 0.3),
            ('中低分 (0.3-0.5)', (df['overall_score'] >= 0.3) & (df['overall_score'] < 0.5)),
            ('中分 (0.5-0.7)', (df['overall_score'] >= 0.5) & (df['overall_score'] < 0.7)),
            ('高分 (0.7-0.9)', (df['overall_score'] >= 0.7) & (df['overall_score'] < 0.9)),
            ('超高分 (>0.9)', df['overall_score'] >= 0.9)
        ]
        
        for range_name, condition in score_ranges:
            subset = df[condition]
            if len(subset) > 0:
                wins = len(subset[subset['net_profit_pct_sell'] > 0])
                win_rate = wins / len(subset) * 100
                avg_profit = subset['net_profit_pct_sell'].mean()
                print(f'   {range_name}: {len(subset)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        # 找出胜率最高的条件组合
        print(f'\n🎯 高胜率条件组合:')
        
        # 高综合评分 + 其他条件
        high_score_conditions = [
            ('高综合评分(>0.7)', df['overall_score'] > 0.7),
            ('高技术评分(>0.7)', df['technical_score'] > 0.7),
            ('高动量评分(>0.7)', df['momentum_score'] > 0.7),
            ('高买入信号强度(>0.7)', df['buy_signal_strength'] > 0.7),
            ('高风险调整评分(>0.7)', df['risk_adjusted_score'] > 0.7)
        ]
        
        for condition_name, condition in high_score_conditions:
            subset = df[condition]
            if len(subset) > 0:
                wins = len(subset[subset['net_profit_pct_sell'] > 0])
                win_rate = wins / len(subset) * 100
                avg_profit = subset['net_profit_pct_sell'].mean()
                print(f'   {condition_name}: {len(subset)}条, 胜率{win_rate:.1f}%, 平均收益{avg_profit:.2f}%')
        
        return df
        
    except Exception as e:
        print(f'❌ 最佳表现分析失败: {e}')
        return None

def suggest_immediate_improvements():
    """提出立即可行的改进建议"""
    print(f'\n🚀 立即可行的改进建议')
    print('=' * 50)
    
    improvements = [
        {
            'priority': '🔥 紧急',
            'action': '提高买入门槛',
            'details': [
                '将overall_score阈值从当前值提高到0.7+',
                '要求technical_score > 0.6',
                '要求buy_signal_strength > 0.6',
                '预期胜率提升至30%+'
            ]
        },
        {
            'priority': '⚡ 高优先级',
            'action': '增强多因子过滤',
            'details': [
                '要求至少3个评分指标同时满足条件',
                '添加ATR > 2.5的波动率要求',
                '要求MACD_hist > 0的趋势确认',
                '预期减少50%的假信号'
            ]
        },
        {
            'priority': '📊 中优先级',
            'action': '优化TRIX参数',
            'details': [
                '测试不同的TRIX周期参数',
                '实施动态TRIX阈值',
                '添加TRIX趋势确认',
                '预期信号质量提升20%'
            ]
        },
        {
            'priority': '🎯 长期',
            'action': '开发替代策略',
            'details': [
                '基于高评分的多因子策略',
                'MACD金叉策略',
                '布林带反弹策略',
                '预期胜率35%+'
            ]
        }
    ]
    
    for improvement in improvements:
        print(f'\n{improvement["priority"]} - {improvement["action"]}:')
        for detail in improvement['details']:
            print(f'   • {detail}')

def create_improved_strategy_config():
    """创建改进的策略配置"""
    print(f'\n⚙️ 改进策略配置建议')
    print('=' * 40)
    
    config_suggestions = {
        'immediate_fixes': {
            'description': '立即可实施的配置修改',
            'changes': [
                'OVERALL_SCORE_THRESHOLD = 0.7  # 从当前值提高',
                'TECHNICAL_SCORE_THRESHOLD = 0.6  # 新增',
                'BUY_SIGNAL_STRENGTH_THRESHOLD = 0.6  # 新增',
                'REQUIRE_MULTIPLE_CONFIRMATIONS = True  # 新增',
                'MIN_ATR_THRESHOLD = 2.5  # 新增波动率要求'
            ]
        },
        'enhanced_filters': {
            'description': '增强过滤条件',
            'changes': [
                'REQUIRE_MACD_GOLDEN_CROSS = True  # 要求MACD金叉',
                'REQUIRE_RSI_OVERSOLD = True  # 要求RSI超卖',
                'MIN_BB_WIDTH = 8.0  # 最小布林带宽度',
                'MAX_TRIX_THRESHOLD = 100  # TRIX上限',
                'MIN_VOLUME_RATIO = 1.2  # 最小成交量比率'
            ]
        },
        'risk_management': {
            'description': '风险管理增强',
            'changes': [
                'MAX_POSITION_SIZE = 0.02  # 最大单笔仓位',
                'STOP_LOSS_PCT = -3.0  # 止损百分比',
                'TAKE_PROFIT_PCT = 8.0  # 止盈百分比',
                'MAX_HOLDING_HOURS = 72  # 最大持仓时间',
                'ENABLE_DYNAMIC_POSITION_SIZING = True  # 动态仓位'
            ]
        }
    }
    
    for category, config in config_suggestions.items():
        print(f'\n📋 {config["description"]}:')
        for change in config['changes']:
            print(f'   {change}')

def main():
    """主函数"""
    print('🔍 TRIX策略实际表现深度分析')
    print('=' * 60)
    
    # 分析当前表现
    conn, current_win_rate = analyze_current_performance()
    
    if conn:
        # 分析TRIX信号
        analyze_trix_signals(conn)
        
        # 分析最佳表现条件
        analyze_best_performers(conn)
        
        conn.close()
        
        # 提出改进建议
        suggest_immediate_improvements()
        
        # 创建改进配置
        create_improved_strategy_config()
        
        print(f'\n🎯 总结与行动计划')
        print('=' * 40)
        print(f'❌ 当前胜率: {current_win_rate:.1f}% (确实偏低)')
        print(f'🎯 目标胜率: 30-35% (通过改进可达到)')
        print(f'🚀 关键行动:')
        print(f'   1. 立即提高overall_score阈值到0.7+')
        print(f'   2. 增加多因子确认条件')
        print(f'   3. 实施更严格的风险管理')
        print(f'   4. 开发高胜率的替代策略')
        print(f'')
        print(f'💡 预期效果: 胜率提升至30%+，信号质量显著改善')

if __name__ == '__main__':
    main()
