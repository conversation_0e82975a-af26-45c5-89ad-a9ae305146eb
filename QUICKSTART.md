# 万和策略分析系统 - 快速入门指南

本指南将帮助您快速上手万和策略分析系统，包括环境配置、基本操作和常用功能。

## 环境准备

### 系统要求

- Python 3.8+
- 至少4GB RAM
- 至少2GB可用磁盘空间

### 安装步骤

1. **克隆或下载代码**

   将代码下载到本地目录。

2. **安装依赖**

   ```bash
   # 在项目根目录下运行
   pip install -r requirements.txt
   ```

3. **初始化系统**

   ```bash
   # 初始化数据目录和基本结构
   python -c "from scripts.data_manager import get_data_manager; get_data_manager()"
   ```

## 快速开始

### 运行策略回测

1. **直接执行主程序**

   ```bash
   python main.py
   ```

   这将使用默认参数运行策略回测：
   - 回测时间段：2023-01-01至2023-03-31
   - 初始资金：1,000,000元
   - 交易费率：0.0003
   - 滑点比例：0.0002

2. **查看回测结果**

   回测完成后，可以查看以下文件：
   - `data/trade_log.csv`：交易日志
   - `data/analysis_log.csv`：分析日志
   - `strategy.log`：策略运行日志

### 生成分析报告

1. **使用集成脚本**

   ```bash
   python scripts/integrate_reports.py
   ```

   根据提示选择操作：
   - 选项1：执行主分析（运行完整的策略回测）
   - 选项2：分析现有数据（仅分析已有的交易数据）
   - 选项3：跳过分析（仅生成报告）

2. **查看HTML报告**

   生成的报告位于：
   - `reports/html/high_performance_trades.html`：高性能交易分析报告
   - `reports/html/reports_guide.html`：报告指南

## 基本操作指南

### 1. 查看交易日志

交易日志包含所有交易记录，可以通过以下方式查看：

```python
# 在Python中查看
from scripts.data_manager import get_trades
trades_df = get_trades()
print(trades_df.head())
```

或者直接打开CSV文件：`data/trade_log.csv`

### 2. 查看分析结果

分析结果包含交易表现统计，可以通过以下方式查看：

```python
# 在Python中查看
from scripts.data_manager import analyze_trades
results = analyze_trades()
print(results.head())
```

或者查看生成的报告文件：`reports/trade_analysis_results.csv`

### 3. 修改回测参数

如需修改回测参数，编辑`main.py`文件底部的`run_args`字典：

```python
run_args = {
    'strategy_id': '39da9282-3bbd-11f0-8755-d4e98a5e8c02',
    'filename': 'main.py',
    'token': '927022466b9f6476ef82fe30991f521c61feac74',
    'mode': MODE_BACKTEST,
    'backtest_start_time': '2023-01-01 09:30:00',  # 修改回测开始时间
    'backtest_end_time': '2023-03-31 15:00:00',    # 修改回测结束时间
    'backtest_initial_cash': 1000000,              # 修改初始资金
    'backtest_adjust': ADJUST_PREV,
    'backtest_commission_ratio': 0.0003,           # 修改手续费率
    'backtest_slippage_ratio': 0.0002              # 修改滑点比例
}
```

### 4. 修改策略参数

策略参数在`main.py`的`init`函数中定义，可以根据需要修改：

```python
# 初始化参数
context.index_symbol = 'SHSE.000300'  # 指数代码
context.max_positions = 95            # 最大持仓数
context.ratio = 0.9                   # 资金利用率
context.trailing_stop = 0.015         # 跟踪止盈阈值
```

## 常用功能示例

### 1. 数据备份与恢复

**备份数据**

```python
from scripts.data_manager import backup_data
backup_data()
```

**查看备份**

```bash
# 列出备份目录
ls -la backups/
```

**恢复数据**

```python
# 从特定备份恢复
import shutil
import os

backup_dir = 'backups/20230615_120000'  # 替换为实际备份目录
shutil.copy(os.path.join(backup_dir, 'trade_log.csv'), 'data/')
```

### 2. 数据分析示例

**计算平均收益率**

```python
from scripts.data_manager import get_trades
import pandas as pd

# 获取所有交易
trades_df = get_trades()

# 计算平均收益
avg_profit = trades_df[trades_df['Action'] == 'SELL']['Net_Profit_Pct_Sell'].mean()
print(f"平均收益率: {avg_profit:.2%}")
```

**计算胜率**

```python
from scripts.data_manager import get_trades
import pandas as pd

# 获取所有交易
trades_df = get_trades()

# 筛选卖出记录
sells = trades_df[trades_df['Action'] == 'SELL']

# 计算胜率
win_rate = len(sells[sells['Net_Profit_Pct_Sell'] > 0]) / len(sells)
print(f"交易胜率: {win_rate:.2%}")
```

### 3. 自定义报告

如需自定义报告内容，可以修改HTML模板：

1. 编辑模板文件：`templates/high_performance_template.html`
2. 修改`scripts/generate_html_reports.py`中的变量替换逻辑

## 常见问题

### 问题：运行主程序时报错找不到模块

**解决方案**：确保已安装所有依赖

```bash
pip install -r requirements.txt
```

### 问题：生成报告时找不到数据文件

**解决方案**：确保先运行了策略回测，生成了必要的数据文件

```bash
python main.py
```

### 问题：报告中没有显示图表

**解决方案**：检查报告模板中的JavaScript是否正确加载

## 下一步学习

- 阅读完整的[README.md](README.md)文档
- 查看[MAINTENANCE.md](MAINTENANCE.md)了解系统维护
- 探索`scripts`目录下的各个模块源码
- 尝试修改策略参数优化交易表现

## 支持与帮助

如有任何问题，请参考系统文档或联系技术支持团队。 