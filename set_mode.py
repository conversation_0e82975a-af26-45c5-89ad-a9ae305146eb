# coding=utf-8
"""
快速模式切换脚本
使用数字1、2、3快速切换日志模式
"""

import os
import re
import sys

def set_mode(mode):
    """快速设置模式"""
    config_file = 'config.py'
    
    if not os.path.exists(config_file):
        print(f'❌ 配置文件不存在: {config_file}')
        return False
    
    # 验证模式
    if mode not in [1, 2, 3]:
        print(f'❌ 无效模式: {mode}')
        print('📋 有效模式:')
        print('  1 = 🚀 极简模式（最高性能）')
        print('  2 = 📊 详细模式（平衡）')
        print('  3 = 🔍 调试模式（完整信息）')
        return False
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新模式
    pattern = r"'mode':\s*\d+"
    replacement = f"'mode': {mode}"
    
    if re.search(pattern, content):
        new_content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        # 显示结果
        mode_names = {1: '🚀 极简模式', 2: '📊 详细模式', 3: '🔍 调试模式'}
        print(f'✅ 已切换到: {mode_names[mode]}')
        
        # 显示特点
        mode_features = {
            1: ['最高性能', '最少日志', '适合回测'],
            2: ['平衡性能', '适量日志', '适合开发'],
            3: ['完整信息', '详细日志', '适合调试']
        }
        
        features = mode_features[mode]
        print(f'📋 特点: {" | ".join(features)}')
        print('🔄 请重启策略以应用新设置')
        
        return True
    else:
        print('❌ 未找到模式配置')
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print('🔧 快速模式切换工具')
        print('=' * 30)
        print('📋 使用方法:')
        print('  python set_mode.py 1   # 极简模式')
        print('  python set_mode.py 2   # 详细模式')
        print('  python set_mode.py 3   # 调试模式')
        print('')
        print('📊 模式说明:')
        print('  1 = 🚀 极简模式 - 最高性能，最少日志')
        print('  2 = 📊 详细模式 - 平衡性能，适量日志')
        print('  3 = 🔍 调试模式 - 完整信息，详细日志')
        return
    
    try:
        mode = int(sys.argv[1])
        set_mode(mode)
    except ValueError:
        print(f'❌ 无效参数: {sys.argv[1]}')
        print('请使用数字 1、2 或 3')

if __name__ == '__main__':
    main()
