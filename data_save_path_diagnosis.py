# coding=utf-8
"""
数据保存路径诊断
诊断分析数据为什么被保存到trades表而不是analysis表
"""

import sqlite3
import os

def check_analysis_table_structure():
    """检查analysis表结构"""
    print('🔍 analysis表结构检查')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 检查analysis表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print('✅ analysis表存在')
            
            # 2. 检查表结构
            cursor.execute("PRAGMA table_info(analysis)")
            columns = cursor.fetchall()
            
            print(f'📊 analysis表字段数: {len(columns)}')
            print('📋 analysis表字段列表:')
            
            for i, (cid, name, type_, notnull, default, pk) in enumerate(columns):
                print(f'  {i+1:2d}. {name} ({type_})')
                if i >= 20:  # 只显示前20个字段
                    print(f'     ... 还有{len(columns)-20}个字段')
                    break
            
            # 3. 检查记录数
            cursor.execute("SELECT COUNT(*) FROM analysis")
            record_count = cursor.fetchone()[0]
            print(f'📊 analysis表记录数: {record_count}')
            
            # 4. 检查最近的记录
            if record_count > 0:
                cursor.execute("SELECT * FROM analysis ORDER BY rowid DESC LIMIT 3")
                recent_records = cursor.fetchall()
                print(f'📅 最近3条记录:')
                for i, record in enumerate(recent_records, 1):
                    print(f'  {i}. {record[:5]}...')  # 只显示前5个字段
            
        else:
            print('❌ analysis表不存在')
            print('💡 这可能是问题的根源！')
        
        conn.close()
        return table_exists is not None
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return False

def check_data_manager_error_handling():
    """检查数据管理器的错误处理"""
    print('\n🔍 数据管理器错误处理检查')
    print('=' * 50)
    
    try:
        # 检查data_manager.py中的错误处理逻辑
        with open('scripts/data_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 错误处理机制分析:')
        
        # 1. 检查_save_analysis_to_db的异常处理
        if '_save_analysis_to_db' in content:
            print('  ✅ _save_analysis_to_db方法存在')
            
            # 检查是否有fallback机制
            if 'except Exception' in content and 'save_analysis' in content:
                print('  ✅ 有异常处理机制')
                
                # 检查是否有fallback到trades表的逻辑
                import re
                fallback_pattern = r'except.*Exception.*:.*save.*trade'
                if re.search(fallback_pattern, content, re.DOTALL | re.IGNORECASE):
                    print('  ⚠️ 可能有fallback到trades表的逻辑')
                else:
                    print('  ✅ 没有发现fallback到trades表的逻辑')
        
        # 2. 检查save_analysis方法的实现
        save_analysis_pattern = r'def save_analysis\(self, analysis_data\):(.*?)def '
        save_analysis_match = re.search(save_analysis_pattern, content, re.DOTALL)
        
        if save_analysis_match:
            save_analysis_code = save_analysis_match.group(1)
            
            print('  📝 save_analysis方法分析:')
            
            if 'self._save_analysis_to_db(analysis_data)' in save_analysis_code:
                print('    ✅ 调用_save_analysis_to_db')
            else:
                print('    ❌ 未调用_save_analysis_to_db')
            
            if 'except Exception' in save_analysis_code:
                print('    ✅ 有异常处理')
            else:
                print('    ❌ 无异常处理')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def test_analysis_data_save():
    """测试分析数据保存"""
    print('\n🧪 分析数据保存测试')
    print('=' * 50)
    
    try:
        # 模拟分析数据
        test_analysis_data = {
            'Timestamp': '2025-07-20 20:30:00',
            'Symbol': 'TEST.000001',
            'Current_Price': 10.50,
            'MA3': 10.45,
            'MA7': 10.40,
            'MA20': 10.35,
            'RSI': 65.5,
            'MACD': 0.15,
            'Final_Buy_Signal': 1
        }
        
        print('📊 测试分析数据:')
        for key, value in test_analysis_data.items():
            print(f'  {key}: {value}')
        
        # 尝试保存到analysis表
        print('\n🔧 尝试保存到analysis表:')
        
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 检查analysis表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis'")
        if cursor.fetchone():
            # 获取analysis表字段
            cursor.execute("PRAGMA table_info(analysis)")
            columns = cursor.fetchall()
            db_fields = [col[1].lower() for col in columns]
            
            print(f'  analysis表字段数: {len(db_fields)}')
            
            # 准备插入数据
            fields = []
            placeholders = []
            values = []
            
            for field, value in test_analysis_data.items():
                field_lower = field.lower()
                if field_lower in db_fields:
                    fields.append(field_lower)
                    placeholders.append('?')
                    values.append(value)
            
            if fields:
                sql = f"INSERT INTO analysis ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
                print(f'  SQL: {sql}')
                print(f'  参数: {values}')
                
                cursor.execute(sql, values)
                conn.commit()
                
                print('  ✅ 测试数据保存成功')
                
                # 验证保存
                cursor.execute("SELECT COUNT(*) FROM analysis WHERE symbol = ?", ('TEST.000001',))
                count = cursor.fetchone()[0]
                print(f'  ✅ 验证: 找到{count}条测试记录')
            else:
                print('  ❌ 没有匹配的字段可以保存')
        else:
            print('  ❌ analysis表不存在')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')

def check_field_mapping_issues():
    """检查字段映射问题"""
    print('\n🔍 字段映射问题检查')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 获取analysis表字段
        cursor.execute("PRAGMA table_info(analysis)")
        analysis_columns = cursor.fetchall()
        analysis_fields = [col[1].lower() for col in analysis_columns]
        
        # 2. 获取trades表字段
        cursor.execute("PRAGMA table_info(trades)")
        trades_columns = cursor.fetchall()
        trades_fields = [col[1].lower() for col in trades_columns]
        
        print(f'📊 字段对比:')
        print(f'  analysis表字段数: {len(analysis_fields)}')
        print(f'  trades表字段数: {len(trades_fields)}')
        
        # 3. 检查关键分析字段
        analysis_key_fields = [
            'timestamp', 'symbol', 'current_price', 'ma3', 'ma7', 'ma20',
            'rsi', 'macd', 'final_buy_signal'
        ]
        
        print(f'\n📋 关键分析字段检查:')
        for field in analysis_key_fields:
            in_analysis = field in analysis_fields
            in_trades = field in trades_fields
            
            print(f'  {field}:')
            print(f'    analysis表: {"✅" if in_analysis else "❌"}')
            print(f'    trades表: {"✅" if in_trades else "❌"}')
            
            if not in_analysis and in_trades:
                print(f'    ⚠️ 只在trades表中存在，可能导致误保存')
        
        # 4. 检查是否有Action字段
        print(f'\n🔍 Action字段检查:')
        print(f'  analysis表有Action字段: {"✅" if "action" in analysis_fields else "❌"}')
        print(f'  trades表有Action字段: {"✅" if "action" in trades_fields else "❌"}')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def diagnose_root_cause():
    """诊断根本原因"""
    print('\n💡 根本原因诊断')
    print('=' * 50)
    
    print('📊 可能的原因分析:')
    
    scenarios = [
        {
            'scenario': 'analysis表字段不匹配',
            'description': 'analysis_data中的字段在analysis表中不存在',
            'indicators': ['字段映射失败', '保存时异常', '数据被丢弃'],
            'solution': '检查并修复analysis表结构'
        },
        {
            'scenario': '_save_analysis_to_db异常',
            'description': '_save_analysis_to_db方法执行时发生异常',
            'indicators': ['异常日志', '保存失败', '数据丢失'],
            'solution': '检查异常处理和日志'
        },
        {
            'scenario': '错误的fallback机制',
            'description': '异常时错误地fallback到trades表',
            'indicators': ['分析数据出现在trades表', '有Action字段'],
            'solution': '修复fallback逻辑'
        },
        {
            'scenario': '数据管理器配置问题',
            'description': 'use_db配置错误或数据库连接问题',
            'indicators': ['数据库操作失败', '配置错误'],
            'solution': '检查数据管理器配置'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f'\n{i}. {scenario["scenario"]}')
        print(f'   描述: {scenario["description"]}')
        print(f'   指标: {", ".join(scenario["indicators"])}')
        print(f'   解决: {scenario["solution"]}')

def suggest_fix_approach():
    """建议修复方法"""
    print('\n🔧 建议修复方法')
    print('=' * 50)
    
    steps = [
        {
            'step': '1. 确认analysis表结构',
            'action': '检查analysis表是否包含所有必要字段',
            'expected': 'analysis表应该有timestamp, symbol等分析字段'
        },
        {
            'step': '2. 测试分析数据保存',
            'action': '手动测试保存分析数据到analysis表',
            'expected': '应该能成功保存到analysis表'
        },
        {
            'step': '3. 检查错误日志',
            'action': '查看策略运行时的错误日志',
            'expected': '找到_save_analysis_to_db的异常信息'
        },
        {
            'step': '4. 修复字段映射',
            'action': '确保analysis_data字段与analysis表匹配',
            'expected': '所有分析字段都能正确映射'
        },
        {
            'step': '5. 添加调试日志',
            'action': '在_save_analysis_to_db中添加详细日志',
            'expected': '能追踪数据保存的完整过程'
        }
    ]
    
    for step in steps:
        print(f'{step["step"]}: {step["action"]}')
        print(f'   预期: {step["expected"]}')
        print()

def main():
    """主函数"""
    print('🔍 数据保存路径诊断报告')
    print('=' * 60)
    
    # 检查analysis表结构
    has_analysis_table = check_analysis_table_structure()
    
    # 检查数据管理器错误处理
    check_data_manager_error_handling()
    
    # 测试分析数据保存
    test_analysis_data_save()
    
    # 检查字段映射问题
    check_field_mapping_issues()
    
    # 诊断根本原因
    diagnose_root_cause()
    
    # 建议修复方法
    suggest_fix_approach()
    
    print(f'\n🎯 诊断结论')
    print('=' * 40)
    
    if has_analysis_table:
        print('✅ analysis表存在，问题可能在字段映射或异常处理')
        print('🔧 建议检查字段匹配和错误日志')
    else:
        print('❌ analysis表不存在，这是主要问题')
        print('🔧 建议创建analysis表或修复表结构')
    
    print('\n📋 下一步行动:')
    print('1. 🔍 检查策略运行日志中的错误信息')
    print('2. 🧪 运行测试验证analysis表功能')
    print('3. 🔧 根据诊断结果修复相应问题')

if __name__ == '__main__':
    main()
