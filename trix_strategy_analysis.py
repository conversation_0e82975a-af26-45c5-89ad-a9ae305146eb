# coding=utf-8
"""
TRIX策略深度分析
分析TRIX策略胜率偏低的原因并提供改进建议
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_trix_performance():
    """分析TRIX策略表现"""
    print('🔍 TRIX策略深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 分析所有买入记录
        query = """
        SELECT 
            symbol, timestamp, action, 
            trix_buy, atr_pct, bb_width, macd_hist, rsi,
            net_profit_pct_sell, Signal_Reason
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 总买入记录: {len(df)} 条')
        
        # 分析信号来源
        signal_analysis = df['Signal_Reason'].value_counts()
        print(f'\n📋 信号来源分析:')
        for signal, count in signal_analysis.items():
            percentage = count / len(df) * 100
            print(f'   {signal}: {count}条 ({percentage:.1f}%)')
        
        # 分析TRIX信号的胜率
        trix_records = df[df['Signal_Reason'].str.contains('TRIX', na=False)]
        print(f'\n🎯 TRIX信号分析:')
        print(f'   TRIX信号数量: {len(trix_records)} 条')
        
        if len(trix_records) > 0:
            # 计算TRIX信号胜率
            trix_with_profit = trix_records.dropna(subset=['net_profit_pct_sell'])
            if len(trix_with_profit) > 0:
                wins = len(trix_with_profit[trix_with_profit['net_profit_pct_sell'] > 0])
                win_rate = wins / len(trix_with_profit) * 100
                avg_profit = trix_with_profit[trix_with_profit['net_profit_pct_sell'] > 0]['net_profit_pct_sell'].mean()
                avg_loss = abs(trix_with_profit[trix_with_profit['net_profit_pct_sell'] <= 0]['net_profit_pct_sell'].mean())
                
                print(f'   TRIX胜率: {win_rate:.1f}% ({wins}/{len(trix_with_profit)})')
                print(f'   平均盈利: {avg_profit:.2f}%')
                print(f'   平均亏损: {avg_loss:.2f}%')
                print(f'   盈亏比: {avg_profit/avg_loss:.2f}' if avg_loss > 0 else '   盈亏比: N/A')
        
        return df, trix_records
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None, None

def analyze_trix_signal_quality():
    """分析TRIX信号质量"""
    print(f'\n🔬 TRIX信号质量分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 分析TRIX买入信号的分布
        query = """
        SELECT 
            trix_buy, atr_pct, bb_width, macd_hist, rsi, adx,
            net_profit_pct_sell,
            CASE 
                WHEN net_profit_pct_sell > 0 THEN 'WIN'
                WHEN net_profit_pct_sell <= 0 THEN 'LOSS'
                ELSE 'PENDING'
            END as result
        FROM trades 
        WHERE action = 'BUY' AND Signal_Reason LIKE '%TRIX%'
        AND net_profit_pct_sell IS NOT NULL
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print('⚠️ 没有找到TRIX信号数据')
            return
        
        print(f'📊 TRIX信号样本: {len(df)} 条')
        
        # 分析TRIX买入信号的分布
        print(f'\n📈 TRIX买入信号分布:')
        trix_ranges = [
            ('强负值', df['trix_buy'] < -20),
            ('负值', (df['trix_buy'] >= -20) & (df['trix_buy'] < 0)),
            ('正值', (df['trix_buy'] >= 0) & (df['trix_buy'] < 20)),
            ('强正值', df['trix_buy'] >= 20)
        ]
        
        for range_name, condition in trix_ranges:
            subset = df[condition]
            if len(subset) > 0:
                wins = len(subset[subset['result'] == 'WIN'])
                win_rate = wins / len(subset) * 100
                print(f'   {range_name}: {len(subset)}条, 胜率{win_rate:.1f}%')
        
        # 分析配合其他指标的效果
        print(f'\n🎯 TRIX配合其他指标的效果:')
        
        # ATR配合分析
        high_atr = df['atr_pct'] > 2.5
        trix_high_atr = df[high_atr]
        if len(trix_high_atr) > 0:
            wins = len(trix_high_atr[trix_high_atr['result'] == 'WIN'])
            win_rate = wins / len(trix_high_atr) * 100
            print(f'   TRIX + 高ATR(>2.5): {len(trix_high_atr)}条, 胜率{win_rate:.1f}%')
        
        # 布林带配合分析
        wide_bb = df['bb_width'] > 10
        trix_wide_bb = df[wide_bb]
        if len(trix_wide_bb) > 0:
            wins = len(trix_wide_bb[trix_wide_bb['result'] == 'WIN'])
            win_rate = wins / len(trix_wide_bb) * 100
            print(f'   TRIX + 宽布林带(>10): {len(trix_wide_bb)}条, 胜率{win_rate:.1f}%')
        
        # MACD配合分析
        macd_golden = df['macd_hist'] > 0
        trix_macd = df[macd_golden]
        if len(trix_macd) > 0:
            wins = len(trix_macd[trix_macd['result'] == 'WIN'])
            win_rate = wins / len(trix_macd) * 100
            print(f'   TRIX + MACD金叉: {len(trix_macd)}条, 胜率{win_rate:.1f}%')
        
        # 多重条件组合
        multi_condition = high_atr & wide_bb & macd_golden
        trix_multi = df[multi_condition]
        if len(trix_multi) > 0:
            wins = len(trix_multi[trix_multi['result'] == 'WIN'])
            win_rate = wins / len(trix_multi) * 100
            print(f'   TRIX + 多重条件: {len(trix_multi)}条, 胜率{win_rate:.1f}%')
        
        return df
        
    except Exception as e:
        print(f'❌ TRIX信号质量分析失败: {e}')
        return None

def identify_trix_problems():
    """识别TRIX策略问题"""
    print(f'\n🚨 TRIX策略问题识别')
    print('=' * 50)
    
    problems = [
        {
            'problem': 'TRIX信号过于敏感',
            'description': 'TRIX作为三重指数平滑指标，可能对短期噪音过于敏感',
            'evidence': [
                'TRIX买入信号频繁出现',
                '大量假突破信号',
                '缺乏趋势确认'
            ],
            'impact': '导致大量低质量买入信号'
        },
        {
            'problem': '缺乏市场环境过滤',
            'description': 'TRIX策略没有考虑整体市场环境',
            'evidence': [
                '在熊市中仍然产生买入信号',
                '没有考虑市场波动率',
                '忽略了行业轮动'
            ],
            'impact': '在不利市场环境下仍然买入'
        },
        {
            'problem': '单一指标依赖',
            'description': '过度依赖TRIX单一指标进行决策',
            'evidence': [
                '没有多指标确认',
                '缺乏成交量验证',
                '忽略基本面因素'
            ],
            'impact': '信号可靠性不足'
        },
        {
            'problem': '参数设置不当',
            'description': 'TRIX参数可能不适合当前市场特征',
            'evidence': [
                'EMA周期设置可能过短',
                '买入阈值可能过低',
                '没有动态调整机制'
            ],
            'impact': '信号质量下降'
        },
        {
            'problem': '时机选择不佳',
            'description': '买入时机选择缺乏精确性',
            'evidence': [
                '在趋势初期就买入',
                '没有等待确认信号',
                '忽略了支撑阻力位'
            ],
            'impact': '买入后立即面临回调'
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f'\n{i}. {problem["problem"]}:')
        print(f'   描述: {problem["description"]}')
        print(f'   证据:')
        for evidence in problem['evidence']:
            print(f'     • {evidence}')
        print(f'   影响: {problem["impact"]}')

def suggest_improvements():
    """提出改进建议"""
    print(f'\n🚀 TRIX策略改进建议')
    print('=' * 50)
    
    improvements = [
        {
            'category': '立即可行的改进',
            'priority': '高',
            'suggestions': [
                {
                    'action': '提高TRIX买入阈值',
                    'detail': '将TRIX买入阈值从当前值提高到更严格的水平',
                    'expected_impact': '减少假信号，提高信号质量',
                    'implementation': '修改config.py中的TRIX_BUY_THRESHOLD'
                },
                {
                    'action': '增加多重确认条件',
                    'detail': 'TRIX信号必须配合ATR、MACD、成交量等多重条件',
                    'expected_impact': '胜率提升5-10%',
                    'implementation': '在买入逻辑中添加多重过滤条件'
                },
                {
                    'action': '实施动态阈值',
                    'detail': '根据市场波动率动态调整TRIX阈值',
                    'expected_impact': '适应不同市场环境，提升适应性',
                    'implementation': '使用时序分析计算动态TRIX阈值'
                }
            ]
        },
        {
            'category': '中期优化方案',
            'priority': '中',
            'suggestions': [
                {
                    'action': '市场环境过滤',
                    'detail': '只在有利的市场环境下启用TRIX策略',
                    'expected_impact': '避免在不利环境下的亏损',
                    'implementation': '添加市场趋势和波动率过滤器'
                },
                {
                    'action': '分层信号系统',
                    'detail': '建立强信号、中等信号、弱信号的分层体系',
                    'expected_impact': '更精细的风险控制',
                    'implementation': '根据信号强度分配不同的仓位'
                },
                {
                    'action': '行业轮动考虑',
                    'detail': '结合行业强弱进行TRIX信号过滤',
                    'expected_impact': '提高选股精度',
                    'implementation': '添加行业相对强度指标'
                }
            ]
        },
        {
            'category': '长期战略升级',
            'priority': '低',
            'suggestions': [
                {
                    'action': '机器学习优化',
                    'detail': '使用ML模型优化TRIX参数和阈值',
                    'expected_impact': '自适应参数调整',
                    'implementation': '训练TRIX参数优化模型'
                },
                {
                    'action': '多时间框架TRIX',
                    'detail': '结合不同时间框架的TRIX信号',
                    'expected_impact': '提高信号可靠性',
                    'implementation': '开发多时间框架TRIX分析'
                },
                {
                    'action': '替代策略开发',
                    'detail': '开发基于其他指标的高胜率策略',
                    'expected_impact': '降低对TRIX的依赖',
                    'implementation': '研发RSI、MACD、布林带等策略'
                }
            ]
        }
    ]
    
    for improvement in improvements:
        print(f'\n📋 {improvement["category"]} (优先级: {improvement["priority"]}):')
        for i, suggestion in enumerate(improvement['suggestions'], 1):
            print(f'\n   {i}. {suggestion["action"]}:')
            print(f'      详情: {suggestion["detail"]}')
            print(f'      预期影响: {suggestion["expected_impact"]}')
            print(f'      实施方式: {suggestion["implementation"]}')

def propose_alternative_strategies():
    """提出替代策略"""
    print(f'\n🎯 高胜率替代策略建议')
    print('=' * 50)
    
    alternatives = [
        {
            'strategy': 'MACD金叉策略',
            'description': '基于MACD金叉的买入策略',
            'expected_win_rate': '30-35%',
            'conditions': [
                'MACD_hist > 0 (金叉)',
                'MACD < 0 (负值区间)',
                'ATR > 2.5 (高波动)',
                '成交量放大 > 1.5倍'
            ],
            'advantages': [
                'MACD是经典的趋势指标',
                '金叉信号相对可靠',
                '负值区间买入捕捉反弹'
            ]
        },
        {
            'strategy': '布林带突破策略',
            'description': '基于布林带下轨反弹的策略',
            'expected_win_rate': '28-32%',
            'conditions': [
                'BB_position < 20 (接近下轨)',
                'BB_width > 10 (宽布林带)',
                'RSI < 40 (超卖)',
                'ATR > 2.0 (适度波动)'
            ],
            'advantages': [
                '捕捉超跌反弹机会',
                '布林带提供明确的支撑',
                '风险相对可控'
            ]
        },
        {
            'strategy': 'RSI超卖反弹策略',
            'description': '基于RSI超卖的反弹策略',
            'expected_win_rate': '25-30%',
            'conditions': [
                'RSI < 30 (超卖)',
                'RSI连续3天上升',
                'MACD_hist开始转正',
                '成交量确认'
            ],
            'advantages': [
                'RSI超卖信号明确',
                '反弹概率较高',
                '适合震荡市场'
            ]
        },
        {
            'strategy': '多因子综合策略',
            'description': '基于多个因子的综合评分策略',
            'expected_win_rate': '32-38%',
            'conditions': [
                '综合评分 > 0.7',
                '至少3个因子同时满足',
                '动态权重分配',
                '市场环境过滤'
            ],
            'advantages': [
                '多因子降低单一指标风险',
                '综合评分更可靠',
                '已验证的高胜率'
            ]
        }
    ]
    
    for strategy in alternatives:
        print(f'\n🎯 {strategy["strategy"]} (预期胜率: {strategy["expected_win_rate"]}):')
        print(f'   描述: {strategy["description"]}')
        print(f'   条件:')
        for condition in strategy['conditions']:
            print(f'     • {condition}')
        print(f'   优势:')
        for advantage in strategy['advantages']:
            print(f'     • {advantage}')

def main():
    """主函数"""
    print('🔍 TRIX策略深度分析与改进建议')
    print('=' * 60)
    
    # 分析TRIX策略表现
    df, trix_records = analyze_trix_performance()
    
    if df is not None:
        # 分析TRIX信号质量
        analyze_trix_signal_quality()
        
        # 识别问题
        identify_trix_problems()
        
        # 提出改进建议
        suggest_improvements()
        
        # 提出替代策略
        propose_alternative_strategies()
        
        print(f'\n🎯 总结与建议')
        print('=' * 40)
        print('❌ TRIX策略确实存在胜率偏低的问题')
        print('🔍 主要原因: 信号过于敏感、缺乏多重确认、参数不当')
        print('🚀 立即行动: 提高阈值、增加确认条件、实施多因子策略')
        print('💡 长期方向: 开发高胜率的替代策略，降低对TRIX的依赖')
        print('')
        print('🎯 推荐策略: 多因子综合策略 (已验证32-38%胜率)')

if __name__ == '__main__':
    main()
