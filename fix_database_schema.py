# coding=utf-8
"""
修复数据库表结构
为增强因子添加必要的数据库字段
"""

import sqlite3
import pandas as pd

def check_current_schema():
    """检查当前数据库表结构"""
    print('🔍 检查当前数据库表结构')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 检查trades表结构
        cursor.execute("PRAGMA table_info(trades)")
        trades_columns = cursor.fetchall()
        
        print(f'📊 trades表当前字段 ({len(trades_columns)}个):')
        
        existing_fields = []
        for col in trades_columns:
            field_name = col[1]
            field_type = col[2]
            existing_fields.append(field_name)
            
        # 检查是否有增强因子字段
        enhanced_factor_fields = [
            'current_price', 'ma3', 'ma5', 'ma7', 'ma10', 'ma20', 'ma30', 'ma60',
            'rsi_6', 'rsi_14', 'rsi_21', 'macd', 'macd_signal', 'macd_hist',
            'adx', 'dmi_plus', 'dmi_minus', 'cci', 'willr', 'stoch_k', 'stoch_d',
            'atr_7', 'atr_14', 'atr_21', 'bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_position',
            'volume_ratio_5', 'volume_ratio_10', 'volume_ratio_20', 'obv', 'mfi',
            'trend_consistency', 'momentum_score', 'comprehensive_buy_score'
        ]
        
        missing_fields = []
        existing_enhanced_fields = []
        
        for field in enhanced_factor_fields:
            if field in existing_fields:
                existing_enhanced_fields.append(field)
            else:
                missing_fields.append(field)
        
        print(f'\n📈 增强因子字段状态:')
        print(f'  已存在: {len(existing_enhanced_fields)}个')
        print(f'  缺失: {len(missing_fields)}个')
        
        if existing_enhanced_fields:
            print(f'\n✅ 已存在的增强因子字段:')
            for field in existing_enhanced_fields[:10]:
                print(f'  • {field}')
            if len(existing_enhanced_fields) > 10:
                print(f'  ... 还有{len(existing_enhanced_fields)-10}个')
        
        if missing_fields:
            print(f'\n❌ 缺失的增强因子字段:')
            for field in missing_fields[:10]:
                print(f'  • {field}')
            if len(missing_fields) > 10:
                print(f'  ... 还有{len(missing_fields)-10}个')
        
        # 检查analysis表结构
        cursor.execute("PRAGMA table_info(analysis)")
        analysis_columns = cursor.fetchall()
        
        print(f'\n📊 analysis表当前字段 ({len(analysis_columns)}个):')
        analysis_fields = [col[1] for col in analysis_columns]
        
        analysis_missing = []
        for field in enhanced_factor_fields:
            if field not in analysis_fields:
                analysis_missing.append(field)
        
        print(f'  analysis表缺失增强因子字段: {len(analysis_missing)}个')
        
        conn.close()
        
        return existing_fields, missing_fields, analysis_fields, analysis_missing
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return [], [], [], []

def add_missing_fields_to_trades(missing_fields):
    """为trades表添加缺失的字段"""
    print(f'\n🔧 为trades表添加缺失字段')
    print('=' * 50)
    
    if not missing_fields:
        print('✅ 无需添加字段')
        return True
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        print(f'📊 准备添加 {len(missing_fields)} 个字段:')
        
        success_count = 0
        for field in missing_fields:
            try:
                # 根据字段名确定数据类型
                if any(keyword in field.lower() for keyword in ['signal', 'cross', 'oversold', 'overbought', 'anomaly']):
                    field_type = 'INTEGER'
                else:
                    field_type = 'REAL'
                
                alter_sql = f"ALTER TABLE trades ADD COLUMN {field} {field_type}"
                cursor.execute(alter_sql)
                print(f'  ✅ 添加字段: {field} ({field_type})')
                success_count += 1
                
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print(f'  ⚠️ 字段已存在: {field}')
                else:
                    print(f'  ❌ 添加失败: {field} - {e}')
        
        conn.commit()
        conn.close()
        
        print(f'\n📊 字段添加结果: 成功{success_count}/{len(missing_fields)}个')
        return success_count == len(missing_fields)
        
    except Exception as e:
        print(f'❌ 添加字段失败: {e}')
        return False

def add_missing_fields_to_analysis(analysis_missing):
    """为analysis表添加缺失的字段"""
    print(f'\n🔧 为analysis表添加缺失字段')
    print('=' * 50)
    
    if not analysis_missing:
        print('✅ 无需添加字段')
        return True
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        print(f'📊 准备添加 {len(analysis_missing)} 个字段:')
        
        success_count = 0
        for field in analysis_missing:
            try:
                # 根据字段名确定数据类型
                if any(keyword in field.lower() for keyword in ['signal', 'cross', 'oversold', 'overbought', 'anomaly']):
                    field_type = 'INTEGER'
                else:
                    field_type = 'REAL'
                
                alter_sql = f"ALTER TABLE analysis ADD COLUMN {field} {field_type}"
                cursor.execute(alter_sql)
                print(f'  ✅ 添加字段: {field} ({field_type})')
                success_count += 1
                
            except sqlite3.OperationalError as e:
                if 'duplicate column name' in str(e):
                    print(f'  ⚠️ 字段已存在: {field}')
                else:
                    print(f'  ❌ 添加失败: {field} - {e}')
        
        conn.commit()
        conn.close()
        
        print(f'\n📊 字段添加结果: 成功{success_count}/{len(analysis_missing)}个')
        return success_count == len(analysis_missing)
        
    except Exception as e:
        print(f'❌ 添加字段失败: {e}')
        return False

def verify_schema_update():
    """验证表结构更新"""
    print(f'\n✅ 验证表结构更新')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 检查trades表
        cursor.execute("PRAGMA table_info(trades)")
        trades_columns = cursor.fetchall()
        
        # 检查analysis表
        cursor.execute("PRAGMA table_info(analysis)")
        analysis_columns = cursor.fetchall()
        
        print(f'📊 更新后的表结构:')
        print(f'  trades表字段数: {len(trades_columns)}')
        print(f'  analysis表字段数: {len(analysis_columns)}')
        
        # 检查关键增强因子字段
        key_fields = ['current_price', 'ma20', 'rsi_14', 'macd', 'comprehensive_buy_score']
        
        trades_fields = [col[1] for col in trades_columns]
        analysis_fields = [col[1] for col in analysis_columns]
        
        print(f'\n📋 关键字段检查:')
        for field in key_fields:
            trades_has = field in trades_fields
            analysis_has = field in analysis_fields
            print(f'  {field}: trades表{"✅" if trades_has else "❌"} | analysis表{"✅" if analysis_has else "❌"}')
        
        conn.close()
        
        return len(trades_columns), len(analysis_columns)
        
    except Exception as e:
        print(f'❌ 验证失败: {e}')
        return 0, 0

def test_factor_data_insertion():
    """测试因子数据插入"""
    print(f'\n🧪 测试因子数据插入')
    print('=' * 50)
    
    try:
        # 创建测试数据
        test_data = {
            'timestamp': '2024-01-01 09:30:00+0800',
            'symbol': 'TEST.000001',
            'action': 'BUY',
            'price': 10.50,
            'volume': 1000,
            'current_price': 10.50,
            'ma20': 10.30,
            'rsi_14': 45.5,
            'macd': 0.15,
            'comprehensive_buy_score': 0.75
        }
        
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 构建插入SQL
        fields = list(test_data.keys())
        placeholders = ', '.join(['?' for _ in fields])
        values = list(test_data.values())
        
        insert_sql = f"INSERT INTO trades ({', '.join(fields)}) VALUES ({placeholders})"
        
        cursor.execute(insert_sql, values)
        conn.commit()
        
        # 验证插入
        cursor.execute("SELECT * FROM trades WHERE symbol = 'TEST.000001'")
        result = cursor.fetchone()
        
        if result:
            print('✅ 测试数据插入成功')
            print(f'  插入字段数: {len(fields)}')
            print(f'  验证记录: {result[1]} @ {result[0]}')  # symbol @ timestamp
            
            # 删除测试数据
            cursor.execute("DELETE FROM trades WHERE symbol = 'TEST.000001'")
            conn.commit()
            print('✅ 测试数据已清理')
        else:
            print('❌ 测试数据插入失败')
        
        conn.close()
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return False

def main():
    """主函数"""
    print('🔧 数据库表结构修复工具')
    print('=' * 60)
    
    # 检查当前表结构
    existing_fields, missing_fields, analysis_fields, analysis_missing = check_current_schema()
    
    # 添加缺失字段到trades表
    if missing_fields:
        trades_success = add_missing_fields_to_trades(missing_fields)
    else:
        trades_success = True
    
    # 添加缺失字段到analysis表
    if analysis_missing:
        analysis_success = add_missing_fields_to_analysis(analysis_missing)
    else:
        analysis_success = True
    
    # 验证更新
    if trades_success and analysis_success:
        trades_count, analysis_count = verify_schema_update()
        
        # 测试数据插入
        if trades_count > 0:
            test_success = test_factor_data_insertion()
        else:
            test_success = False
    else:
        test_success = False
    
    print(f'\n🎯 修复结果')
    print('=' * 40)
    
    if trades_success and analysis_success and test_success:
        print('✅ 数据库表结构修复成功')
        print('✅ 增强因子字段已添加')
        print('✅ 数据插入测试通过')
        print('\n🚀 现在可以重新运行策略测试因子保存')
    else:
        print('❌ 数据库表结构修复失败')
        print('🔧 请检查错误信息并手动修复')
    
    print(f'\n📋 下一步:')
    print('1. 🔄 重新运行策略')
    print('2. 🔍 检查因子数据是否正确保存')
    print('3. 📊 运行因子有效性分析')

if __name__ == '__main__':
    main()
