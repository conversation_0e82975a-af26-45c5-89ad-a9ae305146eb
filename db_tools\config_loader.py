#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和策略分析系统 - 配置加载模块
"""

import os
import sys
import configparser
from pathlib import Path

# 添加父目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_file=None):
        """
        初始化配置加载器
        
        参数:
        - config_file: 配置文件路径，如果为None则使用默认路径
        """
        # 获取当前脚本所在目录
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        
        # 获取项目根目录
        self.root_dir = self.script_dir.parent
        
        # 配置文件路径
        if config_file is None:
            self.config_file = self.script_dir / 'config.ini'
        else:
            self.config_file = Path(config_file)
        
        # 加载配置
        self.config = configparser.ConfigParser(interpolation=None)  # 禁用插值以避免%符号问题
        
        # 设置默认值
        self.set_defaults()
        
        # 从文件加载配置
        if self.config_file.exists():
            self.load_from_file()
        else:
            print(f"警告: 配置文件 '{self.config_file}' 不存在，将使用默认配置")
            self.save_to_file()  # 创建默认配置文件
    
    def set_defaults(self):
        """设置默认配置"""
        self.config['Database'] = {
            'db_file': 'data.db',
            'backup_dir': 'backups'
        }
        
        self.config['Reports'] = {
            'report_dir': 'reports',
            'report_name_format': 'db_report_%Y%m%d_%H%M%S.html'
        }
        
        self.config['Analysis'] = {
            'output_dir': 'analysis',
            'generate_plots': 'true',
            'plot_dpi': '100'
        }
        
        self.config['UI'] = {
            'use_color': 'true',
            'language': 'zh_CN'
        }
    
    def load_from_file(self):
        """从文件加载配置"""
        try:
            self.config.read(self.config_file, encoding='utf-8')
            print(f"已从 '{self.config_file}' 加载配置")
        except Exception as e:
            print(f"加载配置文件时出错: {str(e)}")
            print("将使用默认配置")
    
    def save_to_file(self):
        """将配置保存到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            print(f"配置已保存到 '{self.config_file}'")
        except Exception as e:
            print(f"保存配置文件时出错: {str(e)}")
    
    def get_db_file(self):
        """获取数据库文件路径"""
        db_file = self.config.get('Database', 'db_file')
        
        # 如果是相对路径，则相对于项目根目录
        if not os.path.isabs(db_file):
            db_file = os.path.join(self.root_dir, db_file)
        
        return db_file
    
    def get_backup_dir(self):
        """获取备份目录路径"""
        backup_dir = self.config.get('Database', 'backup_dir')
        
        # 如果是相对路径，则相对于项目根目录
        if not os.path.isabs(backup_dir):
            backup_dir = os.path.join(self.root_dir, backup_dir)
        
        # 确保目录存在
        os.makedirs(backup_dir, exist_ok=True)
        
        return backup_dir
    
    def get_report_dir(self):
        """获取报告目录路径"""
        report_dir = self.config.get('Reports', 'report_dir')
        
        # 如果是相对路径，则相对于项目根目录
        if not os.path.isabs(report_dir):
            report_dir = os.path.join(self.root_dir, report_dir)
        
        # 确保目录存在
        os.makedirs(report_dir, exist_ok=True)
        
        return report_dir
    
    def get_report_name_format(self):
        """获取报告文件名格式"""
        return self.config.get('Reports', 'report_name_format')
    
    def get_analysis_dir(self):
        """获取分析结果目录路径"""
        output_dir = self.config.get('Analysis', 'output_dir')
        
        # 如果是相对路径，则相对于项目根目录
        if not os.path.isabs(output_dir):
            output_dir = os.path.join(self.root_dir, output_dir)
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        return output_dir
    
    def get_generate_plots(self):
        """获取是否生成图表的设置"""
        return self.config.getboolean('Analysis', 'generate_plots')
    
    def get_plot_dpi(self):
        """获取图表DPI设置"""
        return self.config.getint('Analysis', 'plot_dpi')
    
    def get_use_color(self):
        """获取是否使用彩色输出的设置"""
        return self.config.getboolean('UI', 'use_color')
    
    def get_language(self):
        """获取语言设置"""
        return self.config.get('UI', 'language')
    
    def get_all_config(self):
        """获取所有配置"""
        result = {}
        for section in self.config.sections():
            result[section] = {}
            for key, value in self.config[section].items():
                result[section][key] = value
        return result

# 创建全局配置加载器实例
config_loader = ConfigLoader()

# 测试代码
if __name__ == "__main__":
    print("配置加载测试")
    print(f"数据库文件: {config_loader.get_db_file()}")
    print(f"备份目录: {config_loader.get_backup_dir()}")
    print(f"报告目录: {config_loader.get_report_dir()}")
    print(f"分析结果目录: {config_loader.get_analysis_dir()}")
    print(f"是否生成图表: {config_loader.get_generate_plots()}")
    print(f"图表DPI: {config_loader.get_plot_dpi()}")
    print(f"是否使用彩色输出: {config_loader.get_use_color()}")
    print(f"语言: {config_loader.get_language()}")
    print("\n所有配置:")
    import json
    print(json.dumps(config_loader.get_all_config(), indent=4, ensure_ascii=False)) 