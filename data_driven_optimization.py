# coding=utf-8
"""
基于数据驱动的正确优化方案
不强制分散，而是基于市场特征和因子有效性优化
"""

def analyze_opening_signal_quality():
    """分析开盘信号质量"""
    print('🔍 开盘信号质量分析')
    print('=' * 60)
    
    try:
        import sqlite3
        import pandas as pd
        import numpy as np
        
        conn = sqlite3.connect('data/trades.db')
        
        # 分析开盘时段 vs 其他时段的实际表现
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                s.net_profit_pct_sell,
                s.sell_reason,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        ORDER BY buy_time DESC
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 成功匹配: {len(df)} 条交易')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的交易数据')
            return None
        
        # 分析开盘 vs 其他时段表现
        opening_trades = df[df['buy_hour'] == 9]
        other_trades = df[df['buy_hour'] != 9]
        
        print(f'\n📈 开盘时段 vs 其他时段表现对比:')
        
        if len(opening_trades) > 0:
            opening_win_rate = (opening_trades['net_profit_pct_sell'] > 0).mean() * 100
            opening_avg_profit = opening_trades['net_profit_pct_sell'].mean()
            opening_count = len(opening_trades)
            
            print(f'   开盘时段 (09:00):')
            print(f'     交易数: {opening_count}')
            print(f'     胜率: {opening_win_rate:.1f}%')
            print(f'     平均收益: {opening_avg_profit:.2f}%')
        
        if len(other_trades) > 0:
            other_win_rate = (other_trades['net_profit_pct_sell'] > 0).mean() * 100
            other_avg_profit = other_trades['net_profit_pct_sell'].mean()
            other_count = len(other_trades)
            
            print(f'   其他时段:')
            print(f'     交易数: {other_count}')
            print(f'     胜率: {other_win_rate:.1f}%')
            print(f'     平均收益: {other_avg_profit:.2f}%')
            
            # 比较分析
            win_rate_diff = opening_win_rate - other_win_rate
            profit_diff = opening_avg_profit - other_avg_profit
            
            print(f'\n🎯 对比结果:')
            print(f'   胜率差异: {win_rate_diff:+.1f}%')
            print(f'   收益差异: {profit_diff:+.2f}%')
            
            if win_rate_diff > 5:
                print(f'   ✅ 开盘时段表现更好，信号集中是合理的')
                strategy = 'optimize_opening'
            elif win_rate_diff < -5:
                print(f'   ⚠️ 开盘时段表现较差，需要提高筛选标准')
                strategy = 'tighten_opening'
            else:
                print(f'   📊 两个时段表现相近，需要综合优化')
                strategy = 'balanced_optimization'
            
            return {
                'opening_win_rate': opening_win_rate,
                'other_win_rate': other_win_rate,
                'opening_avg_profit': opening_avg_profit,
                'other_avg_profit': other_avg_profit,
                'strategy': strategy
            }
        
        return None
        
    except Exception as e:
        print(f'❌ 开盘信号质量分析失败: {e}')
        return None

def generate_correct_optimization_strategy(analysis_result):
    """生成正确的优化策略"""
    print(f'\n💡 基于数据的正确优化策略')
    print('=' * 50)
    
    if not analysis_result:
        print('⚠️ 缺少分析数据，无法生成策略')
        return
    
    strategy_type = analysis_result['strategy']
    opening_win_rate = analysis_result['opening_win_rate']
    other_win_rate = analysis_result['other_win_rate']
    
    print(f'🎯 策略类型: {strategy_type}')
    
    if strategy_type == 'optimize_opening':
        print(f'\n✅ 开盘时段表现更好 (胜率{opening_win_rate:.1f}% vs {other_win_rate:.1f}%)')
        print(f'📊 正确做法:')
        print(f'   1. 保持开盘时段的信号集中 (这是市场特征的自然反映)')
        print(f'   2. 进一步优化开盘时段的因子筛选质量')
        print(f'   3. 提升其他时段的信号质量，而非强制增加数量')
        print(f'   4. 基于CCI等有效因子的特征差异进行优化')
        
        config_suggestion = '''
# 正确的优化配置 - 保持开盘优势
MARKET_ADAPTIVE_CONFIG = {
    'respect_market_characteristics': True,
    
    # 开盘时段优化 (表现更好，保持集中)
    'opening_optimization': {
        'enable': True,
        'enhance_quality': True,        # 提升质量而非限制数量
        'cci_threshold': 25,            # 基于CCI在开盘时段的特征
        'quality_multiplier': 1.2,     # 提高质量要求20%
    },
    
    # 其他时段优化 (提升质量)
    'other_hours_optimization': {
        'enable': True,
        'lower_thresholds': True,       # 适度降低阈值
        'cci_threshold': 15,            # 适应其他时段CCI特征
        'quality_multiplier': 0.8,     # 降低质量要求20%
    }
}'''
        
    elif strategy_type == 'tighten_opening':
        print(f'\n⚠️ 开盘时段表现较差 (胜率{opening_win_rate:.1f}% vs {other_win_rate:.1f}%)')
        print(f'📊 正确做法:')
        print(f'   1. 提高开盘时段的筛选标准')
        print(f'   2. 基于CCI等因子的开盘特征调整阈值')
        print(f'   3. 不是强制分散，而是提高开盘信号质量')
        print(f'   4. 保持其他时段的优势')
        
        config_suggestion = '''
# 正确的优化配置 - 提高开盘标准
MARKET_ADAPTIVE_CONFIG = {
    'respect_market_characteristics': True,
    
    # 开盘时段收紧 (表现较差，提高标准)
    'opening_optimization': {
        'enable': True,
        'tighten_standards': True,      # 提高标准而非强制限制
        'cci_threshold': 35,            # 提高CCI要求
        'quality_multiplier': 1.5,     # 提高质量要求50%
    },
    
    # 其他时段保持 (表现更好)
    'other_hours_optimization': {
        'enable': True,
        'maintain_standards': True,     # 保持当前标准
        'cci_threshold': 15,            # 保持适中要求
        'quality_multiplier': 1.0,     # 保持当前要求
    }
}'''
        
    else:  # balanced_optimization
        print(f'\n📊 两个时段表现相近 (胜率{opening_win_rate:.1f}% vs {other_win_rate:.1f}%)')
        print(f'📊 正确做法:')
        print(f'   1. 基于因子特征差异进行精细化调整')
        print(f'   2. 开盘时段基于CCI高值特征优化')
        print(f'   3. 其他时段基于CCI低值特征优化')
        print(f'   4. 不强制分散，而是因子自适应')
        
        config_suggestion = '''
# 正确的优化配置 - 因子自适应
MARKET_ADAPTIVE_CONFIG = {
    'respect_market_characteristics': True,
    
    # 基于CCI特征的自适应调整
    'factor_adaptive': {
        'enable': True,
        'cci_based_adjustment': {
            'high_cci_threshold': 30,       # 开盘时段CCI通常>23
            'low_cci_threshold': 15,        # 其他时段CCI通常<20
            'high_cci_multiplier': 1.1,     # CCI高时稍微提高要求
            'low_cci_multiplier': 0.9,      # CCI低时稍微降低要求
        }
    }
}'''
    
    print(f'\n⚙️ 建议配置:')
    print(config_suggestion)
    
    return config_suggestion

def create_market_adaptive_config():
    """创建市场自适应配置"""
    print(f'\n🔧 市场自适应配置生成')
    print('=' * 50)
    
    adaptive_config = '''
# 市场自适应策略配置
# 基于市场特征和因子有效性，而非强制分散

# ==================== 市场自适应核心配置 ====================

# 启用市场自适应策略
ENABLE_MARKET_ADAPTIVE_STRATEGY = True

# 基于CCI因子特征的自适应调整 (CCI是最有效因子，开盘时段+35.9%差异)
CCI_ADAPTIVE_CONFIG = {
    'enable': True,
    'opening_cci_avg': 23.7,           # 开盘时段CCI平均值
    'other_cci_avg': 17.4,             # 其他时段CCI平均值
    
    # 基于CCI值动态调整阈值
    'dynamic_thresholds': {
        'high_cci_zone': {             # CCI > 25 (类似开盘特征)
            'cci_min': 25,
            'other_factor_multiplier': 1.1,  # 其他因子要求提高10%
        },
        'medium_cci_zone': {           # CCI 15-25
            'cci_min': 15,
            'cci_max': 25,
            'other_factor_multiplier': 1.0,  # 其他因子正常要求
        },
        'low_cci_zone': {              # CCI < 15 (其他时段特征)
            'cci_max': 15,
            'other_factor_multiplier': 0.9,  # 其他因子要求降低10%
        }
    }
}

# 基于实际表现的质量优化
QUALITY_BASED_OPTIMIZATION = {
    'enable': True,
    'focus_on_quality': True,          # 专注质量而非数量
    'adaptive_thresholds': True,       # 自适应阈值调整
    
    # 高效因子组合 (基于IC分析结果)
    'effective_factors': {
        'cci': {
            'weight': 0.25,            # 最高权重
            'adaptive_threshold': True, # 启用自适应阈值
        },
        'adx': {
            'weight': 0.20,
            'min_threshold': 25,       # ADX最小要求
        },
        'bb_position': {
            'weight': 0.18,
            'optimal_range': [0.3, 0.8],
        },
        'rsi': {
            'weight': 0.15,
            'optimal_range': [40, 70],
        },
        'atr_pct': {
            'weight': 0.12,
            'min_threshold': 2.0,
        },
        'bb_width': {
            'weight': 0.10,
            'min_threshold': 8.0,
        }
    }
}

# 禁用强制时间分散 (违背市场规律)
DISABLE_FORCED_TIME_DISTRIBUTION = True

# 启用基于表现的动态优化
PERFORMANCE_BASED_OPTIMIZATION = {
    'enable': True,
    'monitor_hourly_performance': True,
    'adjust_based_on_results': True,
    'respect_market_patterns': True,
}
'''
    
    return adaptive_config

def main():
    """主函数"""
    print('🚀 基于数据驱动的正确优化方案')
    print('=' * 60)
    
    print('🎯 核心理念:')
    print('   ✅ 尊重市场特征和规律')
    print('   ✅ 基于数据驱动优化')
    print('   ✅ 因子有效性自适应')
    print('   ❌ 避免强制时间分散')
    
    # 分析开盘信号质量
    analysis_result = analyze_opening_signal_quality()
    
    # 生成正确的优化策略
    if analysis_result:
        config_suggestion = generate_correct_optimization_strategy(analysis_result)
    
    # 创建市场自适应配置
    adaptive_config = create_market_adaptive_config()
    
    # 保存配置
    with open('market_adaptive_config.py', 'w', encoding='utf-8') as f:
        f.write(adaptive_config)
    
    print(f'\n✅ 市场自适应配置已生成: market_adaptive_config.py')
    
    print(f'\n🎯 关键洞察总结')
    print('=' * 40)
    print('✅ 开盘信号集中可能是合理的市场反映')
    print('✅ CCI因子在开盘时段有35.9%的显著差异')
    print('✅ 应该基于因子特征优化，而非强制分散')
    print('✅ 策略应该自适应市场特征')
    
    print(f'\n💡 正确的优化方向:')
    print(f'   1. 基于CCI等因子的时段特征进行自适应调整')
    print(f'   2. 专注信号质量而非强制数量分散')
    print(f'   3. 尊重市场规律和因子有效性')
    print(f'   4. 动态优化而非静态限制')

if __name__ == '__main__':
    main()
