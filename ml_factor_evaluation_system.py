# coding=utf-8
"""
机器学习因子评估系统
基于历史表现自动评估和优化因子权重
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split, cross_val_score
import logging
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLFactorEvaluationSystem:
    """机器学习因子评估系统"""
    
    def __init__(self):
        self.factor_importance_scores = {}
        self.factor_performance_history = {}
        self.ml_models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'linear_regression': LinearRegression()
        }
        self.best_model = None
        self.feature_importance = {}
        
    def generate_synthetic_training_data(self, n_samples=1000):
        """生成合成训练数据"""
        logger.info("生成机器学习训练数据...")
        
        np.random.seed(42)
        
        # 生成68个因子的模拟数据
        factor_names = [
            # 技术因子
            'rsi_6', 'rsi_14', 'rsi_21', 'rsi_adaptive', 'cci_14', 'cci_20',
            'adx_14', 'atr_14', 'atr_pct', 'bb_upper', 'bb_middle', 'bb_lower',
            'bb_width', 'bb_position', 'bb_squeeze', 'macd', 'macd_signal', 'macd_hist',
            'macd_cross', 'momentum_5d', 'momentum_10d', 'momentum_20d', 'momentum_consistency',
            'volume_ratio', 'volume_trend', 'volume_price_correlation', 'price_position_20d',
            'near_resistance', 'near_support',
            
            # 基本面因子
            'pe_relative', 'roe_quality', 'roe_stability', 'revenue_growth', 'profit_growth',
            'financial_health', 'valuation_attractiveness',
            
            # 情绪面因子
            'main_fund_persistence', 'market_attention', 'volume_breakthrough',
            'fund_flow_strength', 'investor_sentiment', 'concept_relevance',
            
            # 跨市场因子
            'industry_relative_strength', 'market_beta', 'concept_heat',
            'sector_rotation', 'size_factor', 'value_growth_factor',
            
            # 实时因子
            'opening_momentum', 'opening_momentum_strong', 'opening_momentum_positive',
            'opening_gap', 'gap_up', 'gap_down', 'intraday_high_ratio',
            'intraday_low_ratio', 'intraday_volatility', 'price_position_pct',
            'near_high_20d', 'near_low_20d', 'hour_of_day', 'is_morning_session',
            'is_afternoon_session',
            
            # 综合评分
            'technical_score', 'fundamental_score', 'sentiment_score',
            'cross_market_score', 'overall_score'
        ]
        
        # 生成因子数据
        factor_data = {}
        for factor in factor_names:
            if 'score' in factor:
                # 评分类因子 (0-1)
                factor_data[factor] = np.random.uniform(0, 1, n_samples)
            elif factor in ['rsi_6', 'rsi_14', 'rsi_21', 'rsi_adaptive']:
                # RSI类因子 (0-100)
                factor_data[factor] = np.random.uniform(20, 80, n_samples)
            elif factor in ['cci_14', 'cci_20']:
                # CCI类因子 (-100 to 100)
                factor_data[factor] = np.random.uniform(-100, 100, n_samples)
            elif 'atr' in factor:
                # ATR类因子 (1-6%)
                factor_data[factor] = np.random.uniform(1, 6, n_samples)
            elif 'momentum' in factor:
                # 动量类因子 (-10% to 10%)
                factor_data[factor] = np.random.uniform(-10, 10, n_samples)
            elif factor in ['pe_relative']:
                # PE相对值 (0.5-2.0)
                factor_data[factor] = np.random.uniform(0.5, 2.0, n_samples)
            elif factor in ['roe_quality']:
                # ROE质量 (5-30%)
                factor_data[factor] = np.random.uniform(5, 30, n_samples)
            elif 'growth' in factor:
                # 增长类因子 (-20% to 30%)
                factor_data[factor] = np.random.uniform(-20, 30, n_samples)
            else:
                # 其他因子 (标准化到0-1)
                factor_data[factor] = np.random.uniform(0, 1, n_samples)
        
        # 生成目标变量 (未来收益率)
        # 基于因子的线性组合 + 噪声
        target_weights = {
            'overall_score': 0.3,
            'technical_score': 0.2,
            'fundamental_score': 0.15,
            'sentiment_score': 0.15,
            'atr_pct': 0.1,
            'cci_14': 0.05,
            'rsi_14': 0.05
        }
        
        target = np.zeros(n_samples)
        for factor, weight in target_weights.items():
            if factor in factor_data:
                # 标准化因子值
                normalized_factor = (factor_data[factor] - np.mean(factor_data[factor])) / np.std(factor_data[factor])
                target += weight * normalized_factor
        
        # 添加噪声
        target += np.random.normal(0, 0.5, n_samples)
        
        # 转换为收益率 (-10% to 15%)
        target = np.tanh(target) * 12.5  # tanh函数限制范围
        
        df = pd.DataFrame(factor_data)
        df['future_return'] = target
        
        logger.info(f"✅ 生成训练数据完成: {len(df)} 样本, {len(factor_names)} 个因子")
        
        return df, factor_names
    
    def evaluate_factor_importance(self, training_data, factor_names):
        """评估因子重要性"""
        logger.info("开始机器学习因子重要性评估...")
        
        try:
            # 准备数据
            X = training_data[factor_names]
            y = training_data['future_return']
            
            # 处理缺失值
            X = X.fillna(X.mean())
            
            # 分割训练测试集
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            model_results = {}
            
            # 训练多个模型
            for model_name, model in self.ml_models.items():
                logger.info(f"训练模型: {model_name}")
                
                # 训练模型
                model.fit(X_train, y_train)
                
                # 预测
                y_pred = model.predict(X_test)
                
                # 评估
                mse = mean_squared_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')
                
                model_results[model_name] = {
                    'mse': mse,
                    'r2': r2,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'model': model
                }
                
                logger.info(f"  {model_name}: R² = {r2:.4f}, CV = {cv_scores.mean():.4f}±{cv_scores.std():.4f}")
            
            # 选择最佳模型
            best_model_name = max(model_results.keys(), key=lambda k: model_results[k]['r2'])
            self.best_model = model_results[best_model_name]['model']
            
            logger.info(f"✅ 最佳模型: {best_model_name}")
            
            # 获取特征重要性
            if hasattr(self.best_model, 'feature_importances_'):
                importance_scores = self.best_model.feature_importances_
                self.feature_importance = dict(zip(factor_names, importance_scores))
            elif hasattr(self.best_model, 'coef_'):
                # 对于线性模型，使用系数的绝对值作为重要性
                importance_scores = np.abs(self.best_model.coef_)
                self.feature_importance = dict(zip(factor_names, importance_scores))
            else:
                # 如果模型没有特征重要性，创建默认重要性
                self.feature_importance = {factor: 1.0/len(factor_names) for factor in factor_names}

            # 排序特征重要性
            sorted_importance = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)

            logger.info("🔍 前10个最重要因子:")
            for i, (factor, importance) in enumerate(sorted_importance[:10], 1):
                logger.info(f"  {i}. {factor}: {importance:.4f}")

            return model_results, self.feature_importance
            
        except Exception as e:
            logger.error(f"因子重要性评估失败: {e}")
            return {}, {}
    
    def generate_optimized_weights(self, current_config):
        """生成优化的权重配置"""
        logger.info("基于机器学习结果生成优化权重...")
        
        if not self.feature_importance:
            logger.warning("没有特征重要性数据，使用默认权重")
            return current_config
        
        try:
            # 获取当前配置中的因子
            config_factors = {}
            for factor_name, factor_config in current_config.items():
                if isinstance(factor_config, dict) and 'weight' in factor_config:
                    config_factors[factor_name] = factor_config['weight']
            
            # 基于机器学习重要性调整权重
            optimized_config = current_config.copy()
            
            # 计算重要性分数的归一化
            total_importance = sum(self.feature_importance.values())
            
            for factor_name in config_factors:
                if factor_name in self.feature_importance:
                    # 获取机器学习重要性
                    ml_importance = self.feature_importance[factor_name]
                    normalized_importance = ml_importance / total_importance
                    
                    # 当前权重
                    current_weight = config_factors[factor_name]
                    
                    # 新权重 = 当前权重 * (1 + 重要性调整)
                    importance_multiplier = 1 + (normalized_importance - 0.01) * 2  # 调整系数
                    new_weight = current_weight * importance_multiplier
                    
                    # 限制权重范围
                    new_weight = max(0.01, min(0.25, new_weight))
                    
                    optimized_config[factor_name]['weight'] = round(new_weight, 3)
                    optimized_config[factor_name]['ml_importance'] = round(ml_importance, 4)
                    optimized_config[factor_name]['optimization_note'] = f'ML优化: 重要性{ml_importance:.4f}, 权重调整{current_weight:.3f}→{new_weight:.3f}'
            
            # 重新归一化权重
            total_weight = sum(config['weight'] for config in optimized_config.values() 
                             if isinstance(config, dict) and 'weight' in config)
            
            if total_weight > 0:
                for factor_name, factor_config in optimized_config.items():
                    if isinstance(factor_config, dict) and 'weight' in factor_config:
                        factor_config['weight'] = round(factor_config['weight'] / total_weight, 3)
            
            logger.info("✅ 权重优化完成")
            
            return optimized_config
            
        except Exception as e:
            logger.error(f"权重优化失败: {e}")
            return current_config
    
    def create_factor_performance_report(self):
        """创建因子表现报告"""
        logger.info("生成因子表现报告...")
        
        if not self.feature_importance:
            return "没有足够的数据生成报告"
        
        # 按重要性排序
        sorted_factors = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        report = "🔍 机器学习因子重要性分析报告\n"
        report += "=" * 60 + "\n\n"
        
        # 顶级因子
        report += "🏆 顶级重要因子 (前10名):\n"
        for i, (factor, importance) in enumerate(sorted_factors[:10], 1):
            report += f"   {i:2d}. {factor:25s}: {importance:.4f}\n"
        
        # 因子分类分析
        factor_categories = {
            'technical': ['rsi', 'cci', 'adx', 'atr', 'bb_', 'macd', 'momentum', 'volume'],
            'fundamental': ['pe_', 'roe_', 'revenue_', 'profit_', 'financial_', 'valuation_'],
            'sentiment': ['fund_', 'market_attention', 'volume_breakthrough', 'investor_', 'concept_'],
            'cross_market': ['industry_', 'market_beta', 'sector_', 'size_', 'value_'],
            'realtime': ['opening_', 'gap_', 'intraday_', 'hour_', 'is_'],
            'scores': ['_score']
        }
        
        report += "\n📊 分类重要性分析:\n"
        for category, keywords in factor_categories.items():
            category_factors = []
            for factor, importance in self.feature_importance.items():
                if any(keyword in factor for keyword in keywords):
                    category_factors.append((factor, importance))
            
            if category_factors:
                avg_importance = np.mean([imp for _, imp in category_factors])
                report += f"   {category:12s}: 平均重要性 {avg_importance:.4f} ({len(category_factors)}个因子)\n"
        
        # 权重调整建议
        report += "\n🔧 权重调整建议:\n"
        high_importance_factors = [factor for factor, importance in sorted_factors[:15]]
        low_importance_factors = [factor for factor, importance in sorted_factors[-10:]]
        
        report += "   提升权重: " + ", ".join(high_importance_factors[:5]) + "\n"
        report += "   降低权重: " + ", ".join(low_importance_factors[:5]) + "\n"
        
        return report
    
    def save_ml_optimization_results(self, optimized_config, filename=None):
        """保存机器学习优化结果"""
        if filename is None:
            filename = f"ml_optimized_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# -*- coding: utf-8 -*-\n")
                f.write('"""\n机器学习优化后的配置\n')
                f.write(f'生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                f.write('基于机器学习因子重要性分析的权重优化\n"""\n\n')
                
                f.write("ML_OPTIMIZED_FACTORS_CONFIG = {\n")
                for factor_name, factor_config in optimized_config.items():
                    if isinstance(factor_config, dict):
                        f.write(f"    '{factor_name}': {{\n")
                        for key, value in factor_config.items():
                            if isinstance(value, str):
                                f.write(f"        '{key}': '{value}',\n")
                            else:
                                f.write(f"        '{key}': {value},\n")
                        f.write("    },\n")
                
                f.write("}\n")
            
            logger.info(f"✅ ML优化配置已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存ML优化结果失败: {e}")
            return None

def main():
    """主函数"""
    print("🤖 机器学习因子评估系统")
    print("=" * 60)
    
    # 创建ML评估系统
    ml_system = MLFactorEvaluationSystem()
    
    # 生成训练数据
    training_data, factor_names = ml_system.generate_synthetic_training_data(1000)
    
    # 评估因子重要性
    model_results, feature_importance = ml_system.evaluate_factor_importance(training_data, factor_names)
    
    if feature_importance:
        # 生成因子表现报告
        report = ml_system.create_factor_performance_report()
        print(f"\n{report}")
        
        # 模拟当前配置
        current_config = {
            'cci_14': {'weight': 0.10},
            'rsi_14': {'weight': 0.08},
            'atr_pct': {'weight': 0.10},
            'adx_14': {'weight': 0.08},
            'overall_score': {'weight': 0.15},
            'technical_score': {'weight': 0.12},
            'fundamental_score': {'weight': 0.10},
            'sentiment_score': {'weight': 0.08}
        }
        
        # 生成优化权重
        optimized_config = ml_system.generate_optimized_weights(current_config)
        
        print(f"\n🔧 权重优化结果:")
        print("=" * 40)
        for factor, config in optimized_config.items():
            if 'weight' in config:
                print(f"{factor:20s}: {config['weight']:.3f}")
        
        # 保存优化结果
        saved_file = ml_system.save_ml_optimization_results(optimized_config)
        
        print(f"\n✅ 机器学习因子评估完成")
        print(f"📊 分析了 {len(factor_names)} 个因子")
        print(f"🎯 识别了最重要的因子")
        print(f"⚙️ 生成了优化权重配置")
        if saved_file:
            print(f"💾 结果已保存到: {saved_file}")
    
    else:
        print("❌ 机器学习评估失败")
    
    return ml_system

if __name__ == '__main__':
    main()
