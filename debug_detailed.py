# coding=utf-8
"""
详细调试多因子评分
"""

import pandas as pd
import numpy as np

def test_with_detailed_logging():
    """详细调试测试"""
    print('🔧 详细调试多因子评分')
    print('=' * 50)
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=30, freq='D')
    np.random.seed(42)
    
    test_data = pd.DataFrame({
        'open': [100 + i + np.random.randn() for i in range(30)],
        'high': [102 + i + np.random.randn() for i in range(30)],
        'low': [98 + i + np.random.randn() for i in range(30)],
        'close': [100 + i + np.random.randn() for i in range(30)],
        'volume': [5000 + np.random.randint(0, 5000) for i in range(30)]
    }, index=dates)
    
    print(f'📊 测试数据创建完成')
    
    # 导入并创建引擎
    from enhanced_factor_engine import EnhancedFactorEngine
    
    # 创建一个带有详细日志的mock context
    class MockContext:
        class MockLog:
            def error(self, msg):
                print(f"ERROR: {msg}")
            def warning(self, msg):
                print(f"WARNING: {msg}")
            def info(self, msg):
                print(f"INFO: {msg}")
        
        def __init__(self):
            self.log = self.MockLog()
    
    context = MockContext()
    engine = EnhancedFactorEngine(context)
    
    print(f'🔍 开始计算因子...')
    
    # 直接调用calculate_all_factors并捕获所有输出
    try:
        factors = engine.calculate_all_factors(test_data, 'DEBUG.TEST')
        print(f'✅ 因子计算完成: {len(factors)} 个')
    except Exception as e:
        print(f'❌ 因子计算失败: {e}')
        import traceback
        print(f'详细错误: {traceback.format_exc()}')
        return
    
    # 检查多因子评分
    multifactor_scores = [
        'overall_score', 'technical_score', 'momentum_score', 
        'volume_score', 'volatility_score', 'trend_score',
        'buy_signal_strength', 'risk_adjusted_score'
    ]
    
    print(f'\n📊 多因子评分检查:')
    found_scores = 0
    for score in multifactor_scores:
        if score in factors:
            value = factors[score]
            print(f'   ✅ {score}: {value}')
            found_scores += 1
        else:
            print(f'   ❌ {score}: 缺失')
    
    print(f'\n📊 找到的多因子评分: {found_scores}/{len(multifactor_scores)}')
    
    if found_scores == 0:
        print(f'\n🔧 尝试手动调用多因子评分计算...')
        try:
            engine._calculate_multifactor_scores(factors, test_data)
            print(f'✅ 手动调用成功')
            
            print(f'\n📊 手动调用后的评分:')
            for score in multifactor_scores:
                if score in factors:
                    value = factors[score]
                    print(f'   ✅ {score}: {value}')
                else:
                    print(f'   ❌ {score}: 仍然缺失')
        except Exception as e:
            print(f'❌ 手动调用失败: {e}')
            import traceback
            print(f'详细错误: {traceback.format_exc()}')
    
    # 检查一些关键的基础因子
    print(f'\n📋 关键基础因子检查:')
    key_factors = ['macd', 'macd_signal', 'macd_hist', 'rsi', 'bb_position', 'bb_width', 'atr_pct', 'trix_buy']
    for factor in key_factors:
        if factor in factors:
            value = factors[factor]
            print(f'   ✅ {factor}: {value}')
        else:
            print(f'   ❌ {factor}: 缺失')

if __name__ == '__main__':
    test_with_detailed_logging()
