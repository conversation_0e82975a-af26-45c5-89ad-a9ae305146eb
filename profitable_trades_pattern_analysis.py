# coding=utf-8
"""
盈利交易模式分析
基于数据库中的盈利交易，分析成功买入时的因子模式
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict

def get_profitable_trades():
    """获取盈利交易数据"""
    print('📈 获取盈利交易数据')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取盈利的买入-卖出配对数据
        query = """
        SELECT 
            b.timestamp as buy_time,
            b.symbol,
            b.price as buy_price,
            b.cci,
            b.rsi,
            b.adx,
            b.macd_hist,
            b.atr_pct,
            b.bb_width,
            b.overall_score,
            s.timestamp as sell_time,
            s.price as sell_price,
            s.net_profit_pct_sell as profit_pct,
            strftime('%H:%M', b.timestamp) as buy_hour,
            DATE(b.timestamp) as buy_date
        FROM trades b
        LEFT JOIN trades s ON b.symbol = s.symbol 
            AND s.action = 'SELL' 
            AND s.timestamp > b.timestamp
            AND s.net_profit_pct_sell IS NOT NULL
        WHERE b.action = 'BUY'
        AND s.net_profit_pct_sell > 0
        ORDER BY s.net_profit_pct_sell DESC
        LIMIT 500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print('❌ 没有找到盈利交易数据')
            return None
        
        print(f'📊 找到盈利交易: {len(df)}条')
        print(f'📈 平均盈利: {df["profit_pct"].mean():.2f}%')
        print(f'🔥 最高盈利: {df["profit_pct"].max():.2f}%')
        
        # 按盈利水平分类
        high_profit = df[df['profit_pct'] > 5.0]  # 高盈利 >5%
        medium_profit = df[(df['profit_pct'] > 2.0) & (df['profit_pct'] <= 5.0)]  # 中盈利 2-5%
        low_profit = df[(df['profit_pct'] > 0) & (df['profit_pct'] <= 2.0)]  # 低盈利 0-2%
        
        print(f'\n📊 盈利分布:')
        print(f'   高盈利 (>5%): {len(high_profit)}条, 平均{high_profit["profit_pct"].mean():.2f}%')
        print(f'   中盈利 (2-5%): {len(medium_profit)}条, 平均{medium_profit["profit_pct"].mean():.2f}%')
        print(f'   低盈利 (0-2%): {len(low_profit)}条, 平均{low_profit["profit_pct"].mean():.2f}%')
        
        return {
            'all': df,
            'high_profit': high_profit,
            'medium_profit': medium_profit,
            'low_profit': low_profit
        }
        
    except Exception as e:
        print(f'❌ 获取盈利交易数据失败: {e}')
        return None

def analyze_factor_patterns(trades_data):
    """分析因子模式"""
    print(f'\n🔍 分析因子模式')
    print('=' * 60)
    
    if not trades_data:
        return None
    
    patterns = {}
    
    # 分析不同盈利水平的因子特征
    for profit_level, data in trades_data.items():
        if len(data) == 0:
            continue
        
        print(f'\n📊 {profit_level} 因子分析 (样本数: {len(data)}):')
        
        # 分析数值型因子
        numeric_factors = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct', 'bb_width', 'overall_score']
        
        factor_stats = {}
        
        print(f'{"因子":<15} {"均值":<8} {"中位数":<8} {"标准差":<8} {"最小值":<8} {"最大值":<8}')
        print('-' * 70)
        
        for factor in numeric_factors:
            if factor in data.columns:
                valid_data = data[factor].dropna()
                if len(valid_data) > 0:
                    stats = {
                        'mean': valid_data.mean(),
                        'median': valid_data.median(),
                        'std': valid_data.std(),
                        'min': valid_data.min(),
                        'max': valid_data.max(),
                        'count': len(valid_data)
                    }
                    factor_stats[factor] = stats
                    
                    print(f'{factor:<15} {stats["mean"]:<8.2f} {stats["median"]:<8.2f} {stats["std"]:<8.2f} {stats["min"]:<8.2f} {stats["max"]:<8.2f}')
        
        patterns[profit_level] = factor_stats
    
    return patterns

def analyze_time_patterns(trades_data):
    """分析时间模式"""
    print(f'\n🕐 分析时间模式')
    print('=' * 60)
    
    if not trades_data:
        return None
    
    time_patterns = {}
    
    for profit_level, data in trades_data.items():
        if len(data) == 0:
            continue
        
        print(f'\n📊 {profit_level} 时间分析:')
        
        # 按小时分析
        hour_dist = data['buy_hour'].value_counts().sort_index()
        
        print(f'买入时间分布:')
        for hour, count in hour_dist.items():
            percentage = count / len(data) * 100
            print(f'   {hour}: {count}次 ({percentage:.1f}%)')
        
        # 按日期分析
        date_dist = data['buy_date'].value_counts().sort_index()
        
        print(f'\n最活跃的交易日期:')
        for date, count in date_dist.head(5).items():
            percentage = count / len(data) * 100
            print(f'   {date}: {count}次 ({percentage:.1f}%)')
        
        time_patterns[profit_level] = {
            'hour_distribution': hour_dist.to_dict(),
            'date_distribution': date_dist.to_dict()
        }
    
    return time_patterns

def identify_success_patterns(patterns):
    """识别成功模式"""
    print(f'\n🎯 识别成功模式')
    print('=' * 60)
    
    if not patterns:
        return None
    
    success_insights = {}
    
    # 对比不同盈利水平的因子差异
    if 'high_profit' in patterns and 'low_profit' in patterns:
        high_profit_factors = patterns['high_profit']
        low_profit_factors = patterns['low_profit']
        
        print(f'🔥 高盈利 vs 低盈利因子对比:')
        print(f'{"因子":<15} {"高盈利均值":<12} {"低盈利均值":<12} {"差异":<10} {"优势":<15}')
        print('-' * 75)
        
        for factor in high_profit_factors.keys():
            if factor in low_profit_factors:
                high_mean = high_profit_factors[factor]['mean']
                low_mean = low_profit_factors[factor]['mean']
                diff = high_mean - low_mean
                
                # 判断优势方向
                if abs(diff) > 0.1:  # 有意义的差异
                    if diff > 0:
                        advantage = "高盈利更高"
                    else:
                        advantage = "高盈利更低"
                else:
                    advantage = "差异不大"
                
                print(f'{factor:<15} {high_mean:<12.2f} {low_mean:<12.2f} {diff:<10.2f} {advantage:<15}')
                
                success_insights[factor] = {
                    'high_profit_mean': high_mean,
                    'low_profit_mean': low_mean,
                    'difference': diff,
                    'advantage': advantage
                }
    
    return success_insights

def generate_strategy_insights(patterns, success_insights, time_patterns):
    """生成策略洞察"""
    print(f'\n🚀 策略洞察和建议')
    print('=' * 60)
    
    insights = '''
📋 基于盈利交易分析的关键发现:

🔍 因子优化建议:
'''
    
    print(insights)
    
    if success_insights:
        print(f'📊 关键因子优化方向:')
        
        # CCI分析
        if 'cci' in success_insights:
            cci_insight = success_insights['cci']
            print(f'\n🎯 CCI优化建议:')
            print(f'   高盈利CCI均值: {cci_insight["high_profit_mean"]:.1f}')
            print(f'   低盈利CCI均值: {cci_insight["low_profit_mean"]:.1f}')
            print(f'   差异: {cci_insight["difference"]:.1f}')
            
            if cci_insight['difference'] > 10:
                print(f'   ✅ 建议: 提高CCI下限，关注更强势的股票')
            elif cci_insight['difference'] < -10:
                print(f'   🔥 建议: 降低CCI下限，关注超卖反弹机会')
            else:
                print(f'   📊 建议: 当前CCI配置基本合理')
        
        # RSI分析
        if 'rsi' in success_insights:
            rsi_insight = success_insights['rsi']
            print(f'\n📈 RSI优化建议:')
            print(f'   高盈利RSI均值: {rsi_insight["high_profit_mean"]:.1f}')
            print(f'   低盈利RSI均值: {rsi_insight["low_profit_mean"]:.1f}')
            
            high_rsi = rsi_insight["high_profit_mean"]
            if 30 <= high_rsi <= 50:
                print(f'   ✅ 建议: 关注RSI 30-50区间的反弹机会')
            elif 50 < high_rsi <= 70:
                print(f'   🚀 建议: 关注RSI 50-70区间的强势股票')
            else:
                print(f'   📊 建议: 根据RSI {high_rsi:.1f}调整筛选策略')
        
        # ATR分析
        if 'atr_pct' in success_insights:
            atr_insight = success_insights['atr_pct']
            print(f'\n📊 ATR优化建议:')
            print(f'   高盈利ATR均值: {atr_insight["high_profit_mean"]:.1f}%')
            print(f'   当前ATR阈值: >1.8%')
            
            high_atr = atr_insight["high_profit_mean"]
            if high_atr > 2.5:
                print(f'   🔥 建议: 提高ATR阈值到{high_atr:.1f}%')
            elif high_atr < 1.5:
                print(f'   📊 建议: 降低ATR阈值到{high_atr:.1f}%')
            else:
                print(f'   ✅ 建议: 当前ATR阈值基本合理')
        
        # ADX分析
        if 'adx' in success_insights:
            adx_insight = success_insights['adx']
            print(f'\n🎯 ADX优化建议:')
            print(f'   高盈利ADX均值: {adx_insight["high_profit_mean"]:.1f}')
            print(f'   当前ADX阈值: >25')
            
            high_adx = adx_insight["high_profit_mean"]
            if high_adx > 30:
                print(f'   🚀 建议: 提高ADX阈值到{high_adx:.0f}')
            elif high_adx < 20:
                print(f'   📊 建议: 降低ADX阈值到{high_adx:.0f}')
            else:
                print(f'   ✅ 建议: 当前ADX阈值基本合理')
    
    # 时间模式分析
    if time_patterns and 'high_profit' in time_patterns:
        high_profit_time = time_patterns['high_profit']['hour_distribution']
        
        print(f'\n🕐 最佳买入时间分析:')
        
        # 找出高盈利交易最集中的时间段
        sorted_hours = sorted(high_profit_time.items(), key=lambda x: x[1], reverse=True)
        
        print(f'   高盈利交易时间分布:')
        for hour, count in sorted_hours[:5]:
            total_high_profit = sum(high_profit_time.values())
            percentage = count / total_high_profit * 100
            print(f'   {hour}: {count}次 ({percentage:.1f}%)')
        
        # 给出时间建议
        top_hour = sorted_hours[0][0]
        if '10:' in top_hour or '11:' in top_hour:
            print(f'   ✅ 建议: 重点关注上午10-11点的交易机会')
        elif '13:' in top_hour or '14:' in top_hour:
            print(f'   🚀 建议: 重点关注下午13-14点的交易机会')
        else:
            print(f'   📊 建议: 重点关注{top_hour}时段的交易机会')

def create_optimized_strategy(patterns, success_insights):
    """创建优化策略建议"""
    print(f'\n🎯 优化策略建议')
    print('=' * 60)
    
    strategy_recommendations = '''
📋 基于盈利交易分析的策略优化:

🔧 立即可执行的优化:
'''
    
    print(strategy_recommendations)
    
    if success_insights:
        print(f'1. 📊 因子阈值精确调整:')
        
        # 基于高盈利交易的因子均值给出具体建议
        if 'cci' in success_insights:
            high_cci = success_insights['cci']['high_profit_mean']
            if high_cci > 0:
                print(f'   CCI下限调整: -50 → {high_cci-20:.0f}')
            else:
                print(f'   CCI区间调整: [-50,150] → [{high_cci-30:.0f},{high_cci+50:.0f}]')
        
        if 'rsi' in success_insights:
            high_rsi = success_insights['rsi']['high_profit_mean']
            print(f'   RSI最优区间: [{max(20, high_rsi-15):.0f}, {min(80, high_rsi+15):.0f}]')
        
        if 'atr_pct' in success_insights:
            high_atr = success_insights['atr_pct']['high_profit_mean']
            print(f'   ATR最优阈值: >{high_atr*0.8:.1f}%')
        
        if 'adx' in success_insights:
            high_adx = success_insights['adx']['high_profit_mean']
            print(f'   ADX最优阈值: >{max(20, high_adx*0.8):.0f}')
    
    print(f'\n2. 🚀 新增成功模式因子:')
    print(f'   - 基于高盈利交易的因子组合筛选')
    print(f'   - 增加盈利概率评分机制')
    print(f'   - 实施动态阈值调整')
    
    print(f'\n3. ⏰ 时间段优化:')
    print(f'   - 重点关注高盈利时间段')
    print(f'   - 降低低盈利时间段的权重')
    print(f'   - 实施时间段差异化策略')

def main():
    """主函数"""
    print('🔍 盈利交易模式分析')
    print('=' * 60)
    
    print('🎯 分析思路: 从历史盈利交易中提取成功模式')
    print('📊 分析内容: 盈利交易的因子特征和时间模式')
    print('🚀 目标: 优化现有策略配置')
    
    # 获取盈利交易数据
    trades_data = get_profitable_trades()
    
    if trades_data:
        # 分析因子模式
        patterns = analyze_factor_patterns(trades_data)
        
        # 分析时间模式
        time_patterns = analyze_time_patterns(trades_data)
        
        # 识别成功模式
        success_insights = identify_success_patterns(patterns)
        
        # 生成策略洞察
        generate_strategy_insights(patterns, success_insights, time_patterns)
        
        # 创建优化策略
        create_optimized_strategy(patterns, success_insights)
        
        print(f'\n🎯 分析完成')
        print('=' * 40)
        print(f'✅ 分析盈利交易: {len(trades_data["all"])} 条')
        print(f'📊 识别成功模式: {len(success_insights) if success_insights else 0} 个关键因子')
        print(f'🚀 策略建议: 已生成具体优化方向')
        
        return {
            'patterns': patterns,
            'success_insights': success_insights,
            'time_patterns': time_patterns
        }
    else:
        print('❌ 分析失败，请检查数据库')
        return None

if __name__ == '__main__':
    main()
