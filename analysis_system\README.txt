# 万和策略分析系统 - 问题解决指南

## 问题描述

启动脚本执行时出现中文字符显示错误，如下所示：
```
正在启动万和策略分析系统...

'��要的Python包...' is not recognized as an internal or external command,
operable program or batch file.
```

这是由于Windows命令提示符的编码问题导致的，无法正确解释中文字符。

## 解决方案

我们提供了几个不同的解决方案：

1. **修复版启动脚本**：
   - `start_analysis_fix.bat` - 修改了编码处理方式的中文界面版本

2. **英文界面版本**：
   - `start_analysis_alt.bat` - 使用英文界面，避免编码问题

3. **简化版启动脚本**：
   - `run.bat` - 直接运行分析系统，不显示菜单

4. **系统检查工具**：
   - `check_system.bat` - 检查系统是否安装了Python和必要组件

5. **启动器选择界面**：
   - `launcher.bat` - 可以选择使用哪个版本的启动脚本

## 使用方法

1. 首先运行 `check_system.bat` 检查您的系统是否满足运行条件
2. 如果系统检查通过，运行 `launcher.bat` 选择启动方式
3. 推荐选择 "修复版本"（选项3）

## 系统要求

- Windows 10 或更高版本
- Python 3.6 或更高版本
- 以下Python包：
  - pandas
  - numpy
  - matplotlib
  - seaborn
  - scikit-learn
  - jinja2

## 如果问题仍然存在

如果以上解决方案都不起作用，请尝试以下步骤：

1. 确保Python已正确安装并添加到系统PATH中
2. 尝试使用PowerShell而不是命令提示符运行脚本
3. 检查Windows系统的区域设置，确保支持中文字符
4. 尝试将脚本文件另存为UTF-8编码（无BOM）

## 联系支持

如有任何问题，请联系系统管理员。 