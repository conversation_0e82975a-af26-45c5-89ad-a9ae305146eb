# coding=utf-8
"""
数据库完整性检查工具
检查数据库中的数据是否完整
"""

import sqlite3
import pandas as pd
from datetime import datetime, timed<PERSON>ta

def check_database_integrity():
    """检查数据库完整性"""
    print('🔍 数据库完整性检查')
    print('=' * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print('📊 数据库表列表:')
        for table in tables:
            print(f'  • {table[0]}')
        
        if not tables:
            print('❌ 数据库中没有表')
            return
        
        # 检查trades表
        if ('trades',) in tables:
            print('\n📈 trades表数据统计:')
            
            # 总记录数
            cursor.execute("SELECT COUNT(*) FROM trades")
            total_count = cursor.fetchone()[0]
            print(f'  总记录数: {total_count}')
            
            # 按操作类型统计
            cursor.execute("SELECT operation, COUNT(*) FROM trades GROUP BY operation")
            operation_stats = cursor.fetchall()
            print(f'  按操作类型统计:')
            for op, count in operation_stats:
                print(f'    {op}: {count}条')
            
            # 按日期统计
            cursor.execute("SELECT DATE(timestamp) as date, COUNT(*) FROM trades GROUP BY DATE(timestamp) ORDER BY date DESC LIMIT 10")
            date_stats = cursor.fetchall()
            print(f'  最近10天记录数:')
            for date, count in date_stats:
                print(f'    {date}: {count}条')
            
            # 检查字段完整性
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            print(f'\n📋 trades表字段信息:')
            print(f'  总字段数: {len(columns)}')
            
            # 检查关键字段
            key_fields = ['symbol', 'operation', 'timestamp', 'price', 'volume']
            missing_fields = []
            existing_fields = [col[1] for col in columns]
            
            for field in key_fields:
                if field in existing_fields:
                    print(f'  ✅ {field}: 存在')
                else:
                    print(f'  ❌ {field}: 缺失')
                    missing_fields.append(field)
            
            # 检查数据质量
            print(f'\n🔍 数据质量检查:')
            
            # 检查空值
            for field in key_fields:
                if field in existing_fields:
                    cursor.execute(f"SELECT COUNT(*) FROM trades WHERE {field} IS NULL OR {field} = ''")
                    null_count = cursor.fetchone()[0]
                    if null_count > 0:
                        print(f'  ⚠️ {field}: {null_count}条空值记录')
                    else:
                        print(f'  ✅ {field}: 无空值')
            
            # 检查最新记录
            cursor.execute("SELECT * FROM trades ORDER BY timestamp DESC LIMIT 5")
            recent_records = cursor.fetchall()
            print(f'\n📅 最新5条记录:')
            for i, record in enumerate(recent_records, 1):
                # 只显示前几个关键字段
                symbol = record[1] if len(record) > 1 else 'N/A'
                operation = record[2] if len(record) > 2 else 'N/A'
                timestamp = record[3] if len(record) > 3 else 'N/A'
                price = record[4] if len(record) > 4 else 'N/A'
                print(f'  {i}. {symbol} | {operation} | {timestamp} | {price}')
        
        else:
            print('❌ trades表不存在')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 数据库检查失败: {e}')

def analyze_trading_data_completeness():
    """分析交易数据完整性"""
    print('\n💼 交易数据完整性分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 检查买入卖出配对情况
        print('📊 买入卖出配对分析:')
        
        # 统计买入记录
        buy_query = "SELECT COUNT(*) FROM trades WHERE operation = 'buy'"
        buy_count = pd.read_sql_query(buy_query, conn).iloc[0, 0]
        print(f'  买入记录: {buy_count}条')
        
        # 统计卖出记录
        sell_query = "SELECT COUNT(*) FROM trades WHERE operation = 'sell'"
        sell_count = pd.read_sql_query(sell_query, conn).iloc[0, 0]
        print(f'  卖出记录: {sell_count}条')
        
        # 分析配对情况
        if buy_count == 0 and sell_count > 0:
            print('  ❌ 问题: 只有卖出记录，没有买入记录')
            print('  💡 可能原因: 买入记录写入失败或数据库初始化问题')
        elif buy_count > 0 and sell_count == 0:
            print('  ⚠️ 问题: 只有买入记录，没有卖出记录')
            print('  💡 可能原因: 策略还在持仓中或卖出逻辑问题')
        elif buy_count == 0 and sell_count == 0:
            print('  ❌ 问题: 没有任何交易记录')
            print('  💡 可能原因: 策略没有执行交易或数据库写入失败')
        else:
            print(f'  ✅ 买入卖出记录都存在')
            ratio = sell_count / buy_count if buy_count > 0 else 0
            print(f'  📊 卖出/买入比例: {ratio:.2f}')
        
        # 检查时间范围
        time_query = "SELECT MIN(timestamp) as min_time, MAX(timestamp) as max_time FROM trades"
        time_result = pd.read_sql_query(time_query, conn)
        if not time_result.empty and time_result.iloc[0, 0] is not None:
            min_time = time_result.iloc[0, 0]
            max_time = time_result.iloc[0, 1]
            print(f'\n📅 数据时间范围:')
            print(f'  最早记录: {min_time}')
            print(f'  最新记录: {max_time}')
        else:
            print(f'\n📅 无法获取时间范围信息')
        
        # 检查股票覆盖情况
        symbol_query = "SELECT COUNT(DISTINCT symbol) FROM trades"
        symbol_count = pd.read_sql_query(symbol_query, conn).iloc[0, 0]
        print(f'\n📈 股票覆盖情况:')
        print(f'  涉及股票数量: {symbol_count}只')
        
        # 显示前10只股票的交易情况
        top_symbols_query = """
        SELECT symbol, COUNT(*) as trade_count, 
               SUM(CASE WHEN operation = 'buy' THEN 1 ELSE 0 END) as buy_count,
               SUM(CASE WHEN operation = 'sell' THEN 1 ELSE 0 END) as sell_count
        FROM trades 
        GROUP BY symbol 
        ORDER BY trade_count DESC 
        LIMIT 10
        """
        top_symbols = pd.read_sql_query(top_symbols_query, conn)
        if not top_symbols.empty:
            print(f'  前10只股票交易情况:')
            for _, row in top_symbols.iterrows():
                print(f'    {row["symbol"]}: 总计{row["trade_count"]}条 (买入{row["buy_count"]}, 卖出{row["sell_count"]})')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 交易数据分析失败: {e}')

def suggest_solutions():
    """建议解决方案"""
    print('\n💡 问题诊断和解决建议')
    print('=' * 50)
    
    solutions = [
        {
            'problem': '数据库中只有卖出记录，没有买入记录',
            'causes': [
                '买入记录写入失败',
                '数据库初始化时清空了买入记录',
                '买入逻辑没有正确执行',
                '数据库字段映射错误'
            ],
            'solutions': [
                '检查买入逻辑中的数据库写入代码',
                '验证数据库连接和写入权限',
                '检查买入记录的字段映射',
                '运行一次完整的回测来重新生成数据',
                '检查日志文件中的买入记录'
            ]
        },
        {
            'problem': '数据库表结构不完整',
            'causes': [
                '数据库初始化失败',
                '表结构更新不完整',
                '字段定义错误'
            ],
            'solutions': [
                '重新初始化数据库',
                '检查数据库初始化脚本',
                '验证表结构定义',
                '备份现有数据后重建表'
            ]
        },
        {
            'problem': '数据质量问题（空值、格式错误等）',
            'causes': [
                '数据验证不充分',
                '数据转换错误',
                '异常处理不当'
            ],
            'solutions': [
                '加强数据验证逻辑',
                '改进异常处理机制',
                '添加数据清洗步骤',
                '定期进行数据质量检查'
            ]
        }
    ]
    
    for solution in solutions:
        print(f'🔍 问题: {solution["problem"]}')
        print(f'  可能原因:')
        for cause in solution['causes']:
            print(f'    • {cause}')
        print(f'  解决方案:')
        for sol in solution['solutions']:
            print(f'    ✅ {sol}')
        print()

def main():
    """主函数"""
    print('🔍 数据库完整性检查报告')
    print('=' * 60)
    
    # 检查数据库完整性
    check_database_integrity()
    
    # 分析交易数据完整性
    analyze_trading_data_completeness()
    
    # 建议解决方案
    suggest_solutions()
    
    print(f'\n📋 检查总结:')
    print('=' * 40)
    print('✅ 数据库连接正常')
    print('⚠️ 发现数据完整性问题')
    print('💡 建议按照上述方案进行修复')
    print('🔄 修复后重新运行胜率分析器')

if __name__ == '__main__':
    main()
