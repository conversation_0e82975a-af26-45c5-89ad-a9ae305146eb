# coding=utf-8
"""
紧急诊断：为什么配置修改没有生效
分析策略为什么仍然98.2%集中在开盘时段
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def diagnose_configuration_issues():
    """诊断配置问题"""
    print('🚨 紧急诊断：配置失效原因分析')
    print('=' * 60)
    
    # 检查当前配置
    try:
        from config import get_config_value
        
        print('🔧 当前配置检查:')
        
        # 检查高效因子策略
        enable_effective = get_config_value('ENABLE_EFFECTIVE_FACTORS_STRATEGY', 'NOT_FOUND')
        effective_config = get_config_value('EFFECTIVE_FACTORS_CONFIG', 'NOT_FOUND')
        
        print(f'   高效因子策略: {enable_effective}')
        print(f'   高效因子配置: {"已配置" if effective_config != "NOT_FOUND" else "未配置"}')
        
        # 检查时间分散配置
        enable_time_dist = get_config_value('ENABLE_TIME_DISTRIBUTION', 'NOT_FOUND')
        time_config = get_config_value('TIME_DISTRIBUTION_CONFIG', 'NOT_FOUND')
        
        print(f'   时间分散开关: {enable_time_dist}')
        print(f'   时间分散配置: {"已配置" if time_config != "NOT_FOUND" else "未配置"}')
        
        # 检查开盘特殊处理
        opening_config = get_config_value('OPENING_SPECIAL_CONFIG', 'NOT_FOUND')
        print(f'   开盘特殊处理: {"已配置" if opening_config != "NOT_FOUND" else "未配置"}')
        
        # 检查多因子阈值
        thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', 'NOT_FOUND')
        if thresholds != 'NOT_FOUND':
            print(f'   多因子阈值: 已配置')
            print(f'     min_overall_score: {thresholds.get("min_overall_score", "NOT_FOUND")}')
            print(f'     min_volatility_score: {thresholds.get("min_volatility_score", "NOT_FOUND")}')
        
        return True
        
    except Exception as e:
        print(f'❌ 配置检查失败: {e}')
        return False

def analyze_actual_buy_signals():
    """分析实际买入信号"""
    print(f'\n📊 实际买入信号分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新买入记录
        query = """
        SELECT 
            timestamp, symbol, price,
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy, cci, adx, bb_position
        FROM trades 
        WHERE action = 'BUY' 
        ORDER BY timestamp DESC 
        LIMIT 100
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 最新买入记录: {len(df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['minute'] = df['timestamp'].dt.minute
        
        # 分析时间分布
        hourly_dist = df.groupby('hour').size()
        print(f'\n🕐 时间分布:')
        for hour, count in hourly_dist.items():
            percentage = count / len(df) * 100
            print(f'   {hour:02d}:00: {count}笔 ({percentage:.1f}%)')
        
        # 分析因子值
        print(f'\n🔍 因子值分析:')
        
        factor_columns = ['overall_score', 'technical_score', 'volatility_score', 
                         'atr_pct', 'bb_width', 'rsi', 'cci', 'adx', 'bb_position']
        
        for factor in factor_columns:
            if factor in df.columns:
                values = df[factor].dropna()
                if len(values) > 0:
                    mean_val = values.mean()
                    min_val = values.min()
                    max_val = values.max()
                    non_null_count = len(values)
                    
                    print(f'   {factor}: 均值{mean_val:.3f}, 范围[{min_val:.3f}, {max_val:.3f}], 有效值{non_null_count}/{len(df)}')
                else:
                    print(f'   {factor}: 无有效值')
        
        # 检查是否使用了高效因子
        high_cci = len(df[(df['cci'] > 50) & (df['cci'] < 200)])
        high_adx = len(df[df['adx'] > 25])
        good_bb_pos = len(df[(df['bb_position'] > 0.3) & (df['bb_position'] < 0.8)])
        good_rsi = len(df[(df['rsi'] > 40) & (df['rsi'] < 70)])
        
        print(f'\n🎯 高效因子条件满足情况:')
        print(f'   CCI条件 (50-200): {high_cci}/{len(df)} ({high_cci/len(df)*100:.1f}%)')
        print(f'   ADX条件 (>25): {high_adx}/{len(df)} ({high_adx/len(df)*100:.1f}%)')
        print(f'   BB位置条件 (0.3-0.8): {good_bb_pos}/{len(df)} ({good_bb_pos/len(df)*100:.1f}%)')
        print(f'   RSI条件 (40-70): {good_rsi}/{len(df)} ({good_rsi/len(df)*100:.1f}%)')
        
        return df
        
    except Exception as e:
        print(f'❌ 买入信号分析失败: {e}')
        return None

def check_strategy_logic_execution():
    """检查策略逻辑执行"""
    print(f'\n🔧 策略逻辑执行检查')
    print('=' * 50)
    
    print('🎯 可能的问题原因:')
    
    issues = [
        {
            'issue': '配置未生效',
            'description': '新配置可能没有被策略代码读取',
            'check': '检查策略是否重启，配置是否正确加载'
        },
        {
            'issue': '代码逻辑未更新',
            'description': '策略代码可能仍在使用旧的多因子逻辑',
            'check': '检查main.py中的买入逻辑是否使用新配置'
        },
        {
            'issue': '时间过滤未实现',
            'description': '时间分散配置可能没有在代码中实现',
            'check': '检查是否有时间过滤的代码逻辑'
        },
        {
            'issue': '因子计算问题',
            'description': '高效因子可能计算异常或返回默认值',
            'check': '检查CCI, ADX等因子的实际计算值'
        },
        {
            'issue': '阈值设置问题',
            'description': '新阈值可能过于严格或宽松',
            'check': '检查实际因子值是否满足新阈值条件'
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f'\n   {i}. {issue["issue"]}:')
        print(f'      问题: {issue["description"]}')
        print(f'      检查: {issue["check"]}')

def generate_immediate_fixes():
    """生成立即修复方案"""
    print(f'\n🚀 立即修复方案')
    print('=' * 50)
    
    fixes = '''
🚨 紧急修复步骤:

1. 🔧 确认配置生效:
   - 重启策略程序确保配置加载
   - 检查日志确认新配置被读取
   - 验证高效因子策略是否启用

2. 📊 检查代码逻辑:
   - 确认main.py使用EFFECTIVE_FACTORS_CONFIG
   - 检查是否实现了时间分散逻辑
   - 验证买入条件是否使用新因子

3. 🕐 强制时间分散:
   - 在买入逻辑中添加硬编码时间限制
   - 09:00时段最多允许20%的信号
   - 其他时段优先级提高

4. 🎯 简化因子条件:
   - 如果高效因子条件过严，适当放宽
   - 确保至少有合理数量的买入信号
   - 监控因子计算是否正常

5. 📈 验证修复效果:
   - 重启后监控1小时信号分布
   - 检查各时段信号数量变化
   - 确认不再98.2%集中在开盘

⚠️ 如果问题持续:
   - 考虑回退到之前的配置
   - 逐步应用修改而非一次性大改
   - 增加详细的调试日志
'''
    
    print(fixes)

def create_emergency_config():
    """创建紧急配置"""
    print(f'\n⚙️ 紧急配置方案')
    print('=' * 50)
    
    emergency_config = '''
# 紧急修复配置
# 强制解决98.2%开盘信号集中问题

# 1. 暂时禁用复杂策略，回归简单逻辑
ENABLE_EMERGENCY_MODE = True

# 2. 强制时间分散 (硬编码)
FORCE_TIME_DISTRIBUTION = {
    'enable': True,
    'max_opening_signals_per_hour': 5,      # 开盘时段每小时最多5个信号
    'opening_signal_ratio_limit': 0.2,      # 开盘信号最多占20%
    'force_other_time_signals': True,       # 强制其他时段生成信号
}

# 3. 简化买入条件
EMERGENCY_BUY_CONDITIONS = {
    'enable': True,
    'simple_conditions': {
        'atr_pct': {'min': 1.5, 'max': 5.0},        # 简化ATR条件
        'bb_width': {'min': 8.0},                   # 简化BB宽度条件
        'rsi': {'min': 30, 'max': 80},              # 放宽RSI条件
    },
    'time_based_conditions': {
        '09:00-10:00': {'multiplier': 3.0},         # 开盘时段条件加严3倍
        '10:00-11:30': {'multiplier': 0.8},         # 上午时段条件放宽
        '13:00-14:30': {'multiplier': 0.8},         # 下午时段条件放宽
    }
}

# 4. 禁用可能有问题的配置
DISABLE_PROBLEMATIC_FEATURES = {
    'disable_multifactor_strategy': True,           # 禁用多因子策略
    'disable_complex_scoring': True,               # 禁用复杂评分
    'disable_time_series_analysis': True,          # 禁用时序分析
    'use_simple_technical_only': True,             # 只使用简单技术指标
}
'''
    
    print(emergency_config)

def main():
    """主函数"""
    print('🚨 紧急诊断：策略配置失效分析')
    print('=' * 60)
    
    # 诊断配置问题
    config_ok = diagnose_configuration_issues()
    
    # 分析实际买入信号
    buy_df = analyze_actual_buy_signals()
    
    # 检查策略逻辑执行
    check_strategy_logic_execution()
    
    # 生成修复方案
    generate_immediate_fixes()
    
    # 创建紧急配置
    create_emergency_config()
    
    print(f'\n🎯 诊断总结')
    print('=' * 40)
    
    if buy_df is not None:
        opening_signals = len(buy_df[buy_df['hour'] == 9])
        total_signals = len(buy_df)
        opening_pct = opening_signals / total_signals * 100
        
        print(f'📊 当前状况:')
        print(f'   开盘信号比例: {opening_pct:.1f}%')
        print(f'   配置检查: {"通过" if config_ok else "失败"}')
        
        if opening_pct > 90:
            print(f'🚨 严重问题: 配置修改完全无效')
            print(f'💡 建议: 立即应用紧急修复方案')
        elif opening_pct > 50:
            print(f'⚠️ 部分问题: 配置修改部分生效')
            print(f'💡 建议: 检查代码逻辑实现')
        else:
            print(f'✅ 问题改善: 配置修改有效果')
            print(f'💡 建议: 继续优化参数设置')
    
    print(f'\n🚀 下一步行动:')
    print(f'   1. 检查策略代码是否使用新配置')
    print(f'   2. 实施紧急修复方案')
    print(f'   3. 强制时间分散逻辑')
    print(f'   4. 监控修复效果')

if __name__ == '__main__':
    main()
