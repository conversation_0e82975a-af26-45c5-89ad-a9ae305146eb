import streamlit as st
import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入工具函数
from utils.data_loader import load_analysis_results

st.set_page_config(page_title="指标优化", page_icon="🔍", layout="wide")

st.title("技术指标优化分析")
st.markdown("分析技术指标在不同范围内的表现，找出最优和最差的指标组合")

# 检查分析结果是否存在
analysis_file = "reports/trade_analysis_results.csv"
if not os.path.exists(analysis_file):
    st.warning("未找到分析结果文件。请先在主页运行交易分析。")
    st.stop()

# 加载分析结果
try:
    df = load_analysis_results()
    if df is None:
        st.error("加载分析结果失败")
        st.stop()
    st.success(f"成功加载分析数据: {len(df)}条交易记录")
except Exception as e:
    st.error(f"加载分析数据失败: {str(e)}")
    st.stop()

# 确保有Actual_Profit_Pct列
if 'Actual_Profit_Pct' not in df.columns:
    # 计算买入金额和卖出金额以及实际收益率
    df['Buy_Amount'] = df['Buy_Price'] * df['Volume']
    df['Sell_Amount'] = df['Sell_Price'] * df['Volume']
    df['Profit_Amount'] = df['Sell_Amount'] - df['Buy_Amount']
    df['Actual_Profit_Pct'] = (df['Profit_Amount'] / df['Buy_Amount'] * 100)

# 为特征名称创建中文映射字典
feature_names_cn = {
    'Actual_Profit_Pct': '收益率',
    'Holding_Hours': '持仓时间',
    'ATR_Pct': 'ATR百分比',
    'TRIX_Buy': 'TRIX指标',
    'Volatility_Buy': '波动率',
    'ATR_Pct_Buy': '买入时ATR',
    'Volatility_Score_Buy': '波动分数',
    'Allocation_Factor_Buy': '资金分配系数'
}

# 查找可用的技术指标
available_indicators = [col for col in df.columns if col.endswith('_Buy') or col in ['Volatility', 'ATR_Pct', 'TRIX']]

if not available_indicators:
    st.warning("未找到可用的技术指标。请确保数据中包含技术指标信息。")
    st.stop()

# 为其他可能的特征添加默认中文名称
for col in available_indicators:
    if col not in feature_names_cn:
        if col.endswith('_Buy'):
            base_name = col[:-4]  # 移除 _Buy 后缀
            feature_names_cn[col] = f'买入时{base_name}'
        else:
            feature_names_cn[col] = col

# 用户选择要分析的指标
st.header("指标选择")
selected_indicator = st.selectbox(
    "选择要分析的技术指标",
    options=available_indicators,
    format_func=lambda x: f"{feature_names_cn.get(x, x)} ({x})"
)

# 指标范围划分
st.header("指标范围设置")
col1, col2 = st.columns(2)

with col1:
    min_val = df[selected_indicator].min()
    max_val = df[selected_indicator].max()
    st.write(f"指标范围: {min_val:.4f} 到 {max_val:.4f}")
    
    num_bins = st.slider("范围划分数量", min_value=3, max_value=20, value=10)

with col2:
    sort_by = st.radio(
        "排序依据",
        ["综合评分(胜率*收益率)", "胜率", "平均收益率", "交易次数"]
    )

# 创建指标范围划分
try:
    # 使用分位数进行划分，确保每个区间有相近数量的样本
    bins = pd.qcut(df[selected_indicator], q=num_bins, duplicates='drop')
    df['Indicator_Range'] = bins
    
    # 对每个范围进行分析
    range_stats = []
    
    for indicator_range in df['Indicator_Range'].unique():
        range_df = df[df['Indicator_Range'] == indicator_range]
        
        # 计算统计数据
        total_trades = len(range_df)
        winning_trades = len(range_df[range_df['Actual_Profit_Pct'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        avg_profit = range_df['Actual_Profit_Pct'].mean()
        avg_win = range_df[range_df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if winning_trades > 0 else 0
        avg_loss = range_df[range_df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if losing_trades > 0 else 0
        max_profit = range_df['Actual_Profit_Pct'].max()
        min_profit = range_df['Actual_Profit_Pct'].min()
        profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 and avg_loss < 0 else float('inf')
        
        # 计算综合评分
        # 胜率和平均收益率的乘积，可以反映整体表现
        composite_score = win_rate * avg_profit / 100  # 除以100使评分更容易理解
        
        # 添加到统计列表
        range_stats.append({
            'Indicator_Range': indicator_range,
            'Range_Min': indicator_range.left,
            'Range_Max': indicator_range.right,
            'Total_Trades': total_trades,
            'Winning_Trades': winning_trades,
            'Losing_Trades': losing_trades,
            'Win_Rate': win_rate,
            'Avg_Profit': avg_profit,
            'Avg_Win': avg_win,
            'Avg_Loss': avg_loss,
            'Max_Profit': max_profit,
            'Min_Profit': min_profit,
            'Profit_Loss_Ratio': profit_loss_ratio,
            'Composite_Score': composite_score
        })
    
    # 创建统计数据框
    stats_df = pd.DataFrame(range_stats)
    
    # 根据选择的指标排序
    if sort_by == "综合评分(胜率*收益率)":
        stats_df = stats_df.sort_values('Composite_Score', ascending=False)
    elif sort_by == "胜率":
        stats_df = stats_df.sort_values('Win_Rate', ascending=False)
    elif sort_by == "平均收益率":
        stats_df = stats_df.sort_values('Avg_Profit', ascending=False)
    else:  # 交易次数
        stats_df = stats_df.sort_values('Total_Trades', ascending=False)
    
    # 显示分析结果
    st.header("指标范围分析结果")
    
    # 显示最优组合
    st.subheader("🏆 最优指标范围组合 (Top 10)")
    
    # 创建用于显示的数据框
    display_df = stats_df.head(10).copy()
    
    # 格式化显示列
    display_df['指标范围'] = display_df['Indicator_Range'].astype(str)
    display_df['交易次数'] = display_df['Total_Trades']
    display_df['盈利交易'] = display_df['Winning_Trades']
    display_df['亏损交易'] = display_df['Losing_Trades']
    display_df['胜率'] = display_df['Win_Rate'].map('{:.2f}%'.format)
    display_df['平均收益率'] = display_df['Avg_Profit'].map('{:.4f}%'.format)
    display_df['平均盈利'] = display_df['Avg_Win'].map('{:.4f}%'.format)
    display_df['平均亏损'] = display_df['Avg_Loss'].map('{:.4f}%'.format)
    display_df['最大盈利'] = display_df['Max_Profit'].map('{:.4f}%'.format)
    display_df['最大亏损'] = display_df['Min_Profit'].map('{:.4f}%'.format)
    display_df['盈亏比'] = display_df['Profit_Loss_Ratio'].map(lambda x: f"{x:.2f}" if x != float('inf') else "∞")
    display_df['综合评分'] = display_df['Composite_Score'].map('{:.4f}'.format)
    
    # 选择要显示的列
    columns_to_display = ['指标范围', '交易次数', '盈利交易', '亏损交易', '胜率', 
                          '平均收益率', '平均盈利', '平均亏损', '最大盈利', '最大亏损', 
                          '盈亏比', '综合评分']
    
    st.dataframe(display_df[columns_to_display], use_container_width=True)
    
    # 显示最差组合
    st.subheader("⚠️ 最差指标范围组合 (Bottom 10)")
    
    # 创建用于显示的数据框
    worst_display_df = stats_df.tail(10).iloc[::-1].copy()  # 反转顺序，使最差的显示在最上面
    
    # 格式化显示列
    worst_display_df['指标范围'] = worst_display_df['Indicator_Range'].astype(str)
    worst_display_df['交易次数'] = worst_display_df['Total_Trades']
    worst_display_df['盈利交易'] = worst_display_df['Winning_Trades']
    worst_display_df['亏损交易'] = worst_display_df['Losing_Trades']
    worst_display_df['胜率'] = worst_display_df['Win_Rate'].map('{:.2f}%'.format)
    worst_display_df['平均收益率'] = worst_display_df['Avg_Profit'].map('{:.4f}%'.format)
    worst_display_df['平均盈利'] = worst_display_df['Avg_Win'].map('{:.4f}%'.format)
    worst_display_df['平均亏损'] = worst_display_df['Avg_Loss'].map('{:.4f}%'.format)
    worst_display_df['最大盈利'] = worst_display_df['Max_Profit'].map('{:.4f}%'.format)
    worst_display_df['最大亏损'] = worst_display_df['Min_Profit'].map('{:.4f}%'.format)
    worst_display_df['盈亏比'] = worst_display_df['Profit_Loss_Ratio'].map(lambda x: f"{x:.2f}" if x != float('inf') else "∞")
    worst_display_df['综合评分'] = worst_display_df['Composite_Score'].map('{:.4f}'.format)
    
    st.dataframe(worst_display_df[columns_to_display], use_container_width=True)
    
    # 可视化分析
    st.header("可视化分析")
    
    # 胜率和平均收益率的散点图
    fig, ax = plt.subplots(figsize=(10, 6))
    scatter = ax.scatter(
        stats_df['Win_Rate'], 
        stats_df['Avg_Profit'], 
        s=stats_df['Total_Trades'] * 2,  # 气泡大小表示交易次数
        alpha=0.7,
        c=stats_df['Composite_Score'],  # 颜色表示综合评分
        cmap='RdYlGn'
    )
    
    # 添加文本标签
    for i, row in stats_df.iterrows():
        ax.annotate(
            f"{row['Range_Min']:.2f}-{row['Range_Max']:.2f}", 
            (row['Win_Rate'], row['Avg_Profit']),
            fontsize=9
        )
    
    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('综合评分')
    
    # 设置图表标题和标签
    ax.set_title(f'{feature_names_cn.get(selected_indicator, selected_indicator)}范围分析')
    ax.set_xlabel('胜率 (%)')
    ax.set_ylabel('平均收益率 (%)')
    ax.axhline(y=0, color='r', linestyle='--')  # 添加收益率为0的水平线
    ax.grid(True, linestyle='--', alpha=0.7)
    
    st.pyplot(fig)
    
    # 显示每个范围内的交易分布
    st.subheader("各指标范围内的交易分布")
    
    # 创建一个条形图，显示每个范围内的盈利和亏损交易数量
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 准备数据
    ranges = [str(r) for r in stats_df['Indicator_Range']]
    winning = stats_df['Winning_Trades'].values
    losing = stats_df['Losing_Trades'].values
    
    # 设置x位置
    x = np.arange(len(ranges))
    width = 0.35
    
    # 绘制条形图
    ax.bar(x - width/2, winning, width, label='盈利交易', color='green', alpha=0.7)
    ax.bar(x + width/2, losing, width, label='亏损交易', color='red', alpha=0.7)
    
    # 添加标签和图例
    ax.set_xlabel('指标范围')
    ax.set_ylabel('交易次数')
    ax.set_title(f'{feature_names_cn.get(selected_indicator, selected_indicator)}范围内的交易分布')
    ax.set_xticks(x)
    ax.set_xticklabels(ranges, rotation=45, ha='right')
    ax.legend()
    
    # 调整布局
    plt.tight_layout()
    
    st.pyplot(fig)
    
    # 显示每个范围内的收益率分布
    st.subheader("各指标范围内的收益率分布")
    
    # 创建箱线图
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 准备数据
    boxplot_data = []
    boxplot_labels = []
    
    for indicator_range in stats_df['Indicator_Range']:
        range_df = df[df['Indicator_Range'] == indicator_range]
        boxplot_data.append(range_df['Actual_Profit_Pct'].values)
        boxplot_labels.append(str(indicator_range))
    
    # 绘制箱线图
    ax.boxplot(boxplot_data, labels=boxplot_labels, showfliers=True)
    
    # 添加水平线表示盈亏平衡点
    ax.axhline(y=0, color='r', linestyle='--')
    
    # 添加标签
    ax.set_xlabel('指标范围')
    ax.set_ylabel('收益率 (%)')
    ax.set_title(f'{feature_names_cn.get(selected_indicator, selected_indicator)}范围内的收益率分布')
    plt.xticks(rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    st.pyplot(fig)
    
    # 添加策略优化建议
    st.header("策略优化建议")
    
    # 获取最佳范围
    best_range = stats_df.iloc[0]
    
    st.markdown(f"""
    ### 最佳指标范围
    
    基于当前分析，对于指标 **{feature_names_cn.get(selected_indicator, selected_indicator)}**，建议的最佳范围是:
    
    **{best_range['Range_Min']:.4f} 到 {best_range['Range_Max']:.4f}**
    
    在此范围内:
    - 胜率: **{best_range['Win_Rate']:.2f}%**
    - 平均收益率: **{best_range['Avg_Profit']:.4f}%**
    - 交易次数: **{best_range['Total_Trades']}**
    - 盈亏比: **{best_range['Profit_Loss_Ratio']:.2f}** (平均盈利/平均亏损)
    
    ### 应避免的范围
    
    分析表明，当 {feature_names_cn.get(selected_indicator, selected_indicator)} 在以下范围时，交易表现较差:
    
    **{stats_df.iloc[-1]['Range_Min']:.4f} 到 {stats_df.iloc[-1]['Range_Max']:.4f}**
    
    在此范围内:
    - 胜率: **{stats_df.iloc[-1]['Win_Rate']:.2f}%**
    - 平均收益率: **{stats_df.iloc[-1]['Avg_Profit']:.4f}%**
    
    ### 优化建议
    
    1. 考虑将 {feature_names_cn.get(selected_indicator, selected_indicator)} 的筛选条件调整为 **{best_range['Range_Min']:.4f} 到 {best_range['Range_Max']:.4f}** 范围内
    2. 避免在 {feature_names_cn.get(selected_indicator, selected_indicator)} 值为 **{stats_df.iloc[-1]['Range_Min']:.4f} 到 {stats_df.iloc[-1]['Range_Max']:.4f}** 时进行交易
    3. 结合其他指标进行交叉验证，进一步提高策略可靠性
    """)
    
    # 显示完整的统计数据表格
    with st.expander("查看完整统计数据"):
        st.dataframe(display_df, use_container_width=True)
        
        # 导出功能
        csv = stats_df.to_csv(index=False).encode('utf-8')
        st.download_button(
            label="下载统计数据 (CSV)",
            data=csv,
            file_name=f"{selected_indicator}_range_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime='text/csv',
        )

except Exception as e:
    st.error(f"分析过程中出错: {str(e)}")
    st.exception(e)

# 多指标组合分析
st.header("多指标组合分析")
st.info("此功能将在后续版本中提供，敬请期待！")

# 添加说明
st.markdown("""
### 使用说明

本页面用于分析技术指标在不同范围内的交易表现，帮助您找出最优和最差的指标组合。

**功能说明:**
1. 选择要分析的技术指标
2. 设置指标范围划分数量
3. 选择排序依据（综合评分、胜率、平均收益率或交易次数）
4. 查看最优和最差的指标范围组合
5. 通过可视化图表直观了解各范围的表现
6. 获取策略优化建议

**综合评分计算方式:**
- 综合评分 = 胜率(%) × 平均收益率(%) ÷ 100
- 该评分同时考虑了胜率和收益率，能更全面反映交易表现

**注意事项:**
- 范围划分采用分位数方法，确保每个区间有相近数量的样本
- 分析结果仅供参考，实际交易中应结合多种因素综合考虑
- 样本数量过少的范围可能存在统计偏差
""") 