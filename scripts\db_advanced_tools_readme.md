# 高级数据库管理工具使用说明

## 概述

高级数据库管理工具（`db_advanced_tools.py`）是一个功能强大的数据库分析和管理工具，专为万和策略分析系统设计。该工具提供了一系列高级功能，包括数据分析、数据可视化、缺失数据修复、批量数据处理、数据库合并以及生成全面的数据库报告等。

## 主要功能

1. **数据分析** - 分析表中数据的分布情况，生成统计报告和可视化图表
2. **数据修复** - 自动检测和修复表中的缺失数据
3. **批量数据处理** - 批量更新或删除符合条件的数据记录
4. **数据库合并** - 将两个不同的数据库合并为一个，支持多种合并策略
5. **数据库报告** - 生成全面的数据库状态报告，包括表结构、数据质量和统计信息

## 使用方法

### 基本语法

```bash
python scripts/db_advanced_tools.py [选项]
```

### 数据分析功能

分析表中数据的分布情况，并生成可视化图表。

```bash
python scripts/db_advanced_tools.py --analyze <表名> [--columns <列名1,列名2,...>] [--output-dir <输出目录>]
```

参数说明：
- `--analyze`: 指定要分析的表名
- `--columns`: 指定要分析的列名（可选，多个列名用逗号分隔）
- `--output-dir`: 指定分析结果的输出目录（可选）

示例：
```bash
# 分析trades表中的所有数值列
python scripts/db_advanced_tools.py --analyze trades

# 仅分析指定列
python scripts/db_advanced_tools.py --analyze trades --columns price,volume,profit_pct
```

### 数据修复功能

修复表中的缺失数据，支持多种填充策略。

```bash
python scripts/db_advanced_tools.py --fix-missing <表名> --column <列名> [--strategy <策略>]
```

参数说明：
- `--fix-missing`: 指定要修复的表名
- `--column`: 指定要修复的列名
- `--strategy`: 指定修复策略（可选，默认为mean）
  - `mean`: 使用平均值填充
  - `median`: 使用中位数填充
  - `mode`: 使用众数填充
  - `zero`: 使用零填充
  - `previous`: 使用前一个有效值填充

示例：
```bash
# 使用平均值填充trades表中profit列的缺失值
python scripts/db_advanced_tools.py --fix-missing trades --column profit --strategy mean

# 使用前一个有效值填充volume列的缺失值
python scripts/db_advanced_tools.py --fix-missing trades --column volume --strategy previous
```

### 批量数据处理功能

批量更新或删除符合条件的数据记录。

```bash
python scripts/db_advanced_tools.py --batch <表名> --operation <操作> [--condition <条件>] [--params <参数>]
```

参数说明：
- `--batch`: 指定要操作的表名
- `--operation`: 指定操作类型（update或delete）
- `--condition`: SQL WHERE条件（可选）
- `--params`: 操作参数，JSON格式（update操作需要）

示例：
```bash
# 删除30天前的数据
python scripts/db_advanced_tools.py --batch trades --operation delete --condition "timestamp < date('now', '-30 days')"

# 更新指定股票的交易类型
python scripts/db_advanced_tools.py --batch trades --operation update --condition "symbol = 'SHSE.600000'" --params '{"action":"BUY"}'
```

### 数据库合并功能

将两个不同的数据库合并为一个，支持多种合并策略。

```bash
python scripts/db_advanced_tools.py --merge --target-db <目标数据库> --source-db <源数据库> [--merge-strategy <策略>]
```

参数说明：
- `--merge`: 启用合并功能
- `--target-db`: 目标数据库路径
- `--source-db`: 源数据库路径
- `--merge-strategy`: 合并策略（可选，默认为replace）
  - `replace`: 覆盖重复记录
  - `append`: 添加所有记录（可能导致重复）

示例：
```bash
# 将备份数据库合并到主数据库，覆盖重复记录
python scripts/db_advanced_tools.py --merge --target-db data/trades.db --source-db backups/db_backup_20250530_120000/trades.db --merge-strategy replace
```

### 数据库报告功能

生成全面的数据库状态报告，包括表结构、数据质量和统计信息。

```bash
python scripts/db_advanced_tools.py --report [--report-file <报告文件路径>]
```

参数说明：
- `--report`: 启用报告生成功能
- `--report-file`: 指定报告文件的输出路径（可选）

示例：
```bash
# 生成数据库报告，使用默认输出路径
python scripts/db_advanced_tools.py --report

# 指定报告输出路径
python scripts/db_advanced_tools.py --report --report-file reports/my_db_report.json
```

## 输出内容

### 数据分析输出

数据分析功能会生成以下内容：

1. **统计信息** (`statistics.csv`) - 包含每列的统计摘要（最小值、最大值、平均值、标准差等）
2. **分布图** (`distributions.png`) - 显示每列数据的分布直方图
3. **时间趋势图** (`*_trend.png`) - 如果数据包含时间戳，显示各指标随时间的变化趋势
4. **相关性矩阵** (`correlation.png`) - 显示各列数据之间的相关性热图

### 数据库报告输出

数据库报告以JSON格式生成，包含以下信息：

1. **数据库基本信息** - 文件路径、大小、创建和修改时间
2. **表信息** - 每个表的结构、记录数量和大小估计
3. **列信息** - 每列的数据类型、约束条件和主键信息
4. **时间范围** - 数据的时间跨度（如果表包含timestamp列）
5. **数据质量信息** - 缺失值和重复记录的详细统计

## 使用建议

1. **在执行数据修改操作前备份数据库**
   ```bash
   python scripts/db_manager.py --backup
   ```

2. **定期分析数据库状态**
   ```bash
   python scripts/db_advanced_tools.py --report
   ```

3. **定期修复缺失数据**
   ```bash
   python scripts/db_advanced_tools.py --fix-missing trades --column volume --strategy median
   ```

4. **定期清理旧数据后优化数据库**
   ```bash
   python scripts/db_advanced_tools.py --batch trades --operation delete --condition "timestamp < date('now', '-90 days')"
   python scripts/db_manager.py --optimize
   ```

## 高级用例

### 数据迁移和合并

1. **从多个备份合并数据**
   ```bash
   # 先将第一个备份合并到主数据库
   python scripts/db_advanced_tools.py --merge --target-db data/trades.db --source-db backups/backup1/trades.db
   # 再合并第二个备份
   python scripts/db_advanced_tools.py --merge --target-db data/trades.db --source-db backups/backup2/trades.db
   ```

2. **创建数据子集用于分析**
   ```bash
   # 先将所有数据导出到临时数据库
   sqlite3 data/trades.db ".backup data/analysis_subset.db"
   # 删除不需要的数据
   python scripts/db_advanced_tools.py --batch trades --operation delete --condition "timestamp < date('now', '-30 days')" --target-db data/analysis_subset.db
   ```

### 数据质量改进流程

1. **检查数据质量**
   ```bash
   python scripts/db_advanced_tools.py --report
   ```

2. **修复缺失数据**
   ```bash
   python scripts/db_advanced_tools.py --fix-missing trades --column price --strategy median
   ```

3. **删除异常值**
   ```bash
   python scripts/db_advanced_tools.py --batch trades --operation delete --condition "price > 1000 OR price < 0"
   ```

4. **验证改进效果**
   ```bash
   python scripts/db_advanced_tools.py --analyze trades --columns price,volume,profit_pct
   ```

## 故障排除

1. **如果工具报错无法找到数据库文件**
   - 确保当前目录是项目根目录
   - 确认数据库文件存在于`data/trades.db`

2. **如果数据分析图表无法生成**
   - 确保已安装matplotlib和seaborn库
   - 检查表中是否有足够的数据进行分析

3. **如果合并数据库出错**
   - 确保两个数据库结构兼容
   - 尝试先导出数据为CSV，然后重新导入 