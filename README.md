# 万和策略分析系统 - 使用与维护文档

## 系统概述

万和策略分析系统是一个基于Python的股票交易策略分析平台，主要用于回测、分析和优化交易策略。系统通过分析历史交易数据，生成高性能交易模式分析报告，帮助用户优化交易策略，提高交易胜率和收益率。

### 核心功能

- **交易策略执行**：执行基于技术指标的交易策略
- **数据管理**：统一管理交易和分析数据
- **交易分析**：分析交易表现，计算胜率、收益率等指标
- **HTML报告生成**：生成可视化的交易分析报告
- **风险管理**：动态止损、持仓限制等风险控制机制

### 系统架构

系统由以下几个主要模块组成：

1. **主程序模块** (`main.py`)：策略执行的入口，包含交易逻辑和回测功能
2. **数据管理模块** (`scripts/data_manager.py`)：统一管理数据访问和存储
3. **报告生成模块** (`scripts/generate_html_reports.py`)：生成HTML分析报告
4. **报告集成模块** (`scripts/integrate_reports.py`)：集成分析和报告生成
5. **风险管理模块** (`scripts/risk_manager.py`)：控制交易风险

## 使用方法

### 1. 系统初始化

首次使用系统前，需要确保环境已正确配置：

```bash
# 安装依赖
pip install -r requirements.txt

# 初始化数据目录
python -c "from scripts.data_manager import get_data_manager; get_data_manager()"
```

### 2. 运行策略回测

```bash
# 直接运行主程序进行回测
python main.py
```

主程序默认会使用以下参数：
- 回测时间段：2023-01-01至2023-03-31
- 初始资金：1,000,000元
- 交易费率：0.0003
- 滑点比例：0.0002

### 3. 生成分析报告

有两种方式生成分析报告：

#### 方式一：集成脚本（推荐）

```bash
# 运行集成脚本
python scripts/integrate_reports.py
```

运行后会提示选择操作：
1. 执行主分析（运行完整的策略回测）
2. 分析现有数据（仅分析已有的交易数据）
3. 跳过分析（仅生成报告）

#### 方式二：直接生成报告

```bash
# 直接生成HTML报告
python scripts/generate_html_reports.py
```

### 4. 查看分析报告

报告生成后，可以在以下位置查看：

- 高性能交易分析报告：`reports/html/high_performance_trades.html`
- 报告指南：`reports/html/reports_guide.html`

## 维护指南

### 1. 目录结构维护

系统使用以下目录结构：

```
├── main.py                  # 主程序
├── data/                    # 数据目录
│   ├── trade_log.csv        # 交易日志
│   └── analysis_log.csv     # 分析日志
├── reports/                 # 报告目录
│   ├── html/                # HTML报告
│   └── trade_analysis_results.csv  # 交易分析结果
├── templates/               # HTML模板
├── scripts/                 # 脚本文件
│   ├── data_manager.py      # 数据管理模块
│   ├── generate_html_reports.py  # HTML报告生成
│   └── integrate_reports.py # 报告集成脚本
├── logs/                    # 日志目录
└── backups/                 # 数据备份目录
```

请确保这些目录存在，系统会在运行时自动创建缺失的目录。

### 2. 数据管理

#### 数据备份

系统会在每次运行主程序前自动备份数据，备份文件存储在`backups/`目录中。您也可以手动触发备份：

```python
from scripts.data_manager import backup_data
backup_data()
```

#### 数据迁移

如需将CSV数据迁移到SQLite数据库：

```python
from scripts.data_manager import get_data_manager
dm = get_data_manager()
dm.migrate_csv_to_db()
```

### 3. 模板管理

HTML报告使用模板文件生成，模板文件存储在`templates/`目录中：

- `high_performance_template.html`：高性能交易报告模板
- `reports_guide_template.html`：报告指南模板

如需修改报告样式或内容，请编辑这些模板文件。模板中使用`{{变量名}}`格式的占位符，会在生成报告时被替换为实际数据。

### 4. 日志管理

系统使用多个日志文件记录不同模块的运行情况：

- `strategy.log`：主策略运行日志
- `data_manager.log`：数据管理模块日志
- `html_reports.log`：报告生成日志
- `reports_integration.log`：报告集成日志

定期检查这些日志文件，及时发现并解决问题。

### 5. 性能优化

如果系统运行速度较慢，可以考虑以下优化措施：

1. **启用数据库存储**：默认情况下，系统使用SQLite数据库存储数据，可以提高数据访问效率
2. **启用Parquet存储**：对于大数据量，可以启用Parquet格式存储：

```python
from scripts.data_manager import get_data_manager
dm = get_data_manager(use_db=True, use_parquet=True)
```

3. **调整缓存大小**：增加数据管理器的缓存大小可以减少磁盘I/O：

```python
from scripts.data_manager import get_data_manager
dm = get_data_manager()
dm.cache_size = 200  # 默认为100
```

### 6. 常见问题排查

#### 报告生成失败

1. 检查数据文件是否存在：`data/trade_log.csv`和`reports/trade_analysis_results.csv`
2. 检查模板文件是否存在：`templates/high_performance_template.html`
3. 查看`html_reports.log`日志文件了解具体错误

#### 数据访问错误

1. 检查文件权限是否正确
2. 尝试重新初始化数据管理器：`from scripts.data_manager import get_data_manager; get_data_manager()`
3. 检查`data_manager.log`日志文件

#### 策略执行异常

1. 检查`strategy.log`日志文件
2. 确保所有依赖模块都已正确安装
3. 检查是否有足够的磁盘空间和内存

## 系统升级与扩展

### 1. 添加新的交易策略

要添加新的交易策略，请修改`main.py`中的`buy_strategy`和`_check_sell_condition`函数。

### 2. 自定义报告内容

要自定义报告内容，请修改`scripts/generate_html_reports.py`中的相关函数，并更新HTML模板文件。

### 3. 添加新的数据源

要添加新的数据源，请扩展`scripts/data_manager.py`中的`DataManager`类，添加新的数据访问方法。

## 联系与支持

如有任何问题或建议，请联系技术支持团队。

---

© 万和策略分析系统 - 技术文档 

# 订阅优化方案实施记录

## 问题描述

策略在运行过程中出现多次重复订阅尝试，特别是在非交易时间段，导致频繁的订阅失败和错误处理，增加了系统负担。

## 优化目标

1. 在非交易时间段不进行订阅操作
2. 股票池为空时不立即重新订阅，等待正常的轮换订阅
3. 订阅失败时不自动减小批次大小，继续使用配置中的原始批次大小
4. 增强冷却机制，避免短时间内多次尝试订阅

## 修改内容

### 1. `buy_strategy`函数修改

- 股票池为空时不再调用`subscribe_constituents`方法尝试重新订阅
- 直接返回，等待下次轮换订阅自动填充股票池

```python
# 修改前
if not hasattr(context.data_fetcher, 'constituents') or not context.data_fetcher.constituents:
    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票池为空，尝试重新获取")
    context.data_fetcher.subscribe_constituents(context.index_symbol)
    
# 修改后
if not hasattr(context.data_fetcher, 'constituents') or not context.data_fetcher.constituents:
    context.log.warning(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票池为空，等待下次轮换订阅")
    return  # 直接返回，不尝试重新订阅
```

### 2. `on_error`函数修改

- 非交易时间段检测到订阅失败时，只记录日志，不尝试重新订阅
- 增强冷却机制，短时间内多次订阅失败时不再立即重试
- 保持使用配置的批次大小，不自动减小

```python
# 修改前（非交易时段处理）
if not is_trading and context.run_mode != 'backtest':
    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，不进行订阅重试")
    # 只订阅持仓，不尝试全量订阅
    _subscribe_holdings_only(context)
    return

# 修改后（非交易时段处理）
if not is_trading and context.run_mode != 'backtest':
    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，不进行订阅重试")
    # 只记录日志，不尝试重新订阅
    return

# 新增冷却检查
if context.subscription_errors > 1:
    context.log.info(f"{now.strftime('%Y-%m-%d %H:%M:%S')} - 短时间内多次订阅失败，等待下次轮换订阅")
    return
```

### 3. `data_fetcher.py`中的订阅方法修改

- 修改`_subscribe_full`方法，订阅失败时不再自动减小批次大小
- 修改`rotate_subscription`方法，订阅失败时不再自动减小批次大小
- 保持使用配置中的原始批次大小进行重试

```python
# 修改前
smaller_batch_size = min(
    self.live_mode_batch_size,  # 配置中的批次大小
    self.max_subscription_limit,  # 配置中的最大订阅限制
    20  # 保底安全值
)

# 修改后
batch_size = self.live_mode_batch_size  # 使用配置中的批次大小，不自动减小
```

## 预期效果

1. 减少不必要的订阅尝试，特别是在非交易时间段
2. 避免因股票池为空触发的重复订阅
3. 保持使用配置中的批次大小，提高订阅稳定性
4. 通过冷却机制减少短时间内的重复订阅尝试
5. 整体降低系统负担，提高策略运行效率 

# 万和交易策略

## 最新更新

### 股票过滤功能

现在支持根据以下条件过滤股票池：

- **ST股票过滤**：过滤掉带有ST标记的股票
- **板块过滤**：可选择性过滤创业板、科创板和北交所股票
- **价格过滤**：设置价格上下限，过滤不在范围内的股票

这些过滤条件可以在`config.py`中进行配置：

```python
# 股票类型过滤参数
FILTER_ST_STOCKS = True           # 是否过滤ST股票
FILTER_STARTUP_BOARD = False      # 是否过滤创业板股票(3开头)
FILTER_SCIENCE_BOARD = True       # 是否过滤科创板股票(688开头)
FILTER_BEIJING_BOARD = False      # 是否过滤北交所股票(8开头)

# 价格过滤参数
PRICE_FILTER_ENABLED = True       # 是否启用价格过滤
MIN_PRICE_FILTER = 5.0            # 最低价格过滤(元)
MAX_PRICE_FILTER = 100.0          # 最高价格过滤(元)
```

过滤功能会自动应用于策略获取的股票池，包括指数成分股和全市场股票。

## 策略概述

万和交易策略是一个基于技术指标和波动性分析的股票交易策略，主要特点：

1. 动态配置管理：支持在不重启策略的情况下更新参数
2. 灵活的股票池管理：支持指数成分股和全市场股票
3. 多重买入信号：支持TRIX和均线交叉等多种买入信号
4. 智能止盈止损：支持跟踪止盈和动态止损
5. 性能优化：支持并行计算和数据缓存

## 主要功能

- 股票池管理：支持沪深300、中证500等指数成分股，也支持全市场股票
- 信号生成：基于TRIX、均线等技术指标生成买入卖出信号
- 波动性分析：计算股票波动性并用于交易决策
- 持仓管理：智能管理持仓数量和资金分配
- 风险控制：实时监控持仓风险，执行止盈止损

## 配置说明

策略的所有参数都在`config.py`文件中，支持动态加载，修改后自动生效。

## 使用方法

1. 修改`config.py`中的参数
2. 运行`main.py`启动策略
3. 查看日志了解策略运行情况

## 性能优化

- 启用并行计算：`ENABLE_PARALLEL_COMPUTING = True`
- 增加缓存大小：`HISTORY_CACHE_SIZE = 500`
- 启用长期缓存：`LONG_CACHE_EXPIRES_SECONDS = 86400` 