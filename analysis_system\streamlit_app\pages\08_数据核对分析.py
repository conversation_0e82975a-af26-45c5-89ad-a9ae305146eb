import streamlit as st
import pandas as pd
import sqlite3
import os
import sys
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入工具函数
from utils.data_loader import load_config

st.set_page_config(page_title="数据核对分析", page_icon="🔍", layout="wide")

st.title("数据核对分析")
st.markdown("在这里您可以比对系统数据库与掘金量化平台数据，进行差异分析和核对。")

# 加载配置
config = load_config()
data_path = config["data_path"]
db_path = os.path.join(data_path, "trades.db")

# 在侧边栏添加目录选择选项
st.sidebar.header("数据源设置")
default_jq_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "回测数据")
custom_jq_path = st.sidebar.text_input("掘金量化数据目录路径", value=default_jq_path)
jq_data_path = custom_jq_path if custom_jq_path else default_jq_path

# 创建侧边栏 - 数据核对选项
st.sidebar.header("数据核对选项")

# 选择核对类型
comparison_type = st.sidebar.radio(
    "选择核对类型",
    ["交易数据核对", "持仓数据核对", "净值数据核对"]
)

# 检查数据库是否存在
if not os.path.exists(db_path):
    st.error(f"系统数据库文件不存在: {db_path}")
    st.info("请先确保数据库文件已创建，或者在主页上传数据库文件。")
    st.stop()

# 检查回测数据目录是否存在
if not os.path.exists(jq_data_path):
    st.error(f"掘金量化回测数据目录不存在: {jq_data_path}")
    st.info(f"请创建目录 '{jq_data_path}' 并将掘金量化导出的CSV文件放入该目录，或在侧边栏中指定正确的目录路径。")
    st.stop()

# 查找掘金量化数据文件（根据前缀查找）
def find_latest_file(directory, prefix):
    """在指定目录中查找以特定前缀开头的最新CSV文件"""
    if not os.path.exists(directory):
        return None
    
    matching_files = [f for f in os.listdir(directory) 
                     if f.startswith(prefix) and f.endswith('.csv')]
    
    if not matching_files:
        return None
    
    # 如果有多个匹配的文件，返回最新的一个
    latest_file = max(matching_files, key=lambda f: os.path.getmtime(os.path.join(directory, f)))
    return os.path.join(directory, latest_file)

# 查找掘金量化数据文件
jq_files = {
    "交易数据": find_latest_file(jq_data_path, "交易数据_"),
    "持仓数据": find_latest_file(jq_data_path, "持仓数据_"),
    "净值数据": find_latest_file(jq_data_path, "净值数据_")
}

# 检查是否找到了所有需要的文件
missing_files = [name for name, path in jq_files.items() if path is None]
if missing_files:
    st.error(f"以下掘金量化数据文件不存在: {', '.join(missing_files)}")
    st.info(f"请确保以下文件位于 {jq_data_path} 目录下：")
    for name in missing_files:
        st.write(f"- {name}_*.csv (文件名以'{name}_'开头的CSV文件)")
    st.stop()

# 显示找到的文件
st.sidebar.subheader("已找到的掘金量化数据文件")
for name, path in jq_files.items():
    if path:
        st.sidebar.success(f"{name}: {os.path.basename(path)}")

# 数据库连接函数
def get_connection():
    """创建到SQLite数据库的连接"""
    try:
        conn = sqlite3.connect(db_path)
        return conn
    except Exception as e:
        st.error(f"连接数据库失败: {str(e)}")
        return None

# 加载掘金量化数据
@st.cache_data(ttl=300)
def load_jq_data(data_type):
    """加载掘金量化平台数据"""
    file_path = jq_files[data_type]
    try:
        if file_path is None:
            st.error(f"未找到{data_type}文件")
            return None
            
        df = pd.read_csv(file_path)
        return df
    except Exception as e:
        st.error(f"加载掘金量化{data_type}失败: {str(e)}")
        return None

# 加载系统数据库数据
@st.cache_data(ttl=300)
def load_db_data(table_name, query=None):
    """从系统数据库加载数据"""
    conn = None
    try:
        conn = get_connection()
        if conn is None:
            return None
        
        if query is None:
            query = f"SELECT * FROM {table_name}"
        
        df = pd.read_sql_query(query, conn)
        return df
    except Exception as e:
        st.error(f"从数据库加载{table_name}表数据失败: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

# 主界面
if comparison_type == "交易数据核对":
    st.header("交易数据核对")
    
    # 加载数据
    with st.spinner("正在加载交易数据..."):
        jq_trades = load_jq_data("交易数据")
        db_trades = load_db_data("trades")
        
        if jq_trades is not None and db_trades is not None:
            # 显示数据概览
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("掘金量化交易数据")
                st.write(f"记录数: {len(jq_trades)}")
                st.dataframe(jq_trades.head(5), use_container_width=True)
            
            with col2:
                st.subheader("系统数据库交易数据")
                st.write(f"记录数: {len(db_trades)}")
                st.dataframe(db_trades.head(5), use_container_width=True)
            
            # 数据预处理
            st.subheader("数据预处理")
            
            # 掘金量化数据预处理
            st.write("掘金量化交易数据字段映射:")
            jq_mapping = {
                "trade_time": "交易时间",
                "symbol": "股票代码",
                "side": "交易方向",
                "volume": "交易量",
                "vwap": "成交均价",
                "amount": "交易金额"
            }
            
            # 系统数据库字段映射
            st.write("系统数据库交易数据字段映射:")
            db_mapping = {
                "timestamp": "交易时间",
                "symbol": "股票代码",
                "direction": "交易方向",
                "volume": "交易量",
                "price": "成交价格",
                "amount": "交易金额"
            }
            
            # 数据核对分析
            st.subheader("交易数据核对分析")
            
            # 日期范围选择
            min_date = min(
                pd.to_datetime(jq_trades["trade_time"]).min().date(),
                pd.to_datetime(db_trades["timestamp"]).min().date()
            )
            max_date = max(
                pd.to_datetime(jq_trades["trade_time"]).max().date(),
                pd.to_datetime(db_trades["timestamp"]).max().date()
            )
            
            date_range = st.date_input(
                "选择日期范围",
                value=(min_date, max_date),
                min_value=min_date,
                max_value=max_date
            )
            
            if len(date_range) == 2:
                start_date, end_date = date_range
                
                # 过滤数据
                jq_filtered = jq_trades[
                    (pd.to_datetime(jq_trades["trade_time"]).dt.date >= start_date) &
                    (pd.to_datetime(jq_trades["trade_time"]).dt.date <= end_date)
                ]
                
                db_filtered = db_trades[
                    (pd.to_datetime(db_trades["timestamp"]).dt.date >= start_date) &
                    (pd.to_datetime(db_trades["timestamp"]).dt.date <= end_date)
                ]
                
                # 交易统计比较
                st.write("### 交易统计比较")
                
                # 按日期统计交易量
                jq_daily = jq_filtered.groupby(pd.to_datetime(jq_filtered["trade_time"]).dt.date).agg(
                    交易笔数=("symbol", "count"),
                    交易总量=("volume", "sum"),
                    交易总金额=("amount", "sum")
                ).reset_index()
                jq_daily.rename(columns={"trade_time": "日期"}, inplace=True)
                
                db_daily = db_filtered.groupby(pd.to_datetime(db_filtered["timestamp"]).dt.date).agg(
                    交易笔数=("symbol", "count"),
                    交易总量=("volume", "sum"),
                    交易总金额=("amount", "sum") if "amount" in db_filtered.columns else ("price", lambda x: 0)
                ).reset_index()
                db_daily.rename(columns={"timestamp": "日期"}, inplace=True)
                
                # 合并数据进行比较
                daily_comparison = pd.merge(
                    jq_daily, db_daily,
                    left_on="日期", right_on="日期",
                    how="outer", suffixes=("_掘金", "_系统")
                )
                
                st.dataframe(daily_comparison, use_container_width=True)
                
                # 可视化比较
                st.write("### 交易数据可视化比较")
                
                # 选择可视化指标
                viz_metric = st.selectbox(
                    "选择要可视化的指标",
                    ["交易笔数", "交易总量", "交易总金额"]
                )
                
                # 创建可视化图表
                fig, ax = plt.subplots(figsize=(10, 6))
                
                if not jq_daily.empty and not db_daily.empty:
                    ax.plot(jq_daily["日期"], jq_daily[viz_metric], label="掘金量化", marker="o")
                    ax.plot(db_daily["日期"], db_daily[viz_metric], label="系统数据库", marker="x")
                    ax.set_xlabel("日期")
                    ax.set_ylabel(viz_metric)
                    ax.set_title(f"{viz_metric}比较")
                    ax.legend()
                    ax.grid(True)
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    st.pyplot(fig)
                else:
                    st.warning("没有足够的数据进行可视化比较")
                
                # 差异分析
                st.write("### 差异分析")
                
                # 计算差异
                if not daily_comparison.empty:
                    daily_comparison["交易笔数差异"] = daily_comparison["交易笔数_掘金"] - daily_comparison["交易笔数_系统"]
                    daily_comparison["交易总量差异"] = daily_comparison["交易总量_掘金"] - daily_comparison["交易总量_系统"]
                    daily_comparison["交易总金额差异"] = daily_comparison["交易总金额_掘金"] - daily_comparison["交易总金额_系统"]
                    
                    st.dataframe(daily_comparison[["日期", "交易笔数差异", "交易总量差异", "交易总金额差异"]], use_container_width=True)
                    
                    # 差异可视化
                    diff_metric = st.selectbox(
                        "选择要可视化的差异指标",
                        ["交易笔数差异", "交易总量差异", "交易总金额差异"]
                    )
                    
                    fig, ax = plt.subplots(figsize=(10, 6))
                    ax.bar(daily_comparison["日期"].astype(str), daily_comparison[diff_metric])
                    ax.set_xlabel("日期")
                    ax.set_ylabel(diff_metric)
                    ax.set_title(f"{diff_metric}分析")
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    st.pyplot(fig)
                else:
                    st.warning("没有足够的数据进行差异分析")

elif comparison_type == "持仓数据核对":
    st.header("持仓数据核对")
    
    # 加载数据
    with st.spinner("正在加载持仓数据..."):
        jq_positions = load_jq_data("持仓数据")
        
        # 从系统数据库加载持仓数据
        db_positions_query = """
        SELECT 
            date(timestamp) as date,
            symbol,
            direction as side,
            SUM(CASE WHEN direction = '买入' THEN volume ELSE -volume END) as posi_balance,
            AVG(price) as posi_vwap,
            price as price,
            SUM(CASE WHEN direction = '买入' THEN volume ELSE -volume END) * price as market_value
        FROM 
            trades 
        WHERE 
            status = 'HOLDING'
        GROUP BY 
            date(timestamp), symbol
        """
        
        db_positions = load_db_data("trades", db_positions_query)
        
        if jq_positions is not None and db_positions is not None:
            # 显示数据概览
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("掘金量化持仓数据")
                st.write(f"记录数: {len(jq_positions)}")
                st.dataframe(jq_positions.head(5), use_container_width=True)
            
            with col2:
                st.subheader("系统数据库持仓数据")
                st.write(f"记录数: {len(db_positions)}")
                st.dataframe(db_positions.head(5), use_container_width=True)
            
            # 日期选择
            unique_dates = sorted(jq_positions["date"].unique())
            selected_date = st.selectbox("选择日期", unique_dates)
            
            # 过滤数据
            jq_filtered = jq_positions[jq_positions["date"] == selected_date]
            db_filtered = db_positions[db_positions["date"] == selected_date]
            
            # 持仓比较
            st.write("### 持仓比较")
            
            # 合并数据进行比较
            position_comparison = pd.merge(
                jq_filtered, db_filtered,
                on="symbol",
                how="outer", suffixes=("_掘金", "_系统")
            )
            
            if not position_comparison.empty:
                # 计算差异
                position_comparison["持仓量差异"] = position_comparison["posi_balance_掘金"] - position_comparison["posi_balance_系统"]
                position_comparison["持仓均价差异"] = position_comparison["posi_vwap_掘金"] - position_comparison["posi_vwap_系统"]
                position_comparison["市值差异"] = position_comparison["market_value_掘金"] - position_comparison["market_value_系统"]
                
                st.dataframe(position_comparison, use_container_width=True)
                
                # 差异可视化
                st.write("### 持仓差异可视化")
                
                # 选择可视化指标
                viz_metric = st.selectbox(
                    "选择要可视化的差异指标",
                    ["持仓量差异", "持仓均价差异", "市值差异"]
                )
                
                # 创建可视化图表
                fig, ax = plt.subplots(figsize=(12, 8))
                
                # 只显示有差异的股票
                diff_data = position_comparison[position_comparison[viz_metric] != 0]
                
                if not diff_data.empty:
                    ax.bar(diff_data["symbol"], diff_data[viz_metric])
                    ax.set_xlabel("股票代码")
                    ax.set_ylabel(viz_metric)
                    ax.set_title(f"{selected_date} {viz_metric}分析")
                    plt.xticks(rotation=90)
                    plt.tight_layout()
                    st.pyplot(fig)
                else:
                    st.success(f"所选日期 {selected_date} 的所有股票 {viz_metric} 均为0，数据完全匹配！")
                
                # 汇总统计
                st.write("### 汇总统计")
                
                total_jq = jq_filtered["market_value"].sum()
                total_db = db_filtered["market_value"].sum()
                diff_pct = ((total_jq - total_db) / total_jq * 100) if total_jq != 0 else 0
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("掘金量化总市值", f"{total_jq:,.2f}")
                with col2:
                    st.metric("系统数据库总市值", f"{total_db:,.2f}")
                with col3:
                    st.metric("差异百分比", f"{diff_pct:.2f}%")
            else:
                st.warning(f"所选日期 {selected_date} 没有足够的数据进行比较")

elif comparison_type == "净值数据核对":
    st.header("净值数据核对")
    
    # 加载数据
    with st.spinner("正在加载净值数据..."):
        jq_nav = load_jq_data("净值数据")
        
        # 从系统数据库加载净值数据
        db_nav_query = """
        SELECT 
            date(timestamp) as date,
            SUM(profit) as profit_loss,
            AVG(nav) as nav,
            AVG(strategy_yield) as strategy_yield
        FROM 
            analysis 
        GROUP BY 
            date(timestamp)
        """
        
        db_nav = load_db_data("analysis", db_nav_query)
        
        if jq_nav is not None and db_nav is not None:
            # 显示数据概览
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("掘金量化净值数据")
                st.write(f"记录数: {len(jq_nav)}")
                st.dataframe(jq_nav.head(5), use_container_width=True)
            
            with col2:
                st.subheader("系统数据库净值数据")
                st.write(f"记录数: {len(db_nav)}")
                st.dataframe(db_nav.head(5), use_container_width=True)
            
            # 数据预处理
            jq_nav["date"] = pd.to_datetime(jq_nav["date"]).dt.date
            if "date" not in db_nav.columns and "timestamp" in db_nav.columns:
                db_nav["date"] = pd.to_datetime(db_nav["timestamp"]).dt.date
            
            # 合并数据进行比较
            nav_comparison = pd.merge(
                jq_nav, db_nav,
                on="date",
                how="outer", suffixes=("_掘金", "_系统")
            )
            
            # 净值比较可视化
            st.write("### 净值比较")
            
            # 日期范围选择
            min_date = min(jq_nav["date"].min(), db_nav["date"].min() if "date" in db_nav.columns else pd.Timestamp.max.date())
            max_date = max(jq_nav["date"].max(), db_nav["date"].max() if "date" in db_nav.columns else pd.Timestamp.min.date())
            
            date_range = st.date_input(
                "选择日期范围",
                value=(min_date, max_date),
                min_value=min_date,
                max_value=max_date
            )
            
            if len(date_range) == 2:
                start_date, end_date = date_range
                
                # 过滤数据
                nav_comparison_filtered = nav_comparison[
                    (nav_comparison["date"] >= start_date) &
                    (nav_comparison["date"] <= end_date)
                ]
                
                # 选择要比较的指标
                nav_metric = st.selectbox(
                    "选择要比较的净值指标",
                    ["nav", "strategy_yield", "benchmark_yield", "profit_loss"]
                )
                
                # 创建可视化图表
                fig, ax = plt.subplots(figsize=(12, 6))
                
                if not nav_comparison_filtered.empty:
                    # 绘制净值曲线
                    ax.plot(
                        nav_comparison_filtered["date"], 
                        nav_comparison_filtered[f"{nav_metric}_掘金"], 
                        label="掘金量化", 
                        marker="o",
                        markersize=4
                    )
                    
                    if f"{nav_metric}_系统" in nav_comparison_filtered.columns:
                        ax.plot(
                            nav_comparison_filtered["date"], 
                            nav_comparison_filtered[f"{nav_metric}_系统"], 
                            label="系统数据库", 
                            marker="x",
                            markersize=4
                        )
                    
                    ax.set_xlabel("日期")
                    ax.set_ylabel(nav_metric)
                    ax.set_title(f"{nav_metric}比较")
                    ax.legend()
                    ax.grid(True)
                    plt.xticks(rotation=45)
                    plt.tight_layout()
                    st.pyplot(fig)
                    
                    # 计算差异
                    if f"{nav_metric}_系统" in nav_comparison_filtered.columns:
                        nav_comparison_filtered[f"{nav_metric}_差异"] = (
                            nav_comparison_filtered[f"{nav_metric}_掘金"] - 
                            nav_comparison_filtered[f"{nav_metric}_系统"]
                        )
                        
                        # 差异可视化
                        st.write("### 净值差异分析")
                        
                        fig, ax = plt.subplots(figsize=(12, 6))
                        ax.bar(
                            nav_comparison_filtered["date"].astype(str), 
                            nav_comparison_filtered[f"{nav_metric}_差异"]
                        )
                        ax.set_xlabel("日期")
                        ax.set_ylabel(f"{nav_metric}差异")
                        ax.set_title(f"{nav_metric}差异分析")
                        plt.xticks(rotation=45)
                        plt.tight_layout()
                        st.pyplot(fig)
                        
                        # 统计分析
                        st.write("### 统计分析")
                        
                        abs_diff = nav_comparison_filtered[f"{nav_metric}_差异"].abs()
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("平均绝对差异", f"{abs_diff.mean():.6f}")
                        with col2:
                            st.metric("最大绝对差异", f"{abs_diff.max():.6f}")
                        with col3:
                            st.metric("标准差", f"{abs_diff.std():.6f}")
                        
                        # 显示差异数据表
                        st.write("### 差异数据表")
                        st.dataframe(
                            nav_comparison_filtered[[
                                "date", 
                                f"{nav_metric}_掘金", 
                                f"{nav_metric}_系统", 
                                f"{nav_metric}_差异"
                            ]],
                            use_container_width=True
                        )
                else:
                    st.warning("所选日期范围内没有足够的数据进行比较")

# 添加数据导出功能
st.sidebar.header("数据导出")
if st.sidebar.button("导出核对结果"):
    # 根据选择的核对类型导出不同的数据
    if comparison_type == "交易数据核对" and "daily_comparison" in locals():
        csv = daily_comparison.to_csv(index=False).encode('utf-8')
        st.sidebar.download_button(
            label="下载交易核对结果 (CSV)",
            data=csv,
            file_name=f"交易核对结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime='text/csv',
        )
    elif comparison_type == "持仓数据核对" and "position_comparison" in locals():
        csv = position_comparison.to_csv(index=False).encode('utf-8')
        st.sidebar.download_button(
            label="下载持仓核对结果 (CSV)",
            data=csv,
            file_name=f"持仓核对结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime='text/csv',
        )
    elif comparison_type == "净值数据核对" and "nav_comparison_filtered" in locals():
        csv = nav_comparison_filtered.to_csv(index=False).encode('utf-8')
        st.sidebar.download_button(
            label="下载净值核对结果 (CSV)",
            data=csv,
            file_name=f"净值核对结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime='text/csv',
        ) 