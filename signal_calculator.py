#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
信号计算器 - 高效批量计算技术指标和生成信号
"""

from __future__ import print_function, absolute_import, unicode_literals
from gm.api import *
import numpy as np
import pandas as pd
import talib
import datetime
from collections import defaultdict
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from numba import njit, prange
import sys # 新增导入

# 直接实现is_trading_hour函数，避免循环导入
def is_trading_hour(current_time=None):
    """
    判断当前是否在交易时间段内
    
    Args:
        current_time: 指定时间，如果为None则使用当前时间
        
    Returns:
        bool: 是否在交易时间段内
    """
    if current_time is None:
        current_time = datetime.datetime.now().time()
    elif isinstance(current_time, datetime.datetime):
        current_time = current_time.time()
        
    # 上午交易时段: 9:30 - 11:30
    morning_session = (
        datetime.time(9, 30) <= current_time <= datetime.time(11, 30)
    )
    
    # 下午交易时段: 13:00 - 15:00
    afternoon_session = (
        datetime.time(13, 0) <= current_time <= datetime.time(15, 0)
    )
    
    return morning_session or afternoon_session

class SignalCalculator:
    """信号计算器 - 高效批量计算技术指标和生成信号"""
    
    def __init__(self, context):
        """
        初始化信号计算器
        
        参数:
        - context: 策略上下文
        """
        self.context = context
        self.signal_cache = {}  # {(symbol, signal_type): (timestamp, signal_value)}
        self.indicator_cache = {}  # {(symbol, indicator_name): (timestamp, indicator_values)}
        
        # 增加长期缓存，用于缓存交易日内不会变化的指标
        self.long_term_cache = {}  # {(symbol, indicator_name): (date, indicator_values)}
        
        # 修改缓存有效期
        self.cache_expires_seconds = 1800  # 常规缓存有效期(秒)，提高到30分钟
        self.long_cache_expires_seconds = 86400  # 长期缓存有效期(秒)，设为24小时
        
        # 并行计算设置
        self.enable_parallel = self._get_config_value('ENABLE_PARALLEL_COMPUTING', False)
        self.parallel_workers = self._get_config_value('PARALLEL_WORKERS', 4)
        
        # 性能分析
        self.perf_stats = {
            'trix_calc_count': 0,
            'trix_calc_time': 0,
            'ma_calc_count': 0,
            'ma_calc_time': 0,
            'signal_calc_count': 0,
            'signal_calc_time': 0
        }
        
        # 记录优化前的性能数据，用于比较
        self.original_perf = {
            'trix_calc_time': 0.0023,  # 每次计算约2.3毫秒
            'ma_calc_time': 0.0018,    # 每次计算约1.8毫秒
            'signal_calc_time': 0.0075 # 每次计算约7.5毫秒
        }
        
        # TRIX参数
        self.trix_period = self._get_config_value('TRIX_PERIOD', 9)
        self.use_talib_trix = self._get_config_value('USE_TALIB_TRIX', True)
        self.use_numba_trix = self._get_config_value('USE_NUMBA_TRIX', True)
        
        # 均线参数
        self.ma_short_period = self._get_config_value('MA_SHORT_PERIOD', 5)
        self.ma_mid_period = self._get_config_value('MA_MID_PERIOD', 20)
        self.ma_long_period = self._get_config_value('MA_LONG_PERIOD', 60)
        
        try:
            # 记录初始化信息
            if hasattr(context, 'log'):
                context.log.info(f"信号计算器初始化完成，TRIX周期={self.trix_period}，使用talib={self.use_talib_trix}，使用Numba={self.use_numba_trix}")
        except Exception as e:
            print(f"信号计算器初始化异常: {str(e)}")
            # 使用默认值
            self.trix_period = 9
            self.use_talib_trix = True
            self.use_numba_trix = False
            self.ma_short_period = 5
            self.ma_mid_period = 20
            self.ma_long_period = 60
    
    def calculate_trix(self, symbol, period=None, is_reversal=False):
        """
        计算单个股票的TRIX指标
        
        参数:
        - symbol: 股票代码
        - period: TRIX的EMA周期，如果为None则使用配置值
        - is_reversal: 是否用于拐点信号计算
        
        返回:
        - trix: TRIX值列表
        - trix_signal: TRIX信号（True表示买入信号）
        - success: 是否计算成功
        """
        # 确定使用的周期
        if period is None:
            if is_reversal:
                period = self._get_config_value('TRIX_REVERSAL_PERIOD', 9)
            else:
                period = self._get_config_value('TRIX_EMA_PERIOD', 3)
        
        # 构建缓存键
        cache_key_prefix = 'trix_reversal' if is_reversal else 'trix'
        cache_key = (symbol, f'{cache_key_prefix}_{period}')
        
        # 检查缓存
        if cache_key in self.indicator_cache:
            timestamp, (trix, trix_signal) = self.indicator_cache[cache_key]
            
            # 检查缓存是否在有效期内
            if (self.context.now - timestamp).total_seconds() < self.cache_expires_seconds:
                return trix, trix_signal, True
        
        # 检查长期缓存
        today = self.context.now.strftime('%Y-%m-%d')
        long_cache_key = (symbol, f'{cache_key_prefix}_{period}', today)
        if long_cache_key in self.long_term_cache:
            cache_date, (trix, trix_signal) = self.long_term_cache[long_cache_key]
            if cache_date == today:
                return trix, trix_signal, True
        
        try:
            # 记录开始时间
            start_time = time.time()
            self.perf_stats['trix_calc_count'] += 1
            
            # 获取历史数据
            if hasattr(self.context, 'history_data_manager'):
                hist_data = self.context.history_data_manager.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=30,
                    fields='close'
                )
            else:
                # 兼容原始数据获取
                hist_data = self.context.data_fetcher.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=30,
                    fields='close'
                )
            
            if hist_data is None or hist_data.empty or len(hist_data) < 10:  # 确保至少有10天数据
                return None, False, False
                
            # 检查是否使用实时价格替代当日收盘价
            use_realtime_price = self._get_config_value('USE_REALTIME_PRICE', True)
            
            # 如果启用实时价格且在交易时间内，获取当前价格并添加到历史数据中
            if use_realtime_price and hasattr(self.context, 'now') and is_trading_hour(self.context.now.time()):
                try:
                    # 获取当前价格
                    current_data = current(symbols=symbol)
                    if current_data and len(current_data) > 0:
                        current_price = current_data[0]['price']
                        
                        # 记录日志
                        signal_type = "拐点" if is_reversal else "买入"
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX{signal_type}信号计算器(周期={period})使用实时价格 {current_price} 替代当日收盘价")
                        
                        # 确保数据类型为float64
                        close_prices = hist_data['close'].values.astype(np.float64)
                        # 替换最后一个收盘价为当前价格
                        if len(close_prices) > 0:
                            close_prices[-1] = current_price
                    else:
                        # 确保数据类型为float64
                        close_prices = hist_data['close'].values.astype(np.float64)
                except Exception as e:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 信号计算器获取{symbol}实时价格异常: {str(e)}")
                    # 确保数据类型为float64
                    close_prices = hist_data['close'].values.astype(np.float64)
            else:
                # 确保数据类型为float64
                close_prices = hist_data['close'].values.astype(np.float64)
            
            # 使用不同的计算方法
            use_talib_trix = self._get_config_value('USE_TALIB_TRIX', False)
            if use_talib_trix:
                # 使用talib直接计算TRIX
                trix = talib.TRIX(close_prices, timeperiod=period)
            elif hasattr(self, 'use_numba_trix') and self.use_numba_trix:
                # 使用Numba加速计算TRIX
                trix = numba_trix(close_prices, period)
            else:
                # 使用自定义计算方法 - 向量化实现
                ema1 = talib.EMA(close_prices, timeperiod=period)
                ema2 = talib.EMA(ema1, timeperiod=period)
                ema3 = talib.EMA(ema2, timeperiod=period)
                
                # 使用向量化操作替代循环
                trix = np.zeros_like(close_prices)
                # 避免除以零
                valid_indices = (ema3[:-1] != 0)
                # 计算有效索引的TRIX值
                trix_values = (ema3[1:] - ema3[:-1]) / ema3[:-1] * 100
                # 将计算结果赋值给有效索引位置
                trix[1:][valid_indices] = trix_values[valid_indices]
            
            # 根据是否为拐点信号，生成不同的信号
            trix_signal = False
            if is_reversal:
                # 拐点信号需要至少4个有效值
                if len(trix) >= 4 and not np.isnan(trix[-1]) and not np.isnan(trix[-2]) and not np.isnan(trix[-3]) and not np.isnan(trix[-4]):
                    # 拐点信号：昨日TRIX > 前日TRIX 且 前日TRIX < 大前日TRIX
                    trix_signal = (trix[-2] > trix[-3]) and (trix[-3] < trix[-4])
                    
                    # 记录TRIX拐点值
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} TRIX拐点值(周期={period}): 今日={trix[-1]:.6f}, 昨日={trix[-2]:.6f}, 前日={trix[-3]:.6f}, 大前日={trix[-4]:.6f}, 信号={trix_signal}")
            else:
                # 普通TRIX信号需要至少3个有效值
                if len(trix) >= 3 and not np.isnan(trix[-1]) and not np.isnan(trix[-2]) and not np.isnan(trix[-3]):
                    # 普通信号：当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX
                    trix_signal = (trix[-1] > trix[-2] and trix[-2] < trix[-3])
            
            # 更新缓存
            self.indicator_cache[cache_key] = (self.context.now, (trix, trix_signal))
            
            # 更新长期缓存
            self.long_term_cache[long_cache_key] = (today, (trix, trix_signal))
            
            # 记录计算耗时
            self.perf_stats['trix_calc_time'] += time.time() - start_time
            
            return trix, trix_signal, True
        except Exception as e:
            self.context.log.error(f"计算{symbol}的TRIX指标异常: {str(e)}")
            return None, False, False
    
    def calculate_trix_reversal(self, symbol):
        """
        计算单个股票的TRIX拐点信号（昨日TRIX>前日TRIX且前日TRIX<大前日TRIX）
        
        参数:
        - symbol: 股票代码
        
        返回:
        - trix: TRIX值列表
        - trix_reversal_signal: TRIX拐点信号（True表示买入信号）
        - success: 是否计算成功
        """
        # 使用通用的TRIX计算函数，指定为拐点模式
        return self.calculate_trix(symbol, is_reversal=True)
    
    def calculate_batch_trix(self, symbols, is_reversal=False):
        """
        批量计算多个股票的TRIX指标
        
        参数:
        - symbols: 股票代码列表
        - is_reversal: 是否用于拐点信号计算
        
        返回:
        - dict: {symbol: (trix, trix_signal, success)}
        """
        # 初始化结果字典
        results = {}
        
        # 如果没有股票，直接返回空结果
        if not symbols:
            return results
            
        # 确定使用的周期和缓存前缀
        if is_reversal:
            period = self._get_config_value('TRIX_REVERSAL_PERIOD', 9)
            cache_key_prefix = 'trix_reversal'
        else:
            period = self._get_config_value('TRIX_EMA_PERIOD', 3)
            cache_key_prefix = 'trix'
            
        # 获取当前日期
        today = self.context.now.strftime('%Y-%m-%d')
        
        # 检查长期缓存
        cached_symbols = []
        for symbol in symbols:
            cache_key = (symbol, f'{cache_key_prefix}_{period}', today)
            if cache_key in self.long_term_cache:
                timestamp, (trix, trix_signal) = self.long_term_cache[cache_key]
                results[symbol] = (trix, trix_signal, True)
                cached_symbols.append(symbol)
        
        # 移除已经有缓存的股票
        symbols_to_calculate = [s for s in symbols if s not in cached_symbols]
        
        # 如果所有股票都有缓存，直接返回结果
        if not symbols_to_calculate:
            return results
        
        # 批量获取历史数据
        hist_data_dict = {}
        
        # 使用历史数据管理器批量获取数据
        if hasattr(self.context, 'history_data_manager'):
            batch_hist_data = self.context.history_data_manager.batch_get_history_data(
                symbols=symbols_to_calculate,
                frequency='1d',
                count=30,
                fields='close'
            )
            hist_data_dict = batch_hist_data
        else:
            # 兼容模式：逐个获取历史数据
            for symbol in symbols_to_calculate:
                try:
                    hist_data = self.context.data_fetcher.get_history_data(
                        symbol=symbol,
                        frequency='1d',
                        count=30,
                        fields='close'
                    )
                    if hist_data is not None and not hist_data.empty:
                        hist_data_dict[symbol] = hist_data
                except Exception as e:
                    self.context.log.error(f"获取{symbol}历史数据异常: {str(e)}")
        
        # 获取当前价格（如果在交易时间内且启用了实时价格替代）
        use_realtime_price = self._get_config_value('USE_REALTIME_PRICE', True)
        current_prices = {}
        
        if use_realtime_price and hasattr(self.context, 'now') and is_trading_hour(self.context.now.time()):
            try:
                current_data = current(symbols=symbols_to_calculate)
                if current_data:
                    for data in current_data:
                        current_prices[data['symbol']] = data['price']
            except Exception as e:
                self.context.log.error(f"批量获取当前价格异常: {str(e)}")
        
        # 计算每个股票的TRIX
        for symbol, hist_data in hist_data_dict.items():
            try:
                # 记录开始时间
                start_time = time.time()
                self.perf_stats['trix_calc_count'] += 1
                
                # 确保数据类型为float64
                close_prices = hist_data['close'].values.astype(np.float64)
                
                # 如果有实时价格，替换最后一个收盘价
                if symbol in current_prices:
                    current_price = current_prices[symbol]
                    if len(close_prices) > 0:
                        close_prices[-1] = current_price
                        # 记录日志
                        signal_type = "拐点" if is_reversal else "买入"
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - {symbol} 批量TRIX{signal_type}计算(周期={period})使用实时价格 {current_price} 替代当日收盘价")
                
                # 使用不同的计算方法
                use_talib_trix = self._get_config_value('USE_TALIB_TRIX', False)
                if use_talib_trix:
                    # 使用talib直接计算TRIX
                    trix = talib.TRIX(close_prices, timeperiod=period)
                elif hasattr(self, 'use_numba_trix') and self.use_numba_trix:
                    # 使用Numba加速计算TRIX
                    trix = numba_trix(close_prices, period)
                else:
                    # 使用自定义计算方法 - 向量化实现
                    ema1 = talib.EMA(close_prices, timeperiod=period)
                    ema2 = talib.EMA(ema1, timeperiod=period)
                    ema3 = talib.EMA(ema2, timeperiod=period)
                    
                    # 使用向量化操作替代循环
                    trix = np.zeros_like(close_prices)
                    # 避免除以零
                    valid_indices = (ema3[:-1] != 0)
                    # 计算有效索引的TRIX值
                    trix_values = (ema3[1:] - ema3[:-1]) / ema3[:-1] * 100
                    # 将计算结果赋值给有效索引位置
                    trix[1:][valid_indices] = trix_values[valid_indices]
                
                # 根据是否为拐点信号，生成不同的信号
                trix_signal = False
                if is_reversal:
                    # 拐点信号需要至少4个有效值
                    if len(trix) >= 4 and not np.isnan(trix[-1]) and not np.isnan(trix[-2]) and not np.isnan(trix[-3]) and not np.isnan(trix[-4]):
                        # 拐点信号：昨日TRIX > 前日TRIX 且 前日TRIX < 大前日TRIX
                        trix_signal = (trix[-2] > trix[-3]) and (trix[-3] < trix[-4])
                else:
                    # 普通TRIX信号需要至少3个有效值
                    if len(trix) >= 3 and not np.isnan(trix[-1]) and not np.isnan(trix[-2]) and not np.isnan(trix[-3]):
                        # 普通信号：当日TRIX > 昨日TRIX且昨日TRIX < 前日TRIX
                        trix_signal = (trix[-1] > trix[-2] and trix[-2] < trix[-3])
                
                # 更新缓存
                cache_key = (symbol, f'{cache_key_prefix}_{period}')
                self.indicator_cache[cache_key] = (self.context.now, (trix, trix_signal))
                
                # 更新长期缓存
                long_cache_key = (symbol, f'{cache_key_prefix}_{period}', today)
                self.long_term_cache[long_cache_key] = (today, (trix, trix_signal))
                
                # 添加到结果
                results[symbol] = (trix, trix_signal, True)
                
                # 记录计算耗时
                self.perf_stats['trix_calc_time'] += time.time() - start_time
            except Exception as e:
                self.context.log.error(f"计算{symbol}的TRIX指标异常: {str(e)}")
                results[symbol] = (None, False, False)
        
        return results
    
    def calculate_ma_cross(self, symbol):
        """
        计算单个股票的均线交叉指标
        
        参数:
        - symbol: 股票代码
        
        返回:
        - ma_values: (MA短期, MA中期, MA长期)
        - ma_cross_signal: 均线交叉信号（True表示买入信号）
        - success: 是否计算成功
        """
        # 检查长期缓存
        today = self.context.now.strftime('%Y-%m-%d')
        long_cache_key = (symbol, 'ma_cross', today)
        if long_cache_key in self.long_term_cache:
            cache_date, (ma_values, ma_cross_signal) = self.long_term_cache[long_cache_key]
            if cache_date == today:
                return ma_values, ma_cross_signal, True
        
        # 检查短期缓存
        cache_key = (symbol, 'ma_cross')
        if cache_key in self.indicator_cache:
            timestamp, (ma_values, ma_cross_signal) = self.indicator_cache[cache_key]
            
            # 检查缓存是否在有效期内
            if (self.context.now - timestamp).total_seconds() < self.cache_expires_seconds:
                return ma_values, ma_cross_signal, True
        
        try:
            # 记录开始时间
            start_time = time.time()
            self.perf_stats['ma_calc_count'] += 1
            
            # 获取历史数据
            if hasattr(self.context, 'history_data_manager'):
                hist_data = self.context.history_data_manager.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=self.ma_long_period + 5,  # 确保足够的数据
                    fields='close'
                )
            else:
                # 兼容原始数据获取
                hist_data = self.context.data_fetcher.get_history_data(
                    symbol=symbol,
                    frequency='1d',
                    count=self.ma_long_period + 5,
                    fields='close'
                )
            
            if hist_data is None or hist_data.empty or len(hist_data) < self.ma_long_period:
                return None, False, False
                
            # 计算均线
            close_prices = hist_data['close'].values.astype(np.float64)
            
            # 使用talib一次性计算所有均线，避免重复计算
            ma_short = talib.SMA(close_prices, timeperiod=self.ma_short_period)[-1]
            ma_mid = talib.SMA(close_prices, timeperiod=self.ma_mid_period)[-1]
            ma_long = talib.SMA(close_prices, timeperiod=self.ma_long_period)[-1]
            
            # 均线交叉买入信号：短期均线 > 中期均线 > 长期均线
            ma_cross_signal = (ma_short > ma_mid) and (ma_mid > ma_long)
            
            ma_values = (ma_short, ma_mid, ma_long)
            
            # 更新短期缓存
            self.indicator_cache[cache_key] = (self.context.now, (ma_values, ma_cross_signal))
            
            # 更新长期缓存
            self.long_term_cache[long_cache_key] = (today, (ma_values, ma_cross_signal))
            
            # 记录计算耗时
            self.perf_stats['ma_calc_time'] += time.time() - start_time
            
            return ma_values, ma_cross_signal, True
                
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 计算{symbol}的均线交叉指标异常: {str(e)}")
            return None, False, False
    
    def calculate_batch_ma_cross(self, symbols):
        """
        批量计算多只股票的均线交叉指标
        
        参数:
        - symbols: 股票代码列表
        
        返回:
        - dict: {symbol: (ma_values, ma_cross_signal, success)}
        """
        results = {}
        missing_symbols = []
        today = self.context.now.strftime('%Y-%m-%d')
        
        # 从缓存中检索数据
        for symbol in symbols:
            # 首先检查长期缓存
            long_cache_key = (symbol, 'ma_cross', today)
            if long_cache_key in self.long_term_cache:
                cache_date, (ma_values, ma_cross_signal) = self.long_term_cache[long_cache_key]
                if cache_date == today:
                    results[symbol] = (ma_values, ma_cross_signal, True)
                    continue
            
            # 然后检查短期缓存
            cache_key = (symbol, 'ma_cross')
            if cache_key in self.indicator_cache:
                timestamp, (ma_values, ma_cross_signal) = self.indicator_cache[cache_key]
                
                # 检查缓存是否在有效期内
                if (self.context.now - timestamp).total_seconds() < self.cache_expires_seconds:
                    results[symbol] = (ma_values, ma_cross_signal, True)
                    continue
            
            missing_symbols.append(symbol)
        
        if not missing_symbols:
            return results
        
        # 批量获取历史数据
        hist_data_dict = {}
        if hasattr(self.context, 'history_data_manager'):
            # 使用历史数据管理器批量获取
            hist_data_dict = self.context.history_data_manager.get_batch_history(
                symbols=missing_symbols,
                frequency='1d',
                count=self.ma_long_period + 5,
                fields='close'
            )
        else:
            # 逐个获取历史数据
            for symbol in missing_symbols:
                try:
                    hist_data = self.context.data_fetcher.get_history_data(
                        symbol=symbol,
                        frequency='1d',
                        count=self.ma_long_period + 5,
                        fields='close'
                    )
                    if hist_data is not None and not hist_data.empty and len(hist_data) >= self.ma_long_period:
                        hist_data_dict[symbol] = hist_data
                except Exception as e:
                    self.context.log.error(f"获取{symbol}历史数据异常: {str(e)}")
        
        # 计算均线交叉指标
        for symbol, hist_data in hist_data_dict.items():
            try:
                # 记录开始时间
                start_time = time.time()
                self.perf_stats['ma_calc_count'] += 1
                
                close_prices = hist_data['close'].values.astype(np.float64)
                
                # 使用talib一次性计算所有均线，避免重复计算
                ma_short = talib.SMA(close_prices, timeperiod=self.ma_short_period)[-1]
                ma_mid = talib.SMA(close_prices, timeperiod=self.ma_mid_period)[-1]
                ma_long = talib.SMA(close_prices, timeperiod=self.ma_long_period)[-1]
                
                # 均线交叉买入信号：短期均线 > 中期均线 > 长期均线
                ma_cross_signal = (ma_short > ma_mid) and (ma_mid > ma_long)
                
                ma_values = (ma_short, ma_mid, ma_long)
                
                # 更新缓存
                self.indicator_cache[(symbol, 'ma_cross')] = (self.context.now, (ma_values, ma_cross_signal))
                
                # 更新长期缓存
                self.long_term_cache[(symbol, 'ma_cross', today)] = (today, (ma_values, ma_cross_signal))
                
                results[symbol] = (ma_values, ma_cross_signal, True)
                
                # 记录计算耗时
                self.perf_stats['ma_calc_time'] += time.time() - start_time
            except Exception as e:
                self.context.log.error(f"计算{symbol}的均线交叉指标异常: {str(e)}")
                results[symbol] = (None, False, False)
        
        return results
    
    def calculate_batch_buy_signals(self, symbols):
        """
        批量计算多只股票的买入信号
        
        参数:
        - symbols: 股票代码列表
        
        返回:
        - dict: {symbol: {signal_info}}
        """
        if not symbols:
            return {}
            
        # 记录开始时间
        start_time = time.time()
        self.perf_stats['signal_calc_count'] += 1
            
        # 检查是否使用并行计算
        if self.enable_parallel and len(symbols) > 10:
            return self._parallel_calculate_batch_buy_signals(symbols)
        
        # 使用长期缓存检查今日是否已经计算过
        today = self.context.now.strftime('%Y-%m-%d')
        cached_results = {}
        symbols_to_calculate = []
        
        # 先检查长期缓存
        for symbol in symbols:
            cache_key = (symbol, 'buy_signal', today)
            if cache_key in self.long_term_cache:
                cache_date, result = self.long_term_cache[cache_key]
                
                # 如果是今天的缓存，直接使用
                if cache_date == today:
                    cached_results[symbol] = result
                    continue
            
            # 需要计算的股票
            symbols_to_calculate.append(symbol)
        
        # 如果所有股票都已经计算过，直接返回缓存结果
        if not symbols_to_calculate:
            # 记录计算耗时
            self.perf_stats['signal_calc_time'] += time.time() - start_time
            return cached_results
        
        # 批量计算需要的信号
        results = self._calculate_batch_signals_subset(symbols_to_calculate)
        
        # 合并缓存和新计算的结果
        results.update(cached_results)
        
        # 记录计算耗时
        self.perf_stats['signal_calc_time'] += time.time() - start_time
        
        # 每100次计算输出一次性能统计
        if self.perf_stats['signal_calc_count'] % 100 == 0:
            self.log_performance_stats()
            
        return results

    def _parallel_calculate_batch_buy_signals(self, symbols):
        """
        并行批量计算多只股票的买入信号
        
        参数:
        - symbols: 股票代码列表
        
        返回:
        - dict: {symbol: {signal_info}}
        """
        try:
            import concurrent.futures
            
            # 分批计算，每批次处理部分股票
            batch_size = max(10, len(symbols) // self.parallel_workers)
            batches = [symbols[i:i+batch_size] for i in range(0, len(symbols), batch_size)]
            
            results = {}
            
            # 使用线程池并行处理每批股票
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
                # 提交每批次的计算任务
                future_to_batch = {
                    executor.submit(self._calculate_batch_signals_subset, batch): i 
                    for i, batch in enumerate(batches)
                }
                
                # 收集结果
                for future in concurrent.futures.as_completed(future_to_batch):
                    batch_results = future.result()
                    results.update(batch_results)
            
            return results
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 并行计算买入信号异常: {str(e)}")
            # 如果并行计算失败，回退到串行计算
            return self.calculate_batch_buy_signals(symbols)
    

            

    
    def _calculate_batch_signals_subset(self, symbols_subset):
        """
        计算一个子集的股票信号，用于并行计算
        
        参数:
        - symbols_subset: 股票代码子集
        
        返回:
        - dict: {symbol: {signal_info}}
        """
        # 基本上是复制常规计算逻辑，但不再尝试并行
        today = self.context.now.strftime('%Y-%m-%d')
        results = {}
        symbols_to_calculate = []
        
        # 首先检查缓存
        for symbol in symbols_subset:
            cache_key = (symbol, 'buy_signal', today)
            if cache_key in self.long_term_cache:
                cache_date, result = self.long_term_cache[cache_key]
                if cache_date == today:
                    results[symbol] = result
                    continue
            symbols_to_calculate.append(symbol)
            
        if not symbols_to_calculate:
            return results
        
        # 批量获取当前价格
        current_prices = {}
        try:
            current_data = current(symbols_to_calculate)
            if current_data:
                for data in current_data:
                    current_prices[data['symbol']] = data['price']
        except Exception as e:
            self.context.log.error(f"获取批量当前价格异常: {str(e)}")
        
        # 获取配置参数
        enable_trix_buy = self._get_config_value('ENABLE_TRIX_BUY_SIGNAL', True)
        enable_trix_reversal = self._get_config_value('ENABLE_TRIX_REVERSAL_SIGNAL', True)
        enable_ma_cross_buy = self._get_config_value('ENABLE_MA_CROSS_BUY_SIGNAL', False)

        
        # 批量计算TRIX指标 - 使用优化后的批量计算
        trix_results = {}
        if enable_trix_buy:
            trix_results = self.calculate_batch_trix(symbols_to_calculate, is_reversal=False)
        
        # 批量计算TRIX拐点信号
        trix_reversal_results = {}
        if enable_trix_reversal:
            trix_reversal_results = self.calculate_batch_trix(symbols_to_calculate, is_reversal=True)
        
        # 批量计算均线交叉信号 - 使用优化后的批量计算
        ma_cross_results = {}
        if enable_ma_cross_buy:
            ma_cross_results = self.calculate_batch_ma_cross(symbols_to_calculate)
            

        
        # 对每只股票综合计算买入信号
        for symbol in symbols_to_calculate:
            try:
                # 获取当前价格
                current_price = current_prices.get(symbol)
                if current_price is None:
                    continue
                
                # 获取TRIX信号
                trix_data = trix_results.get(symbol, (None, False, False))
                _, trix_signal, trix_success = trix_data
                
                # 获取TRIX拐点信号
                trix_reversal_data = trix_reversal_results.get(symbol, (None, False, False))
                _, trix_reversal_signal, trix_reversal_success = trix_reversal_data
                
                # 获取均线交叉信号
                ma_data = ma_cross_results.get(symbol, (None, False, False))
                ma_values, ma_cross_signal, ma_success = ma_data
                

                
                # 计算最终买入信号
                final_buy_signal = False
                
                # 检查各种买入信号
                if enable_trix_buy and trix_success and trix_signal:
                    final_buy_signal = True
                    
                if enable_trix_reversal and trix_reversal_success and trix_reversal_signal:
                    final_buy_signal = True
                    
                if enable_ma_cross_buy and ma_success and ma_cross_signal:
                    final_buy_signal = True
                    

                
                # 准备结果
                result = {
                    'symbol': symbol,
                    'current_price': current_price,
                    'final_buy_signal': final_buy_signal,
                    'success': True
                }
                
                # 添加TRIX信号数据
                if trix_success and trix_data[0] is not None:
                    trix_values = trix_data[0]
                    if len(trix_values) >= 3:
                        result.update({
                            'trix_signal': trix_signal,
                            'trix_current': float(trix_values[-1]),
                            'trix_prev': float(trix_values[-2]),
                            'trix_prev2': float(trix_values[-3])
                        })
                
                # 添加TRIX拐点信号数据
                if trix_reversal_success and trix_reversal_data[0] is not None:
                    trix_reversal_values = trix_reversal_data[0]
                    if len(trix_reversal_values) >= 4:
                        result.update({
                            'trix_reversal_signal': trix_reversal_signal,
                            'trix_reversal_current': float(trix_reversal_values[-1]),
                            'trix_reversal_prev': float(trix_reversal_values[-2]),
                            'trix_reversal_prev2': float(trix_reversal_values[-3]),
                            'trix_reversal_prev3': float(trix_reversal_values[-4])
                        })
                
                # 添加MA数据
                if ma_success and ma_values is not None:
                    ma_short, ma_mid, ma_long = ma_values
                    result.update({
                        'ma_cross_signal': ma_cross_signal,
                        'ma_short': ma_short,
                        'ma_mid': ma_mid,
                        'ma_long': ma_long
                    })
                    

                
                # 更新信号缓存
                self.signal_cache[(symbol, 'buy_signal')] = (self.context.now, result)
                
                # 更新长期缓存 (整天有效)
                self.long_term_cache[(symbol, 'buy_signal', today)] = (today, result)
                
                results[symbol] = result
            except Exception as e:
                self.context.log.error(f"计算{symbol}的买入信号异常: {str(e)}")
                results[symbol] = {'symbol': symbol, 'success': False, 'error': str(e)}
        
        return results

    def clear_cache(self):
        """清除所有缓存"""
        self.signal_cache.clear()
        self.indicator_cache.clear()
        # 新增：清除长期缓存
        self.long_term_cache.clear()
        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 信号计算器缓存已清除")
    
    def _get_config_value(self, param_name, default=None):
        """从context获取配置参数值"""
        try:
            # 优先直接从context属性获取
            if hasattr(self.context, param_name):
                return getattr(self.context, param_name)
                
            # 然后从context的get_config_value方法获取
            elif hasattr(self.context, 'get_config_value'):
                return self.context.get_config_value(param_name, default)
            
            # 尝试从context._get_config_value方法获取
            elif hasattr(self.context, '_get_config_value'):
                # 确保context是对象而不是字符串
                if not isinstance(self.context, str):
                    return self.context._get_config_value(param_name, default)
            
            # 其次从context.config获取
            elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
                return getattr(self.context.config, param_name)
        except Exception as e:
            # 安全地处理任何错误
            print(f"获取配置值异常 {param_name}: {str(e)}")
            
        # 最后返回默认值
        return default 

    def log_performance_stats(self):
        """
        输出性能统计信息
        """
        if not hasattr(self, 'perf_stats'):
            return
            
        # 计算平均耗时
        trix_avg_time = self.perf_stats.get('trix_calc_time', 0) / max(1, self.perf_stats.get('trix_calc_count', 0))
        ma_avg_time = self.perf_stats.get('ma_calc_time', 0) / max(1, self.perf_stats.get('ma_calc_count', 0))
        signal_avg_time = self.perf_stats.get('signal_calc_time', 0) / max(1, self.perf_stats.get('signal_calc_count', 0))
        
        # 输出统计信息
        self.context.log.info(f"性能统计:")
        self.context.log.info(f"- TRIX计算: {self.perf_stats.get('trix_calc_count', 0)}次, 总耗时: {self.perf_stats.get('trix_calc_time', 0):.6f}秒, 平均: {trix_avg_time:.6f}秒/次")
        self.context.log.info(f"- MA计算: {self.perf_stats.get('ma_calc_count', 0)}次, 总耗时: {self.perf_stats.get('ma_calc_time', 0):.6f}秒, 平均: {ma_avg_time:.6f}秒/次")
        self.context.log.info(f"- 信号计算: {self.perf_stats.get('signal_calc_count', 0)}次, 总耗时: {self.perf_stats.get('signal_calc_time', 0):.6f}秒, 平均: {signal_avg_time:.6f}秒/次")
        
        # 计算向量化带来的性能提升
        if hasattr(self, 'original_perf') and 'signal_calc_time' in self.original_perf and self.original_perf['signal_calc_time'] > 0:
            speedup = self.original_perf['signal_calc_time'] / max(0.001, self.perf_stats.get('signal_calc_time', 0))
            self.context.log.info(f"- 向量化优化性能提升: {speedup:.2f}倍")
            self.context.log.info(f"- 优化后性能提升百分比: {(speedup-1)*100:.2f}%")
        
        # 重置计数器
        for key in self.perf_stats:
            self.perf_stats[key] = 0 

@njit
def numba_trix(close_prices, period):
    """
    使用Numba加速计算TRIX指标
    
    参数:
    - close_prices: 收盘价数组
    - period: 计算周期
    
    返回:
    - trix: TRIX值数组
    """
    # 计算三重指数移动平均
    ema1 = np.zeros_like(close_prices)
    ema2 = np.zeros_like(close_prices)
    ema3 = np.zeros_like(close_prices)
    trix = np.zeros_like(close_prices)
    
    # 初始值
    ema1[0] = close_prices[0]
    ema2[0] = ema1[0]
    ema3[0] = ema2[0]
    
    # EMA系数
    k = 2.0 / (period + 1.0)
    
    # 计算三重EMA
    for i in range(1, len(close_prices)):
        ema1[i] = close_prices[i] * k + ema1[i-1] * (1 - k)
        ema2[i] = ema1[i] * k + ema2[i-1] * (1 - k)
        ema3[i] = ema2[i] * k + ema3[i-1] * (1 - k)
        
        # 计算TRIX
        if i > 0 and ema3[i-1] != 0:
            trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
    
    return trix

@njit(parallel=True)
def numba_batch_trix(close_prices_array, period):
    """
    使用Numba并行计算多个股票的TRIX指标
    
    参数:
    - close_prices_array: 二维数组，每行是一个股票的收盘价
    - period: 计算周期
    
    返回:
    - trix_array: 二维数组，每行是一个股票的TRIX值
    """
    n_stocks = close_prices_array.shape[0]
    n_prices = close_prices_array.shape[1]
    trix_array = np.zeros_like(close_prices_array)
    
    for i in prange(n_stocks):
        trix_array[i] = numba_trix(close_prices_array[i], period)
    
    return trix_array 