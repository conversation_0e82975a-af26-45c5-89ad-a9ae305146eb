# coding=utf-8
"""
策略回测和验证系统
基于完整数据的策略回测和性能评估
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import logging
from advanced_factor_engine import AdvancedFactorEngine
from intelligent_strategy_selector import IntelligentStrategySelector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyBacktestingSystem:
    """策略回测和验证系统"""
    
    def __init__(self, db_path='data/enhanced_market_data.db'):
        self.db_path = db_path
        self.factor_engine = AdvancedFactorEngine(db_path)
        self.strategy_selector = IntelligentStrategySelector(db_path)
        
    def get_historical_data_for_backtest(self, start_date, end_date):
        """获取回测用的历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT symbol, trade_date, open_price, high_price, low_price, close_price, 
                       volume, pct_change
                FROM daily_market_data 
                WHERE trade_date BETWEEN ? AND ?
                ORDER BY trade_date, symbol
            """
            
            data = pd.read_sql_query(query, conn, params=(start_date, end_date))
            data['trade_date'] = pd.to_datetime(data['trade_date'])
            
            conn.close()
            
            logger.info(f"获取回测数据: {len(data)} 条记录，时间范围: {start_date} ~ {end_date}")
            return data
            
        except Exception as e:
            logger.error(f"获取回测数据失败: {e}")
            return pd.DataFrame()
    
    def simulate_factor_calculation_for_date(self, trade_date):
        """模拟指定日期的因子计算"""
        try:
            # 获取该日期之前的数据用于计算因子
            conn = sqlite3.connect(self.db_path)
            
            # 获取所有股票在该日期的数据
            query = """
                SELECT DISTINCT symbol 
                FROM daily_market_data 
                WHERE trade_date <= ?
            """
            
            symbols = pd.read_sql_query(query, conn, params=(trade_date,))['symbol'].tolist()
            conn.close()
            
            # 为每只股票计算因子
            factors_data = []
            
            for symbol in symbols[:5]:  # 限制数量以提高速度
                try:
                    # 获取该股票截止到指定日期的历史数据
                    factors = self.calculate_factors_for_date(symbol, trade_date)
                    if factors:
                        factors['symbol'] = symbol
                        factors['trade_date'] = trade_date
                        factors_data.append(factors)
                        
                except Exception as e:
                    logger.warning(f"计算 {symbol} 在 {trade_date} 的因子失败: {e}")
                    continue
            
            if factors_data:
                factors_df = pd.DataFrame(factors_data)
                logger.info(f"成功计算 {len(factors_df)} 只股票在 {trade_date} 的因子")
                return factors_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"模拟因子计算失败 {trade_date}: {e}")
            return pd.DataFrame()
    
    def calculate_factors_for_date(self, symbol, trade_date):
        """计算指定股票在指定日期的因子"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取该日期之前60天的数据
            query = """
                SELECT trade_date, open_price, high_price, low_price, close_price, volume
                FROM daily_market_data 
                WHERE symbol = ? AND trade_date <= ?
                ORDER BY trade_date DESC
                LIMIT 60
            """
            
            market_data = pd.read_sql_query(query, conn, params=(symbol, trade_date))
            
            if len(market_data) < 20:
                return None
            
            # 按日期排序
            market_data = market_data.sort_values('trade_date').reset_index(drop=True)
            
            # 计算技术因子
            factors = self.factor_engine.calculate_technical_factors(market_data)
            
            # 添加一些简化的基本面和情绪因子
            factors.update({
                'fundamental_score': np.random.uniform(0.3, 0.8),  # 模拟基本面评分
                'sentiment_score': np.random.uniform(0.3, 0.8),   # 模拟情绪评分
            })
            
            # 计算综合评分
            technical_score = self.calculate_simple_technical_score(factors)
            overall_score = (
                technical_score * 0.6 + 
                factors['fundamental_score'] * 0.2 + 
                factors['sentiment_score'] * 0.2
            )
            
            factors.update({
                'technical_score': technical_score,
                'overall_score': overall_score
            })
            
            conn.close()
            return factors
            
        except Exception as e:
            logger.error(f"计算因子失败 {symbol} {trade_date}: {e}")
            return None
    
    def calculate_simple_technical_score(self, factors):
        """计算简化的技术评分"""
        try:
            score = 0.5  # 默认中性评分
            
            # RSI评分
            if 'rsi_14' in factors and not np.isnan(factors['rsi_14']):
                rsi = factors['rsi_14']
                if 30 <= rsi <= 70:
                    score += 0.1 * (1 - abs(rsi - 50) / 20)
            
            # CCI评分
            if 'cci_14' in factors and not np.isnan(factors['cci_14']):
                cci = factors['cci_14']
                if -30 <= cci <= 100:
                    score += 0.1
            
            # ATR评分
            if 'atr_pct' in factors and not np.isnan(factors['atr_pct']):
                atr = factors['atr_pct']
                if 3.5 <= atr <= 6.0:
                    score += 0.1
            
            # ADX评分
            if 'adx_14' in factors and not np.isnan(factors['adx_14']):
                adx = factors['adx_14']
                if adx >= 25:
                    score += 0.1
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"计算技术评分失败: {e}")
            return 0.5
    
    def apply_strategy_selection(self, factors_df, trade_date):
        """应用策略选择逻辑"""
        if factors_df.empty:
            return pd.DataFrame()
        
        try:
            # 应用筛选条件 (放宽条件以获得更多信号)
            filtered_df = factors_df.copy()
            
            # CCI筛选 (放宽)
            filtered_df = filtered_df[
                (filtered_df['cci_14'] >= -50) & 
                (filtered_df['cci_14'] <= 150)
            ]
            
            # ATR筛选 (放宽)
            filtered_df = filtered_df[filtered_df['atr_pct'] >= 2.0]
            
            # RSI筛选
            filtered_df = filtered_df[
                (filtered_df['rsi_14'] >= 25) & 
                (filtered_df['rsi_14'] <= 75)
            ]
            
            # 综合评分筛选 (放宽)
            filtered_df = filtered_df[filtered_df['overall_score'] >= 0.5]
            
            # 按评分排序，选择前3只
            top_stocks = filtered_df.nlargest(3, 'overall_score')
            
            logger.info(f"{trade_date}: 筛选出 {len(top_stocks)} 只股票")
            
            return top_stocks
            
        except Exception as e:
            logger.error(f"策略选择失败 {trade_date}: {e}")
            return pd.DataFrame()
    
    def simulate_trades(self, selected_stocks, trade_date, holding_days=5):
        """模拟交易"""
        if selected_stocks.empty:
            return []
        
        trades = []
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            for _, stock in selected_stocks.iterrows():
                symbol = stock['symbol']
                
                # 获取买入价格 (下一个交易日开盘价)
                buy_query = """
                    SELECT open_price, close_price
                    FROM daily_market_data 
                    WHERE symbol = ? AND trade_date > ?
                    ORDER BY trade_date
                    LIMIT 1
                """
                
                buy_data = pd.read_sql_query(buy_query, conn, params=(symbol, trade_date))
                
                if buy_data.empty:
                    continue
                
                buy_price = buy_data.iloc[0]['open_price']
                
                # 获取卖出价格 (持有N天后的收盘价)
                sell_query = """
                    SELECT close_price, trade_date
                    FROM daily_market_data 
                    WHERE symbol = ? AND trade_date > ?
                    ORDER BY trade_date
                    LIMIT ?
                """
                
                sell_data = pd.read_sql_query(sell_query, conn, params=(symbol, trade_date, holding_days))
                
                if len(sell_data) < holding_days:
                    continue
                
                sell_price = sell_data.iloc[-1]['close_price']
                sell_date = sell_data.iloc[-1]['trade_date']
                
                # 计算收益
                profit_pct = (sell_price - buy_price) / buy_price * 100
                
                trade = {
                    'symbol': symbol,
                    'buy_date': trade_date,
                    'sell_date': sell_date,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'profit_pct': profit_pct,
                    'holding_days': holding_days,
                    'overall_score': stock['overall_score']
                }
                
                trades.append(trade)
            
            conn.close()
            
            logger.info(f"{trade_date}: 模拟了 {len(trades)} 笔交易")
            
            return trades
            
        except Exception as e:
            logger.error(f"模拟交易失败 {trade_date}: {e}")
            return []
    
    def run_backtest(self, start_date='2024-01-01', end_date='2024-06-30', interval_days=5):
        """运行回测"""
        logger.info(f"🚀 开始策略回测: {start_date} ~ {end_date}")
        
        all_trades = []
        
        # 生成回测日期序列
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        backtest_dates = []
        
        while current_date <= end_dt:
            backtest_dates.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=interval_days)
        
        logger.info(f"回测日期: {len(backtest_dates)} 个时间点")
        
        for i, trade_date in enumerate(backtest_dates):
            logger.info(f"回测进度: {i+1}/{len(backtest_dates)} - {trade_date}")
            
            # 1. 计算因子
            factors_df = self.simulate_factor_calculation_for_date(trade_date)
            
            # 2. 应用策略选择
            selected_stocks = self.apply_strategy_selection(factors_df, trade_date)
            
            # 3. 模拟交易
            trades = self.simulate_trades(selected_stocks, trade_date)
            
            all_trades.extend(trades)
        
        # 分析回测结果
        backtest_results = self.analyze_backtest_results(all_trades)
        
        return backtest_results
    
    def analyze_backtest_results(self, trades):
        """分析回测结果"""
        if not trades:
            logger.warning("没有交易数据可分析")
            return None
        
        trades_df = pd.DataFrame(trades)
        
        # 基本统计
        total_trades = len(trades_df)
        profitable_trades = len(trades_df[trades_df['profit_pct'] > 0])
        win_rate = profitable_trades / total_trades * 100
        
        avg_profit = trades_df['profit_pct'].mean()
        max_profit = trades_df['profit_pct'].max()
        min_profit = trades_df['profit_pct'].min()
        
        # 按月份统计
        trades_df['buy_date'] = pd.to_datetime(trades_df['buy_date'])
        trades_df['month'] = trades_df['buy_date'].dt.to_period('M')
        monthly_stats = trades_df.groupby('month')['profit_pct'].agg(['count', 'mean', lambda x: (x > 0).sum() / len(x) * 100])
        monthly_stats.columns = ['trades_count', 'avg_profit', 'win_rate']
        
        results = {
            'summary': {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_profit': avg_profit,
                'max_profit': max_profit,
                'min_profit': min_profit,
                'profitable_trades': profitable_trades,
                'losing_trades': total_trades - profitable_trades
            },
            'monthly_stats': monthly_stats,
            'trades_detail': trades_df
        }
        
        # 打印结果
        self.print_backtest_results(results)
        
        return results
    
    def print_backtest_results(self, results):
        """打印回测结果"""
        summary = results['summary']
        
        print("\n🎯 策略回测结果")
        print("=" * 60)
        
        print(f"📊 总体表现:")
        print(f"   总交易次数: {summary['total_trades']}")
        print(f"   胜率: {summary['win_rate']:.2f}%")
        print(f"   平均收益: {summary['avg_profit']:.2f}%")
        print(f"   最大收益: {summary['max_profit']:.2f}%")
        print(f"   最大亏损: {summary['min_profit']:.2f}%")
        print(f"   盈利交易: {summary['profitable_trades']} 笔")
        print(f"   亏损交易: {summary['losing_trades']} 笔")
        
        print(f"\n📈 月度表现:")
        monthly_stats = results['monthly_stats']
        for month, stats in monthly_stats.iterrows():
            print(f"   {month}: {stats['trades_count']}笔交易, 胜率{stats['win_rate']:.1f}%, 平均收益{stats['avg_profit']:.2f}%")
        
        # 显示最佳和最差交易
        trades_df = results['trades_detail']
        best_trade = trades_df.loc[trades_df['profit_pct'].idxmax()]
        worst_trade = trades_df.loc[trades_df['profit_pct'].idxmin()]
        
        print(f"\n🏆 最佳交易:")
        print(f"   {best_trade['symbol']} ({best_trade['buy_date'].strftime('%Y-%m-%d')}): +{best_trade['profit_pct']:.2f}%")
        
        print(f"\n💔 最差交易:")
        print(f"   {worst_trade['symbol']} ({worst_trade['buy_date'].strftime('%Y-%m-%d')}): {worst_trade['profit_pct']:.2f}%")

def main():
    """主函数"""
    print("📊 策略回测和验证系统启动")
    print("=" * 60)
    
    backtester = StrategyBacktestingSystem()
    
    # 运行回测
    results = backtester.run_backtest(
        start_date='2024-01-01',
        end_date='2024-03-31',  # 缩短时间范围以提高速度
        interval_days=7  # 每周回测一次
    )
    
    if results:
        print(f"\n✅ 回测完成!")
        print(f"📈 策略胜率: {results['summary']['win_rate']:.2f}%")
        print(f"💰 平均收益: {results['summary']['avg_profit']:.2f}%")
    else:
        print("❌ 回测失败")
    
    return results

if __name__ == '__main__':
    main()
