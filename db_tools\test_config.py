#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试配置加载器
"""

import os
import sys
import traceback
from pathlib import Path

# 添加父目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
print(f"系统路径: {sys.path}")

try:
    print("尝试导入配置加载器...")
    # 检查模块是否存在
    import importlib.util
    spec = importlib.util.find_spec('db_tools.config_loader')
    if spec is None:
        print("错误: 找不到模块 'db_tools.config_loader'")
        
    # 尝试导入
    from db_tools.config_loader import ConfigLoader
    print("成功导入配置加载器")
    
    config_loader = ConfigLoader()
    print("成功创建配置加载器实例")
    
    print("=" * 60)
    print("配置测试")
    print("=" * 60)
    
    # 获取数据库文件路径
    db_file = config_loader.get_db_file()
    print(f"数据库文件路径: {db_file}")
    print(f"数据库文件是否存在: {os.path.exists(db_file)}")
    
    # 获取其他配置
    print(f"备份目录: {config_loader.get_backup_dir()}")
    print(f"报告目录: {config_loader.get_report_dir()}")
    print(f"分析结果目录: {config_loader.get_analysis_dir()}")
    
    # 获取当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    print(f"项目根目录: {config_loader.root_dir}")
    
except Exception as e:
    print(f"测试过程中出错: {str(e)}")
    print("详细错误信息:")
    traceback.print_exc()

input("按回车键退出...") 