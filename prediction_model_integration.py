# coding=utf-8
"""
预测模型集成系统
集成多种机器学习模型进行股票收益预测
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import logging
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PredictionModelIntegration:
    """预测模型集成系统"""
    
    def __init__(self):
        self.models = {}
        self.ensemble_model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.model_performance = {}
        
    def initialize_models(self):
        """初始化预测模型"""
        logger.info("初始化预测模型...")
        
        self.models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            'ridge_regression': Ridge(
                alpha=1.0,
                random_state=42
            ),
            'lasso_regression': Lasso(
                alpha=0.1,
                random_state=42
            ),
            'svr': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            )
        }
        
        logger.info(f"✅ 初始化了 {len(self.models)} 个预测模型")
    
    def generate_prediction_training_data(self, n_samples=2000):
        """生成预测训练数据"""
        logger.info("生成预测模型训练数据...")
        
        np.random.seed(42)
        
        # 生成特征数据
        features = {}
        
        # 技术指标特征
        features['rsi_14'] = np.random.uniform(20, 80, n_samples)
        features['cci_14'] = np.random.uniform(-100, 100, n_samples)
        features['atr_pct'] = np.random.uniform(1, 6, n_samples)
        features['adx_14'] = np.random.uniform(15, 50, n_samples)
        features['bb_position'] = np.random.uniform(0, 100, n_samples)
        features['macd_hist'] = np.random.uniform(-1, 1, n_samples)
        features['volume_ratio'] = np.random.uniform(0.5, 3.0, n_samples)
        
        # 基本面特征
        features['pe_relative'] = np.random.uniform(0.5, 2.0, n_samples)
        features['roe_quality'] = np.random.uniform(5, 30, n_samples)
        features['revenue_growth'] = np.random.uniform(-20, 30, n_samples)
        features['financial_health'] = np.random.uniform(0.2, 0.9, n_samples)
        
        # 情绪面特征
        features['main_fund_persistence'] = np.random.uniform(0, 1, n_samples)
        features['market_attention'] = np.random.uniform(0.5, 3.0, n_samples)
        features['volume_breakthrough'] = np.random.uniform(0.8, 2.5, n_samples)
        
        # 跨市场特征
        features['industry_relative_strength'] = np.random.uniform(-0.1, 0.1, n_samples)
        features['market_beta'] = np.random.uniform(0.5, 1.8, n_samples)
        features['concept_heat'] = np.random.uniform(0, 1, n_samples)
        
        # 综合评分
        features['technical_score'] = np.random.uniform(0.2, 0.9, n_samples)
        features['fundamental_score'] = np.random.uniform(0.2, 0.9, n_samples)
        features['sentiment_score'] = np.random.uniform(0.2, 0.9, n_samples)
        features['overall_score'] = np.random.uniform(0.3, 0.8, n_samples)
        
        # 市场环境特征
        features['market_volatility'] = np.random.uniform(0.01, 0.05, n_samples)
        features['market_trend'] = np.random.uniform(-0.02, 0.02, n_samples)
        features['sector_momentum'] = np.random.uniform(-0.05, 0.05, n_samples)
        
        # 时间特征
        features['day_of_week'] = np.random.randint(1, 6, n_samples)  # 1-5 (周一到周五)
        features['month'] = np.random.randint(1, 13, n_samples)  # 1-12
        features['quarter'] = np.random.randint(1, 5, n_samples)  # 1-4
        
        # 生成目标变量 (未来1天、3天、5天收益率)
        # 基于特征的复杂非线性关系
        base_return = (
            0.3 * features['overall_score'] +
            0.2 * (features['technical_score'] * features['sentiment_score']) +
            0.15 * np.tanh(features['cci_14'] / 50) +
            0.1 * (1 / features['pe_relative']) +
            0.1 * (features['roe_quality'] / 20) +
            0.05 * features['volume_ratio'] +
            0.1 * features['market_trend']
        )
        
        # 添加非线性交互项
        interaction_term = (
            0.1 * features['atr_pct'] * features['volume_breakthrough'] +
            0.05 * features['rsi_14'] * features['bb_position'] / 1000 +
            0.05 * features['industry_relative_strength'] * features['concept_heat']
        )
        
        base_return += interaction_term
        
        # 生成不同时间窗口的收益率
        targets = {}
        for days in [1, 3, 5]:
            # 添加时间衰减和随机噪声
            noise_level = 0.02 * days  # 预测期越长，噪声越大
            targets[f'return_{days}d'] = (
                base_return * (1 - 0.1 * (days - 1)) +  # 时间衰减
                np.random.normal(0, noise_level, n_samples)  # 随机噪声
            )
            
            # 限制收益率范围
            targets[f'return_{days}d'] = np.clip(targets[f'return_{days}d'], -0.1, 0.15)
        
        # 创建DataFrame
        df = pd.DataFrame(features)
        for target_name, target_values in targets.items():
            df[target_name] = target_values
        
        self.feature_columns = list(features.keys())
        
        logger.info(f"✅ 生成预测训练数据: {len(df)} 样本, {len(self.feature_columns)} 个特征")
        
        return df
    
    def train_individual_models(self, training_data, target_column='return_3d'):
        """训练单个模型"""
        logger.info(f"训练单个预测模型，目标: {target_column}")
        
        try:
            # 准备数据
            X = training_data[self.feature_columns]
            y = training_data[target_column]
            
            # 处理缺失值
            X = X.fillna(X.mean())
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 标准化特征
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # 训练每个模型
            for model_name, model in self.models.items():
                logger.info(f"训练模型: {model_name}")
                
                try:
                    # 对于需要标准化的模型使用标准化数据
                    if model_name in ['svr', 'neural_network', 'ridge_regression', 'lasso_regression']:
                        model.fit(X_train_scaled, y_train)
                        y_pred = model.predict(X_test_scaled)
                    else:
                        model.fit(X_train, y_train)
                        y_pred = model.predict(X_test)
                    
                    # 评估模型
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)

                    # 检查异常值
                    if np.isnan(r2) or np.isinf(r2) or r2 < -10 or r2 > 1:
                        logger.warning(f"模型 {model_name} R²异常: {r2}, 设置为0")
                        r2 = 0.0

                    if np.isnan(mse) or np.isinf(mse) or mse < 0:
                        logger.warning(f"模型 {model_name} MSE异常: {mse}, 设置为1.0")
                        mse = 1.0

                    self.model_performance[model_name] = {
                        'mse': mse,
                        'mae': mae,
                        'r2': r2,
                        'rmse': np.sqrt(max(0, mse))
                    }

                    logger.info(f"  {model_name}: R² = {r2:.4f}, RMSE = {np.sqrt(max(0, mse)):.4f}")
                    
                except Exception as e:
                    logger.error(f"训练模型 {model_name} 失败: {e}")
                    continue
            
            logger.info("✅ 单个模型训练完成")
            return True
            
        except Exception as e:
            logger.error(f"训练单个模型失败: {e}")
            return False
    
    def create_ensemble_model(self, training_data, target_column='return_3d'):
        """创建集成模型"""
        logger.info("创建集成预测模型...")
        
        try:
            # 选择表现最好的模型进行集成
            if not self.model_performance:
                logger.error("没有模型性能数据，无法创建集成模型")
                return False
            
            # 按R²排序，选择前3个模型 (过滤掉异常值)
            valid_models = {k: v for k, v in self.model_performance.items()
                          if -1 <= v['r2'] <= 1 and not np.isnan(v['r2'])}

            if not valid_models:
                logger.error("没有有效的模型性能数据")
                return False

            sorted_models = sorted(valid_models.items(),
                                 key=lambda x: x[1]['r2'], reverse=True)
            
            top_models = []
            for model_name, performance in sorted_models[:3]:
                if model_name in self.models:
                    top_models.append((model_name, self.models[model_name]))
            
            if len(top_models) < 2:
                logger.error("可用模型数量不足，无法创建集成模型")
                return False
            
            # 创建投票回归器
            self.ensemble_model = VotingRegressor(
                estimators=top_models,
                weights=None  # 等权重
            )
            
            # 准备数据
            X = training_data[self.feature_columns]
            y = training_data[target_column]
            X = X.fillna(X.mean())
            
            # 训练集成模型
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 对于集成模型，使用原始数据（因为包含了不同类型的模型）
            self.ensemble_model.fit(X_train, y_train)
            
            # 评估集成模型
            y_pred = self.ensemble_model.predict(X_test)

            ensemble_mse = mean_squared_error(y_test, y_pred)
            ensemble_r2 = r2_score(y_test, y_pred)
            ensemble_mae = mean_absolute_error(y_test, y_pred)

            # 检查异常值
            if np.isnan(ensemble_r2) or np.isinf(ensemble_r2) or ensemble_r2 < -10:
                ensemble_r2 = 0.0

            self.model_performance['ensemble'] = {
                'mse': ensemble_mse,
                'mae': ensemble_mae,
                'r2': ensemble_r2,
                'rmse': np.sqrt(max(0, ensemble_mse))
            }

            logger.info(f"✅ 集成模型创建完成: R² = {ensemble_r2:.4f}")
            
            # 显示集成模型组成
            logger.info("集成模型组成:")
            for model_name, _ in top_models:
                r2_score = self.model_performance[model_name]['r2']
                logger.info(f"  - {model_name}: R² = {r2_score:.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"创建集成模型失败: {e}")
            return False
    
    def predict_stock_returns(self, factor_data):
        """预测股票收益率"""
        if self.ensemble_model is None:
            logger.error("集成模型未训练，无法进行预测")
            return None
        
        try:
            # 准备预测数据
            X = factor_data[self.feature_columns]
            X = X.fillna(X.mean())
            
            # 进行预测
            predictions = self.ensemble_model.predict(X)
            
            # 添加预测置信度（基于模型一致性）
            individual_predictions = []
            for model_name, model in self.ensemble_model.named_estimators_.items():
                if model_name in ['svr', 'neural_network', 'ridge_regression', 'lasso_regression']:
                    # 对需要标准化的模型进行标准化
                    X_scaled = self.scaler.transform(X)
                    pred = model.predict(X_scaled)
                else:
                    pred = model.predict(X)
                individual_predictions.append(pred)
            
            # 计算预测一致性（标准差越小，一致性越高）
            individual_predictions = np.array(individual_predictions)
            prediction_std = np.std(individual_predictions, axis=0)
            confidence = 1 / (1 + prediction_std)  # 转换为置信度
            
            result = pd.DataFrame({
                'predicted_return': predictions,
                'prediction_confidence': confidence,
                'prediction_std': prediction_std
            })
            
            return result
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return None
    
    def generate_model_performance_report(self):
        """生成模型性能报告"""
        if not self.model_performance:
            return "没有模型性能数据"
        
        report = "🤖 预测模型性能报告\n"
        report += "=" * 60 + "\n\n"
        
        # 按R²排序
        sorted_performance = sorted(self.model_performance.items(), 
                                  key=lambda x: x[1]['r2'], reverse=True)
        
        report += "📊 模型性能排名:\n"
        for i, (model_name, performance) in enumerate(sorted_performance, 1):
            report += f"   {i}. {model_name:20s}: "
            report += f"R² = {performance['r2']:6.4f}, "
            report += f"RMSE = {performance['rmse']:6.4f}, "
            report += f"MAE = {performance['mae']:6.4f}\n"
        
        # 最佳模型分析
        best_model = sorted_performance[0]
        report += f"\n🏆 最佳模型: {best_model[0]}\n"
        report += f"   R² = {best_model[1]['r2']:.4f} (解释了{best_model[1]['r2']*100:.1f}%的方差)\n"
        report += f"   RMSE = {best_model[1]['rmse']:.4f} (平均预测误差)\n"
        
        # 集成模型效果
        if 'ensemble' in self.model_performance:
            ensemble_perf = self.model_performance['ensemble']
            report += f"\n🔗 集成模型效果:\n"
            report += f"   R² = {ensemble_perf['r2']:.4f}\n"
            report += f"   相比最佳单模型提升: {(ensemble_perf['r2'] - best_model[1]['r2'])*100:.2f}%\n"
        
        # 模型适用性分析
        report += f"\n📈 模型适用性分析:\n"
        report += f"   - 高R²模型 (>0.3): 适合短期预测\n"
        report += f"   - 低RMSE模型: 适合风险控制\n"
        report += f"   - 集成模型: 平衡准确性和稳定性\n"
        
        return report
    
    def save_prediction_model(self, filename=None):
        """保存预测模型"""
        if filename is None:
            filename = f"prediction_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        
        try:
            import pickle
            
            model_data = {
                'ensemble_model': self.ensemble_model,
                'scaler': self.scaler,
                'feature_columns': self.feature_columns,
                'model_performance': self.model_performance
            }
            
            with open(filename, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"✅ 预测模型已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存预测模型失败: {e}")
            return None

def main():
    """主函数"""
    print("🤖 预测模型集成系统")
    print("=" * 60)
    
    # 创建预测系统
    prediction_system = PredictionModelIntegration()
    
    # 初始化模型
    prediction_system.initialize_models()
    
    # 生成训练数据
    training_data = prediction_system.generate_prediction_training_data(2000)
    
    # 训练单个模型
    success = prediction_system.train_individual_models(training_data, 'return_3d')
    
    if success:
        # 创建集成模型
        ensemble_success = prediction_system.create_ensemble_model(training_data, 'return_3d')
        
        if ensemble_success:
            # 生成性能报告
            report = prediction_system.generate_model_performance_report()
            print(f"\n{report}")
            
            # 测试预测功能
            test_data = training_data.sample(5)[prediction_system.feature_columns]
            predictions = prediction_system.predict_stock_returns(test_data)
            
            if predictions is not None:
                print(f"\n🎯 预测测试结果:")
                print("=" * 40)
                for i, (_, row) in enumerate(predictions.iterrows(), 1):
                    print(f"样本{i}: 预测收益 {row['predicted_return']:6.2%}, "
                          f"置信度 {row['prediction_confidence']:.3f}")
            
            # 保存模型
            saved_file = prediction_system.save_prediction_model()
            
            print(f"\n✅ 预测模型集成完成")
            print(f"🤖 训练了 {len(prediction_system.models)} 个基础模型")
            print(f"🔗 创建了集成预测模型")
            print(f"📊 最佳模型R²: {max(prediction_system.model_performance.values(), key=lambda x: x['r2'])['r2']:.4f}")
            if saved_file:
                print(f"💾 模型已保存到: {saved_file}")
        
        else:
            print("❌ 集成模型创建失败")
    
    else:
        print("❌ 模型训练失败")
    
    return prediction_system

if __name__ == '__main__':
    main()
