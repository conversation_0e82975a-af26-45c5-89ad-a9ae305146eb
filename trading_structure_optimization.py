
# 交易结构优化配置
# 目标: 将胜率从43.9%提升到55%+

# ==================== 卖出优先级重构 ====================

# 大幅提高固定止盈优先级 (让100%胜率的方式优先)
SELL_SIGNAL_PRIORITY = {
    'fixed_profit_stop': 1.0,            # 最高优先级 (100%胜率)
    'max_holding_days': 1.1,             # 第二优先级 (高胜率)
    'trailing_stop': 2.0,                # 大幅降低优先级 (40.6%胜率)
    'fixed_stop_loss': 3.0,              # 保持最低优先级
    'dynamic_stop_loss': 3.1,            # 保持最低优先级
}

# ==================== 固定止盈优化 ====================

# 降低固定止盈门槛 (增加使用频率)
FIXED_PROFIT_RATIO = 0.03               # 从5%降低到3%
ENABLE_FIXED_PROFIT_STOP = True

# 添加多层固定止盈
ENABLE_MULTI_LEVEL_PROFIT = True        # 启用多层止盈
PROFIT_LEVELS = [0.02, 0.03, 0.05]     # 2%, 3%, 5%多层止盈
PROFIT_LEVEL_RATIOS = [0.3, 0.4, 0.3]  # 各层级仓位比例

# ==================== 跟踪止盈优化 ====================

# 进一步优化跟踪止盈参数
TRAILING_STOP = 0.008                   # 从1%收紧到0.8%
TRAILING_STOP_ACTIVATION = 0.015        # 1.5%后启动跟踪止盈

# ==================== 多因子策略微调 ====================

# 适度降低阈值 (增加买入机会)
MULTIFACTOR_THRESHOLDS = {
    'min_overall_score': 0.12,           # 从0.15降到0.12
    'min_technical_score': 0.08,         # 从0.10降到0.08
    'min_momentum_score': 0.06,          # 从0.08降到0.06
    'min_volume_score': 0.00,            # 保持0
    'min_volatility_score': 0.00,        # 保持0
    'min_trend_score': 0.35,             # 从0.40降到0.35
    'min_buy_signal_strength': 0.00,     # 保持0
    'min_risk_adjusted_score': 0.03,     # 从0.05降到0.03
}

# ==================== 可选增强功能 ====================

# 考虑重新启用智能评分 (如果需要更高胜率)
# SMART_SCORING_CONFIG = {"enable_smart_scoring": True}

# 考虑重新启用时序分析 (如果需要更高胜率)
# enable_timeseries_analysis = True
