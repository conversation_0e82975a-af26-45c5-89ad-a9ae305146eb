# coding=utf-8
"""
增强数据收集器
在买入时收集全面的市场环境、基本面、资金流向等因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EnhancedDataCollector:
    """增强数据收集器"""
    
    def __init__(self):
        self.db_path = 'data/trades.db'
        self.market_data_cache = {}
        self.fundamental_data_cache = {}
    
    def collect_market_environment_factors(self, symbol, timestamp):
        """收集市场环境因子"""
        try:
            market_factors = {}
            
            # 1. 大盘指数相关
            market_factors.update({
                'market_index_change': self._get_market_index_change(timestamp),
                'market_index_ma5_trend': self._get_market_ma5_trend(timestamp),
                'market_volume_ratio': self._get_market_volume_ratio(timestamp),
                'market_volatility': self._get_market_volatility(timestamp),
            })
            
            # 2. 市场情绪
            market_factors.update({
                'market_sentiment_score': self._get_market_sentiment(timestamp),
                'advance_decline_ratio': self._get_advance_decline_ratio(timestamp),
                'new_high_low_ratio': self._get_new_high_low_ratio(timestamp),
                'northbound_flow': self._get_northbound_flow(timestamp),
            })
            
            # 3. 行业轮动
            sector_code = self._get_sector_code(symbol)
            market_factors.update({
                'sector_relative_strength': self._get_sector_relative_strength(sector_code, timestamp),
                'sector_momentum': self._get_sector_momentum(sector_code, timestamp),
                'hot_sector_rank': self._get_hot_sector_rank(sector_code, timestamp),
            })
            
            return market_factors
            
        except Exception as e:
            print(f"收集市场环境因子失败: {e}")
            return self._get_default_market_factors()
    
    def collect_fundamental_factors(self, symbol, timestamp):
        """收集基本面因子"""
        try:
            fundamental_factors = {}
            
            # 1. 估值指标
            fundamental_factors.update({
                'pe_ratio_current': self._get_pe_ratio(symbol, timestamp),
                'pe_ratio_ttm': self._get_pe_ratio_ttm(symbol, timestamp),
                'pb_ratio': self._get_pb_ratio(symbol, timestamp),
                'ps_ratio': self._get_ps_ratio(symbol, timestamp),
                'peg_ratio': self._get_peg_ratio(symbol, timestamp),
            })
            
            # 2. 盈利能力
            fundamental_factors.update({
                'roe_ttm': self._get_roe_ttm(symbol, timestamp),
                'roa_ttm': self._get_roa_ttm(symbol, timestamp),
                'gross_margin': self._get_gross_margin(symbol, timestamp),
                'net_margin': self._get_net_margin(symbol, timestamp),
                'eps_growth_yoy': self._get_eps_growth(symbol, timestamp),
                'revenue_growth_yoy': self._get_revenue_growth(symbol, timestamp),
            })
            
            # 3. 财务质量
            fundamental_factors.update({
                'debt_to_equity': self._get_debt_to_equity(symbol, timestamp),
                'current_ratio': self._get_current_ratio(symbol, timestamp),
                'quick_ratio': self._get_quick_ratio(symbol, timestamp),
                'operating_cash_flow': self._get_operating_cash_flow(symbol, timestamp),
                'free_cash_flow': self._get_free_cash_flow(symbol, timestamp),
            })
            
            # 4. 成长性
            fundamental_factors.update({
                'revenue_growth_3y_cagr': self._get_revenue_cagr_3y(symbol, timestamp),
                'profit_growth_3y_cagr': self._get_profit_cagr_3y(symbol, timestamp),
                'eps_growth_consistency': self._get_eps_consistency(symbol, timestamp),
            })
            
            return fundamental_factors
            
        except Exception as e:
            print(f"收集基本面因子失败: {e}")
            return self._get_default_fundamental_factors()
    
    def collect_money_flow_factors(self, symbol, timestamp):
        """收集资金流向因子"""
        try:
            money_flow_factors = {}
            
            # 1. 机构资金
            money_flow_factors.update({
                'institutional_ownership': self._get_institutional_ownership(symbol, timestamp),
                'institutional_change': self._get_institutional_change(symbol, timestamp),
                'fund_holdings_count': self._get_fund_holdings_count(symbol, timestamp),
                'qfii_holdings': self._get_qfii_holdings(symbol, timestamp),
            })
            
            # 2. 主力资金
            money_flow_factors.update({
                'main_force_inflow': self._get_main_force_inflow(symbol, timestamp),
                'large_order_ratio': self._get_large_order_ratio(symbol, timestamp),
                'block_trade_volume': self._get_block_trade_volume(symbol, timestamp),
                'smart_money_flow': self._get_smart_money_flow(symbol, timestamp),
            })
            
            # 3. 散户行为
            money_flow_factors.update({
                'retail_sentiment': self._get_retail_sentiment(symbol, timestamp),
                'retail_participation': self._get_retail_participation(symbol, timestamp),
                'small_order_ratio': self._get_small_order_ratio(symbol, timestamp),
            })
            
            return money_flow_factors
            
        except Exception as e:
            print(f"收集资金流向因子失败: {e}")
            return self._get_default_money_flow_factors()
    
    def collect_enhanced_technical_factors(self, symbol, price_data, current_price):
        """收集增强技术因子"""
        try:
            technical_factors = {}
            
            # 1. 高级技术指标
            technical_factors.update({
                'ichimoku_cloud_signal': self._calculate_ichimoku_signal(price_data),
                'parabolic_sar': self._calculate_parabolic_sar(price_data),
                'supertrend_signal': self._calculate_supertrend(price_data),
                'pivot_points': self._calculate_pivot_points(price_data),
            })
            
            # 2. 量价关系
            technical_factors.update({
                'price_volume_divergence': self._calculate_pv_divergence(price_data),
                'accumulation_distribution': self._calculate_ad_line(price_data),
                'chaikin_money_flow': self._calculate_cmf(price_data),
                'volume_weighted_price': self._calculate_vwap(price_data),
                'vwap_distance': self._calculate_vwap_distance(price_data, current_price),
            })
            
            # 3. 波动性指标
            technical_factors.update({
                'realized_volatility': self._calculate_realized_volatility(price_data),
                'volatility_skew': self._calculate_volatility_skew(price_data),
                'garch_volatility': self._calculate_garch_volatility(price_data),
            })
            
            # 4. 趋势强度
            technical_factors.update({
                'trend_intensity': self._calculate_trend_intensity(price_data),
                'trend_consistency': self._calculate_trend_consistency(price_data),
                'trend_acceleration': self._calculate_trend_acceleration(price_data),
                'aroon_oscillator': self._calculate_aroon_oscillator(price_data),
            })
            
            return technical_factors
            
        except Exception as e:
            print(f"收集增强技术因子失败: {e}")
            return self._get_default_technical_factors()
    
    def collect_event_driven_factors(self, symbol, timestamp):
        """收集事件驱动因子"""
        try:
            event_factors = {}
            
            # 1. 公司事件
            event_factors.update({
                'earnings_announcement': self._check_earnings_announcement(symbol, timestamp),
                'dividend_announcement': self._check_dividend_announcement(symbol, timestamp),
                'stock_split': self._check_stock_split(symbol, timestamp),
                'share_buyback': self._check_share_buyback(symbol, timestamp),
                'management_change': self._check_management_change(symbol, timestamp),
            })
            
            # 2. 市场事件
            event_factors.update({
                'index_inclusion': self._check_index_inclusion(symbol, timestamp),
                'analyst_upgrade': self._check_analyst_upgrade(symbol, timestamp),
                'insider_trading': self._check_insider_trading(symbol, timestamp),
            })
            
            # 3. 政策事件
            sector_code = self._get_sector_code(symbol)
            event_factors.update({
                'policy_announcement': self._check_policy_announcement(sector_code, timestamp),
                'regulatory_change': self._check_regulatory_change(sector_code, timestamp),
                'industry_policy': self._check_industry_policy(sector_code, timestamp),
            })
            
            return event_factors
            
        except Exception as e:
            print(f"收集事件驱动因子失败: {e}")
            return self._get_default_event_factors()
    
    def collect_time_series_factors(self, timestamp):
        """收集时间序列因子"""
        try:
            time_factors = {}
            
            dt = pd.to_datetime(timestamp)
            
            # 1. 季节性
            time_factors.update({
                'seasonal_pattern': self._get_seasonal_pattern(dt),
                'monthly_effect': self._get_monthly_effect(dt.month),
                'day_of_week_effect': self._get_day_of_week_effect(dt.weekday()),
                'holiday_effect': self._get_holiday_effect(dt),
                'earnings_season_effect': self._get_earnings_season_effect(dt),
            })
            
            # 2. 周期性
            time_factors.update({
                'business_cycle_stage': self._get_business_cycle_stage(dt),
                'economic_cycle': self._get_economic_cycle(dt),
                'market_cycle': self._get_market_cycle(dt),
            })
            
            return time_factors
            
        except Exception as e:
            print(f"收集时间序列因子失败: {e}")
            return self._get_default_time_factors()
    
    def collect_risk_factors(self, symbol, price_data, timestamp):
        """收集风险因子"""
        try:
            risk_factors = {}
            
            # 1. 系统性风险
            risk_factors.update({
                'beta_stability': self._calculate_beta_stability(symbol, price_data),
                'correlation_breakdown': self._calculate_correlation_breakdown(symbol, price_data),
                'systemic_risk_score': self._calculate_systemic_risk(symbol, timestamp),
            })
            
            # 2. 特异性风险
            risk_factors.update({
                'idiosyncratic_volatility': self._calculate_idiosyncratic_volatility(symbol, price_data),
                'company_specific_risk': self._calculate_company_risk(symbol, timestamp),
                'liquidity_risk': self._calculate_liquidity_risk(symbol, price_data),
            })
            
            # 3. 下行风险
            risk_factors.update({
                'downside_deviation': self._calculate_downside_deviation(price_data),
                'maximum_drawdown_risk': self._calculate_max_drawdown_risk(price_data),
                'value_at_risk': self._calculate_var(price_data),
                'expected_shortfall': self._calculate_expected_shortfall(price_data),
            })
            
            return risk_factors
            
        except Exception as e:
            print(f"收集风险因子失败: {e}")
            return self._get_default_risk_factors()
    
    def collect_all_enhanced_factors(self, symbol, price_data, current_price, timestamp):
        """收集所有增强因子"""
        print(f"🔍 收集 {symbol} 的增强因子...")
        
        all_factors = {}
        
        # 1. 市场环境因子
        market_factors = self.collect_market_environment_factors(symbol, timestamp)
        all_factors.update(market_factors)
        
        # 2. 基本面因子
        fundamental_factors = self.collect_fundamental_factors(symbol, timestamp)
        all_factors.update(fundamental_factors)
        
        # 3. 资金流向因子
        money_flow_factors = self.collect_money_flow_factors(symbol, timestamp)
        all_factors.update(money_flow_factors)
        
        # 4. 增强技术因子
        technical_factors = self.collect_enhanced_technical_factors(symbol, price_data, current_price)
        all_factors.update(technical_factors)
        
        # 5. 事件驱动因子
        event_factors = self.collect_event_driven_factors(symbol, timestamp)
        all_factors.update(event_factors)
        
        # 6. 时间序列因子
        time_factors = self.collect_time_series_factors(timestamp)
        all_factors.update(time_factors)
        
        # 7. 风险因子
        risk_factors = self.collect_risk_factors(symbol, price_data, timestamp)
        all_factors.update(risk_factors)
        
        print(f"✅ 收集完成，共 {len(all_factors)} 个增强因子")
        return all_factors

    # ==================== 市场环境因子计算方法 ====================

    def _get_market_index_change(self, timestamp):
        """获取大盘指数变化"""
        try:
            # 模拟获取大盘数据（实际应该从数据源获取）
            # 这里使用随机数模拟，实际应该调用数据接口
            return np.random.normal(0, 0.02)  # 模拟大盘涨跌幅
        except:
            return 0.0

    def _get_market_ma5_trend(self, timestamp):
        """获取大盘5日均线趋势"""
        try:
            return np.random.normal(0.001, 0.005)  # 模拟均线趋势
        except:
            return 0.0

    def _get_market_volume_ratio(self, timestamp):
        """获取大盘成交量比"""
        try:
            return np.random.uniform(0.8, 1.5)  # 模拟成交量比
        except:
            return 1.0

    def _get_market_volatility(self, timestamp):
        """获取市场波动率"""
        try:
            return np.random.uniform(0.01, 0.04)  # 模拟市场波动率
        except:
            return 0.02

    def _get_market_sentiment(self, timestamp):
        """获取市场情绪评分"""
        try:
            return np.random.uniform(0, 100)  # 模拟情绪评分
        except:
            return 50.0

    def _get_advance_decline_ratio(self, timestamp):
        """获取涨跌比"""
        try:
            return np.random.uniform(0.5, 2.0)  # 模拟涨跌比
        except:
            return 1.0

    def _get_new_high_low_ratio(self, timestamp):
        """获取新高新低比"""
        try:
            return np.random.uniform(0.5, 2.0)  # 模拟新高新低比
        except:
            return 1.0

    def _get_northbound_flow(self, timestamp):
        """获取北向资金流入"""
        try:
            return np.random.normal(0, 50)  # 模拟北向资金流入（亿元）
        except:
            return 0.0

    def _get_sector_code(self, symbol):
        """获取股票所属板块代码"""
        # 简化处理，实际应该查询股票板块信息
        return symbol[:6] + "_SECTOR"

    def _get_sector_relative_strength(self, sector_code, timestamp):
        """获取板块相对强度"""
        try:
            return np.random.uniform(-0.1, 0.1)  # 模拟板块相对强度
        except:
            return 0.0

    def _get_sector_momentum(self, sector_code, timestamp):
        """获取板块动量"""
        try:
            return np.random.uniform(-0.05, 0.05)  # 模拟板块动量
        except:
            return 0.0

    def _get_hot_sector_rank(self, sector_code, timestamp):
        """获取热门板块排名"""
        try:
            return np.random.randint(1, 100)  # 模拟板块排名
        except:
            return 50

    # ==================== 基本面因子计算方法 ====================

    def _get_pe_ratio(self, symbol, timestamp):
        """获取PE比率"""
        try:
            return np.random.uniform(10, 50)  # 模拟PE比率
        except:
            return 25.0

    def _get_pe_ratio_ttm(self, symbol, timestamp):
        """获取滚动PE比率"""
        try:
            return np.random.uniform(10, 50)  # 模拟滚动PE
        except:
            return 25.0

    def _get_pb_ratio(self, symbol, timestamp):
        """获取PB比率"""
        try:
            return np.random.uniform(1, 5)  # 模拟PB比率
        except:
            return 2.0

    def _get_ps_ratio(self, symbol, timestamp):
        """获取PS比率"""
        try:
            return np.random.uniform(1, 10)  # 模拟PS比率
        except:
            return 3.0

    def _get_peg_ratio(self, symbol, timestamp):
        """获取PEG比率"""
        try:
            return np.random.uniform(0.5, 3.0)  # 模拟PEG比率
        except:
            return 1.0

    def _get_roe_ttm(self, symbol, timestamp):
        """获取净资产收益率"""
        try:
            return np.random.uniform(0.05, 0.25)  # 模拟ROE
        except:
            return 0.10

    def _get_roa_ttm(self, symbol, timestamp):
        """获取总资产收益率"""
        try:
            return np.random.uniform(0.02, 0.15)  # 模拟ROA
        except:
            return 0.05

    def _get_gross_margin(self, symbol, timestamp):
        """获取毛利率"""
        try:
            return np.random.uniform(0.1, 0.6)  # 模拟毛利率
        except:
            return 0.3

    def _get_net_margin(self, symbol, timestamp):
        """获取净利率"""
        try:
            return np.random.uniform(0.05, 0.3)  # 模拟净利率
        except:
            return 0.1

    def _get_eps_growth(self, symbol, timestamp):
        """获取EPS增长率"""
        try:
            return np.random.uniform(-0.2, 0.5)  # 模拟EPS增长率
        except:
            return 0.1

    def _get_revenue_growth(self, symbol, timestamp):
        """获取营收增长率"""
        try:
            return np.random.uniform(-0.1, 0.3)  # 模拟营收增长率
        except:
            return 0.1

    def _get_debt_to_equity(self, symbol, timestamp):
        """获取资产负债率"""
        try:
            return np.random.uniform(0.2, 0.8)  # 模拟资产负债率
        except:
            return 0.4

    def _get_current_ratio(self, symbol, timestamp):
        """获取流动比率"""
        try:
            return np.random.uniform(1.0, 3.0)  # 模拟流动比率
        except:
            return 1.5

    def _get_quick_ratio(self, symbol, timestamp):
        """获取速动比率"""
        try:
            return np.random.uniform(0.8, 2.5)  # 模拟速动比率
        except:
            return 1.2

    def _get_operating_cash_flow(self, symbol, timestamp):
        """获取经营现金流"""
        try:
            return np.random.uniform(-1000, 5000)  # 模拟经营现金流（万元）
        except:
            return 1000.0

    def _get_free_cash_flow(self, symbol, timestamp):
        """获取自由现金流"""
        try:
            return np.random.uniform(-500, 3000)  # 模拟自由现金流（万元）
        except:
            return 500.0

    def _get_revenue_cagr_3y(self, symbol, timestamp):
        """获取3年营收复合增长率"""
        try:
            return np.random.uniform(-0.1, 0.3)  # 模拟3年营收CAGR
        except:
            return 0.1

    def _get_profit_cagr_3y(self, symbol, timestamp):
        """获取3年利润复合增长率"""
        try:
            return np.random.uniform(-0.2, 0.4)  # 模拟3年利润CAGR
        except:
            return 0.1

    def _get_eps_consistency(self, symbol, timestamp):
        """获取EPS增长一致性"""
        try:
            return np.random.uniform(0.3, 0.9)  # 模拟EPS一致性评分
        except:
            return 0.6

    # ==================== 默认值方法 ====================

    def _get_default_market_factors(self):
        """获取默认市场因子"""
        return {
            'market_index_change': 0.0,
            'market_index_ma5_trend': 0.0,
            'market_volume_ratio': 1.0,
            'market_volatility': 0.02,
            'market_sentiment_score': 50.0,
            'advance_decline_ratio': 1.0,
            'new_high_low_ratio': 1.0,
            'northbound_flow': 0.0,
            'sector_relative_strength': 0.0,
            'sector_momentum': 0.0,
            'hot_sector_rank': 50,
        }

    def _get_default_fundamental_factors(self):
        """获取默认基本面因子"""
        return {
            'pe_ratio_current': 25.0,
            'pe_ratio_ttm': 25.0,
            'pb_ratio': 2.0,
            'ps_ratio': 3.0,
            'peg_ratio': 1.0,
            'roe_ttm': 0.10,
            'roa_ttm': 0.05,
            'gross_margin': 0.3,
            'net_margin': 0.1,
            'eps_growth_yoy': 0.1,
            'revenue_growth_yoy': 0.1,
            'debt_to_equity': 0.4,
            'current_ratio': 1.5,
            'quick_ratio': 1.2,
            'operating_cash_flow': 1000.0,
            'free_cash_flow': 500.0,
            'revenue_growth_3y_cagr': 0.1,
            'profit_growth_3y_cagr': 0.1,
            'eps_growth_consistency': 0.6,
        }

    def _get_default_money_flow_factors(self):
        """获取默认资金流向因子"""
        return {
            'institutional_ownership': 0.3,
            'institutional_change': 0.0,
            'fund_holdings_count': 10,
            'qfii_holdings': 0.0,
            'main_force_inflow': 0.0,
            'large_order_ratio': 0.3,
            'block_trade_volume': 0.0,
            'smart_money_flow': 0.0,
            'retail_sentiment': 50.0,
            'retail_participation': 0.7,
            'small_order_ratio': 0.7,
        }

    def _get_default_technical_factors(self):
        """获取默认技术因子"""
        return {
            'ichimoku_cloud_signal': 0,
            'parabolic_sar': 0.0,
            'supertrend_signal': 0,
            'pivot_points': 0.0,
            'price_volume_divergence': 0,
            'accumulation_distribution': 0.0,
            'chaikin_money_flow': 0.0,
            'volume_weighted_price': 0.0,
            'vwap_distance': 0.0,
            'realized_volatility': 0.02,
            'volatility_skew': 0.0,
            'garch_volatility': 0.02,
            'trend_intensity': 0.0,
            'trend_consistency': 0.5,
            'trend_acceleration': 0.0,
            'aroon_oscillator': 0.0,
        }

    def _get_default_event_factors(self):
        """获取默认事件因子"""
        return {
            'earnings_announcement': 0,
            'dividend_announcement': 0,
            'stock_split': 0,
            'share_buyback': 0,
            'management_change': 0,
            'index_inclusion': 0,
            'analyst_upgrade': 0,
            'insider_trading': 0,
            'policy_announcement': 0,
            'regulatory_change': 0,
            'industry_policy': 0,
        }

    def _get_default_time_factors(self):
        """获取默认时间因子"""
        return {
            'seasonal_pattern': 0.0,
            'monthly_effect': 0.0,
            'day_of_week_effect': 0.0,
            'holiday_effect': 0.0,
            'earnings_season_effect': 0.0,
            'business_cycle_stage': 2,
            'economic_cycle': 2,
            'market_cycle': 2,
        }

    def _get_default_risk_factors(self):
        """获取默认风险因子"""
        return {
            'beta_stability': 1.0,
            'correlation_breakdown': 0.0,
            'systemic_risk_score': 0.5,
            'idiosyncratic_volatility': 0.02,
            'company_specific_risk': 0.5,
            'liquidity_risk': 0.5,
            'downside_deviation': 0.02,
            'maximum_drawdown_risk': 0.1,
            'value_at_risk': 0.05,
            'expected_shortfall': 0.08,
        }

    # ==================== 简化的计算方法（实际使用时需要完善） ====================

    def _get_institutional_ownership(self, symbol, timestamp):
        """获取机构持股比例"""
        return np.random.uniform(0.1, 0.8)

    def _get_institutional_change(self, symbol, timestamp):
        """获取机构持股变化"""
        return np.random.uniform(-0.1, 0.1)

    def _get_fund_holdings_count(self, symbol, timestamp):
        """获取基金持股数量"""
        return np.random.randint(0, 50)

    def _get_qfii_holdings(self, symbol, timestamp):
        """获取QFII持股"""
        return np.random.uniform(0, 0.1)

    def _get_main_force_inflow(self, symbol, timestamp):
        """获取主力资金流入"""
        return np.random.normal(0, 1000)  # 万元

    def _get_large_order_ratio(self, symbol, timestamp):
        """获取大单成交比例"""
        return np.random.uniform(0.1, 0.6)

    def _get_block_trade_volume(self, symbol, timestamp):
        """获取大宗交易量"""
        return np.random.uniform(0, 10000)  # 万股

    def _get_smart_money_flow(self, symbol, timestamp):
        """获取聪明钱流向"""
        return np.random.normal(0, 500)  # 万元

    def _get_retail_sentiment(self, symbol, timestamp):
        """获取散户情绪"""
        return np.random.uniform(0, 100)

    def _get_retail_participation(self, symbol, timestamp):
        """获取散户参与度"""
        return np.random.uniform(0.3, 0.9)

    def _get_small_order_ratio(self, symbol, timestamp):
        """获取小单比例"""
        return np.random.uniform(0.4, 0.9)
