#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和策略分析系统 - 数据分析启动脚本
此脚本直接启动数据库管理工具的数据分析功能
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

# 添加父目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 尝试导入配置加载器
try:
    from db_tools.config_loader import ConfigLoader
    config_loader = ConfigLoader()
except ImportError:
    # 如果无法导入，则创建一个简单的配置加载器
    class SimpleConfigLoader:
        def __init__(self):
            self.root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
            
        def get_db_file(self):
            return os.path.join(self.root_dir, 'data', 'trades.db')
            
        def get_analysis_dir(self):
            analysis_dir = os.path.join(self.root_dir, 'analysis')
            os.makedirs(analysis_dir, exist_ok=True)
            return analysis_dir
    
    config_loader = SimpleConfigLoader()

def find_python_executable():
    """查找Python可执行文件路径"""
    # 首先尝试使用当前Python解释器
    current_python = sys.executable
    if current_python and os.path.exists(current_python):
        return current_python
    
    # 尝试查找特定版本的Python
    python_paths = [
        # Python 3.9 路径
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe",
        # 其他可能的Python路径
        r"C:\Python39\python.exe",
        r"C:\Program Files\Python39\python.exe",
        r"C:\Program Files (x86)\Python39\python.exe",
        # 系统Python路径
        "python3.9",
        "python3",
        "python"
    ]
    
    for path in python_paths:
        try:
            # 检查Python可执行文件是否存在并可用
            if os.path.exists(path):
                return path
            elif platform.system() != "Windows":
                # 在非Windows系统上尝试使用which命令
                result = subprocess.run(["which", path], capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    return result.stdout.strip()
        except Exception:
            continue
    
    return None

def check_dependencies(python_path):
    """检查必要的依赖库是否已安装"""
    dependencies = ["seaborn", "pandas", "numpy", "matplotlib"]
    missing_deps = []
    
    for dep in dependencies:
        try:
            cmd = [python_path, "-c", f"import {dep}"]
            result = subprocess.run(cmd, capture_output=True)
            if result.returncode != 0:
                missing_deps.append(dep)
        except Exception:
            missing_deps.append(dep)
    
    return missing_deps

def install_dependencies(python_path, dependencies):
    """安装缺失的依赖库"""
    print(f"正在安装缺失的依赖库: {', '.join(dependencies)}")
    try:
        cmd = [python_path, "-m", "pip", "install"] + dependencies
        subprocess.run(cmd, check=True)
        print("依赖库安装完成！")
        return True
    except Exception as e:
        print(f"安装依赖库时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("万和策略分析系统 - 数据分析工具")
    print("=" * 60)
    
    # 获取项目根目录
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # 查找Python可执行文件
    python_path = find_python_executable()
    if not python_path:
        print("错误: 无法找到Python可执行文件。请确保已安装Python 3.6+。")
        input("按回车键退出...")
        return
    
    print(f"找到Python解释器: {python_path}")
    
    # 检查脚本文件是否存在
    script_path = os.path.join(root_dir, "scripts", "db_advanced_tools.py")
    if not os.path.exists(script_path):
        print(f"错误: 找不到数据库管理工具脚本 '{script_path}'")
        input("按回车键退出...")
        return
    
    # 检查依赖
    missing_deps = check_dependencies(python_path)
    if missing_deps:
        print(f"发现缺失的依赖库: {', '.join(missing_deps)}")
        install = input("是否自动安装这些依赖库? (y/n): ").lower()
        if install == 'y':
            if not install_dependencies(python_path, missing_deps):
                print("无法安装依赖库，请手动安装后再试。")
                input("按回车键退出...")
                return
        else:
            print("请手动安装缺失的依赖库后再试。")
            input("按回车键退出...")
            return
    
    # 获取数据库文件路径
    db_file = config_loader.get_db_file()
    print(f"数据库文件路径: {db_file}")
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_file):
        print(f"警告: 数据库文件 '{db_file}' 不存在")
        create_new = input("是否创建新的数据库文件? (y/n): ").lower()
        if create_new != 'y':
            print("无法继续，请确保数据库文件存在。")
            input("按回车键退出...")
            return
    
    # 获取分析结果输出目录
    analysis_dir = config_loader.get_analysis_dir()
    print(f"分析结果将保存在: {analysis_dir}")
    
    # 启动数据分析工具
    print("\n正在启动数据分析工具...")
    try:
        # 切换到项目根目录，确保相对路径正确
        os.chdir(root_dir)
        
        # 直接使用--analyze参数启动数据分析功能
        cmd = [python_path, script_path, "--interactive"]
        process = subprocess.Popen(cmd)
        
        print("\n数据分析工具已启动。")
        print("请在交互界面中选择选项 2 进入数据分析功能。")
        
        # 等待进程结束
        process.wait()
    except Exception as e:
        print(f"启动数据分析工具时出错: {str(e)}")
    
    print("\n数据分析工具已关闭。")
    input("按回车键退出...")

if __name__ == "__main__":
    main() 