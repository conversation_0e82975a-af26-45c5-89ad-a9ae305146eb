
# 基于因子分析的反转组合C策略实现
# MACD负值 + 布林带下轨 + 高成交量
# 预期胜率: 30%+, 实际胜率: 60.0%

def apply_反转组合c_strategy(data, context):
    """
    应用反转组合C策略
    MACD负值 + 布林带下轨 + 高成交量
    """
    # 初始化信号
    buy_signal = False
    signal_reason = ""
    
    # 检查数据有效性
    if data is None or len(data) < 30:
        return False, "数据不足"
    
    try:
        # 计算技术指标
        from enhanced_factor_engine import EnhancedFactorEngine
        factor_engine = EnhancedFactorEngine(context)
        factors = factor_engine.calculate_all_factors(data, data.index[-1])
        
        # 应用反转组合C条件
        # 检查macd < -0.12条件
        if "macd" not in factors:
            return False, "macd指标缺失"
        
        if not (factors["macd"] < -0.12):
            return False, "macd = {:.2f} 不满足 < -0.12".format(factors["macd"])
        
        # 检查bb_position < 20条件
        if "bb_position" not in factors:
            return False, "bb_position指标缺失"
        
        if not (factors["bb_position"] < 20):
            return False, "bb_position = {:.2f} 不满足 < 20".format(factors["bb_position"])
        
        # 检查relative_volume > 1.5条件
        if "relative_volume" not in factors:
            return False, "relative_volume指标缺失"
        
        if not (factors["relative_volume"] > 1.5):
            return False, "relative_volume = {:.2f} 不满足 > 1.5".format(factors["relative_volume"])
        
        # 所有条件满足，生成买入信号
        buy_signal = True
        signal_reason = "MACD负值 + 布林带下轨 + 高成交量信号触发"
        
        # 记录关键指标值
        signal_reason += ", macd={:.2f}".format(factors["macd"])
        
        signal_reason += ", bb_position={:.2f}".format(factors["bb_position"])
        
        signal_reason += ", relative_volume={:.2f}".format(factors["relative_volume"])
        
        return buy_signal, signal_reason
        
    except Exception as e:
        context.log.error(f"{context.now.strftime('%Y-%m-%d %H:%M:%S')} - ❌ 应用反转组合C策略失败: {str(e)}")
        return False, f"策略应用异常: {str(e)}"
