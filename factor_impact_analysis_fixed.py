# coding=utf-8
"""
因子影响力深度分析 (修复版)
分析各因子对胜率、收益率等关键指标的实际影响
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_factor_impact_on_performance():
    """分析因子对表现的影响"""
    print('📊 因子影响力深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 简化查询，获取最新的交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'SELL' 
        AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        
        # 获取对应的买入数据
        buy_query = """
        SELECT * FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        conn.close()
        
        print(f'📈 卖出记录: {len(df)} 条')
        print(f'📈 买入记录: {len(buy_df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有卖出记录')
            return None, None, None
        
        # 基础表现统计
        total_trades = len(df)
        profitable_trades = len(df[df['net_profit_pct_sell'] > 0])
        overall_win_rate = profitable_trades / total_trades * 100
        overall_avg_profit = df['net_profit_pct_sell'].mean()
        
        print(f'\n📊 整体表现基准:')
        print(f'   总交易数: {total_trades}')
        print(f'   整体胜率: {overall_win_rate:.1f}%')
        print(f'   平均收益: {overall_avg_profit:.2f}%')
        
        # 尝试匹配买入数据获取因子信息
        matched_data = []
        
        for _, sell_row in df.iterrows():
            symbol = sell_row['symbol']
            sell_time = pd.to_datetime(sell_row['timestamp'])
            
            # 找到对应的买入记录
            symbol_buys = buy_df[buy_df['symbol'] == symbol]
            if len(symbol_buys) > 0:
                # 找最近的买入记录
                symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
                recent_buy = symbol_buys[symbol_buys['timestamp'] < sell_time].iloc[-1] if len(symbol_buys[symbol_buys['timestamp'] < sell_time]) > 0 else symbol_buys.iloc[-1]
                
                # 合并数据
                combined_row = {
                    'symbol': symbol,
                    'net_profit_pct_sell': sell_row['net_profit_pct_sell'],
                    'sell_reason': sell_row.get('sell_reason', ''),
                    'cci': recent_buy.get('cci'),
                    'rsi': recent_buy.get('rsi'),
                    'adx': recent_buy.get('adx'),
                    'macd_hist': recent_buy.get('macd_hist'),
                    'atr_pct': recent_buy.get('atr_pct'),
                    'bb_width': recent_buy.get('bb_width'),
                    'bb_position': recent_buy.get('bb_position'),
                }
                matched_data.append(combined_row)
        
        matched_df = pd.DataFrame(matched_data)
        print(f'📊 成功匹配: {len(matched_df)} 条交易')
        
        return matched_df, overall_win_rate, overall_avg_profit
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None

def analyze_individual_factor_impact(df, overall_win_rate, overall_avg_profit):
    """分析单个因子影响"""
    print(f'\n🎯 单因子影响分析')
    print('=' * 50)
    
    factors_analysis = {}
    
    # 定义因子分析配置
    factor_configs = {
        'cci': {
            'name': 'CCI',
            'ranges': [
                ('极低', lambda x: x < 0),
                ('低', lambda x: (x >= 0) & (x < 25)),
                ('中', lambda x: (x >= 25) & (x < 75)),
                ('高', lambda x: (x >= 75) & (x < 150)),
                ('极高', lambda x: x >= 150)
            ]
        },
        'rsi': {
            'name': 'RSI',
            'ranges': [
                ('超卖', lambda x: x < 30),
                ('偏低', lambda x: (x >= 30) & (x < 45)),
                ('中性', lambda x: (x >= 45) & (x < 65)),
                ('偏高', lambda x: (x >= 65) & (x < 80)),
                ('超买', lambda x: x >= 80)
            ]
        },
        'adx': {
            'name': 'ADX',
            'ranges': [
                ('弱趋势', lambda x: x < 20),
                ('中趋势', lambda x: (x >= 20) & (x < 30)),
                ('强趋势', lambda x: (x >= 30) & (x < 45)),
                ('极强趋势', lambda x: x >= 45)
            ]
        },
        'macd_hist': {
            'name': 'MACD柱',
            'ranges': [
                ('负值', lambda x: x < 0),
                ('小正值', lambda x: (x >= 0) & (x <= 0.005)),
                ('中正值', lambda x: (x > 0.005) & (x <= 0.02)),
                ('大正值', lambda x: x > 0.02)
            ]
        },
        'atr_pct': {
            'name': 'ATR',
            'ranges': [
                ('低波动', lambda x: x < 2.5),
                ('中波动', lambda x: (x >= 2.5) & (x < 4.5)),
                ('高波动', lambda x: x >= 4.5)
            ]
        }
    }
    
    print(f'因子     区间        样本数  胜率%   收益%   胜率差异  收益差异  影响评分')
    print(f'-' * 80)
    
    for factor_key, config in factor_configs.items():
        if factor_key not in df.columns:
            continue
            
        factor_data = df.dropna(subset=[factor_key])
        if len(factor_data) == 0:
            continue
        
        factor_name = config['name']
        ranges = config['ranges']
        
        factor_results = []
        
        for range_name, range_func in ranges:
            try:
                range_data = factor_data[range_func(factor_data[factor_key])]
                
                if len(range_data) >= 5:  # 降低最小样本要求
                    win_rate = (range_data['net_profit_pct_sell'] > 0).mean() * 100
                    avg_profit = range_data['net_profit_pct_sell'].mean()
                    sample_count = len(range_data)
                    
                    win_rate_diff = win_rate - overall_win_rate
                    profit_diff = avg_profit - overall_avg_profit
                    
                    # 影响评分：胜率差异权重60%，收益差异权重40%
                    impact_score = win_rate_diff * 0.6 + profit_diff * 10 * 0.4
                    
                    factor_results.append({
                        'range': range_name,
                        'sample_count': sample_count,
                        'win_rate': win_rate,
                        'avg_profit': avg_profit,
                        'win_rate_diff': win_rate_diff,
                        'profit_diff': profit_diff,
                        'impact_score': impact_score
                    })
                    
                    print(f'{factor_name:<8} {range_name:<10} {sample_count:6d} {win_rate:6.1f} {avg_profit:7.2f} {win_rate_diff:8.1f} {profit_diff:8.2f} {impact_score:8.1f}')
            
            except Exception as e:
                continue
        
        # 找出该因子的最佳区间
        if factor_results:
            best_range = max(factor_results, key=lambda x: x['impact_score'])
            worst_range = min(factor_results, key=lambda x: x['impact_score'])
            
            factors_analysis[factor_key] = {
                'name': factor_name,
                'results': factor_results,
                'best_range': best_range,
                'worst_range': worst_range,
                'overall_impact': best_range['impact_score'] - worst_range['impact_score']
            }
        
        print()  # 空行分隔
    
    return factors_analysis

def rank_factors_by_impact(factors_analysis):
    """按影响力排序因子"""
    print(f'\n🏆 因子影响力排序')
    print('=' * 50)
    
    # 按整体影响力排序
    sorted_factors = sorted(
        factors_analysis.items(),
        key=lambda x: x[1]['overall_impact'],
        reverse=True
    )
    
    print(f'排名  因子     影响力评分  最佳区间        最佳胜率  最差区间        最差胜率')
    print(f'-' * 80)
    
    optimization_priorities = []
    
    for i, (factor_key, analysis) in enumerate(sorted_factors, 1):
        factor_name = analysis['name']
        overall_impact = analysis['overall_impact']
        best_range = analysis['best_range']
        worst_range = analysis['worst_range']
        
        print(f'{i:2d}   {factor_name:<8} {overall_impact:8.1f}     {best_range["range"]:<12} {best_range["win_rate"]:7.1f}% {worst_range["range"]:<12} {worst_range["win_rate"]:7.1f}%')
        
        # 确定优化优先级
        if overall_impact > 8 and best_range['sample_count'] > 10:
            priority = 'HIGH'
        elif overall_impact > 4 and best_range['sample_count'] > 5:
            priority = 'MEDIUM'
        else:
            priority = 'LOW'
        
        optimization_priorities.append({
            'factor': factor_key,
            'name': factor_name,
            'priority': priority,
            'impact': overall_impact,
            'best_range': best_range,
            'worst_range': worst_range,
            'current_performance': best_range['win_rate']
        })
    
    return optimization_priorities

def generate_optimization_recommendations(optimization_priorities, overall_win_rate):
    """生成优化建议"""
    print(f'\n🚀 优化建议')
    print('=' * 50)
    
    high_priority = [f for f in optimization_priorities if f['priority'] == 'HIGH']
    medium_priority = [f for f in optimization_priorities if f['priority'] == 'MEDIUM']
    
    print(f'📊 当前基准胜率: {overall_win_rate:.1f}%')
    
    if high_priority:
        print(f'\n🔥 高优先级优化 (立即执行):')
        for i, factor in enumerate(high_priority, 1):
            print(f'   {i}. {factor["name"]}因子优化')
            print(f'      最佳区间: {factor["best_range"]["range"]} (胜率{factor["best_range"]["win_rate"]:.1f}%)')
            print(f'      最差区间: {factor["worst_range"]["range"]} (胜率{factor["worst_range"]["win_rate"]:.1f}%)')
            print(f'      影响力: {factor["impact"]:.1f}分')
            print(f'      建议: 调整配置偏向最佳区间，避免最差区间')
            
            # 给出具体配置建议
            if factor['factor'] == 'cci':
                if factor['best_range']['range'] == '中':
                    print(f'      具体配置: CCI [25, 75]')
                elif factor['best_range']['range'] == '高':
                    print(f'      具体配置: CCI [75, 150]')
                elif factor['best_range']['range'] == '低':
                    print(f'      具体配置: CCI [0, 25]')
            
            elif factor['factor'] == 'rsi':
                if factor['best_range']['range'] == '超买':
                    print(f'      具体配置: RSI [80, 100]')
                elif factor['best_range']['range'] == '偏高':
                    print(f'      具体配置: RSI [65, 80]')
                elif factor['best_range']['range'] == '中性':
                    print(f'      具体配置: RSI [45, 65]')
            
            elif factor['factor'] == 'adx':
                if factor['best_range']['range'] == '强趋势':
                    print(f'      具体配置: ADX [30, 45]')
                elif factor['best_range']['range'] == '极强趋势':
                    print(f'      具体配置: ADX [45, 100]')
            
            print()
    
    if medium_priority:
        print(f'\n📊 中优先级优化 (后续考虑):')
        for i, factor in enumerate(medium_priority, 1):
            print(f'   {i}. {factor["name"]} - 影响力{factor["impact"]:.1f}分')
    
    if not high_priority and not medium_priority:
        print(f'\n📊 当前因子配置相对合理')
        print(f'   建议: 保持现有配置，关注其他优化方向')
    
    return high_priority

def main():
    """主函数"""
    print('🚀 因子影响力深度分析 (修复版)')
    print('=' * 60)
    
    print('🎯 目标: 基于实际数据分析各因子影响，制定科学优化计划')
    print('📊 当前胜率: ~44% (回退后恢复正常)')
    
    # 获取数据并分析整体表现
    result = analyze_factor_impact_on_performance()
    
    if result[0] is not None:
        df, overall_win_rate, overall_avg_profit = result
        
        # 分析单个因子影响
        factors_analysis = analyze_individual_factor_impact(df, overall_win_rate, overall_avg_profit)
        
        # 按影响力排序因子
        optimization_priorities = rank_factors_by_impact(factors_analysis)
        
        # 生成优化建议
        high_priority_factors = generate_optimization_recommendations(optimization_priorities, overall_win_rate)
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print('✅ 因子影响力分析完成')
        print(f'📊 发现 {len(high_priority_factors)} 个高优先级优化机会')
        print('✅ 具体优化建议已生成')
        
        if high_priority_factors:
            print(f'\n🚀 建议立即优化: {high_priority_factors[0]["name"]}因子')
            print(f'💡 原则: 单因子优化，立即验证，谨慎推进')
        else:
            print(f'\n📊 当前配置已相对优化')
            print(f'💡 建议: 保持现状，关注其他优化方向')
    
    else:
        print('❌ 数据分析失败，请检查数据库')

if __name__ == '__main__':
    main()
