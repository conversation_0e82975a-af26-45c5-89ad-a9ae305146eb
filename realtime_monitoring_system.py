# coding=utf-8
"""
实时监控和预警系统
监控策略表现并提供实时预警
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import threading
import time
import json
from collections import deque

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealtimeMonitoringSystem:
    """实时监控和预警系统"""
    
    def __init__(self):
        self.monitoring_active = False
        self.performance_metrics = {}
        self.alert_rules = {}
        self.alert_history = deque(maxlen=1000)
        self.real_time_data = deque(maxlen=10000)
        self.monitoring_thread = None
        
    def initialize_alert_rules(self):
        """初始化预警规则"""
        logger.info("初始化实时预警规则...")
        
        self.alert_rules = {
            # 胜率预警
            'win_rate_alerts': {
                'critical_low': 0.35,      # 胜率低于35%严重预警
                'warning_low': 0.40,       # 胜率低于40%警告
                'target': 0.60,            # 目标胜率60%
                'lookback_trades': 10,     # 最近10笔交易
                'enabled': True
            },
            
            # 收益率预警
            'return_alerts': {
                'critical_loss': -0.05,    # 单日亏损超过5%严重预警
                'warning_loss': -0.03,     # 单日亏损超过3%警告
                'consecutive_loss_limit': 3, # 连续亏损3次预警
                'daily_target': 0.015,     # 日收益目标1.5%
                'enabled': True
            },
            
            # 风险控制预警
            'risk_alerts': {
                'max_drawdown': 0.08,      # 最大回撤8%
                'position_concentration': 0.3, # 单一持仓不超过30%
                'sector_concentration': 0.4,   # 单一行业不超过40%
                'volatility_spike': 0.05,      # 波动率突增5%
                'enabled': True
            },
            
            # 系统运行预警
            'system_alerts': {
                'signal_drought': 24,      # 24小时无信号预警
                'error_rate_limit': 0.05,  # 错误率超过5%预警
                'latency_limit': 5.0,      # 延迟超过5秒预警
                'memory_usage_limit': 0.8, # 内存使用超过80%预警
                'enabled': True
            },
            
            # 市场异常预警
            'market_alerts': {
                'extreme_volatility': 0.06, # 市场极端波动6%
                'volume_spike': 3.0,        # 成交量异常放大3倍
                'gap_threshold': 0.05,      # 跳空超过5%
                'correlation_breakdown': 0.3, # 相关性破坏
                'enabled': True
            }
        }
        
        logger.info("✅ 预警规则初始化完成")
    
    def start_monitoring(self):
        """启动实时监控"""
        if self.monitoring_active:
            logger.warning("监控已在运行中")
            return
        
        logger.info("🚀 启动实时监控系统...")
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("✅ 实时监控系统已启动")
    
    def stop_monitoring(self):
        """停止实时监控"""
        logger.info("停止实时监控系统...")
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("✅ 实时监控系统已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        logger.info("监控主循环开始...")
        
        while self.monitoring_active:
            try:
                # 更新性能指标
                self._update_performance_metrics()
                
                # 检查预警条件
                self._check_alert_conditions()
                
                # 监控系统健康状态
                self._monitor_system_health()
                
                # 等待下一次检查
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(30)  # 错误后等待30秒
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 模拟实时数据更新
            current_time = datetime.now()
            
            # 生成模拟的实时数据
            simulated_data = {
                'timestamp': current_time,
                'total_trades': len(self.real_time_data) + np.random.randint(1, 3),
                'win_rate': np.random.uniform(0.35, 0.65),
                'daily_return': np.random.normal(0.01, 0.03),
                'current_drawdown': np.random.uniform(0, 0.1),
                'active_positions': np.random.randint(0, 8),
                'system_latency': np.random.uniform(0.5, 3.0),
                'memory_usage': np.random.uniform(0.4, 0.9),
                'error_count': np.random.randint(0, 3)
            }
            
            self.real_time_data.append(simulated_data)
            
            # 计算关键指标
            recent_data = list(self.real_time_data)[-100:]  # 最近100个数据点
            
            if recent_data:
                self.performance_metrics = {
                    'current_win_rate': np.mean([d['win_rate'] for d in recent_data[-10:]]),
                    'daily_return': sum([d['daily_return'] for d in recent_data[-24:]]),  # 最近24小时
                    'max_drawdown': max([d['current_drawdown'] for d in recent_data]),
                    'avg_latency': np.mean([d['system_latency'] for d in recent_data[-10:]]),
                    'current_memory_usage': recent_data[-1]['memory_usage'],
                    'error_rate': sum([d['error_count'] for d in recent_data[-10:]]) / len(recent_data[-10:]),
                    'active_positions': recent_data[-1]['active_positions'],
                    'last_update': current_time
                }
            
        except Exception as e:
            logger.error(f"更新性能指标失败: {e}")
    
    def _check_alert_conditions(self):
        """检查预警条件"""
        try:
            if not self.performance_metrics:
                return
            
            alerts_triggered = []
            
            # 1. 检查胜率预警
            if self.alert_rules['win_rate_alerts']['enabled']:
                win_rate = self.performance_metrics.get('current_win_rate', 0)
                rules = self.alert_rules['win_rate_alerts']
                
                if win_rate < rules['critical_low']:
                    alerts_triggered.append({
                        'type': 'win_rate',
                        'level': 'critical',
                        'message': f'胜率严重偏低: {win_rate:.2%} < {rules["critical_low"]:.2%}',
                        'value': win_rate,
                        'threshold': rules['critical_low']
                    })
                elif win_rate < rules['warning_low']:
                    alerts_triggered.append({
                        'type': 'win_rate',
                        'level': 'warning',
                        'message': f'胜率偏低: {win_rate:.2%} < {rules["warning_low"]:.2%}',
                        'value': win_rate,
                        'threshold': rules['warning_low']
                    })
            
            # 2. 检查收益率预警
            if self.alert_rules['return_alerts']['enabled']:
                daily_return = self.performance_metrics.get('daily_return', 0)
                rules = self.alert_rules['return_alerts']
                
                if daily_return < rules['critical_loss']:
                    alerts_triggered.append({
                        'type': 'return',
                        'level': 'critical',
                        'message': f'单日严重亏损: {daily_return:.2%} < {rules["critical_loss"]:.2%}',
                        'value': daily_return,
                        'threshold': rules['critical_loss']
                    })
                elif daily_return < rules['warning_loss']:
                    alerts_triggered.append({
                        'type': 'return',
                        'level': 'warning',
                        'message': f'单日亏损警告: {daily_return:.2%} < {rules["warning_loss"]:.2%}',
                        'value': daily_return,
                        'threshold': rules['warning_loss']
                    })
            
            # 3. 检查风险控制预警
            if self.alert_rules['risk_alerts']['enabled']:
                drawdown = self.performance_metrics.get('max_drawdown', 0)
                rules = self.alert_rules['risk_alerts']
                
                if drawdown > rules['max_drawdown']:
                    alerts_triggered.append({
                        'type': 'risk',
                        'level': 'critical',
                        'message': f'最大回撤超限: {drawdown:.2%} > {rules["max_drawdown"]:.2%}',
                        'value': drawdown,
                        'threshold': rules['max_drawdown']
                    })
            
            # 4. 检查系统运行预警
            if self.alert_rules['system_alerts']['enabled']:
                latency = self.performance_metrics.get('avg_latency', 0)
                memory_usage = self.performance_metrics.get('current_memory_usage', 0)
                error_rate = self.performance_metrics.get('error_rate', 0)
                rules = self.alert_rules['system_alerts']
                
                if latency > rules['latency_limit']:
                    alerts_triggered.append({
                        'type': 'system',
                        'level': 'warning',
                        'message': f'系统延迟过高: {latency:.2f}s > {rules["latency_limit"]:.2f}s',
                        'value': latency,
                        'threshold': rules['latency_limit']
                    })
                
                if memory_usage > rules['memory_usage_limit']:
                    alerts_triggered.append({
                        'type': 'system',
                        'level': 'warning',
                        'message': f'内存使用过高: {memory_usage:.1%} > {rules["memory_usage_limit"]:.1%}',
                        'value': memory_usage,
                        'threshold': rules['memory_usage_limit']
                    })
                
                if error_rate > rules['error_rate_limit']:
                    alerts_triggered.append({
                        'type': 'system',
                        'level': 'critical',
                        'message': f'错误率过高: {error_rate:.2%} > {rules["error_rate_limit"]:.2%}',
                        'value': error_rate,
                        'threshold': rules['error_rate_limit']
                    })
            
            # 处理触发的预警
            for alert in alerts_triggered:
                self._handle_alert(alert)
            
        except Exception as e:
            logger.error(f"检查预警条件失败: {e}")
    
    def _handle_alert(self, alert):
        """处理预警"""
        try:
            # 添加时间戳
            alert['timestamp'] = datetime.now()
            alert['id'] = f"{alert['type']}_{alert['timestamp'].strftime('%Y%m%d_%H%M%S')}"
            
            # 检查是否为重复预警（避免频繁预警）
            recent_alerts = [a for a in self.alert_history if 
                           a['type'] == alert['type'] and 
                           a['level'] == alert['level'] and
                           (alert['timestamp'] - a['timestamp']).seconds < 300]  # 5分钟内
            
            if recent_alerts:
                return  # 跳过重复预警
            
            # 记录预警
            self.alert_history.append(alert)
            
            # 根据预警级别采取不同行动
            if alert['level'] == 'critical':
                logger.critical(f"🚨 严重预警: {alert['message']}")
                self._send_critical_alert(alert)
            elif alert['level'] == 'warning':
                logger.warning(f"⚠️ 警告: {alert['message']}")
                self._send_warning_alert(alert)
            
        except Exception as e:
            logger.error(f"处理预警失败: {e}")
    
    def _send_critical_alert(self, alert):
        """发送严重预警"""
        # 这里可以集成邮件、短信、钉钉等通知方式
        logger.critical(f"严重预警通知: {alert['message']}")
        
        # 可以在这里添加自动应急措施
        if alert['type'] == 'risk' and alert['value'] > 0.1:
            logger.critical("触发风险应急措施：建议立即停止交易")
    
    def _send_warning_alert(self, alert):
        """发送警告预警"""
        logger.warning(f"警告通知: {alert['message']}")
    
    def _monitor_system_health(self):
        """监控系统健康状态"""
        try:
            # 检查数据更新频率
            if self.real_time_data:
                last_update = self.real_time_data[-1]['timestamp']
                time_since_update = (datetime.now() - last_update).seconds
                
                if time_since_update > 300:  # 5分钟无数据更新
                    self._handle_alert({
                        'type': 'system',
                        'level': 'warning',
                        'message': f'数据更新延迟: {time_since_update}秒无新数据',
                        'value': time_since_update,
                        'threshold': 300
                    })
            
            # 检查监控线程状态
            if not self.monitoring_active:
                logger.error("监控线程异常停止")
            
        except Exception as e:
            logger.error(f"系统健康监控失败: {e}")
    
    def get_current_status(self):
        """获取当前状态"""
        try:
            status = {
                'monitoring_active': self.monitoring_active,
                'last_update': self.performance_metrics.get('last_update'),
                'performance_metrics': self.performance_metrics.copy(),
                'recent_alerts': list(self.alert_history)[-10:],  # 最近10个预警
                'system_health': {
                    'data_points': len(self.real_time_data),
                    'alert_count': len(self.alert_history),
                    'uptime': datetime.now() - (self.performance_metrics.get('last_update', datetime.now()) - timedelta(hours=1))
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取当前状态失败: {e}")
            return {}
    
    def generate_monitoring_report(self):
        """生成监控报告"""
        try:
            if not self.performance_metrics:
                return "监控系统尚未收集到足够数据"
            
            report = "📊 实时监控系统报告\n"
            report += "=" * 60 + "\n\n"
            
            # 当前性能指标
            metrics = self.performance_metrics
            report += "📈 当前性能指标:\n"
            report += f"   胜率: {metrics.get('current_win_rate', 0):.2%}\n"
            report += f"   日收益: {metrics.get('daily_return', 0):.2%}\n"
            report += f"   最大回撤: {metrics.get('max_drawdown', 0):.2%}\n"
            report += f"   活跃持仓: {metrics.get('active_positions', 0)}个\n"
            
            # 系统状态
            report += f"\n🖥️ 系统状态:\n"
            report += f"   平均延迟: {metrics.get('avg_latency', 0):.2f}秒\n"
            report += f"   内存使用: {metrics.get('current_memory_usage', 0):.1%}\n"
            report += f"   错误率: {metrics.get('error_rate', 0):.2%}\n"
            
            # 最近预警
            recent_alerts = list(self.alert_history)[-5:]
            if recent_alerts:
                report += f"\n🚨 最近预警 (最近5条):\n"
                for alert in recent_alerts:
                    level_icon = "🚨" if alert['level'] == 'critical' else "⚠️"
                    report += f"   {level_icon} {alert['timestamp'].strftime('%H:%M:%S')} - {alert['message']}\n"
            else:
                report += f"\n✅ 最近无预警\n"
            
            # 监控统计
            report += f"\n📊 监控统计:\n"
            report += f"   数据点数量: {len(self.real_time_data)}\n"
            report += f"   总预警次数: {len(self.alert_history)}\n"
            report += f"   监控状态: {'运行中' if self.monitoring_active else '已停止'}\n"
            
            return report
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
            return f"生成报告失败: {e}"
    
    def save_monitoring_data(self, filename=None):
        """保存监控数据"""
        if filename is None:
            filename = f"monitoring_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            monitoring_data = {
                'performance_metrics': self.performance_metrics,
                'alert_rules': self.alert_rules,
                'alert_history': [
                    {**alert, 'timestamp': alert['timestamp'].isoformat()}
                    for alert in list(self.alert_history)
                ],
                'real_time_data': [
                    {**data, 'timestamp': data['timestamp'].isoformat()}
                    for data in list(self.real_time_data)[-1000:]  # 保存最近1000个数据点
                ],
                'export_time': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(monitoring_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 监控数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存监控数据失败: {e}")
            return None

def main():
    """主函数"""
    print("📊 实时监控和预警系统")
    print("=" * 60)
    
    # 创建监控系统
    monitor = RealtimeMonitoringSystem()
    
    # 初始化预警规则
    monitor.initialize_alert_rules()
    
    # 启动监控
    monitor.start_monitoring()
    
    try:
        # 运行监控一段时间
        print("🚀 监控系统运行中...")
        print("按 Ctrl+C 停止监控")
        
        for i in range(30):  # 运行30次循环 (5分钟)
            time.sleep(10)
            
            # 每30秒显示一次状态
            if i % 3 == 0:
                status = monitor.get_current_status()
                if status and 'performance_metrics' in status:
                    metrics = status['performance_metrics']
                    print(f"📈 当前状态: 胜率{metrics.get('current_win_rate', 0):.2%}, "
                          f"日收益{metrics.get('daily_return', 0):.2%}, "
                          f"预警{len(status.get('recent_alerts', []))}条")
        
        # 生成监控报告
        report = monitor.generate_monitoring_report()
        print(f"\n{report}")
        
        # 保存监控数据
        saved_file = monitor.save_monitoring_data()
        
        print(f"\n✅ 实时监控系统测试完成")
        print(f"📊 收集了 {len(monitor.real_time_data)} 个数据点")
        print(f"🚨 触发了 {len(monitor.alert_history)} 次预警")
        if saved_file:
            print(f"💾 数据已保存到: {saved_file}")
    
    except KeyboardInterrupt:
        print("\n用户中断监控")
    
    finally:
        # 停止监控
        monitor.stop_monitoring()
        print("✅ 监控系统已停止")
    
    return monitor

if __name__ == '__main__':
    main()
