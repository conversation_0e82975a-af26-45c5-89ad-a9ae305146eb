# coding=utf-8
"""
数据库字段和因子分析
检查当前数据库中收集的数据和因子
"""

import sqlite3
import pandas as pd

def check_database_structure():
    """检查数据库表结构"""
    print('🔍 数据库表结构分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        # 1. 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print('📊 数据库中的表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 2. 检查trades表字段
        print(f'\n📋 trades表字段分析:')
        cursor.execute("PRAGMA table_info(trades)")
        trades_columns = cursor.fetchall()
        
        print(f'  总字段数: {len(trades_columns)}')
        print(f'  字段列表:')
        
        field_categories = {
            'basic': [],      # 基础交易字段
            'technical': [],  # 技术指标
            'signal': [],     # 信号字段
            'other': []       # 其他字段
        }
        
        for i, (cid, name, type_, notnull, default, pk) in enumerate(trades_columns):
            field_info = f'{name} ({type_})'
            
            # 分类字段
            name_lower = name.lower()
            if name_lower in ['timestamp', 'symbol', 'action', 'price', 'volume']:
                field_categories['basic'].append(field_info)
            elif any(indicator in name_lower for indicator in ['ma', 'rsi', 'macd', 'kdj', 'boll', 'trix', 'cci', 'roc', 'wr', 'bias']):
                field_categories['technical'].append(field_info)
            elif 'signal' in name_lower or 'buy' in name_lower or 'sell' in name_lower:
                field_categories['signal'].append(field_info)
            else:
                field_categories['other'].append(field_info)
        
        # 显示分类结果
        for category, fields in field_categories.items():
            category_names = {
                'basic': '基础交易字段',
                'technical': '技术指标字段', 
                'signal': '信号字段',
                'other': '其他字段'
            }
            
            print(f'\n  📈 {category_names[category]} ({len(fields)}个):')
            for field in fields[:20]:  # 显示前20个
                print(f'    {field}')
            if len(fields) > 20:
                print(f'    ... 还有{len(fields)-20}个字段')
        
        # 3. 检查analysis表字段
        if 'analysis' in [table[0] for table in tables]:
            print(f'\n📋 analysis表字段分析:')
            cursor.execute("PRAGMA table_info(analysis)")
            analysis_columns = cursor.fetchall()
            
            print(f'  总字段数: {len(analysis_columns)}')
            print(f'  字段列表 (前20个):')
            for i, (cid, name, type_, notnull, default, pk) in enumerate(analysis_columns[:20]):
                print(f'    {i+1:2d}. {name} ({type_})')
            
            if len(analysis_columns) > 20:
                print(f'    ... 还有{len(analysis_columns)-20}个字段')
        
        conn.close()
        return trades_columns, field_categories
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return None, None

def check_data_completeness():
    """检查数据完整性"""
    print('\n🔍 数据完整性分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 检查trades表数据
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM trades")
        total_records = cursor.fetchone()[0]
        
        print(f'📊 trades表数据统计:')
        print(f'  总记录数: {total_records:,}条')
        
        # 检查关键字段的完整性
        key_fields = ['timestamp', 'symbol', 'action', 'price', 'volume']
        
        print(f'\n📋 关键字段完整性:')
        for field in key_fields:
            cursor.execute(f"SELECT COUNT(*) FROM trades WHERE {field} IS NOT NULL")
            not_null_count = cursor.fetchone()[0]
            completeness = (not_null_count / total_records * 100) if total_records > 0 else 0
            print(f'  {field}: {not_null_count:,}/{total_records:,} ({completeness:.1f}%)')
        
        # 检查技术指标字段的完整性
        cursor.execute("PRAGMA table_info(trades)")
        all_columns = cursor.fetchall()
        
        technical_fields = []
        for col in all_columns:
            name = col[1].lower()
            if any(indicator in name for indicator in ['ma', 'rsi', 'macd', 'kdj', 'boll', 'trix']):
                technical_fields.append(col[1])
        
        if technical_fields:
            print(f'\n📈 技术指标字段完整性 (前10个):')
            for field in technical_fields[:10]:
                cursor.execute(f"SELECT COUNT(*) FROM trades WHERE {field} IS NOT NULL")
                not_null_count = cursor.fetchone()[0]
                completeness = (not_null_count / total_records * 100) if total_records > 0 else 0
                print(f'  {field}: {not_null_count:,}/{total_records:,} ({completeness:.1f}%)')
        
        # 检查analysis表数据
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='analysis'")
        if cursor.fetchone():
            cursor.execute("SELECT COUNT(*) FROM analysis")
            analysis_records = cursor.fetchone()[0]
            print(f'\n📊 analysis表数据统计:')
            print(f'  总记录数: {analysis_records:,}条')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def check_factor_availability():
    """检查因子可用性"""
    print('\n🔍 因子可用性分析')
    print('=' * 50)
    
    try:
        # 检查signal_generator.py中的因子计算
        with open('signal_generator.py', 'r', encoding='utf-8') as f:
            signal_content = f.read()
        
        print('📊 signal_generator.py中的因子:')
        
        # 查找技术指标计算
        import re
        
        indicators = [
            (r'def.*ma.*\(', 'MA移动平均线'),
            (r'def.*rsi.*\(', 'RSI相对强弱指标'),
            (r'def.*macd.*\(', 'MACD指标'),
            (r'def.*kdj.*\(', 'KDJ指标'),
            (r'def.*boll.*\(', 'BOLL布林带'),
            (r'def.*trix.*\(', 'TRIX指标'),
            (r'def.*cci.*\(', 'CCI指标'),
            (r'def.*roc.*\(', 'ROC变动率'),
            (r'def.*wr.*\(', 'WR威廉指标'),
            (r'def.*bias.*\(', 'BIAS乖离率')
        ]
        
        found_indicators = []
        for pattern, name in indicators:
            matches = re.findall(pattern, signal_content, re.IGNORECASE)
            if matches:
                found_indicators.append(name)
                print(f'  ✅ {name}: {len(matches)}个函数')
            else:
                print(f'  ❌ {name}: 未找到')
        
        # 检查data_fetcher.py中的因子
        try:
            with open('data_fetcher.py', 'r', encoding='utf-8') as f:
                fetcher_content = f.read()
            
            print(f'\n📊 data_fetcher.py中的因子:')
            
            # 查找因子计算函数
            factor_patterns = [
                (r'def.*factor.*\(', '因子计算函数'),
                (r'def.*indicator.*\(', '指标计算函数'),
                (r'def.*technical.*\(', '技术分析函数'),
                (r'def.*calculate.*\(', '计算函数')
            ]
            
            for pattern, name in factor_patterns:
                matches = re.findall(pattern, fetcher_content, re.IGNORECASE)
                if matches:
                    print(f'  ✅ {name}: {len(matches)}个')
        
        except FileNotFoundError:
            print(f'  ❌ data_fetcher.py文件不存在')
        
        return found_indicators
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return []

def analyze_current_factor_usage():
    """分析当前因子使用情况"""
    print('\n🔍 当前因子使用情况分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取一条买入记录查看包含的因子
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM trades WHERE action = 'BUY' LIMIT 1")
        sample_record = cursor.fetchone()
        
        if sample_record:
            # 获取字段名
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            field_names = [col[1] for col in columns]
            
            print('📊 买入记录中包含的因子数据:')
            
            # 创建字段值字典
            record_dict = dict(zip(field_names, sample_record))
            
            # 分析技术指标字段
            technical_factors = []
            signal_factors = []
            other_factors = []
            
            for field, value in record_dict.items():
                field_lower = field.lower()
                
                if any(indicator in field_lower for indicator in ['ma', 'rsi', 'macd', 'kdj', 'boll', 'trix', 'cci', 'roc', 'wr', 'bias']):
                    technical_factors.append((field, value))
                elif 'signal' in field_lower or 'buy' in field_lower or 'sell' in field_lower:
                    signal_factors.append((field, value))
                elif field_lower not in ['timestamp', 'symbol', 'action', 'price', 'volume']:
                    other_factors.append((field, value))
            
            print(f'  📈 技术指标因子 ({len(technical_factors)}个):')
            for field, value in technical_factors[:10]:
                print(f'    {field}: {value}')
            if len(technical_factors) > 10:
                print(f'    ... 还有{len(technical_factors)-10}个')
            
            print(f'  🎯 信号因子 ({len(signal_factors)}个):')
            for field, value in signal_factors:
                print(f'    {field}: {value}')
            
            print(f'  📊 其他因子 ({len(other_factors)}个):')
            for field, value in other_factors[:10]:
                print(f'    {field}: {value}')
            if len(other_factors) > 10:
                print(f'    ... 还有{len(other_factors)-10}个')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')

def suggest_factor_enhancements():
    """建议因子增强"""
    print('\n💡 因子增强建议')
    print('=' * 50)
    
    print('📊 可以添加的因子类别:')
    
    factor_categories = [
        {
            'category': '价格因子',
            'factors': [
                '价格动量 (Price Momentum)',
                '价格反转 (Price Reversal)', 
                '价格波动率 (Price Volatility)',
                '价格相对位置 (Price Position)',
                '价格缺口 (Price Gap)'
            ]
        },
        {
            'category': '成交量因子',
            'factors': [
                '成交量比率 (Volume Ratio)',
                '成交量动量 (Volume Momentum)',
                '量价关系 (Price-Volume Relationship)',
                '成交量异常 (Volume Anomaly)',
                '资金流向 (Money Flow)'
            ]
        },
        {
            'category': '技术形态因子',
            'factors': [
                '支撑阻力位 (Support/Resistance)',
                '趋势强度 (Trend Strength)',
                '形态识别 (Pattern Recognition)',
                '突破信号 (Breakout Signal)',
                '背离信号 (Divergence Signal)'
            ]
        },
        {
            'category': '市场微观结构因子',
            'factors': [
                '买卖盘比例 (Bid-Ask Ratio)',
                '订单流不平衡 (Order Flow Imbalance)',
                '市场深度 (Market Depth)',
                '价格冲击 (Price Impact)',
                '流动性指标 (Liquidity Metrics)'
            ]
        },
        {
            'category': '时间序列因子',
            'factors': [
                '自相关性 (Autocorrelation)',
                '季节性 (Seasonality)',
                '周期性 (Cyclicality)',
                '长期记忆 (Long Memory)',
                '状态转换 (Regime Change)'
            ]
        }
    ]
    
    for category in factor_categories:
        print(f'\n📈 {category["category"]}:')
        for factor in category['factors']:
            print(f'  • {factor}')

def main():
    """主函数"""
    print('🔍 数据库字段和因子分析报告')
    print('=' * 60)
    
    # 检查数据库结构
    trades_columns, field_categories = check_database_structure()
    
    # 检查数据完整性
    check_data_completeness()
    
    # 检查因子可用性
    found_indicators = check_factor_availability()
    
    # 分析当前因子使用情况
    analyze_current_factor_usage()
    
    # 建议因子增强
    suggest_factor_enhancements()
    
    print(f'\n🎯 分析总结')
    print('=' * 40)
    
    if trades_columns:
        total_fields = len(trades_columns)
        technical_fields = len(field_categories.get('technical', []))
        signal_fields = len(field_categories.get('signal', []))
        
        print(f'📊 当前数据库状态:')
        print(f'  总字段数: {total_fields}个')
        print(f'  技术指标字段: {technical_fields}个')
        print(f'  信号字段: {signal_fields}个')
        print(f'  发现的技术指标: {len(found_indicators)}种')
        
        if total_fields >= 100:
            print('✅ 数据库包含丰富的因子数据')
        elif total_fields >= 50:
            print('⚠️ 数据库包含中等数量的因子数据')
        else:
            print('❌ 数据库因子数据较少，建议增加')
    
    print(f'\n💡 建议:')
    print('1. 🔍 检查是否保留了190+因子的计算逻辑')
    print('2. 📊 确认因子数据是否正确保存到数据库')
    print('3. 🚀 考虑添加更多类型的因子')
    print('4. 📈 建立因子有效性分析框架')

if __name__ == '__main__':
    main()
