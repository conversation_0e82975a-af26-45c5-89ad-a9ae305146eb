# coding=utf-8
"""
综合胜率分析工具
基于回测数据分析因子有效性和胜率
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def load_trading_data():
    """加载交易数据"""
    print('📊 加载交易数据')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 加载所有交易记录
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'✅ 成功加载 {len(df)} 条交易记录')
        
        # 分析买卖记录分布
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'📈 买入记录: {len(buy_records)} 条')
        print(f'📉 卖出记录: {len(sell_records)} 条')
        print(f'📊 买卖比例: {len(sell_records)/len(buy_records)*100:.1f}%' if len(buy_records) > 0 else '📊 买卖比例: 无卖出记录')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 加载数据失败: {e}')
        return None, None, None

def analyze_buy_sell_matching(buy_records, sell_records):
    """分析买卖记录匹配情况"""
    print(f'\n🔍 买卖记录匹配分析')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录，无法进行匹配分析')
        return None
    
    # 按股票分组分析
    buy_by_symbol = buy_records.groupby('symbol').size()
    sell_by_symbol = sell_records.groupby('symbol').size()
    
    print(f'📊 买入股票数量: {len(buy_by_symbol)} 只')
    print(f'📊 卖出股票数量: {len(sell_by_symbol)} 只')
    
    # 找出有买有卖的股票
    matched_symbols = set(buy_by_symbol.index) & set(sell_by_symbol.index)
    print(f'📊 有买有卖的股票: {len(matched_symbols)} 只')
    
    if len(matched_symbols) > 0:
        print(f'\n📋 匹配股票示例 (前10只):')
        for symbol in list(matched_symbols)[:10]:
            buy_count = buy_by_symbol[symbol]
            sell_count = sell_by_symbol[symbol]
            print(f'  {symbol}: 买入{buy_count}次, 卖出{sell_count}次')
    
    return matched_symbols

def calculate_win_rates_by_factors(buy_records, sell_records):
    """按因子计算胜率"""
    print(f'\n📈 因子胜率分析')
    print('=' * 50)
    
    if len(sell_records) == 0:
        print('⚠️ 没有卖出记录，无法计算胜率')
        return None
    
    # 技术指标字段
    factor_fields = [
        'rsi', 'macd', 'macd_signal', 'macd_hist', 'adx', 'cci', 
        'atr_pct', 'bb_width', 'bb_position', 'ma20', 'trix_buy',
        'relative_volume', 'volume_change_rate', 'distance_from_high'
    ]
    
    factor_analysis = {}
    
    for factor in factor_fields:
        if factor not in buy_records.columns:
            continue
            
        # 过滤有效数据
        valid_buys = buy_records[
            (buy_records[factor].notna()) & 
            (buy_records[factor] != 0) &
            (buy_records[factor] != '')
        ].copy()
        
        if len(valid_buys) == 0:
            continue
        
        print(f'\n📊 分析因子: {factor}')
        print(f'   有效买入记录: {len(valid_buys)} 条')
        
        # 计算因子分位数
        try:
            valid_buys[f'{factor}_quartile'] = pd.qcut(
                valid_buys[factor], 
                q=4, 
                labels=['Q1(低)', 'Q2(中低)', 'Q3(中高)', 'Q4(高)'],
                duplicates='drop'
            )
            
            # 按分位数分析胜率
            quartile_analysis = {}
            for quartile in ['Q1(低)', 'Q2(中低)', 'Q3(中高)', 'Q4(高)']:
                quartile_buys = valid_buys[valid_buys[f'{factor}_quartile'] == quartile]
                
                if len(quartile_buys) > 0:
                    # 简单胜率计算：假设有net_profit_pct_sell字段
                    if 'net_profit_pct_sell' in sell_records.columns:
                        # 匹配买卖记录计算实际胜率
                        wins = 0
                        total = 0
                        for _, buy in quartile_buys.iterrows():
                            # 查找对应的卖出记录
                            matching_sells = sell_records[
                                (sell_records['symbol'] == buy['symbol']) &
                                (sell_records['timestamp'] > buy['timestamp'])
                            ]
                            if len(matching_sells) > 0:
                                sell = matching_sells.iloc[0]
                                if pd.notna(sell['net_profit_pct_sell']) and sell['net_profit_pct_sell'] > 0:
                                    wins += 1
                                total += 1
                        
                        win_rate = wins / total * 100 if total > 0 else 0
                        quartile_analysis[quartile] = {
                            'count': len(quartile_buys),
                            'matched_trades': total,
                            'wins': wins,
                            'win_rate': win_rate,
                            'avg_factor_value': quartile_buys[factor].mean()
                        }
                    else:
                        # 如果没有实际盈亏数据，使用因子值分析
                        quartile_analysis[quartile] = {
                            'count': len(quartile_buys),
                            'avg_factor_value': quartile_buys[factor].mean(),
                            'min_factor_value': quartile_buys[factor].min(),
                            'max_factor_value': quartile_buys[factor].max()
                        }
            
            factor_analysis[factor] = quartile_analysis
            
            # 显示分析结果
            print(f'   分位数分析:')
            for quartile, data in quartile_analysis.items():
                if 'win_rate' in data:
                    print(f'     {quartile}: {data["count"]}次买入, {data["matched_trades"]}次匹配, 胜率{data["win_rate"]:.1f}%, 平均值{data["avg_factor_value"]:.4f}')
                else:
                    print(f'     {quartile}: {data["count"]}次买入, 平均值{data["avg_factor_value"]:.4f}')
                    
        except Exception as e:
            print(f'   ⚠️ 分析失败: {e}')
    
    return factor_analysis

def analyze_factor_combinations(buy_records, sell_records):
    """分析因子组合效果"""
    print(f'\n🎯 因子组合分析')
    print('=' * 50)
    
    # 定义一些有意义的因子组合
    combinations = [
        {
            'name': 'RSI超卖+MACD金叉',
            'conditions': [
                ('rsi', '<', 30),
                ('macd_hist', '>', 0)
            ]
        },
        {
            'name': 'RSI中性+ADX强趋势',
            'conditions': [
                ('rsi', '>=', 30),
                ('rsi', '<=', 70),
                ('adx', '>', 25)
            ]
        },
        {
            'name': '布林带下轨+高成交量',
            'conditions': [
                ('bb_position', '<', 0.2),
                ('relative_volume', '>', 1.5)
            ]
        },
        {
            'name': 'TRIX买入+低波动',
            'conditions': [
                ('trix_buy', '>', 0),
                ('atr_pct', '<', 3)
            ]
        }
    ]
    
    combination_results = {}
    
    for combo in combinations:
        print(f'\n📊 组合: {combo["name"]}')
        
        # 应用组合条件
        filtered_buys = buy_records.copy()
        
        valid_combo = True
        for field, operator, value in combo['conditions']:
            if field not in buy_records.columns:
                print(f'   ⚠️ 缺少字段: {field}')
                valid_combo = False
                break
            
            # 过滤有效数据
            valid_data = (filtered_buys[field].notna()) & (filtered_buys[field] != 0)
            filtered_buys = filtered_buys[valid_data]
            
            # 应用条件
            if operator == '>':
                filtered_buys = filtered_buys[filtered_buys[field] > value]
            elif operator == '<':
                filtered_buys = filtered_buys[filtered_buys[field] < value]
            elif operator == '>=':
                filtered_buys = filtered_buys[filtered_buys[field] >= value]
            elif operator == '<=':
                filtered_buys = filtered_buys[filtered_buys[field] <= value]
        
        if not valid_combo:
            continue
        
        print(f'   符合条件的买入: {len(filtered_buys)} 条')
        
        if len(filtered_buys) > 0:
            # 计算组合的平均因子值
            avg_values = {}
            for field, _, _ in combo['conditions']:
                if field in filtered_buys.columns:
                    avg_values[field] = filtered_buys[field].mean()
            
            combination_results[combo['name']] = {
                'count': len(filtered_buys),
                'avg_values': avg_values,
                'symbols': filtered_buys['symbol'].unique().tolist()[:5]  # 前5个股票
            }
            
            print(f'   平均因子值:')
            for field, avg_val in avg_values.items():
                print(f'     {field}: {avg_val:.4f}')
            
            print(f'   涉及股票: {", ".join(combination_results[combo["name"]]["symbols"])}')
    
    return combination_results

def generate_factor_ranking(factor_analysis):
    """生成因子排名"""
    print(f'\n🏆 因子有效性排名')
    print('=' * 50)
    
    if not factor_analysis:
        print('⚠️ 没有因子分析数据')
        return
    
    factor_scores = {}
    
    for factor, quartiles in factor_analysis.items():
        # 计算因子得分（基于分位数间的差异）
        if len(quartiles) >= 2:
            # 如果有胜率数据，使用胜率差异
            if 'win_rate' in list(quartiles.values())[0]:
                win_rates = [q['win_rate'] for q in quartiles.values() if q['matched_trades'] > 0]
                if len(win_rates) >= 2:
                    score = max(win_rates) - min(win_rates)  # 胜率差异
                    factor_scores[factor] = {
                        'score': score,
                        'type': 'win_rate_spread',
                        'max_win_rate': max(win_rates),
                        'min_win_rate': min(win_rates)
                    }
            else:
                # 使用因子值的分布特征
                avg_values = [q['avg_factor_value'] for q in quartiles.values()]
                if len(avg_values) >= 2:
                    score = (max(avg_values) - min(avg_values)) / (abs(np.mean(avg_values)) + 1e-6)
                    factor_scores[factor] = {
                        'score': score,
                        'type': 'value_spread',
                        'max_value': max(avg_values),
                        'min_value': min(avg_values)
                    }
    
    # 排序
    sorted_factors = sorted(factor_scores.items(), key=lambda x: x[1]['score'], reverse=True)
    
    print('📊 因子有效性排名 (按区分度排序):')
    print(f'{"排名":<4} | {"因子名称":<15} | {"得分":<8} | {"类型":<12} | {"详情"}')
    print('-' * 70)
    
    for i, (factor, data) in enumerate(sorted_factors, 1):
        if data['type'] == 'win_rate_spread':
            detail = f"胜率差异: {data['max_win_rate']:.1f}% - {data['min_win_rate']:.1f}%"
        else:
            detail = f"值域: {data['max_value']:.4f} - {data['min_value']:.4f}"
        
        print(f'{i:<4} | {factor:<15} | {data["score"]:<8.4f} | {data["type"]:<12} | {detail}')
    
    return sorted_factors

def main():
    """主函数"""
    print('📊 综合胜率分析报告')
    print('=' * 60)
    
    # 加载数据
    df, buy_records, sell_records = load_trading_data()
    
    if df is None:
        return
    
    # 分析买卖记录匹配
    matched_symbols = analyze_buy_sell_matching(buy_records, sell_records)
    
    # 因子胜率分析
    factor_analysis = calculate_win_rates_by_factors(buy_records, sell_records)
    
    # 因子组合分析
    combination_results = analyze_factor_combinations(buy_records, sell_records)
    
    # 生成因子排名
    factor_ranking = generate_factor_ranking(factor_analysis)
    
    print(f'\n🎯 分析总结')
    print('=' * 40)
    
    if len(sell_records) > 0:
        print(f'✅ 完成买卖记录匹配分析')
        print(f'✅ 完成因子胜率分析')
        print(f'✅ 完成因子组合分析')
        print(f'✅ 生成因子有效性排名')
    else:
        print(f'⚠️ 缺少卖出记录，仅完成因子分布分析')
        print(f'💡 建议: 等待更多交易数据或检查卖出逻辑')
    
    print(f'\n📈 下一步建议:')
    if factor_ranking and len(factor_ranking) > 0:
        top_factor = factor_ranking[0][0]
        print(f'🎯 重点关注因子: {top_factor}')
        print(f'🔧 优化建议: 基于排名前3的因子调整选股逻辑')
    
    print(f'📊 数据质量: 买入记录{len(buy_records)}条, 卖出记录{len(sell_records)}条')

if __name__ == '__main__':
    main()
