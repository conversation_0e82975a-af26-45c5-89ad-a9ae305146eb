# coding=utf-8
"""
CCI因子深度分析脚本 (修复版)
正确匹配买入-卖出数据进行分析
"""
import sqlite3
import pandas as pd
import numpy as np

def analyze_cci_effectiveness():
    """分析CCI因子有效性"""
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配的CCI数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.cci,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.cci IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 CCI匹配数据分析 ({len(df)}条记录):')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的CCI数据')
            return None
        
        # CCI值分布分析
        cci_stats = df['cci'].describe()
        print(f'\n📈 CCI值分布:')
        print(f'   均值: {cci_stats["mean"]:.2f}')
        print(f'   中位数: {cci_stats["50%"]:.2f}')
        print(f'   标准差: {cci_stats["std"]:.2f}')
        print(f'   范围: [{cci_stats["min"]:.2f}, {cci_stats["max"]:.2f}]')
        
        # 收益分布分析
        profit_stats = df['net_profit_pct_sell'].describe()
        print(f'\n💰 收益分布:')
        print(f'   均值: {profit_stats["mean"]:.2f}%')
        print(f'   中位数: {profit_stats["50%"]:.2f}%')
        print(f'   胜率: {(df["net_profit_pct_sell"] > 0).mean() * 100:.1f}%')
        
        # 按时段分析CCI
        if 'buy_hour' in df.columns:
            hourly_cci = df.groupby('buy_hour').agg({
                'cci': ['mean', 'count'],
                'net_profit_pct_sell': ['mean', lambda x: (x > 0).mean() * 100]
            }).round(2)
            
            print(f'\n🕐 各时段CCI和收益分析:')
            print(f'时段  CCI均值  样本数  平均收益%  胜率%')
            print(f'-' * 45)
            
            for hour in sorted(hourly_cci.index):
                cci_mean = hourly_cci.loc[hour, ('cci', 'mean')]
                count = hourly_cci.loc[hour, ('cci', 'count')]
                profit_mean = hourly_cci.loc[hour, ('net_profit_pct_sell', 'mean')]
                win_rate = hourly_cci.loc[hour, ('net_profit_pct_sell', '<lambda>')]
                
                print(f'{hour:02d}:00 {cci_mean:7.1f} {count:6.0f} {profit_mean:8.2f} {win_rate:6.1f}')
        
        # CCI阈值效果分析
        thresholds = [15, 20, 25, 30, 35]
        print(f'\n🎯 CCI阈值效果分析:')
        
        for threshold in thresholds:
            high_cci = df[df['cci'] >= threshold]
            low_cci = df[df['cci'] < threshold]
            
            if len(high_cci) > 10 and len(low_cci) > 10:
                high_profit = high_cci['net_profit_pct_sell'].mean()
                low_profit = low_cci['net_profit_pct_sell'].mean()
                high_win_rate = (high_cci['net_profit_pct_sell'] > 0).mean() * 100
                low_win_rate = (low_cci['net_profit_pct_sell'] > 0).mean() * 100
                
                print(f'   CCI>={threshold}: 胜率{high_win_rate:.1f}%, 收益{high_profit:.2f}% (样本{len(high_cci)})')
                print(f'   CCI<{threshold}: 胜率{low_win_rate:.1f}%, 收益{low_profit:.2f}% (样本{len(low_cci)})')
                print(f'   差异: 胜率{high_win_rate-low_win_rate:+.1f}%, 收益{high_profit-low_profit:+.2f}%')
                print()
        
        return df
        
    except Exception as e:
        print(f'❌ CCI分析失败: {e}')
        return None

def optimize_cci_thresholds(df):
    """优化CCI阈值"""
    if df is None or len(df) == 0:
        print('⚠️ 没有数据进行CCI阈值优化')
        return None
    
    print(f'🔧 CCI阈值优化:')
    
    # 测试不同阈值组合
    best_threshold = None
    best_performance = -999
    
    results = []
    
    for low_threshold in [10, 15, 20]:
        for high_threshold in [25, 30, 35, 40]:
            if high_threshold <= low_threshold:
                continue
            
            # 分析该阈值组合的效果
            medium_cci = df[(df['cci'] >= low_threshold) & (df['cci'] < high_threshold)]
            
            if len(medium_cci) > 30:  # 确保样本量足够
                avg_profit = medium_cci['net_profit_pct_sell'].mean()
                win_rate = (medium_cci['net_profit_pct_sell'] > 0).mean() * 100
                sample_count = len(medium_cci)
                
                # 综合评分 (胜率权重60%, 收益权重40%)
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                results.append({
                    'range': f'[{low_threshold}, {high_threshold})',
                    'win_rate': win_rate,
                    'avg_profit': avg_profit,
                    'sample_count': sample_count,
                    'score': score
                })
                
                if score > best_performance:
                    best_performance = score
                    best_threshold = (low_threshold, high_threshold)
    
    # 按评分排序显示结果
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f'   CCI区间        胜率%   收益%   样本数  评分')
    print(f'   ' + '-' * 45)
    
    for result in results:
        print(f'   {result["range"]:<12} {result["win_rate"]:5.1f} {result["avg_profit"]:7.2f} {result["sample_count"]:6d} {result["score"]:5.1f}')
    
    if best_threshold:
        print(f'\n🏆 最优CCI阈值: [{best_threshold[0]}, {best_threshold[1]})')
        print(f'   综合评分: {best_performance:.1f}')
        
        # 分析最优区间的详细表现
        optimal_data = df[(df['cci'] >= best_threshold[0]) & (df['cci'] < best_threshold[1])]
        if len(optimal_data) > 0:
            opt_win_rate = (optimal_data['net_profit_pct_sell'] > 0).mean() * 100
            opt_avg_profit = optimal_data['net_profit_pct_sell'].mean()
            opt_sample = len(optimal_data)
            
            print(f'   详细表现: 胜率{opt_win_rate:.1f}%, 平均收益{opt_avg_profit:.2f}%, 样本{opt_sample}')
    
    return best_threshold, results

def analyze_cci_distribution_insights(df):
    """分析CCI分布洞察"""
    if df is None or len(df) == 0:
        return
    
    print(f'\n💡 CCI分布洞察:')
    
    # 分析极值情况
    very_high_cci = df[df['cci'] > 100]
    very_low_cci = df[df['cci'] < -100]
    normal_cci = df[(df['cci'] >= -100) & (df['cci'] <= 100)]
    
    if len(very_high_cci) > 5:
        vh_win_rate = (very_high_cci['net_profit_pct_sell'] > 0).mean() * 100
        vh_profit = very_high_cci['net_profit_pct_sell'].mean()
        print(f'   极高CCI (>100): 胜率{vh_win_rate:.1f}%, 收益{vh_profit:.2f}% (样本{len(very_high_cci)})')
    
    if len(very_low_cci) > 5:
        vl_win_rate = (very_low_cci['net_profit_pct_sell'] > 0).mean() * 100
        vl_profit = very_low_cci['net_profit_pct_sell'].mean()
        print(f'   极低CCI (<-100): 胜率{vl_win_rate:.1f}%, 收益{vl_profit:.2f}% (样本{len(very_low_cci)})')
    
    if len(normal_cci) > 5:
        n_win_rate = (normal_cci['net_profit_pct_sell'] > 0).mean() * 100
        n_profit = normal_cci['net_profit_pct_sell'].mean()
        print(f'   正常CCI (-100到100): 胜率{n_win_rate:.1f}%, 收益{n_profit:.2f}% (样本{len(normal_cci)})')
    
    # 分析当前策略使用的CCI范围
    current_range = df[(df['cci'] >= 25) & (df['cci'] <= 200)]
    if len(current_range) > 0:
        cr_win_rate = (current_range['net_profit_pct_sell'] > 0).mean() * 100
        cr_profit = current_range['net_profit_pct_sell'].mean()
        print(f'   当前策略范围 (25-200): 胜率{cr_win_rate:.1f}%, 收益{cr_profit:.2f}% (样本{len(current_range)})')

def main():
    """主函数"""
    print('🚀 CCI因子深度分析 (修复版)')
    print('=' * 50)
    
    # 执行CCI分析
    df = analyze_cci_effectiveness()
    
    if df is not None:
        # 优化CCI阈值
        best_threshold, results = optimize_cci_thresholds(df)
        
        # 分析CCI分布洞察
        analyze_cci_distribution_insights(df)
        
        print(f'\n🎯 CCI优化总结:')
        if best_threshold:
            print(f'✅ 建议CCI阈值: [{best_threshold[0]}, {best_threshold[1]})')
            print(f'✅ 当前策略可以考虑调整CCI范围')
        else:
            print(f'⚠️ 未找到明显更优的CCI阈值')
        
        print(f'✅ CCI因子分析完成，数据已保存')
        
        # 保存分析结果
        if len(df) > 0:
            df.to_csv('cci_analysis_results.csv', index=False, encoding='utf-8-sig')
            print(f'📊 详细数据已保存到: cci_analysis_results.csv')
    
    else:
        print(f'❌ CCI分析失败，请检查数据')

if __name__ == '__main__':
    main()
