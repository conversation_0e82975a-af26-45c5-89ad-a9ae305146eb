# coding=utf-8
"""
深度现实检查分析
重新分析当前真实胜率，找出分析错误的根本原因
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def get_current_reality_comprehensive():
    """获取当前真实情况的综合分析"""
    print('🔍 当前真实情况深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取所有交易数据
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp DESC
        """
        
        all_trades = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 总交易记录: {len(all_trades)} 条')
        
        # 转换时间戳
        all_trades['timestamp'] = pd.to_datetime(all_trades['timestamp'])
        
        # 分析买入和卖出记录
        buy_records = all_trades[all_trades['action'] == 'BUY'].copy()
        sell_records = all_trades[all_trades['action'] == 'SELL'].copy()
        
        print(f'\n📈 交易记录分布:')
        print(f'   买入记录: {len(buy_records)} 条')
        print(f'   卖出记录: {len(sell_records)} 条')
        
        # 分析有净收益数据的卖出记录
        completed_sells = sell_records.dropna(subset=['net_profit_pct_sell'])
        print(f'   完成交易: {len(completed_sells)} 条')
        
        if len(completed_sells) == 0:
            print('⚠️ 没有完成的交易记录')
            return None
        
        # 按时间分段分析
        latest_time = completed_sells['timestamp'].max()
        
        # 最新100条、200条、500条、1000条的表现
        segments = [100, 200, 500, 1000, len(completed_sells)]
        
        print(f'\n📊 分段胜率分析:')
        print(f'样本数    胜率%    平均收益%   时间范围')
        print(f'-' * 50)
        
        segment_results = {}
        
        for segment_size in segments:
            if segment_size > len(completed_sells):
                segment_size = len(completed_sells)
            
            segment_data = completed_sells.head(segment_size)
            win_rate = (segment_data['net_profit_pct_sell'] > 0).mean() * 100
            avg_profit = segment_data['net_profit_pct_sell'].mean()
            
            start_time = segment_data['timestamp'].min()
            end_time = segment_data['timestamp'].max()
            
            segment_results[segment_size] = {
                'win_rate': win_rate,
                'avg_profit': avg_profit,
                'start_time': start_time,
                'end_time': end_time,
                'data': segment_data
            }
            
            print(f'{segment_size:6d}   {win_rate:6.1f}   {avg_profit:8.2f}   {start_time.strftime("%m-%d %H:%M")} - {end_time.strftime("%m-%d %H:%M")}')
        
        return segment_results, buy_records, completed_sells
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None

def analyze_data_matching_accuracy(segment_results, buy_records):
    """分析数据匹配准确性"""
    print(f'\n🔍 数据匹配准确性分析')
    print('=' * 60)
    
    # 使用最新500条数据进行匹配分析
    latest_500 = segment_results[500]['data'] if 500 in segment_results else segment_results[list(segment_results.keys())[-1]]['data']
    
    print(f'📊 分析最新 {len(latest_500)} 条卖出记录的匹配情况')
    
    matched_count = 0
    unmatched_count = 0
    matching_details = []
    
    for _, sell_row in latest_500.iterrows():
        symbol = sell_row['symbol']
        sell_time = sell_row['timestamp']
        
        # 查找对应的买入记录
        symbol_buys = buy_records[buy_records['symbol'] == symbol].copy()
        
        if len(symbol_buys) > 0:
            symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
            # 找到卖出时间之前的买入记录
            valid_buys = symbol_buys[symbol_buys['timestamp'] < sell_time]
            
            if len(valid_buys) > 0:
                # 找最近的买入记录
                recent_buy = valid_buys.loc[valid_buys['timestamp'].idxmax()]
                time_diff = (sell_time - recent_buy['timestamp']).total_seconds() / 3600  # 小时
                
                matching_details.append({
                    'symbol': symbol,
                    'sell_time': sell_time,
                    'buy_time': recent_buy['timestamp'],
                    'time_diff_hours': time_diff,
                    'profit': sell_row['net_profit_pct_sell'],
                    'matched': True
                })
                matched_count += 1
            else:
                matching_details.append({
                    'symbol': symbol,
                    'sell_time': sell_time,
                    'profit': sell_row['net_profit_pct_sell'],
                    'matched': False
                })
                unmatched_count += 1
        else:
            unmatched_count += 1
    
    print(f'\n📈 匹配结果:')
    print(f'   成功匹配: {matched_count} 条 ({matched_count/len(latest_500)*100:.1f}%)')
    print(f'   无法匹配: {unmatched_count} 条 ({unmatched_count/len(latest_500)*100:.1f}%)')
    
    # 分析匹配的交易的时间间隔分布
    matched_details = [d for d in matching_details if d['matched']]
    if matched_details:
        time_diffs = [d['time_diff_hours'] for d in matched_details]
        
        print(f'\n⏰ 买卖时间间隔分析:')
        print(f'   平均间隔: {np.mean(time_diffs):.1f} 小时')
        print(f'   中位间隔: {np.median(time_diffs):.1f} 小时')
        print(f'   最短间隔: {np.min(time_diffs):.1f} 小时')
        print(f'   最长间隔: {np.max(time_diffs):.1f} 小时')
        
        # 分析不同时间间隔的胜率
        short_term = [d for d in matched_details if d['time_diff_hours'] <= 2]
        medium_term = [d for d in matched_details if 2 < d['time_diff_hours'] <= 24]
        long_term = [d for d in matched_details if d['time_diff_hours'] > 24]
        
        print(f'\n📊 不同持仓时间的胜率:')
        if short_term:
            short_win_rate = sum(1 for d in short_term if d['profit'] > 0) / len(short_term) * 100
            print(f'   短期(≤2h): {len(short_term)}条, 胜率{short_win_rate:.1f}%')
        
        if medium_term:
            medium_win_rate = sum(1 for d in medium_term if d['profit'] > 0) / len(medium_term) * 100
            print(f'   中期(2-24h): {len(medium_term)}条, 胜率{medium_win_rate:.1f}%')
        
        if long_term:
            long_win_rate = sum(1 for d in long_term if d['profit'] > 0) / len(long_term) * 100
            print(f'   长期(>24h): {len(long_term)}条, 胜率{long_win_rate:.1f}%')
    
    return matching_details

def analyze_current_configuration_effect(buy_records):
    """分析当前配置的实际效果"""
    print(f'\n⚙️ 当前配置实际效果分析')
    print('=' * 60)
    
    # 获取最新的买入记录
    latest_buys = buy_records.head(1000)
    
    print(f'📊 分析最新 {len(latest_buys)} 条买入记录')
    
    # 分析各因子的分布
    factors = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct', 'bb_width', 'overall_score']
    
    print(f'\n📈 当前因子分布情况:')
    print(f'因子        样本数   均值     中位数   最小值   最大值')
    print(f'-' * 60)
    
    factor_stats = {}
    
    for factor in factors:
        if factor in latest_buys.columns:
            factor_data = latest_buys[factor].dropna()
            
            if len(factor_data) > 0:
                stats = {
                    'count': len(factor_data),
                    'mean': factor_data.mean(),
                    'median': factor_data.median(),
                    'min': factor_data.min(),
                    'max': factor_data.max(),
                    'std': factor_data.std()
                }
                
                factor_stats[factor] = stats
                
                print(f'{factor:<10} {stats["count"]:6d} {stats["mean"]:8.2f} {stats["median"]:8.2f} {stats["min"]:8.2f} {stats["max"]:8.2f}')
    
    # 检查当前配置是否生效
    print(f'\n🔍 配置生效检查:')
    
    # 检查CCI配置 [75, 300]
    if 'cci' in factor_stats:
        cci_stats = factor_stats['cci']
        cci_in_range = latest_buys[(latest_buys['cci'] >= 75) & (latest_buys['cci'] <= 300)]['cci'].count()
        print(f'   CCI [75,300]: {cci_in_range}条 ({cci_in_range/cci_stats["count"]*100:.1f}%)')
        print(f'   CCI均值: {cci_stats["mean"]:.1f}, 中位数: {cci_stats["median"]:.1f}')
    
    # 检查MACD配置 > -2.089
    if 'macd_hist' in factor_stats:
        macd_stats = factor_stats['macd_hist']
        macd_above_threshold = latest_buys[latest_buys['macd_hist'] > -2.089]['macd_hist'].count()
        print(f'   MACD > -2.089: {macd_above_threshold}条 ({macd_above_threshold/macd_stats["count"]*100:.1f}%)')
        print(f'   MACD均值: {macd_stats["mean"]:.3f}, 中位数: {macd_stats["median"]:.3f}')
    
    # 检查ATR配置 > 3.0
    if 'atr_pct' in factor_stats:
        atr_stats = factor_stats['atr_pct']
        atr_above_threshold = latest_buys[latest_buys['atr_pct'] > 3.0]['atr_pct'].count()
        print(f'   ATR > 3.0%: {atr_above_threshold}条 ({atr_above_threshold/atr_stats["count"]*100:.1f}%)')
        print(f'   ATR均值: {atr_stats["mean"]:.1f}%, 中位数: {atr_stats["median"]:.1f}%')
    
    return factor_stats

def identify_analysis_errors():
    """识别分析错误"""
    print(f'\n🚨 分析错误识别')
    print('=' * 60)
    
    potential_errors = '''
🔍 可能的分析错误:

1. 📊 数据时间窗口错误:
   - 可能分析了错误的时间段
   - 优化前后的数据可能重叠
   - 时间戳解析可能有误

2. 🔄 数据匹配逻辑错误:
   - 买入-卖出记录匹配可能不准确
   - 同一股票多次交易的处理有误
   - 时间顺序判断可能错误

3. 📈 胜率计算错误:
   - 净收益计算可能有问题
   - 盈亏判断标准可能不对
   - 样本选择可能有偏差

4. ⚙️ 配置生效判断错误:
   - 新配置可能没有真正生效
   - 配置文件可能没有重新加载
   - 策略代码可能使用了缓存的旧配置

5. 🕐 时间段划分错误:
   - "最新"、"中期"、"早期"的划分可能不准确
   - 优化时间点的判断可能有误
   - 数据更新频率的理解可能错误

6. 📊 统计方法错误:
   - 样本量可能不足
   - 统计显著性判断可能有误
   - 异常值处理可能不当
'''
    
    print(potential_errors)

def generate_corrective_analysis_plan():
    """生成纠正分析计划"""
    print(f'\n🔧 纠正分析计划')
    print('=' * 60)
    
    plan = '''
🚀 重新分析计划:

1. 📊 数据验证:
   - 重新检查数据库结构
   - 验证时间戳格式和时区
   - 确认买入-卖出记录的对应关系
   - 检查净收益计算逻辑

2. ⚙️ 配置验证:
   - 确认当前配置是否真正生效
   - 检查策略代码是否使用了新配置
   - 验证配置文件的加载时间
   - 测试配置变更的生效机制

3. 🕐 时间段重新划分:
   - 明确优化时间点
   - 重新定义"优化前"和"优化后"
   - 使用更精确的时间窗口
   - 考虑配置生效的延迟

4. 📈 胜率重新计算:
   - 使用更严格的数据匹配逻辑
   - 重新验证盈亏判断标准
   - 排除异常交易记录
   - 使用多种计算方法交叉验证

5. 🔍 深度诊断:
   - 分析每个优化步骤的实际影响
   - 检查是否存在系统性问题
   - 验证数据的一致性和完整性
   - 重新评估优化策略的有效性

💡 验证方法:
   - 手工验证部分交易记录
   - 对比不同时间段的数据
   - 使用多种统计方法
   - 交叉验证分析结果
'''
    
    print(plan)

def main():
    """主函数"""
    print('🔍 深度现实检查分析')
    print('=' * 60)
    
    print('🚨 问题: 当前胜率只有40多，需要重新深入分析')
    print('🎯 目标: 找出分析错误，获取真实情况')
    
    # 获取当前真实情况
    result = get_current_reality_comprehensive()
    
    if result[0] is not None:
        segment_results, buy_records, completed_sells = result
        
        # 分析数据匹配准确性
        matching_details = analyze_data_matching_accuracy(segment_results, buy_records)
        
        # 分析当前配置效果
        factor_stats = analyze_current_configuration_effect(buy_records)
        
        # 识别分析错误
        identify_analysis_errors()
        
        # 生成纠正计划
        generate_corrective_analysis_plan()
        
        print(f'\n🎯 关键发现')
        print('=' * 40)
        
        # 显示最新的真实胜率
        latest_100 = segment_results[100] if 100 in segment_results else None
        latest_500 = segment_results[500] if 500 in segment_results else None
        
        if latest_100:
            print(f'📊 最新100条交易胜率: {latest_100["win_rate"]:.1f}%')
        if latest_500:
            print(f'📊 最新500条交易胜率: {latest_500["win_rate"]:.1f}%')
        
        print(f'📈 数据匹配成功率: {len([d for d in matching_details if d["matched"]])/len(matching_details)*100:.1f}%')
        
        if latest_100 and latest_100["win_rate"] < 45:
            print(f'🚨 确认: 当前胜率确实偏低')
            print(f'💡 需要: 重新评估所有优化策略')
        
        print(f'\n🔧 立即行动建议:')
        print(f'   1. 验证配置是否真正生效')
        print(f'   2. 重新分析数据匹配逻辑')
        print(f'   3. 检查时间段划分的准确性')
        print(f'   4. 重新制定基于真实数据的策略')
    
    else:
        print('❌ 数据分析失败，请检查数据库')
    
    print(f'\n💡 感谢您的质疑！让我们基于真实数据重新制定策略。')

if __name__ == '__main__':
    main()
