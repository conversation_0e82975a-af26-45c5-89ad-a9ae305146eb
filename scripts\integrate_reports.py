#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import importlib.util
import subprocess
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("reports_integration.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 导入数据管理模块
try:
    from scripts.data_manager import get_data_manager, backup_data, DATA_DIR, REPORTS_DIR
    data_manager = get_data_manager()
    logger.info("成功导入数据管理模块")
except ImportError:
    logger.warning("无法导入数据管理模块，将使用默认路径")
    DATA_DIR = 'data'
    REPORTS_DIR = 'reports'

def ensure_directories():
    """确保必要的目录结构存在"""
    logger.info("开始创建必要的目录结构")
    dirs = [DATA_DIR, REPORTS_DIR, f'{REPORTS_DIR}/html', 'templates', 'scripts', 'logs', 'backups']
    for directory in dirs:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")
    logger.info("目录结构创建完成")
    return True

def check_html_templates():
    """检查HTML模板文件是否存在，如果不存在则复制"""
    logger.info("开始检查HTML模板文件")
    templates = {
        'high_performance_template.html': 'high_performance_trades.html',
        'reports_guide_template.html': 'reports_guide.html'
    }
    
    for template_name, source_file in templates.items():
        template_path = os.path.join('templates', template_name)
        if not os.path.exists(template_path):
            if os.path.exists(source_file):
                # 如果源文件存在，复制为模板
                try:
                    with open(source_file, 'r', encoding='utf-8') as src:
                        content = src.read()
                    
                    with open(template_path, 'w', encoding='utf-8') as dest:
                        dest.write(content)
                    
                    logger.info(f"已创建模板文件: {template_path}")
                except Exception as e:
                    logger.error(f"创建模板文件失败 {template_path}: {str(e)}")
            else:
                logger.warning(f"源文件不存在，无法创建模板: {source_file}")
        else:
            logger.info(f"模板文件已存在: {template_path}")
    
    logger.info("HTML模板文件检查完成")
    return True

def check_report_generator():
    """检查报告生成器脚本是否存在"""
    generator_path = 'scripts/generate_html_reports.py'
    if not os.path.exists(generator_path):
        logger.error(f"报告生成器脚本不存在: {generator_path}")
        return False
    
    logger.info(f"报告生成器脚本存在: {generator_path}")
    return True

def check_data_files():
    """检查必要的数据文件是否存在"""
    logger.info("检查必要的数据文件")
    
    # 使用数据管理器中定义的文件路径
    from scripts.data_manager import TRADE_LOG_FILE, ANALYSIS_LOG_FILE, TRADE_RESULTS_FILE
    
    files_to_check = {
        "交易日志": TRADE_LOG_FILE,
        "分析日志": ANALYSIS_LOG_FILE,
        "交易结果": TRADE_RESULTS_FILE
    }
    
    all_exist = True
    for desc, file_path in files_to_check.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / 1024  # KB
            logger.info(f"{desc}文件存在: {file_path} ({file_size:.1f} KB)")
        else:
            logger.warning(f"{desc}文件不存在: {file_path}")
            all_exist = False
    
    return all_exist

def run_main_analysis():
    """运行主分析脚本"""
    try:
        # 检查main.py是否存在
        if not os.path.exists('main.py'):
            logger.error("主分析脚本 main.py 不存在")
            return False
        
        # 备份现有数据
        logger.info("备份现有数据...")
        try:
            backup_data()
            logger.info("数据备份完成")
        except Exception as e:
            logger.warning(f"数据备份失败: {str(e)}")
        
        logger.info("开始执行主分析...")
        # 获取Python可执行文件路径
        python_exe = sys.executable
        logger.info(f"使用Python解释器: {python_exe}")
        
        # 执行主分析脚本，使用完整路径
        cmd = [python_exe, 'main.py']
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 实时捕获并输出命令的执行结果
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 记录输出
        stdout_lines = []
        stderr_lines = []
        
        # 读取并处理标准输出
        for line in process.stdout:
            line = line.strip()
            if line:
                stdout_lines.append(line)
                logger.info(f"主分析输出: {line}")
                
        # 等待进程完成
        process.wait()
        
        # 读取标准错误（如果有）
        for line in process.stderr:
            line = line.strip()
            if line:
                stderr_lines.append(line)
                logger.error(f"主分析错误: {line}")
        
        # 记录结束时间和耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 检查返回码
        if process.returncode != 0:
            logger.error(f"主分析执行失败，返回代码: {process.returncode}，耗时: {duration:.2f}秒")
            if stderr_lines:
                logger.error(f"错误输出摘要: {stderr_lines[:5]}")
            return False
            
        logger.info(f"主分析完成，共输出{len(stdout_lines)}行，耗时: {duration:.2f}秒")
        
        # 将完整输出保存到日志文件
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"main_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("\n".join(stdout_lines))
        logger.info(f"主分析完整输出已保存到 {log_file}")
        
        # 检查分析是否生成了数据文件
        check_data_files()
        
        return True
    except subprocess.SubprocessError as e:
        logger.error(f"主分析执行失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"运行主分析时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def generate_html_reports():
    """生成HTML报告"""
    try:
        logger.info("开始生成HTML报告...")
        
        # 检查报告生成器脚本是否存在
        if not check_report_generator():
            return False
        
        # 检查必要的数据文件
        if not check_data_files():
            logger.warning("部分数据文件不存在，报告可能不完整")
        
        # 获取Python可执行文件路径
        python_exe = sys.executable
        logger.info(f"使用Python解释器: {python_exe}")
        
        # 执行报告生成器脚本，使用完整路径
        cmd = [python_exe, 'scripts/generate_html_reports.py']
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 记录开始时间
        start_time = datetime.now()
        
        # 实时捕获并输出命令的执行结果
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 记录输出
        stdout_lines = []
        stderr_lines = []
        
        # 读取并处理标准输出
        for line in process.stdout:
            line = line.strip()
            if line:
                stdout_lines.append(line)
                logger.info(f"报告生成输出: {line}")
                
        # 等待进程完成
        process.wait()
        
        # 读取标准错误（如果有）
        for line in process.stderr:
            line = line.strip()
            if line:
                stderr_lines.append(line)
                logger.error(f"报告生成错误: {line}")
        
        # 记录结束时间和耗时
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 检查返回码
        if process.returncode != 0:
            logger.error(f"HTML报告生成失败，返回代码: {process.returncode}，耗时: {duration:.2f}秒")
            if stderr_lines:
                logger.error(f"错误输出摘要: {stderr_lines[:5]}")
            return False
            
        logger.info(f"HTML报告生成完成，共输出{len(stdout_lines)}行，耗时: {duration:.2f}秒")
        
        # 检查生成的文件
        html_files = [
            os.path.join(REPORTS_DIR, 'html', 'high_performance_trades.html'),
            os.path.join(REPORTS_DIR, 'html', 'reports_guide.html')
        ]
        
        missing_files = []
        for html_file in html_files:
            if os.path.exists(html_file):
                file_size = os.path.getsize(html_file) / 1024  # KB
                logger.info(f"HTML报告已生成: {html_file} ({file_size:.1f} KB)")
            else:
                logger.warning(f"HTML报告未生成: {html_file}")
                missing_files.append(html_file)
        
        if missing_files:
            logger.warning(f"有{len(missing_files)}个HTML报告文件未生成")
            return False
                
        return True
    except subprocess.SubprocessError as e:
        logger.error(f"HTML报告生成失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"生成HTML报告时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_main_script():
    """更新主脚本以集成HTML报告生成"""
    try:
        # 检查main.py是否存在
        if not os.path.exists('main.py'):
            logger.error("主脚本 main.py 不存在，无法更新")
            return False
        
        logger.info("开始更新主脚本...")
        
        # 读取main.py内容
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含了报告生成代码
        if "generate_html_reports" in content:
            logger.info("主脚本已包含HTML报告生成代码，无需更新")
            return True
        
        # 寻找合适的位置插入报告生成代码
        # 通常在主函数结束前插入
        if "def main():" in content:
            # 在main函数中寻找结束位置
            lines = content.split('\n')
            new_lines = []
            in_main = False
            main_indent = ""
            
            for line in lines:
                new_lines.append(line)
                
                if line.strip().startswith("def main():"):
                    in_main = True
                    main_indent = " " * (len(line) - len(line.lstrip()))
                
                # 在main函数的最后添加报告生成代码
                if in_main and line.strip().startswith("return") or line.strip() == "pass":
                    # 插入报告生成代码
                    report_code = [
                        f"{main_indent}    # 生成HTML报告",
                        f"{main_indent}    try:",
                        f"{main_indent}        logger.info('正在生成HTML分析报告...')",
                        f"{main_indent}        from scripts.generate_html_reports import main as generate_reports",
                        f"{main_indent}        generate_reports()",
                        f"{main_indent}        logger.info('HTML报告生成完成')",
                        f"{main_indent}    except Exception as e:",
                        f"{main_indent}        logger.error(f'生成HTML报告时出错: {{str(e)}}')",
                        f"{main_indent}        # 继续执行，不因报告生成失败而中断主流程"
                    ]
                    
                    # 在return语句前插入代码
                    for i, code_line in enumerate(report_code):
                        new_lines.insert(len(new_lines) - 1, code_line)
                    
                    in_main = False
            
            # 将更新后的内容写回文件
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            logger.info("已更新主脚本，添加了HTML报告生成代码")
            return True
        else:
            logger.warning("在主脚本中未找到main函数，无法自动更新")
            return False
    
    except Exception as e:
        logger.error(f"更新主脚本时发生错误: {str(e)}")
        return False

def analyze_existing_data():
    """分析现有数据，不运行主分析脚本"""
    try:
        logger.info("开始分析现有数据...")
        
        # 使用数据管理器分析交易表现
        from scripts.data_manager import analyze_trades
        
        result_df = analyze_trades()
        
        if result_df is not None and not result_df.empty:
            logger.info(f"成功分析现有交易数据，共找到 {len(result_df)} 条完整交易记录")
            
            # 计算一些基本统计信息
            win_trades = result_df[result_df['Profit_Pct'] > 0]
            loss_trades = result_df[result_df['Profit_Pct'] <= 0]
            
            win_rate = len(win_trades) / len(result_df) * 100 if len(result_df) > 0 else 0
            avg_profit = result_df['Profit_Pct'].mean() * 100 if not result_df.empty else 0
            
            logger.info(f"交易胜率: {win_rate:.2f}%")
            logger.info(f"平均收益率: {avg_profit:.2f}%")
            
            return True
        else:
            logger.warning("没有找到有效的交易数据进行分析")
            return False
    
    except Exception as e:
        logger.error(f"分析现有数据时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    start_time = datetime.now()
    logger.info(f"=== 开始集成HTML报告生成到分析流程 === {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    status = {
        "directories": False,
        "templates": False,
        "main_script": False,
        "analysis": False,
        "reports": False
    }
    
    # 确保目录结构
    try:
        status["directories"] = ensure_directories()
        if not status["directories"]:
            logger.error("创建目录结构失败")
    except Exception as e:
        logger.error(f"创建目录结构时发生异常: {str(e)}")
        status["directories"] = False
    
    # 检查HTML模板
    try:
        status["templates"] = check_html_templates()
        if not status["templates"]:
            logger.warning("HTML模板检查失败")
    except Exception as e:
        logger.error(f"检查HTML模板时发生异常: {str(e)}")
        status["templates"] = False
    
    # 更新主脚本
    try:
        status["main_script"] = update_main_script()
        if not status["main_script"]:
            logger.warning("更新主脚本失败，将尝试直接执行分析和报告生成")
    except Exception as e:
        logger.error(f"更新主脚本时发生异常: {str(e)}")
        status["main_script"] = False
    
    # 执行主分析或分析现有数据
    try:
        options = input("请选择操作: (1) 执行主分析 (2) 分析现有数据 (3) 跳过分析: ")
        
        if options == "1":
            analysis_start = datetime.now()
            logger.info(f"开始执行主分析，时间: {analysis_start.strftime('%H:%M:%S')}")
            status["analysis"] = run_main_analysis()
            analysis_end = datetime.now()
            analysis_duration = (analysis_end - analysis_start).total_seconds()
            
            if status["analysis"]:
                logger.info(f"主分析执行完成，耗时: {analysis_duration:.2f}秒")
            else:
                logger.error(f"主分析执行失败，耗时: {analysis_duration:.2f}秒")
        elif options == "2":
            analysis_start = datetime.now()
            logger.info(f"开始分析现有数据，时间: {analysis_start.strftime('%H:%M:%S')}")
            status["analysis"] = analyze_existing_data()
            analysis_end = datetime.now()
            analysis_duration = (analysis_end - analysis_start).total_seconds()
            
            if status["analysis"]:
                logger.info(f"现有数据分析完成，耗时: {analysis_duration:.2f}秒")
            else:
                logger.error(f"现有数据分析失败，耗时: {analysis_duration:.2f}秒")
        else:
            logger.info("跳过分析步骤")
            status["analysis"] = None  # 表示跳过
    except Exception as e:
        logger.error(f"执行分析过程中发生异常: {str(e)}")
        status["analysis"] = False
    
    # 生成HTML报告
    try:
        reports_start = datetime.now()
        logger.info(f"开始生成HTML报告，时间: {reports_start.strftime('%H:%M:%S')}")
        status["reports"] = generate_html_reports()
        reports_end = datetime.now()
        reports_duration = (reports_end - reports_start).total_seconds()
        
        if status["reports"]:
            logger.info(f"HTML报告生成完成，耗时: {reports_duration:.2f}秒")
        else:
            logger.error(f"HTML报告生成失败，耗时: {reports_duration:.2f}秒")
    except Exception as e:
        logger.error(f"生成HTML报告过程中发生异常: {str(e)}")
        status["reports"] = False
    
    # 总结执行情况
    end_time = datetime.now()
    total_duration = (end_time - start_time).total_seconds()
    
    logger.info(f"=== 集成流程执行完成 ===")
    logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总耗时: {total_duration:.2f}秒")
    
    # 打印执行状态汇总
    logger.info("执行状态汇总:")
    logger.info(f"  - 目录结构创建: {'成功' if status['directories'] else '失败'}")
    logger.info(f"  - HTML模板检查: {'成功' if status['templates'] else '失败'}")
    logger.info(f"  - 主脚本更新: {'成功' if status['main_script'] else '失败'}")
    
    if status["analysis"] is None:
        logger.info(f"  - 数据分析: 跳过")
    else:
        logger.info(f"  - 数据分析: {'成功' if status['analysis'] else '失败'}")
        
    logger.info(f"  - HTML报告生成: {'成功' if status['reports'] else '失败'}")
    
    # 返回总体执行结果
    if status["reports"]:
        print("\n报告集成完成! 可以查看以下HTML报告文件:")
        print(f"  - {REPORTS_DIR}/html/high_performance_trades.html")
        print(f"  - {REPORTS_DIR}/html/reports_guide.html")
        return True
    else:
        print("\n报告生成失败。请查看日志了解详情。")
        return False

if __name__ == "__main__":
    main() 