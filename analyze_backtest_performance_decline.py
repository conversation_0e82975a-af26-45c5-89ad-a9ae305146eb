# coding=utf-8
"""
分析回测胜率41%的问题
深度分析优化效果和参数设置
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def analyze_backtest_results():
    """分析回测结果"""
    print("📊 分析回测胜率41%的问题")
    print("=" * 80)
    
    print("🔍 当前状况:")
    print("   - 预期胜率: 55%+")
    print("   - 实际回测胜率: 41%")
    print("   - 胜率下降: 从42%基线下降到41%")
    print("   - 问题: 优化没有带来预期效果，反而略有下降")
    
    # 分析可能的原因
    possible_reasons = [
        "1. 筛选阈值过低导致信号质量下降",
        "2. 68个因子中某些因子在当前市场环境下失效",
        "3. ML权重配置不适合当前市场",
        "4. 多因子组合产生了负面协同效应",
        "5. 新的筛选逻辑存在bug或逻辑错误",
        "6. 市场环境变化导致历史优化参数失效",
        "7. 过度优化导致过拟合",
        "8. 因子权重需要重新校准"
    ]
    
    print(f"\n🤔 可能的原因:")
    for reason in possible_reasons:
        print(f"   {reason}")

def analyze_current_config():
    """分析当前配置"""
    print("\n⚙️ 分析当前配置参数")
    print("=" * 60)
    
    try:
        # 检查当前配置
        from config import EFFECTIVE_FACTORS_CONFIG
        
        print("📋 当前配置分析:")
        
        # 检查买入条件
        if 'buy_conditions' in EFFECTIVE_FACTORS_CONFIG:
            conditions = EFFECTIVE_FACTORS_CONFIG['buy_conditions']
            print(f"   买入条件:")
            print(f"     min_combined_score: {conditions.get('min_combined_score')}")
            print(f"     min_factors_count: {conditions.get('min_factors_count')}")
            print(f"     require_top3_factors: {conditions.get('require_top3_factors')}")
            
            # 分析阈值设置
            min_score = conditions.get('min_combined_score', 1.0)
            min_factors = conditions.get('min_factors_count', 10)
            
            print(f"\n   📊 阈值分析:")
            if min_score <= 0.35:
                print(f"     ⚠️ 评分阈值可能过低: {min_score}")
                print(f"        过低的阈值可能导致低质量信号增加")
            
            if min_factors <= 3:
                print(f"     ⚠️ 因子数量要求可能过低: {min_factors}")
                print(f"        过低的要求可能导致信号质量下降")
        
        # 检查权重配置
        if 'scoring_weights' in EFFECTIVE_FACTORS_CONFIG:
            weights = EFFECTIVE_FACTORS_CONFIG['scoring_weights']
            print(f"\n   📊 权重配置:")
            total_weight = 0
            for key, value in weights.items():
                if key != 'optimization_note' and isinstance(value, (int, float)):
                    print(f"     {key}: {value}")
                    total_weight += value
            
            print(f"     总权重: {total_weight}")
            
            if abs(total_weight - 1.0) > 0.01:
                print(f"     ⚠️ 权重总和不为1.0: {total_weight}")
                print(f"        这可能导致评分计算异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置分析失败: {e}")
        return False

def analyze_factor_performance():
    """分析因子表现"""
    print("\n📊 分析因子表现")
    print("=" * 60)
    
    # 从日志中提取因子数据进行分析
    try:
        with open('logs/strategy.log', 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 提取因子评分数据
        import re
        
        # 查找因子评分模式
        score_patterns = {
            'technical_score': r'technical_score[\'"]:\s*([\d.]+)',
            'fundamental_score': r'fundamental_score[\'"]:\s*([\d.]+)',
            'sentiment_score': r'sentiment_score[\'"]:\s*([\d.]+)',
            'cross_market_score': r'cross_market_score[\'"]:\s*([\d.]+)',
            'overall_score': r'overall_score[\'"]:\s*([\d.]+)'
        }
        
        factor_scores = {}
        for factor, pattern in score_patterns.items():
            matches = re.findall(pattern, log_content)
            if matches:
                scores = [float(score) for score in matches[-20:]]  # 取最近20个
                factor_scores[factor] = {
                    'mean': np.mean(scores),
                    'std': np.std(scores),
                    'min': np.min(scores),
                    'max': np.max(scores),
                    'count': len(scores)
                }
        
        if factor_scores:
            print("   📈 最近因子评分统计:")
            for factor, stats in factor_scores.items():
                print(f"     {factor}:")
                print(f"       平均值: {stats['mean']:.4f}")
                print(f"       标准差: {stats['std']:.4f}")
                print(f"       范围: {stats['min']:.4f} - {stats['max']:.4f}")
                print(f"       样本数: {stats['count']}")
                
                # 分析因子质量
                if stats['mean'] < 0.4:
                    print(f"       ⚠️ 平均评分偏低，可能影响整体表现")
                if stats['std'] > 0.2:
                    print(f"       ⚠️ 波动较大，稳定性不足")
        else:
            print("   ❌ 未能从日志中提取因子评分数据")
        
        return factor_scores
        
    except Exception as e:
        print(f"❌ 因子表现分析失败: {e}")
        return {}

def analyze_signal_quality():
    """分析信号质量"""
    print("\n🎯 分析信号质量")
    print("=" * 60)
    
    try:
        with open('logs/strategy.log', 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 统计买入信号
        buy_signals = log_content.count('多因子综合策略')
        print(f"   买入信号总数: {buy_signals}")
        
        # 分析综合评分分布
        import re
        score_matches = re.findall(r'综合评分([\d.]+)', log_content)
        
        if score_matches:
            scores = [float(score) for score in score_matches]
            print(f"   📊 综合评分分析:")
            print(f"     信号数量: {len(scores)}")
            print(f"     平均评分: {np.mean(scores):.4f}")
            print(f"     评分范围: {np.min(scores):.4f} - {np.max(scores):.4f}")
            print(f"     标准差: {np.std(scores):.4f}")
            
            # 评分分布
            high_quality = len([s for s in scores if s >= 0.5])
            medium_quality = len([s for s in scores if 0.4 <= s < 0.5])
            low_quality = len([s for s in scores if s < 0.4])
            
            print(f"   📈 信号质量分布:")
            print(f"     高质量(≥0.5): {high_quality} ({high_quality/len(scores)*100:.1f}%)")
            print(f"     中等质量(0.4-0.5): {medium_quality} ({medium_quality/len(scores)*100:.1f}%)")
            print(f"     低质量(<0.4): {low_quality} ({low_quality/len(scores)*100:.1f}%)")
            
            # 分析问题
            if low_quality / len(scores) > 0.5:
                print(f"     ⚠️ 低质量信号占比过高: {low_quality/len(scores)*100:.1f}%")
                print(f"        这可能是胜率下降的主要原因")
            
            if np.mean(scores) < 0.45:
                print(f"     ⚠️ 平均评分偏低: {np.mean(scores):.4f}")
                print(f"        建议提高筛选阈值")
        
        return scores if score_matches else []
        
    except Exception as e:
        print(f"❌ 信号质量分析失败: {e}")
        return []

def generate_optimization_recommendations():
    """生成优化建议"""
    print("\n💡 优化建议")
    print("=" * 60)
    
    recommendations = [
        {
            'problem': '胜率从42%下降到41%',
            'analysis': '优化没有带来预期效果，反而略有下降',
            'solutions': [
                '提高min_combined_score从0.35到0.45',
                '增加min_factors_count从3到5',
                '重新启用require_top3_factors=True',
                '调整权重配置，降低表现不佳的因子权重'
            ]
        },
        {
            'problem': '筛选阈值可能过低',
            'analysis': '过低的阈值导致低质量信号增加',
            'solutions': [
                '逐步提高min_combined_score阈值',
                '增加因子数量要求',
                '添加额外的质量筛选条件',
                '实施动态阈值调整'
            ]
        },
        {
            'problem': '因子权重可能不合适',
            'analysis': 'ML优化的权重可能不适合当前市场',
            'solutions': [
                '重新训练ML模型使用最新数据',
                '调整各维度权重比例',
                '增加市场环境适应性',
                '实施权重动态调整机制'
            ]
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. 问题: {rec['problem']}")
        print(f"      分析: {rec['analysis']}")
        print(f"      解决方案:")
        for j, solution in enumerate(rec['solutions'], 1):
            print(f"        {j}) {solution}")
        print()

def generate_immediate_fixes():
    """生成立即修复方案"""
    print("\n🔧 立即修复方案")
    print("=" * 60)
    
    fixes = '''
⚡ 立即执行的修复 (针对41%胜率):

第1优先级: 提高筛选质量
   1. 提高min_combined_score: 0.35 → 0.45
   2. 增加min_factors_count: 3 → 5  
   3. 重新启用require_top3_factors: False → True
   4. 目标: 减少低质量信号，提升信号质量

第2优先级: 调整权重配置
   1. 降低表现不佳因子的权重
   2. 增加稳定因子的权重
   3. 重新平衡各维度权重
   4. 目标: 优化因子组合效果

第3优先级: 添加质量控制
   1. 添加最小评分要求
   2. 增加稳定性检查
   3. 实施信号质量监控
   4. 目标: 确保信号质量稳定

🎯 预期效果:
   - 信号数量: 适度减少 (质量优于数量)
   - 信号质量: 显著提升
   - 胜率目标: 从41%提升到50%+
   - 时间框架: 1-2周内见效

📊 验证方法:
   1. 回测验证修复效果
   2. 监控信号质量分布
   3. 跟踪胜率变化趋势
   4. 分析因子贡献度
'''
    
    print(fixes)

def create_config_fix():
    """创建配置修复建议"""
    print("\n📝 配置修复建议")
    print("=" * 60)
    
    config_fix = '''
建议的config.py修复:

# 修复买入条件 (提高质量标准)
'buy_conditions': {
    'min_combined_score': 0.45,           # 从0.35提高到0.45
    'min_factors_count': 5,               # 从3提高到5
    'require_top3_factors': True,         # 重新启用
    'max_signals_per_stock': 1,
    'optimization_note': '修复41%胜率问题：提高筛选标准确保信号质量',
},

# 调整权重配置 (重新平衡)
'scoring_weights': {
    'technical_score': 0.35,             # 技术面权重
    'fundamental_score': 0.30,           # 基本面权重  
    'sentiment_score': 0.20,             # 情绪面权重 (降低)
    'cross_market_score': 0.15,          # 跨市场权重 (降低)
    'optimization_note': '重新平衡权重，降低不稳定因子权重',
},

修复逻辑:
1. 提高筛选标准减少低质量信号
2. 重新平衡权重降低不稳定因子影响
3. 启用顶级因子要求确保核心指标满足
4. 优先质量而非数量
'''
    
    print(config_fix)

def main():
    """主函数"""
    print("🚨 回测胜率41%问题深度分析")
    print("=" * 80)
    
    print("🎯 目标: 找出胜率下降原因并提供修复方案")
    
    # 1. 分析回测结果
    analyze_backtest_results()
    
    # 2. 分析当前配置
    config_ok = analyze_current_config()
    
    # 3. 分析因子表现
    factor_scores = analyze_factor_performance()
    
    # 4. 分析信号质量
    signal_scores = analyze_signal_quality()
    
    # 5. 生成优化建议
    generate_optimization_recommendations()
    
    # 6. 生成立即修复方案
    generate_immediate_fixes()
    
    # 7. 创建配置修复建议
    create_config_fix()
    
    # 总结分析结果
    print(f"\n🏆 分析结果总结")
    print("=" * 40)
    print(f"📊 当前胜率: 41% (低于42%基线)")
    print(f"🎯 目标胜率: 55%+")
    print(f"📉 问题: 优化导致胜率下降而非提升")
    print(f"🔧 根本原因: 筛选阈值过低导致信号质量下降")
    
    print(f"\n💡 核心发现:")
    print("1. 68个因子系统正常工作，但筛选标准过低")
    print("2. 过低的阈值导致低质量信号增加")
    print("3. 需要提高筛选标准而非降低")
    print("4. 质量优于数量的策略更有效")
    
    print(f"\n🎯 立即行动:")
    print("1. 提高min_combined_score到0.45")
    print("2. 增加min_factors_count到5")
    print("3. 重新启用require_top3_factors")
    print("4. 重新平衡因子权重")
    
    print(f"\n🏆 预期效果: 胜率从41%提升到50%+")

if __name__ == '__main__':
    main()
