import streamlit as st
import pandas as pd
import os
import base64
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
# 添加分析系统目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
# 添加streamlit_app目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入公共的特征名称中文映射
from utils.data_loader import load_analysis_results

st.set_page_config(page_title="分析报告", page_icon="📊", layout="wide")

st.title("交易策略分析报告")

# 定义特征名称中文映射字典
feature_names_cn = {
    'Actual_Profit_Pct': '收益率',
    'Holding_Hours': '持仓时间',
    'ATR_Pct': 'ATR百分比',
    'TRIX_Buy': 'TRIX指标',
    'Volatility': '波动率',
    'Volatility_Buy': '买入时波动率',
    'ATR_Pct_Buy': '买入时ATR',
    'Volatility_Score_Buy': '波动分数',
    'Allocation_Factor_Buy': '资金分配系数',
    'Entry_Time': '入场时间',
    'Exit_Time': '出场时间',
    'Trade_ID': '交易ID',
    'Symbol': '交易品种',
    'Entry_Price': '入场价格',
    'Exit_Price': '出场价格',
    'Position_Size': '仓位大小',
    'Trade_Duration': '交易持续时间',
    'Trade_Result': '交易结果',
    'Stop_Loss': '止损点',
    'Take_Profit': '止盈点'
}

# 检查报告文件
reports_dir = "reports"
html_reports = [f for f in os.listdir(reports_dir) if f.endswith('.html')]
csv_reports = [f for f in os.listdir(reports_dir) if f.endswith('.csv')]
image_reports = [f for f in os.listdir(reports_dir) if f.endswith(('.png', '.jpg', '.jpeg'))]

# 创建下载链接函数
def get_download_link(file_path, file_name):
    with open(file_path, 'rb') as f:
        data = f.read()
    b64 = base64.b64encode(data).decode()
    href = f'<a href="data:file/octet-stream;base64,{b64}" download="{file_name}">下载文件</a>'
    return href

# 文件预览函数
def preview_html(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    st.components.v1.html(html_content, height=600, scrolling=True)

# 侧边栏报告导航
st.sidebar.header("报告导航")

report_type = st.sidebar.radio(
    "选择报告类型",
    ["HTML报告", "数据报告", "图表报告"]
)

# 主界面内容
if report_type == "HTML报告":
    st.header("HTML分析报告")
    
    if not html_reports:
        st.warning("未找到HTML报告文件。请先在主页生成报告。")
    else:
        selected_html = st.selectbox("选择HTML报告", html_reports)
        
        if selected_html:
            file_path = os.path.join(reports_dir, selected_html)
            st.markdown(get_download_link(file_path, selected_html), unsafe_allow_html=True)
            st.subheader("报告预览")
            preview_html(file_path)

elif report_type == "数据报告":
    st.header("数据分析报告")
    
    if not csv_reports:
        st.warning("未找到数据报告文件。请先在主页运行分析。")
    else:
        selected_csv = st.selectbox("选择数据报告", csv_reports)
        
        if selected_csv:
            file_path = os.path.join(reports_dir, selected_csv)
            st.markdown(get_download_link(file_path, selected_csv), unsafe_allow_html=True)
            
            # 显示CSV数据
            try:
                df = pd.read_csv(file_path)
                st.write(f"数据行数: {len(df)}")
                
                # 添加中文列名
                df_display = df.copy()
                # 将英文列名映射为中文
                cn_columns = {}
                for col in df.columns:
                    if col in feature_names_cn:
                        cn_columns[col] = feature_names_cn[col]
                    elif col.endswith('_Buy'):
                        base_name = col[:-4]  # 移除 _Buy 后缀
                        if base_name in feature_names_cn:
                            cn_columns[col] = f'买入时{feature_names_cn[base_name]}'
                        else:
                            cn_columns[col] = f'买入时{base_name}'
                    else:
                        cn_columns[col] = col
                
                # 显示数据摘要
                st.subheader("数据摘要")
                desc_df = df.describe()
                # 添加中文索引名称
                desc_display = desc_df.copy()
                desc_display.index = ['统计量', '平均值', '标准差', '最小值', '25%分位数', '中位数', '75%分位数', '最大值']
                st.dataframe(desc_display)
                
                # 显示前10行数据
                st.subheader("数据预览")
                # 创建一个新的DataFrame，添加中文列名
                display_df = df.head(10).copy()
                display_df.columns = [f"{cn_columns.get(col, col)} ({col})" for col in display_df.columns]
                st.dataframe(display_df)
                
                # 如果是交易分析结果，显示额外分析
                if "trade_analysis_results.csv" in selected_csv:
                    st.subheader("交易绩效摘要")
                    
                    # 计算买入金额和卖出金额以及实际收益率
                    df['Buy_Amount'] = df['Buy_Price'] * df['Volume']
                    df['Sell_Amount'] = df['Sell_Price'] * df['Volume']
                    df['Profit_Amount'] = df['Sell_Amount'] - df['Buy_Amount']
                    df['Actual_Profit_Pct'] = (df['Profit_Amount'] / df['Buy_Amount'] * 100)
                    
                    # 计算关键指标
                    total_trades = len(df)
                    winning_trades = len(df[df['Actual_Profit_Pct'] > 0])
                    losing_trades = total_trades - winning_trades
                    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
                    
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("总交易次数", total_trades)
                    with col2:
                        st.metric("盈利交易", winning_trades)
                    with col3:
                        st.metric("亏损交易", losing_trades)
                    with col4:
                        st.metric("胜率", f"{win_rate:.2f}%")
                    
                    # 盈亏统计
                    if 'Actual_Profit_Pct' in df.columns:
                        avg_profit = df['Actual_Profit_Pct'].mean()
                        avg_win = df[df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if winning_trades > 0 else 0
                        avg_loss = df[df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if losing_trades > 0 else 0
                        profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 else float('inf')
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("平均单笔收益率", f"{avg_profit:.2f}%")
                        with col2:
                            st.metric("平均单笔盈利", f"{avg_win:.2f}%")
                        with col3:
                            st.metric("平均单笔亏损", f"{avg_loss:.2f}%")
                        
                        st.metric("盈亏比", f"{profit_loss_ratio:.2f}" if profit_loss_ratio != float('inf') else "∞")
                        
                        # 添加更详细的收益率统计
                        st.subheader("单笔交易收益率统计")
                        col1, col2, col3, col4 = st.columns(4)
                        
                        with col1:
                            st.metric("最大单笔收益", f"{df['Actual_Profit_Pct'].max():.2f}%")
                        with col2:
                            st.metric("最大单笔亏损", f"{df['Actual_Profit_Pct'].min():.2f}%")
                        with col3:
                            st.metric("收益率中位数", f"{df['Actual_Profit_Pct'].median():.2f}%")
                        with col4:
                            st.metric("收益率标准差", f"{df['Actual_Profit_Pct'].std():.2f}%")
                        
                        # 添加收益率分布图
                        st.subheader("单笔交易收益率分布")
                        fig, ax = plt.subplots(figsize=(10, 6))
                        sns.histplot(df['Actual_Profit_Pct'], bins=30, kde=True, ax=ax)
                        ax.axvline(x=0, color='red', linestyle='--')
                        ax.set_title('单笔交易收益率分布')
                        ax.set_xlabel('收益率 (%)')
                        ax.set_ylabel('频率')
                        st.pyplot(fig)
                        
                        # 添加交易列表
                        st.subheader("交易列表")
                        
                        # 选择要显示的列
                        display_columns = [
                            'Symbol', 'Buy_Time', 'Sell_Time', 'Buy_Price', 'Sell_Price', 
                            'Volume', 'Actual_Profit_Pct', 'Holding_Hours', 'Sell_Reason'
                        ]
                        
                        # 过滤可用的列
                        available_columns = [col for col in display_columns if col in df.columns]
                        
                        if available_columns:
                            # 创建显示用的DataFrame
                            trades_df = df[available_columns].copy()
                            
                            # 添加买入金额、卖出金额和盈利金额列
                            if 'Buy_Amount' not in trades_df.columns and 'Buy_Price' in trades_df.columns and 'Volume' in trades_df.columns:
                                trades_df['Buy_Amount'] = trades_df['Buy_Price'] * trades_df['Volume']
                            
                            if 'Sell_Amount' not in trades_df.columns and 'Sell_Price' in trades_df.columns and 'Volume' in trades_df.columns:
                                trades_df['Sell_Amount'] = trades_df['Sell_Price'] * trades_df['Volume']
                            
                            if 'Profit_Amount' not in trades_df.columns and 'Buy_Amount' in trades_df.columns and 'Sell_Amount' in trades_df.columns:
                                trades_df['Profit_Amount'] = trades_df['Sell_Amount'] - trades_df['Buy_Amount']
                            
                            # 重命名列以便于显示
                            column_mapping = {
                                'Symbol': '股票代码',
                                'Buy_Time': '买入时间',
                                'Sell_Time': '卖出时间',
                                'Buy_Price': '买入价格',
                                'Sell_Price': '卖出价格',
                                'Volume': '交易量',
                                'Buy_Amount': '买入金额',
                                'Sell_Amount': '卖出金额',
                                'Profit_Amount': '盈利金额',
                                'Actual_Profit_Pct': '收益率(%)',
                                'Holding_Hours': '持仓时间(小时)',
                                'Sell_Reason': '卖出原因'
                            }
                            
                            # 确定最终要显示的列
                            final_display_columns = list(available_columns)
                            
                            # 添加金额相关列
                            if 'Buy_Amount' in trades_df.columns:
                                final_display_columns.append('Buy_Amount')
                            if 'Sell_Amount' in trades_df.columns:
                                final_display_columns.append('Sell_Amount')
                            if 'Profit_Amount' in trades_df.columns:
                                final_display_columns.append('Profit_Amount')
                            
                            # 创建最终显示的DataFrame
                            display_df = trades_df[final_display_columns].copy()
                            display_df.rename(columns={col: column_mapping.get(col, col) for col in final_display_columns}, inplace=True)
                            
                            # 格式化数值列
                            if '收益率(%)' in display_df.columns:
                                display_df['收益率(%)'] = display_df['收益率(%)'].map('{:.4f}%'.format)
                            if '买入价格' in display_df.columns:
                                display_df['买入价格'] = display_df['买入价格'].map('{:.2f}'.format)
                            if '卖出价格' in display_df.columns:
                                display_df['卖出价格'] = display_df['卖出价格'].map('{:.2f}'.format)
                            if '买入金额' in display_df.columns:
                                display_df['买入金额'] = display_df['买入金额'].map('{:.2f}'.format)
                            if '卖出金额' in display_df.columns:
                                display_df['卖出金额'] = display_df['卖出金额'].map('{:.2f}'.format)
                            if '盈利金额' in display_df.columns:
                                display_df['盈利金额'] = display_df['盈利金额'].map('{:.2f}'.format)
                            if '持仓时间(小时)' in display_df.columns:
                                display_df['持仓时间(小时)'] = display_df['持仓时间(小时)'].map('{:.1f}'.format)
                            
                            # 显示交易列表
                            st.dataframe(display_df, use_container_width=True)
                            
                        # 交易特征分析（如果有足够的数据）
                        if len(df) > 10:
                            st.subheader("特征分析")
                            
                            # 选择特征进行分析
                            available_features = [col for col in df.columns if col not in ['Trade_ID', 'Entry_Time', 'Exit_Time']]
                            feature_options = [(col, cn_columns.get(col, col)) for col in available_features]
                            
                            selected_feature = st.selectbox(
                                "选择要分析的特征",
                                options=[f[0] for f in feature_options],
                                format_func=lambda x: f"{cn_columns.get(x, x)} ({x})"
                            )
                            
                            if selected_feature:
                                # 特征统计分析
                                feature_stats = df[selected_feature].describe()
                                st.write(f"**{cn_columns.get(selected_feature, selected_feature)}统计信息**")
                                
                                stats_df = pd.DataFrame({
                                    '统计量': ['均值', '标准差', '最小值', '25%分位数', '中位数', '75%分位数', '最大值'],
                                    '数值': [
                                        feature_stats['mean'],
                                        feature_stats['std'],
                                        feature_stats['min'],
                                        feature_stats['25%'],
                                        feature_stats['50%'],
                                        feature_stats['75%'],
                                        feature_stats['max']
                                    ]
                                })
                                st.dataframe(stats_df)
                                
                                # 绘制特征分布图
                                st.write(f"**{cn_columns.get(selected_feature, selected_feature)}分布**")
                                fig, ax = plt.subplots(figsize=(10, 5))
                                sns.histplot(df[selected_feature], kde=True, ax=ax)
                                ax.set_title(f"{cn_columns.get(selected_feature, selected_feature)}分布")
                                ax.set_xlabel(cn_columns.get(selected_feature, selected_feature))
                                ax.set_ylabel("频率")
                                st.pyplot(fig)
            except Exception as e:
                st.error(f"读取CSV文件失败: {str(e)}")

elif report_type == "图表报告":
    st.header("可视化图表报告")
    
    if not image_reports:
        st.warning("未找到图表文件。请先在主页运行分析。")
    else:
        # 创建图表网格
        cols = 2
        rows = (len(image_reports) + cols - 1) // cols
        
        for i in range(rows):
            row_cols = st.columns(cols)
            
            for j in range(cols):
                idx = i * cols + j
                if idx < len(image_reports):
                    img_file = image_reports[idx]
                    file_path = os.path.join(reports_dir, img_file)
                    
                    # 提取图表中文名称
                    chart_name = img_file.replace('.png', '').replace('_', ' ').title()
                    chart_names_cn = {
                        'Cumulative Profit Curve': '累积收益曲线',
                        'Daily Profit Calendar': '每日收益日历',
                        'Feature Importance': '特征重要性',
                        'Profit Distribution': '收益分布',
                        'Trade Distribution': '交易分布',
                        'Volatility Analysis': '波动率分析',
                        'Win Rate By Day': '按日胜率',
                        'Win Rate By Hour': '按小时胜率',
                        'Confusion Matrix': '混淆矩阵',
                        'Holding Time Distribution': '持仓时间分布',
                        'Correlation Heatmap': '相关性热图',
                        'Trade Count By Day': '按日交易次数',
                        'Performance Metrics': '性能指标'
                    }
                    
                    display_name = chart_names_cn.get(chart_name, chart_name)
                    
                    with row_cols[j]:
                        st.image(file_path, caption=display_name, use_column_width=True)
                        st.markdown(get_download_link(file_path, img_file), unsafe_allow_html=True)
        
        # 添加导出所有图表选项
        st.subheader("批量导出")
        if st.button("导出所有图表为ZIP"):
            try:
                import zipfile
                import io
                
                # 创建内存中的ZIP文件
                zip_buffer = io.BytesIO()
                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for img_file in image_reports:
                        file_path = os.path.join(reports_dir, img_file)
                        zip_file.write(file_path, arcname=img_file)
                
                # 创建下载链接
                zip_buffer.seek(0)
                b64 = base64.b64encode(zip_buffer.read()).decode()
                href = f'<a href="data:application/zip;base64,{b64}" download="trade_analysis_charts.zip">下载所有图表的ZIP文件</a>'
                st.markdown(href, unsafe_allow_html=True)
                
                st.success("已生成所有图表的ZIP文件，点击上方链接下载")
            except Exception as e:
                st.error(f"创建ZIP文件失败: {str(e)}")

# 添加报告生成功能
st.header("生成新报告")

with st.expander("生成HTML综合报告"):
    st.info("此功能将根据最新的分析结果生成综合HTML报告。")
    
    report_title = st.text_input("报告标题", "万和策略交易分析报告")
    include_images = st.checkbox("包含图表图像", value=True)
    include_tables = st.checkbox("包含数据表格", value=True)
    
    if st.button("生成综合报告"):
        with st.spinner("正在生成综合报告..."):
            try:
                # 检查必要文件
                if not os.path.exists("reports/trade_analysis_results.csv"):
                    st.error("未找到交易分析结果，请先运行交易分析")
                else:
                    # 生成HTML报告
                    import pandas as pd
                    from jinja2 import Template
                    from datetime import datetime
                    
                    # 读取交易分析结果
                    df = pd.read_csv("reports/trade_analysis_results.csv")
                    
                    # 读取模板
                    with open("analysis_system/templates/reports_guide_template.html", "r", encoding="utf-8") as f:
                        template_text = f.read()
                    
                    template = Template(template_text)
                    
                    # 准备模板数据
                    template_data = {
                        "report_title": report_title,
                        "total_reports": len(os.listdir("reports")),
                        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "win_rate": (df["Actual_Profit_Pct"] > 0).mean() * 100,
                        "avg_profit": df["Actual_Profit_Pct"].mean(),
                        "total_trades": len(df),
                        "data_files_count": len([f for f in os.listdir("reports") if f.endswith(".csv")]),
                        "image_files_count": len([f for f in os.listdir("reports") if f.endswith(".png")]),
                        "rule_files_count": len([f for f in os.listdir("reports") if f.endswith(".txt")]),
                        "model_files_count": len([f for f in os.listdir("reports") if f.endswith(".pkl")]),
                        "include_images": include_images,
                        "include_tables": include_tables,
                        "feature_names_cn": feature_names_cn  # 添加中文特征名称映射
                    }
                    
                    # 如果包含图片，添加图片路径
                    if include_images:
                        template_data["image_paths"] = [
                            f for f in os.listdir("reports") 
                            if f.endswith(('.png', '.jpg', '.jpeg'))
                        ]
                    
                    # 如果包含表格，添加表格数据
                    if include_tables:
                        # 添加交易摘要
                        template_data["trade_summary"] = {
                            "total_trades": len(df),
                            "winning_trades": len(df[df['Actual_Profit_Pct'] > 0]),
                            "losing_trades": len(df[df['Actual_Profit_Pct'] <= 0]),
                            "win_rate": (df["Actual_Profit_Pct"] > 0).mean() * 100,
                            "avg_profit": df["Actual_Profit_Pct"].mean(),
                            "avg_win": df[df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if len(df[df['Actual_Profit_Pct'] > 0]) > 0 else 0,
                            "avg_loss": df[df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if len(df[df['Actual_Profit_Pct'] <= 0]) > 0 else 0
                        }
                        
                        # 添加前10条交易记录，并包含中文列名
                        top_trades_df = df.head(10)
                        # 创建带中文标题的交易记录
                        top_trades_with_cn = []
                        for _, row in top_trades_df.iterrows():
                            trade_dict = row.to_dict()
                            # 添加中文标题
                            for key in list(trade_dict.keys()):
                                if key in feature_names_cn:
                                    trade_dict[feature_names_cn[key]] = trade_dict[key]
                            top_trades_with_cn.append(trade_dict)
                        
                        template_data["top_trades"] = top_trades_with_cn
                    
                    # 渲染模板
                    output = template.render(**template_data)
                    
                    # 生成时间戳
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    
                    # 保存HTML报告
                    report_path = f"reports/trade_analysis_report_{timestamp}.html"
                    with open(report_path, "w", encoding="utf-8") as f:
                        f.write(output)
                    
                    st.success(f"综合报告生成成功: {report_path}")
                    
                    # 创建下载链接
                    st.markdown(get_download_link(report_path, os.path.basename(report_path)), unsafe_allow_html=True)
                    
                    # 预览报告
                    st.subheader("报告预览")
                    preview_html(report_path)
            except Exception as e:
                st.error(f"生成报告时出错: {str(e)}")

# 报告管理功能
st.header("报告管理")

with st.expander("管理报告文件"):
    st.info("在此处可以删除或归档旧的报告文件。")
    
    # 列出所有报告文件
    all_reports = html_reports + csv_reports + image_reports
    all_reports = list(set(all_reports))  # 去重
    
    if not all_reports:
        st.warning("未找到任何报告文件。")
    else:
        # 选择要删除的文件
        files_to_delete = st.multiselect(
            "选择要删除的文件",
            options=all_reports
        )
        
        if files_to_delete:
            if st.button("删除选中的文件"):
                try:
                    for file_name in files_to_delete:
                        file_path = os.path.join(reports_dir, file_name)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    
                    st.success(f"已删除 {len(files_to_delete)} 个文件")
                    st.experimental_rerun()  # 刷新页面
                except Exception as e:
                    st.error(f"删除文件时出错: {str(e)}")
        
        # 创建归档功能
        st.subheader("创建报告归档")
        archive_name = st.text_input("归档名称", f"reports_archive_{datetime.now().strftime('%Y%m%d')}")
        
        if st.button("创建报告归档"):
            try:
                import zipfile
                import io
                
                # 创建ZIP文件
                archive_path = f"{archive_name}.zip"
                with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_name in all_reports:
                        file_path = os.path.join(reports_dir, file_name)
                        if os.path.exists(file_path):
                            zip_file.write(file_path, arcname=file_name)
                
                # 创建下载链接
                with open(archive_path, 'rb') as f:
                    data = f.read()
                b64 = base64.b64encode(data).decode()
                href = f'<a href="data:application/zip;base64,{b64}" download="{archive_path}">下载报告归档</a>'
                st.markdown(href, unsafe_allow_html=True)
                
                st.success(f"已创建报告归档: {archive_path}")
                
                # 删除临时文件
                try:
                    os.remove(archive_path)
                except:
                    pass
                    
            except Exception as e:
                st.error(f"创建归档时出错: {str(e)}") 