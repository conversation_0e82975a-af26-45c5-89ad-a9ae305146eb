# 万和策略分析系统 - 维护指南

本文档提供了万和策略分析系统的详细维护指南，包括日常维护任务、问题排查和解决方案。

## 目录

1. [日常维护任务](#日常维护任务)
2. [数据维护](#数据维护)
3. [系统监控](#系统监控)
4. [常见问题排查](#常见问题排查)
5. [系统升级](#系统升级)
6. [灾难恢复](#灾难恢复)

## 日常维护任务

### 日志文件管理

系统会生成多个日志文件，需要定期检查和管理：

1. **检查日志文件大小**
   ```bash
   # 查看日志文件大小
   ls -lh *.log logs/*.log
   ```

2. **日志文件轮转**
   系统使用RotatingFileHandler自动管理日志文件大小，但仍需定期检查：
   ```bash
   # 手动压缩旧日志文件
   find logs/ -name "*.log.*" -exec gzip {} \;
   ```

3. **清理过期日志**
   ```bash
   # 删除30天前的日志文件
   find logs/ -name "*.log.*" -mtime +30 -delete
   ```

### 数据库维护

如果使用SQLite数据库存储数据：

1. **压缩数据库文件**
   ```bash
   # 压缩SQLite数据库
   sqlite3 data/trades.db "VACUUM;"
   ```

2. **检查数据库完整性**
   ```bash
   sqlite3 data/trades.db "PRAGMA integrity_check;"
   ```

### 模板文件更新

定期检查HTML模板文件是否需要更新：

1. **检查模板文件**
   ```bash
   # 查看模板文件修改时间
   ls -la templates/
   ```

2. **备份模板文件**
   ```bash
   # 备份当前模板
   cp -r templates/ templates_backup_$(date +%Y%m%d)
   ```

## 数据维护

### 数据备份

系统自动在每次运行主程序前备份数据，但也可以手动触发备份：

```python
# 在Python中手动触发备份
from scripts.data_manager import backup_data
backup_data()
```

或者使用脚本：

```bash
# 通过命令行触发备份
python -c "from scripts.data_manager import backup_data; backup_data()"
```

### 数据清理

定期清理不必要的数据以提高系统性能：

1. **清理临时文件**
   ```bash
   # 清理临时文件
   find . -name "*.tmp" -delete
   ```

2. **清理缓存文件**
   ```bash
   # 清理__pycache__目录
   find . -name "__pycache__" -type d -exec rm -rf {} +
   ```

### 数据迁移

如需将CSV数据迁移到SQLite数据库：

```python
from scripts.data_manager import get_data_manager
dm = get_data_manager()
dm.migrate_csv_to_db()
```

## 系统监控

### 性能监控

监控系统性能指标：

1. **检查CPU和内存使用情况**
   ```bash
   # 在Linux/Mac上
   top -n 1 | grep python
   
   # 在Windows上
   tasklist | findstr python
   ```

2. **监控磁盘空间**
   ```bash
   # 检查磁盘空间
   df -h
   ```

3. **检查文件数量**
   ```bash
   # 检查报告文件数量
   find reports/ -type f | wc -l
   ```

### 运行时监控

监控系统运行时状态：

1. **检查进程状态**
   ```bash
   # 检查Python进程
   ps aux | grep python
   ```

2. **监控日志输出**
   ```bash
   # 实时查看日志
   tail -f strategy.log
   ```

## 常见问题排查

### 问题：报告生成失败

**症状**：运行`generate_html_reports.py`后没有生成HTML报告文件。

**排查步骤**：

1. **检查日志文件**
   ```bash
   cat html_reports.log
   ```

2. **检查数据文件是否存在**
   ```bash
   ls -la data/trade_log.csv reports/trade_analysis_results.csv
   ```

3. **检查模板文件是否存在**
   ```bash
   ls -la templates/high_performance_template.html
   ```

4. **检查文件权限**
   ```bash
   ls -la reports/html/
   ```

**解决方案**：

1. 如果数据文件不存在，需要先运行主程序生成交易数据
2. 如果模板文件不存在，运行以下命令创建默认模板：
   ```python
   from scripts.generate_html_reports import create_default_templates
   create_default_templates()
   ```
3. 如果权限问题，修改目录权限：
   ```bash
   chmod -R 755 reports/
   ```

### 问题：数据访问错误

**症状**：程序运行时出现"无法访问数据文件"的错误。

**排查步骤**：

1. **检查数据文件路径**
   ```bash
   ls -la data/
   ```

2. **检查数据文件格式**
   ```bash
   head -n 5 data/trade_log.csv
   ```

3. **检查数据管理器日志**
   ```bash
   cat data_manager.log
   ```

**解决方案**：

1. 如果文件不存在，初始化数据管理器：
   ```python
   from scripts.data_manager import get_data_manager
   get_data_manager()
   ```
2. 如果文件格式错误，可能需要修复或重新生成数据文件
3. 如果是权限问题，修改文件权限：
   ```bash
   chmod 644 data/*.csv
   ```

### 问题：策略执行异常

**症状**：主程序运行时崩溃或报错。

**排查步骤**：

1. **检查策略日志**
   ```bash
   cat strategy.log
   ```

2. **检查依赖包是否正确安装**
   ```bash
   pip list | grep -E "pandas|numpy|talib"
   ```

3. **检查Python版本**
   ```bash
   python --version
   ```

**解决方案**：

1. 根据日志错误信息修复代码问题
2. 重新安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 确保使用兼容的Python版本（推荐Python 3.8+）

### 问题：数据库连接错误

**症状**：使用数据库功能时出现连接错误。

**排查步骤**：

1. **检查数据库文件是否存在**
   ```bash
   ls -la data/trades.db
   ```

2. **检查数据库文件权限**
   ```bash
   ls -la data/trades.db
   ```

3. **尝试直接连接数据库**
   ```bash
   sqlite3 data/trades.db ".tables"
   ```

**解决方案**：

1. 如果数据库文件不存在，初始化数据库：
   ```python
   from scripts.data_manager import get_data_manager
   dm = get_data_manager(use_db=True)
   ```
2. 修复文件权限问题
3. 如果数据库损坏，从备份恢复或重新创建

## 系统升级

### 代码升级

升级系统代码时的步骤：

1. **备份当前代码和数据**
   ```bash
   # 创建备份
   cp -r . ../wanhe_strategy_backup_$(date +%Y%m%d)
   ```

2. **更新代码**
   ```bash
   # 如果使用Git
   git pull origin main
   
   # 或手动更新文件
   ```

3. **更新依赖**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

4. **测试系统功能**
   ```bash
   # 运行测试
   python -c "from scripts.data_manager import get_data_manager; print('数据管理器测试:', get_data_manager() is not None)"
   ```

### 添加新功能

添加新功能的一般步骤：

1. **创建功能分支**（如果使用Git）
   ```bash
   git checkout -b feature/new-feature
   ```

2. **实现新功能**

3. **测试新功能**

4. **合并到主分支**
   ```bash
   git checkout main
   git merge feature/new-feature
   ```

## 灾难恢复

### 数据恢复

从备份恢复数据：

1. **查找最新备份**
   ```bash
   ls -lt backups/
   ```

2. **恢复数据文件**
   ```bash
   # 恢复交易日志
   cp backups/[最新备份目录]/trade_log.csv data/
   ```

3. **恢复数据库**（如果使用数据库）
   ```bash
   # 恢复数据库文件
   cp backups/[最新备份目录]/trades.db data/
   ```

### 系统重建

如果需要完全重建系统：

1. **准备新环境**
   ```bash
   # 创建新目录
   mkdir wanhe_strategy_new
   cd wanhe_strategy_new
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **复制核心代码**
   ```bash
   cp ../wanhe_strategy_backup/scripts/*.py scripts/
   cp ../wanhe_strategy_backup/main.py .
   ```

4. **恢复数据**
   ```bash
   mkdir -p data reports/html templates logs backups
   cp ../wanhe_strategy_backup/data/*.csv data/
   ```

5. **初始化系统**
   ```bash
   python -c "from scripts.data_manager import get_data_manager; get_data_manager()"
   ```

## 附录：维护检查清单

### 每日检查

- [ ] 检查策略日志文件
- [ ] 确认数据文件正常更新
- [ ] 检查系统磁盘空间

### 每周检查

- [ ] 备份重要数据
- [ ] 检查日志文件大小并清理
- [ ] 检查数据库完整性

### 每月检查

- [ ] 完整系统备份
- [ ] 检查并更新依赖包
- [ ] 优化数据库（如使用SQLite）
- [ ] 清理过期备份和日志 