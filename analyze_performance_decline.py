# coding=utf-8
"""
深度分析胜率和收益率下降问题
检查优化效果和机器学习功能
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def analyze_win_rate_decline():
    """分析胜率下降情况"""
    print("📉 分析胜率和收益率下降问题")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 1. 检查交易记录和胜率变化
        print("📊 检查交易记录和胜率变化:")
        
        trades_query = '''
            SELECT 
                symbol,
                buy_date,
                sell_date,
                buy_price,
                sell_price,
                return_pct,
                hold_days,
                sell_reason
            FROM trades 
            WHERE sell_date IS NOT NULL
            ORDER BY sell_date DESC
            LIMIT 50
        '''
        
        trades_df = pd.read_sql_query(trades_query, conn)
        
        if not trades_df.empty:
            # 计算整体胜率
            total_trades = len(trades_df)
            winning_trades = (trades_df['return_pct'] > 0).sum()
            overall_win_rate = winning_trades / total_trades
            avg_return = trades_df['return_pct'].mean()
            
            print(f"   最近50笔交易统计:")
            print(f"   总交易数: {total_trades}")
            print(f"   盈利交易: {winning_trades}")
            print(f"   胜率: {overall_win_rate:.2%}")
            print(f"   平均收益率: {avg_return:.2%}")
            
            # 按日期分析胜率趋势
            trades_df['sell_date'] = pd.to_datetime(trades_df['sell_date'])
            trades_df['date'] = trades_df['sell_date'].dt.date
            
            daily_stats = trades_df.groupby('date').agg({
                'return_pct': ['count', lambda x: (x > 0).mean(), 'mean']
            }).round(4)
            
            print(f"\n   📅 每日胜率趋势 (最近10天):")
            for date, stats in daily_stats.tail(10).iterrows():
                count = int(stats[('return_pct', 'count')])
                win_rate = stats[('return_pct', '<lambda>')]
                avg_ret = stats[('return_pct', 'mean')]
                print(f"     {date}: {count}笔, 胜率{win_rate:.2%}, 平均{avg_ret:.2%}")
            
            # 分析收益分布
            print(f"\n   📊 收益分布分析:")
            print(f"   最大收益: {trades_df['return_pct'].max():.2%}")
            print(f"   最大亏损: {trades_df['return_pct'].min():.2%}")
            print(f"   收益标准差: {trades_df['return_pct'].std():.2%}")
            
            # 分析持仓天数
            avg_hold_days = trades_df['hold_days'].mean()
            print(f"   平均持仓天数: {avg_hold_days:.1f}天")
            
            return trades_df, overall_win_rate, avg_return
        else:
            print("   ❌ 没有找到交易记录")
            return None, None, None
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None, None, None

def analyze_buy_signals_quality():
    """分析买入信号质量"""
    print("\n🎯 分析买入信号质量")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 检查买入记录
        buy_records_query = '''
            SELECT 
                symbol,
                date,
                combined_score,
                factors_count,
                technical_score,
                fundamental_score,
                sentiment_score,
                cross_market_score,
                overall_score
            FROM buy_records 
            ORDER BY date DESC
            LIMIT 100
        '''
        
        try:
            buy_records = pd.read_sql_query(buy_records_query, conn)
            
            if not buy_records.empty:
                print(f"   最近100个买入信号分析:")
                print(f"   信号数量: {len(buy_records)}")
                print(f"   平均综合评分: {buy_records['combined_score'].mean():.4f}")
                print(f"   平均因子数量: {buy_records['factors_count'].mean():.1f}")
                
                # 检查新因子字段
                new_factor_fields = ['technical_score', 'fundamental_score', 'sentiment_score', 'cross_market_score', 'overall_score']
                available_fields = [field for field in new_factor_fields if field in buy_records.columns]
                
                if available_fields:
                    print(f"   ✅ 新因子字段可用: {len(available_fields)}/5")
                    for field in available_fields:
                        avg_score = buy_records[field].mean()
                        print(f"     {field}: {avg_score:.4f}")
                else:
                    print(f"   ❌ 新因子字段不可用，可能仍在使用旧系统")
                
                # 分析评分分布
                print(f"\n   📊 评分分布:")
                print(f"   评分范围: {buy_records['combined_score'].min():.4f} - {buy_records['combined_score'].max():.4f}")
                
                # 检查是否符合新标准
                new_standard_signals = buy_records[
                    (buy_records['combined_score'] >= 0.35) & 
                    (buy_records['factors_count'] >= 3)
                ]
                
                compliance_rate = len(new_standard_signals) / len(buy_records)
                print(f"   符合新标准(≥0.35, ≥3因子): {len(new_standard_signals)}/{len(buy_records)} ({compliance_rate:.1%})")
                
                return buy_records, available_fields
            else:
                print("   ❌ 没有买入记录")
                return None, []
                
        except Exception as e:
            print(f"   ⚠️ 买入记录表可能不存在或结构不同: {e}")
            return None, []
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None, []

def analyze_ml_optimization_effect():
    """分析机器学习优化效果"""
    print("\n🤖 分析机器学习优化效果")
    print("=" * 60)
    
    try:
        # 检查配置文件中的ML优化
        from config import EFFECTIVE_FACTORS_CONFIG
        
        print("📋 当前配置分析:")
        
        # 检查权重配置
        if 'scoring_weights' in EFFECTIVE_FACTORS_CONFIG:
            weights = EFFECTIVE_FACTORS_CONFIG['scoring_weights']
            print(f"   权重配置:")
            for key, value in weights.items():
                if key != 'optimization_note':
                    print(f"     {key}: {value}")
            
            # 检查是否使用了ML优化的权重
            sentiment_weight = weights.get('sentiment_score', 0)
            if sentiment_weight >= 0.25:
                print(f"   ✅ 使用ML优化权重 (情绪面权重: {sentiment_weight})")
            else:
                print(f"   ⚠️ 可能未使用ML优化权重 (情绪面权重: {sentiment_weight})")
        
        # 检查买入条件
        if 'buy_conditions' in EFFECTIVE_FACTORS_CONFIG:
            conditions = EFFECTIVE_FACTORS_CONFIG['buy_conditions']
            print(f"\n   买入条件:")
            print(f"     min_combined_score: {conditions.get('min_combined_score')}")
            print(f"     min_factors_count: {conditions.get('min_factors_count')}")
            print(f"     require_top3_factors: {conditions.get('require_top3_factors')}")
            
            # 分析阈值设置
            min_score = conditions.get('min_combined_score', 1.0)
            if min_score <= 0.35:
                print(f"   ✅ 使用优化后的低阈值: {min_score}")
            else:
                print(f"   ⚠️ 阈值可能过高: {min_score}")
        
        # 检查是否有ML模型文件
        ml_files = []
        for file in os.listdir('.'):
            if file.endswith('.pkl') or file.endswith('.joblib') or 'model' in file.lower():
                ml_files.append(file)
        
        if ml_files:
            print(f"\n   🔍 发现ML模型文件: {ml_files}")
        else:
            print(f"\n   ⚠️ 未发现ML模型文件")
        
        return True
        
    except Exception as e:
        print(f"❌ ML优化分析失败: {e}")
        return False

def analyze_factor_effectiveness():
    """分析因子有效性"""
    print("\n📊 分析因子有效性")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 尝试关联买入记录和交易结果
        correlation_query = '''
            SELECT 
                b.symbol,
                b.date as buy_date,
                b.combined_score,
                b.factors_count,
                t.return_pct,
                t.hold_days
            FROM buy_records b
            LEFT JOIN trades t ON b.symbol = t.symbol 
                AND DATE(b.date) = DATE(t.buy_date)
            WHERE t.return_pct IS NOT NULL
            ORDER BY b.date DESC
            LIMIT 100
        '''
        
        try:
            correlation_df = pd.read_sql_query(correlation_query, conn)
            
            if not correlation_df.empty:
                print(f"   成功关联的交易记录: {len(correlation_df)}")
                
                # 分析评分与收益的相关性
                score_return_corr = correlation_df['combined_score'].corr(correlation_df['return_pct'])
                factors_return_corr = correlation_df['factors_count'].corr(correlation_df['return_pct'])
                
                print(f"   评分与收益相关性: {score_return_corr:.4f}")
                print(f"   因子数量与收益相关性: {factors_return_corr:.4f}")
                
                # 按评分分组分析胜率
                correlation_df['score_group'] = pd.cut(correlation_df['combined_score'], 
                                                     bins=[0, 0.3, 0.4, 0.5, 1.0], 
                                                     labels=['低(≤0.3)', '中(0.3-0.4)', '高(0.4-0.5)', '极高(>0.5)'])
                
                group_stats = correlation_df.groupby('score_group').agg({
                    'return_pct': ['count', lambda x: (x > 0).mean(), 'mean']
                }).round(4)
                
                print(f"\n   📊 按评分分组的胜率分析:")
                for group, stats in group_stats.iterrows():
                    count = int(stats[('return_pct', 'count')])
                    win_rate = stats[('return_pct', '<lambda>')]
                    avg_return = stats[('return_pct', 'mean')]
                    print(f"     {group}: {count}笔, 胜率{win_rate:.2%}, 平均{avg_return:.2%}")
                
                # 检查高评分信号的表现
                high_score_signals = correlation_df[correlation_df['combined_score'] >= 0.4]
                if not high_score_signals.empty:
                    high_score_win_rate = (high_score_signals['return_pct'] > 0).mean()
                    high_score_avg_return = high_score_signals['return_pct'].mean()
                    print(f"\n   🎯 高评分信号(≥0.4)表现:")
                    print(f"     数量: {len(high_score_signals)}")
                    print(f"     胜率: {high_score_win_rate:.2%}")
                    print(f"     平均收益: {high_score_avg_return:.2%}")
                
                return correlation_df
            else:
                print("   ❌ 无法关联买入记录和交易结果")
                return None
                
        except Exception as e:
            print(f"   ⚠️ 关联分析失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 因子有效性分析失败: {e}")
        return None

def analyze_system_problems():
    """分析系统问题"""
    print("\n🔧 分析系统问题")
    print("=" * 60)
    
    problems = []
    recommendations = []
    
    # 1. 检查日志中的错误
    try:
        with open('logs/strategy.log', 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 检查错误和警告
        error_keywords = ['ERROR', 'Exception', 'Failed', 'Error', '错误', '失败']
        warning_keywords = ['WARNING', 'Warning', '警告', '注意']
        
        error_count = sum(log_content.count(keyword) for keyword in error_keywords)
        warning_count = sum(log_content.count(keyword) for keyword in warning_keywords)
        
        print(f"   日志分析:")
        print(f"     错误数量: {error_count}")
        print(f"     警告数量: {warning_count}")
        
        if error_count > 0:
            problems.append(f"日志中发现{error_count}个错误")
            recommendations.append("检查日志中的具体错误信息")
        
        # 检查智能化系统标识
        intelligent_indicators = [
            '智能化68个因子计算完成',
            '智能化筛选: 通过',
            'EnhancedMultiFactorEngine',
            'IntelligentStrategyExecutor'
        ]
        
        found_indicators = 0
        for indicator in intelligent_indicators:
            if indicator in log_content:
                found_indicators += 1
        
        print(f"     智能化系统标识: {found_indicators}/{len(intelligent_indicators)}")
        
        if found_indicators < 2:
            problems.append("智能化系统可能未完全激活")
            recommendations.append("检查智能化模块是否正确加载")
            
    except Exception as e:
        problems.append(f"无法读取日志文件: {e}")
    
    # 2. 检查配置问题
    try:
        from config import EFFECTIVE_FACTORS_CONFIG
        
        conditions = EFFECTIVE_FACTORS_CONFIG.get('buy_conditions', {})
        min_score = conditions.get('min_combined_score', 1.0)
        min_factors = conditions.get('min_factors_count', 10)
        
        if min_score > 0.4:
            problems.append(f"买入阈值过高: {min_score}")
            recommendations.append("降低min_combined_score到0.30-0.35")
        
        if min_factors > 5:
            problems.append(f"因子数量要求过高: {min_factors}")
            recommendations.append("降低min_factors_count到3-5")
            
    except Exception as e:
        problems.append(f"配置检查失败: {e}")
    
    # 3. 输出问题和建议
    if problems:
        print(f"\n   🚨 发现的问题:")
        for i, problem in enumerate(problems, 1):
            print(f"     {i}. {problem}")
        
        print(f"\n   💡 改进建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"     {i}. {rec}")
    else:
        print(f"\n   ✅ 未发现明显的系统问题")
    
    return problems, recommendations

def generate_performance_analysis_report():
    """生成性能分析报告"""
    print("\n📋 生成性能分析报告")
    print("=" * 80)
    
    # 执行所有分析
    trades_df, win_rate, avg_return = analyze_win_rate_decline()
    buy_records, new_fields = analyze_buy_signals_quality()
    ml_ok = analyze_ml_optimization_effect()
    correlation_df = analyze_factor_effectiveness()
    problems, recommendations = analyze_system_problems()
    
    # 生成总结报告
    print(f"\n🏆 性能分析总结报告")
    print("=" * 50)
    
    if win_rate is not None:
        print(f"📊 当前表现:")
        print(f"   胜率: {win_rate:.2%}")
        print(f"   平均收益率: {avg_return:.2%}")
        
        if win_rate < 0.42:
            print(f"   ⚠️ 胜率低于预期基线(42%)")
        else:
            print(f"   ✅ 胜率符合或超过基线")
    
    print(f"\n🔧 系统状态:")
    print(f"   新因子字段: {'✅ 可用' if new_fields else '❌ 不可用'}")
    print(f"   ML优化配置: {'✅ 正常' if ml_ok else '❌ 异常'}")
    print(f"   系统问题: {len(problems)}个")
    
    # 给出核心建议
    print(f"\n🎯 核心建议:")
    
    if win_rate is not None and win_rate < 0.42:
        print("1. 🚨 胜率下降需要立即调整策略参数")
        print("2. 📊 考虑进一步降低筛选阈值")
        print("3. 🔍 分析失败交易的共同特征")
    
    if not new_fields:
        print("4. 🔧 确保智能化68个因子系统正确运行")
        print("5. 📋 检查数据库表结构和字段")
    
    if problems:
        print("6. 🛠️ 解决发现的系统问题")
        print("7. 📝 优化日志记录和错误处理")
    
    print(f"\n💡 可能的原因:")
    print("1. 市场环境变化，历史优化参数不适用")
    print("2. 智能化系统虽然运行但权重配置需要调整")
    print("3. 68个因子中某些因子在当前市场失效")
    print("4. 筛选阈值仍然过于严格")
    print("5. 需要更多的历史数据来验证优化效果")

def main():
    """主函数"""
    print("🔍 深度分析胜率和收益率下降问题")
    print("=" * 80)
    
    print("🎯 目标: 找出优化无效的根本原因并提供解决方案")
    
    # 生成完整的性能分析报告
    generate_performance_analysis_report()
    
    print(f"\n🏆 分析完成")
    print("=" * 40)
    print("📊 请根据以上分析结果调整策略参数")
    print("🔧 建议优先解决发现的系统问题")
    print("📈 持续监控胜率变化趋势")

if __name__ == '__main__':
    main()
