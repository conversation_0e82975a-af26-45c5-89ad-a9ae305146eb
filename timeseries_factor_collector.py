# coding=utf-8
"""
时序因子数据采集器
定时采集所有股票的因子数据，构建时序数据库
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import threading
from enhanced_factor_engine import EnhancedFactorEngine
import logging

class TimeSeriesFactorCollector:
    """时序因子数据采集器"""
    
    def __init__(self, context=None):
        self.context = context
        self.factor_engine = None
        self.db_path = 'data/timeseries_factors.db'
        self.collection_interval = 15 * 60  # 15分钟
        self.is_running = False
        self.collection_thread = None
        
        # 初始化数据库
        self.init_database()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    def init_database(self):
        """初始化时序数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建时序因子数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS timeseries_factors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    symbol VARCHAR(20) NOT NULL,
                    timeframe VARCHAR(10) DEFAULT '15m',
                    atr_pct FLOAT,
                    macd FLOAT,
                    macd_signal FLOAT,
                    macd_hist FLOAT,
                    bb_width FLOAT,
                    bb_position FLOAT,
                    rsi FLOAT,
                    adx FLOAT,
                    cci FLOAT,
                    trix_buy FLOAT,
                    relative_volume FLOAT,
                    volume_change_rate FLOAT,
                    ma20 FLOAT,
                    distance_from_high FLOAT,
                    price FLOAT,
                    volume FLOAT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol_timestamp ON timeseries_factors(symbol, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON timeseries_factors(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timeframe ON timeseries_factors(timeframe)')
            
            conn.commit()
            conn.close()
            
            print(f'✅ 时序数据库初始化完成: {self.db_path}')
            
        except Exception as e:
            print(f'❌ 数据库初始化失败: {e}')
    
    def get_stock_list(self):
        """获取股票列表"""
        try:
            # 从现有trades数据库获取股票列表
            conn = sqlite3.connect('data/trades.db')
            query = "SELECT DISTINCT symbol FROM trades WHERE action = 'BUY'"
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            symbols = df['symbol'].tolist()
            print(f'📊 获取到{len(symbols)}只股票用于时序数据采集')
            return symbols
            
        except Exception as e:
            print(f'❌ 获取股票列表失败: {e}')
            return []
    
    def collect_factors_for_symbol(self, symbol):
        """为单只股票采集因子数据"""
        try:
            if not self.factor_engine:
                if self.context:
                    self.factor_engine = EnhancedFactorEngine(self.context)
                else:
                    print(f'⚠️ 无法为{symbol}创建因子引擎，缺少context')
                    return None
            
            # 这里需要获取股票的历史数据
            # 由于我们没有实时数据源，暂时使用模拟数据
            # 在实际应用中，这里应该调用数据接口获取最新的K线数据
            
            # 模拟数据生成（实际应用中应该替换为真实数据获取）
            current_time = datetime.now()
            
            # 生成模拟的价格和成交量数据
            np.random.seed(int(time.time()) % 1000)
            base_price = 10 + np.random.random() * 90  # 10-100的随机价格
            price_change = (np.random.random() - 0.5) * 0.1  # ±5%的价格变化
            current_price = base_price * (1 + price_change)
            current_volume = np.random.randint(1000, 100000)
            
            # 创建模拟的历史数据DataFrame
            dates = pd.date_range(end=current_time, periods=100, freq='15T')
            prices = [base_price * (1 + (np.random.random() - 0.5) * 0.02) for _ in range(100)]
            volumes = [np.random.randint(1000, 50000) for _ in range(100)]
            
            data = pd.DataFrame({
                'timestamp': dates,
                'open': prices,
                'high': [p * (1 + np.random.random() * 0.02) for p in prices],
                'low': [p * (1 - np.random.random() * 0.02) for p in prices],
                'close': prices,
                'volume': volumes
            })
            data.set_index('timestamp', inplace=True)
            
            # 计算因子
            factors = self.factor_engine.calculate_all_factors(data, symbol)
            
            # 添加基础数据
            factors['price'] = current_price
            factors['volume'] = current_volume
            
            return factors
            
        except Exception as e:
            print(f'❌ 为{symbol}采集因子失败: {e}')
            return None
    
    def save_factors_to_db(self, symbol, factors, timestamp=None):
        """保存因子数据到数据库"""
        try:
            if not factors:
                return False
            
            if timestamp is None:
                timestamp = datetime.now()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 准备插入数据
            insert_data = {
                'timestamp': timestamp,
                'symbol': symbol,
                'timeframe': '15m'
            }
            
            # 添加因子数据
            factor_fields = [
                'atr_pct', 'macd', 'macd_signal', 'macd_hist', 'bb_width', 
                'bb_position', 'rsi', 'adx', 'cci', 'trix_buy', 
                'relative_volume', 'volume_change_rate', 'ma20', 
                'distance_from_high', 'price', 'volume'
            ]
            
            for field in factor_fields:
                if field in factors:
                    value = factors[field]
                    # 处理NaN值
                    if pd.isna(value) or np.isnan(value) if isinstance(value, (int, float)) else False:
                        insert_data[field] = None
                    else:
                        insert_data[field] = float(value)
                else:
                    insert_data[field] = None
            
            # 构建SQL语句
            fields = list(insert_data.keys())
            placeholders = ', '.join(['?' for _ in fields])
            sql = f"INSERT INTO timeseries_factors ({', '.join(fields)}) VALUES ({placeholders})"
            
            cursor.execute(sql, list(insert_data.values()))
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f'❌ 保存{symbol}因子数据失败: {e}')
            return False
    
    def collect_all_factors(self):
        """采集所有股票的因子数据"""
        print(f'🔄 开始采集时序因子数据 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        symbols = self.get_stock_list()
        if not symbols:
            print('⚠️ 没有获取到股票列表')
            return
        
        success_count = 0
        total_count = len(symbols)
        
        for i, symbol in enumerate(symbols, 1):
            try:
                print(f'📊 采集 {symbol} ({i}/{total_count})')
                
                # 采集因子
                factors = self.collect_factors_for_symbol(symbol)
                
                if factors:
                    # 保存到数据库
                    if self.save_factors_to_db(symbol, factors):
                        success_count += 1
                        print(f'✅ {symbol} 因子数据保存成功')
                    else:
                        print(f'❌ {symbol} 因子数据保存失败')
                else:
                    print(f'⚠️ {symbol} 因子计算失败')
                
                # 避免过于频繁的请求
                time.sleep(0.1)
                
            except Exception as e:
                print(f'❌ 处理{symbol}时出错: {e}')
        
        print(f'📊 采集完成: {success_count}/{total_count} 成功')
        return success_count, total_count
    
    def start_collection(self):
        """开始定时采集"""
        if self.is_running:
            print('⚠️ 采集器已经在运行中')
            return
        
        self.is_running = True
        print(f'🚀 启动时序因子采集器，间隔: {self.collection_interval/60}分钟')
        
        def collection_loop():
            while self.is_running:
                try:
                    # 执行采集
                    self.collect_all_factors()
                    
                    # 等待下一次采集
                    if self.is_running:
                        print(f'⏰ 等待{self.collection_interval/60}分钟后进行下一次采集...')
                        time.sleep(self.collection_interval)
                        
                except Exception as e:
                    print(f'❌ 采集循环出错: {e}')
                    time.sleep(60)  # 出错后等待1分钟再重试
        
        self.collection_thread = threading.Thread(target=collection_loop, daemon=True)
        self.collection_thread.start()
    
    def stop_collection(self):
        """停止采集"""
        if not self.is_running:
            print('⚠️ 采集器未在运行')
            return
        
        self.is_running = False
        print('🛑 正在停止时序因子采集器...')
        
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        
        print('✅ 时序因子采集器已停止')
    
    def get_timeseries_data(self, symbol, lookback_hours=24):
        """获取指定股票的时序数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 计算查询的时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=lookback_hours)
            
            query = """
                SELECT * FROM timeseries_factors 
                WHERE symbol = ? AND timestamp >= ? AND timestamp <= ?
                ORDER BY timestamp DESC
            """
            
            df = pd.read_sql_query(query, conn, params=[symbol, start_time, end_time])
            conn.close()
            
            return df
            
        except Exception as e:
            print(f'❌ 获取{symbol}时序数据失败: {e}')
            return pd.DataFrame()
    
    def cleanup_old_data(self, days_to_keep=30):
        """清理旧数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            cursor.execute("DELETE FROM timeseries_factors WHERE timestamp < ?", [cutoff_date])
            deleted_count = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            print(f'🧹 清理了{deleted_count}条超过{days_to_keep}天的旧数据')
            return deleted_count
            
        except Exception as e:
            print(f'❌ 清理旧数据失败: {e}')
            return 0

def main():
    """测试函数"""
    print('🧪 时序因子采集器测试')
    print('=' * 50)
    
    # 创建采集器
    collector = TimeSeriesFactorCollector()
    
    # 执行一次采集测试
    print('\n📊 执行一次采集测试...')
    success, total = collector.collect_all_factors()
    
    print(f'\n📈 采集结果: {success}/{total}')
    
    # 测试数据查询
    if success > 0:
        symbols = collector.get_stock_list()
        if symbols:
            test_symbol = symbols[0]
            print(f'\n🔍 测试查询{test_symbol}的时序数据...')
            data = collector.get_timeseries_data(test_symbol, lookback_hours=1)
            print(f'📊 查询到{len(data)}条记录')
            
            if len(data) > 0:
                print('\n📋 最新记录:')
                latest = data.iloc[0]
                print(f'   时间: {latest["timestamp"]}')
                print(f'   ATR: {latest["atr_pct"]:.4f}' if pd.notna(latest["atr_pct"]) else '   ATR: None')
                print(f'   MACD: {latest["macd"]:.4f}' if pd.notna(latest["macd"]) else '   MACD: None')
                print(f'   RSI: {latest["rsi"]:.4f}' if pd.notna(latest["rsi"]) else '   RSI: None')

if __name__ == '__main__':
    main()
