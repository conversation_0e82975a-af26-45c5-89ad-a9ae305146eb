@echo off
echo 正在启动万和策略分析系统 - 数据分析工具...
echo.

:: 获取当前脚本所在的目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

:: 尝试找到Python解释器
set PYTHON_PATH=python
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    :: 如果默认的python命令不可用，尝试特定路径
    if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
        set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
    ) else if exist "C:\Python39\python.exe" (
        set PYTHON_PATH=C:\Python39\python.exe
    ) else if exist "C:\Program Files\Python39\python.exe" (
        set PYTHON_PATH=C:\Program Files\Python39\python.exe
    ) else (
        echo 错误: 找不到Python解释器!
        echo 请确保已安装Python 3.6+，或者手动修改此批处理文件中的Python路径。
        pause
        exit /b 1
    )
)

echo 使用Python解释器: %PYTHON_PATH%
echo.

:: 运行数据分析启动脚本
"%PYTHON_PATH%" start_analysis.py

:: 如果直接运行脚本失败，尝试直接启动数据库工具
if %ERRORLEVEL% NEQ 0 (
    echo 直接运行分析脚本失败，尝试启动数据库管理工具...
    cd /d "%SCRIPT_DIR%\.."
    "%PYTHON_PATH%" scripts/db_advanced_tools.py --interactive
)

:: 脚本结束
exit /b 