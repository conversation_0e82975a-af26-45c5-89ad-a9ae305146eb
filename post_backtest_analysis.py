# coding=utf-8
"""
CCI优化回测后分析
深度分析CCI[20,30]优化的实际效果
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def analyze_backtest_results():
    """分析回测结果"""
    print('📊 CCI优化回测结果分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据 (回测后)
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 最新交易数据: {len(df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 确定回测时间范围 (假设最近2小时为回测数据)
        latest_time = df['timestamp'].max()
        backtest_start = latest_time - timedelta(hours=2)
        
        # 分离回测前后数据
        backtest_data = df[df['timestamp'] >= backtest_start]
        historical_data = df[df['timestamp'] < backtest_start]
        
        print(f'📊 数据分布:')
        print(f'   回测数据: {len(backtest_data)} 条 (最近2小时)')
        print(f'   历史数据: {len(historical_data)} 条')
        print(f'   回测时间: {backtest_start.strftime("%Y-%m-%d %H:%M")} 到 {latest_time.strftime("%Y-%m-%d %H:%M")}')
        
        return df, backtest_data, historical_data
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None

def compare_performance(backtest_data, historical_data):
    """对比回测前后表现"""
    print(f'\n📈 回测前后表现对比')
    print('=' * 50)
    
    # 分析买入和卖出记录
    def analyze_period(data, period_name):
        buy_records = data[data['action'] == 'BUY']
        sell_records = data[data['action'] == 'SELL']
        completed_trades = sell_records.dropna(subset=['net_profit_pct_sell'])
        
        if len(completed_trades) == 0:
            return None
        
        total_trades = len(completed_trades)
        profitable_trades = len(completed_trades[completed_trades['net_profit_pct_sell'] > 0])
        win_rate = profitable_trades / total_trades * 100
        avg_profit = completed_trades['net_profit_pct_sell'].mean()
        median_profit = completed_trades['net_profit_pct_sell'].median()
        
        return {
            'period': period_name,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'median_profit': median_profit,
            'buy_signals': len(buy_records)
        }
    
    # 分析历史数据 (优化前)
    historical_stats = analyze_period(historical_data, '优化前')
    
    # 分析回测数据 (优化后)
    backtest_stats = analyze_period(backtest_data, '优化后')
    
    if historical_stats and backtest_stats:
        print(f'📊 核心指标对比:')
        print(f'指标           优化前      优化后      变化')
        print(f'-' * 50)
        
        # 胜率对比
        win_rate_change = backtest_stats['win_rate'] - historical_stats['win_rate']
        print(f'胜率           {historical_stats["win_rate"]:6.1f}%    {backtest_stats["win_rate"]:6.1f}%    {win_rate_change:+6.1f}%')
        
        # 平均收益对比
        profit_change = backtest_stats['avg_profit'] - historical_stats['avg_profit']
        print(f'平均收益       {historical_stats["avg_profit"]:6.2f}%    {backtest_stats["avg_profit"]:6.2f}%    {profit_change:+6.2f}%')
        
        # 交易数量对比
        trade_change = backtest_stats['total_trades'] - historical_stats['total_trades']
        print(f'完成交易数     {historical_stats["total_trades"]:6d}      {backtest_stats["total_trades"]:6d}      {trade_change:+6d}')
        
        # 买入信号对比
        signal_change = backtest_stats['buy_signals'] - historical_stats['buy_signals']
        print(f'买入信号数     {historical_stats["buy_signals"]:6d}      {backtest_stats["buy_signals"]:6d}      {signal_change:+6d}')
        
        # 效果评估
        print(f'\n🎯 优化效果评估:')
        
        if win_rate_change >= 10:
            print(f'   🔥 胜率提升显著: +{win_rate_change:.1f}% (超预期)')
        elif win_rate_change >= 5:
            print(f'   ✅ 胜率提升良好: +{win_rate_change:.1f}% (符合预期)')
        elif win_rate_change >= 0:
            print(f'   📊 胜率小幅提升: +{win_rate_change:.1f}% (低于预期)')
        else:
            print(f'   ⚠️ 胜率下降: {win_rate_change:.1f}% (需要分析)')
        
        if profit_change >= 1.0:
            print(f'   🔥 收益提升显著: +{profit_change:.2f}% (超预期)')
        elif profit_change >= 0.5:
            print(f'   ✅ 收益提升良好: +{profit_change:.2f}% (符合预期)')
        elif profit_change >= 0:
            print(f'   📊 收益小幅提升: +{profit_change:.2f}% (低于预期)')
        else:
            print(f'   ⚠️ 收益下降: {profit_change:.2f}% (需要分析)')
        
        return historical_stats, backtest_stats, win_rate_change, profit_change
    
    else:
        print('⚠️ 数据不足，无法进行对比分析')
        return None, None, None, None

def analyze_cci_effectiveness(backtest_data):
    """分析CCI优化效果"""
    print(f'\n🔍 CCI优化效果深度分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取回测期间的CCI数据
        latest_time = backtest_data['timestamp'].max()
        backtest_start = latest_time - timedelta(hours=2)
        
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.cci,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.cci IS NOT NULL
            AND b.timestamp >= ?
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        """
        
        cci_df = pd.read_sql_query(query, conn, params=[backtest_start])
        conn.close()
        
        if len(cci_df) == 0:
            print('⚠️ 回测期间没有CCI匹配数据')
            return
        
        print(f'📊 回测期间CCI数据: {len(cci_df)} 条匹配交易')
        
        # 分析CCI区间分布
        cci_ranges = [
            ('最优区间 [20,30]', (cci_df['cci'] >= 20) & (cci_df['cci'] <= 30)),
            ('低于最优 [<20]', cci_df['cci'] < 20),
            ('高于最优 [>30]', cci_df['cci'] > 30),
            ('原策略范围 [25,200]', (cci_df['cci'] >= 25) & (cci_df['cci'] <= 200)),
        ]
        
        print(f'\n📈 CCI区间表现分析:')
        print(f'CCI区间           交易数  胜率%   平均收益%')
        print(f'-' * 45)
        
        for range_name, condition in cci_ranges:
            range_data = cci_df[condition]
            
            if len(range_data) > 0:
                win_rate = (range_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = range_data['net_profit_pct_sell'].mean()
                trade_count = len(range_data)
                
                print(f'{range_name:<15} {trade_count:6d} {win_rate:6.1f} {avg_profit:9.2f}')
        
        # 验证预期效果
        optimal_range = cci_df[(cci_df['cci'] >= 20) & (cci_df['cci'] <= 30)]
        if len(optimal_range) > 0:
            optimal_win_rate = (optimal_range['net_profit_pct_sell'] > 0).mean() * 100
            optimal_profit = optimal_range['net_profit_pct_sell'].mean()
            
            print(f'\n🎯 CCI[20,30]最优区间验证:')
            print(f'   实际胜率: {optimal_win_rate:.1f}%')
            print(f'   预期胜率: 66.7%')
            print(f'   胜率差异: {optimal_win_rate - 66.7:+.1f}%')
            print(f'   实际收益: {optimal_profit:.2f}%')
            print(f'   预期收益: 1.94%')
            print(f'   收益差异: {optimal_profit - 1.94:+.2f}%')
            
            if optimal_win_rate >= 60:
                print(f'   ✅ CCI优化效果显著')
            elif optimal_win_rate >= 50:
                print(f'   📊 CCI优化效果一般')
            else:
                print(f'   ⚠️ CCI优化效果不佳')
        
        return cci_df
        
    except Exception as e:
        print(f'❌ CCI分析失败: {e}')
        return None

def generate_optimization_decision(win_rate_change, profit_change):
    """生成优化决策建议"""
    print(f'\n🎯 优化决策建议')
    print('=' * 50)
    
    if win_rate_change >= 10 and profit_change >= 1.0:
        decision = 'EXCELLENT'
        recommendation = '''
🔥 优化效果卓越!

✅ 决策建议:
   1. 立即固化CCI[20,30]配置
   2. 加速执行第2天BB位置优化
   3. 提高整体优化目标 (胜率55% → 60%)
   4. 考虑扩大CCI权重

🚀 下一步行动:
   - 今晚准备第2天优化脚本
   - 明天上午开始BB位置分析
   - 加快整体优化进度
   - 提升最终目标预期
'''
        
    elif win_rate_change >= 5 and profit_change >= 0.5:
        decision = 'GOOD'
        recommendation = '''
✅ 优化效果良好!

📊 决策建议:
   1. 保持CCI[20,30]配置
   2. 按计划执行第2天优化
   3. 维持原定优化目标
   4. 继续监控CCI效果

🚀 下一步行动:
   - 按原计划准备第2天任务
   - 继续BB位置和RSI优化
   - 保持稳健优化节奏
   - 积累更多验证数据
'''
        
    elif win_rate_change >= 0 and profit_change >= 0:
        decision = 'MODERATE'
        recommendation = '''
📊 优化效果一般

🔧 决策建议:
   1. 微调CCI参数 (考虑[18,32]或[22,28])
   2. 延缓第2天优化，先完善CCI
   3. 深度分析CCI效果不佳原因
   4. 增加CCI验证时间

⚠️ 下一步行动:
   - 分析CCI效果不达预期的原因
   - 考虑参数微调
   - 延长验证时间
   - 谨慎推进后续优化
'''
        
    else:
        decision = 'POOR'
        recommendation = '''
⚠️ 优化效果不佳

🚨 决策建议:
   1. 立即回退到原配置
   2. 深度分析失败原因
   3. 重新评估CCI分析结果
   4. 暂停后续优化计划

🔄 下一步行动:
   - 执行配置回退
   - 深度分析数据差异
   - 重新验证分析方法
   - 制定新的优化策略
'''
    
    print(f'📋 优化效果评级: {decision}')
    print(recommendation)
    
    return decision

def main():
    """主函数"""
    print('🚀 CCI优化回测后分析')
    print('=' * 60)
    
    print('🎯 分析目标:')
    print('   验证CCI[20,30]优化的实际效果')
    print('   对比优化前后的关键指标')
    print('   制定下一步优化决策')
    
    # 分析回测结果
    df, backtest_data, historical_data = analyze_backtest_results()
    
    if df is not None and len(backtest_data) > 0:
        # 对比回测前后表现
        hist_stats, back_stats, win_change, profit_change = compare_performance(backtest_data, historical_data)
        
        # 分析CCI优化效果
        cci_df = analyze_cci_effectiveness(backtest_data)
        
        # 生成优化决策
        if win_change is not None and profit_change is not None:
            decision = generate_optimization_decision(win_change, profit_change)
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print('✅ 回测数据分析完成')
        print('✅ CCI优化效果评估完成')
        print('✅ 下一步决策建议已生成')
        
        if win_change is not None:
            print(f'📊 胜率变化: {win_change:+.1f}%')
            print(f'💰 收益变化: {profit_change:+.2f}%')
        
        print(f'\n💡 请根据分析结果决定下一步行动!')
    
    else:
        print('❌ 回测数据不足，请确保策略已运行足够时间')

if __name__ == '__main__':
    main()
