# coding=utf-8
"""
自适应策略优化器
基于实时表现自动调整策略参数
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from scipy.optimize import minimize
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdaptiveStrategyOptimizer:
    """自适应策略优化器"""
    
    def __init__(self):
        self.performance_history = []
        self.parameter_history = []
        self.optimization_results = {}
        self.current_parameters = {}
        self.adaptation_rules = {}
        
    def initialize_adaptation_rules(self):
        """初始化自适应规则"""
        logger.info("初始化自适应优化规则...")
        
        self.adaptation_rules = {
            # 胜率自适应规则
            'win_rate_adaptation': {
                'target_win_rate': 0.60,  # 目标胜率60%
                'tolerance': 0.05,        # 容忍度±5%
                'adjustment_strength': 0.1, # 调整强度10%
                'lookback_period': 20,    # 回看20个交易日
            },
            
            # 收益率自适应规则
            'return_adaptation': {
                'target_avg_return': 0.015,  # 目标平均收益1.5%
                'tolerance': 0.005,          # 容忍度±0.5%
                'adjustment_strength': 0.15,  # 调整强度15%
                'lookback_period': 15,       # 回看15个交易日
            },
            
            # 风险控制自适应规则
            'risk_adaptation': {
                'max_drawdown': 0.08,        # 最大回撤8%
                'max_single_loss': 0.05,     # 单次最大亏损5%
                'adjustment_strength': 0.2,   # 调整强度20%
                'lookback_period': 10,       # 回看10个交易日
            },
            
            # 信号数量自适应规则
            'signal_count_adaptation': {
                'target_daily_signals': 3,   # 目标每日信号数
                'min_signals': 1,            # 最少信号数
                'max_signals': 8,            # 最多信号数
                'adjustment_strength': 0.1,   # 调整强度10%
            },
            
            # 市场环境自适应规则
            'market_adaptation': {
                'volatility_threshold': 0.02,  # 波动率阈值2%
                'trend_threshold': 0.01,       # 趋势阈值1%
                'adaptation_speed': 0.2,       # 适应速度20%
            }
        }
        
        logger.info("✅ 自适应规则初始化完成")
    
    def record_performance(self, trade_result):
        """记录交易表现"""
        try:
            performance_record = {
                'timestamp': datetime.now(),
                'symbol': trade_result.get('symbol', ''),
                'entry_price': trade_result.get('entry_price', 0),
                'exit_price': trade_result.get('exit_price', 0),
                'return_pct': trade_result.get('return_pct', 0),
                'holding_days': trade_result.get('holding_days', 0),
                'win': trade_result.get('return_pct', 0) > 0,
                'factors_used': trade_result.get('factors_used', {}),
                'market_conditions': trade_result.get('market_conditions', {})
            }
            
            self.performance_history.append(performance_record)
            
            # 保持历史记录在合理范围内
            if len(self.performance_history) > 1000:
                self.performance_history = self.performance_history[-1000:]
            
            logger.info(f"记录交易表现: {trade_result.get('symbol', '')} {trade_result.get('return_pct', 0):.2%}")
            
        except Exception as e:
            logger.error(f"记录交易表现失败: {e}")
    
    def analyze_recent_performance(self, lookback_days=20):
        """分析最近表现"""
        try:
            if len(self.performance_history) < 5:
                logger.warning("交易历史数据不足，无法进行分析")
                return None
            
            # 获取最近的交易记录
            cutoff_date = datetime.now() - timedelta(days=lookback_days)
            recent_trades = [
                trade for trade in self.performance_history 
                if trade['timestamp'] >= cutoff_date
            ]
            
            if len(recent_trades) < 3:
                logger.warning(f"最近{lookback_days}天交易数据不足")
                return None
            
            # 计算关键指标
            returns = [trade['return_pct'] for trade in recent_trades]
            wins = [trade['win'] for trade in recent_trades]
            
            analysis = {
                'total_trades': len(recent_trades),
                'win_rate': np.mean(wins),
                'avg_return': np.mean(returns),
                'total_return': np.sum(returns),
                'max_loss': np.min(returns),
                'max_gain': np.max(returns),
                'volatility': np.std(returns),
                'sharpe_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0,
                'consecutive_losses': self._calculate_consecutive_losses(recent_trades),
                'avg_holding_days': np.mean([trade['holding_days'] for trade in recent_trades])
            }
            
            logger.info(f"最近{lookback_days}天表现分析: 胜率{analysis['win_rate']:.2%}, 平均收益{analysis['avg_return']:.2%}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析最近表现失败: {e}")
            return None
    
    def _calculate_consecutive_losses(self, trades):
        """计算连续亏损次数"""
        max_consecutive = 0
        current_consecutive = 0
        
        for trade in reversed(trades):  # 从最新开始
            if not trade['win']:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def detect_adaptation_needs(self):
        """检测自适应需求"""
        logger.info("检测策略自适应需求...")
        
        adaptation_needs = {}
        
        try:
            # 分析最近表现
            performance = self.analyze_recent_performance()
            if not performance:
                return {}
            
            rules = self.adaptation_rules
            
            # 1. 胜率自适应检测
            win_rate_rule = rules['win_rate_adaptation']
            target_win_rate = win_rate_rule['target_win_rate']
            tolerance = win_rate_rule['tolerance']
            current_win_rate = performance['win_rate']
            
            if abs(current_win_rate - target_win_rate) > tolerance:
                adaptation_needs['win_rate'] = {
                    'current': current_win_rate,
                    'target': target_win_rate,
                    'deviation': current_win_rate - target_win_rate,
                    'action': 'tighten_criteria' if current_win_rate < target_win_rate else 'relax_criteria',
                    'urgency': 'high' if abs(current_win_rate - target_win_rate) > tolerance * 2 else 'medium'
                }
            
            # 2. 收益率自适应检测
            return_rule = rules['return_adaptation']
            target_return = return_rule['target_avg_return']
            tolerance = return_rule['tolerance']
            current_return = performance['avg_return']
            
            if abs(current_return - target_return) > tolerance:
                adaptation_needs['return'] = {
                    'current': current_return,
                    'target': target_return,
                    'deviation': current_return - target_return,
                    'action': 'increase_selectivity' if current_return < target_return else 'increase_aggressiveness',
                    'urgency': 'high' if abs(current_return - target_return) > tolerance * 2 else 'medium'
                }
            
            # 3. 风险控制自适应检测
            risk_rule = rules['risk_adaptation']
            max_loss_threshold = risk_rule['max_single_loss']
            current_max_loss = abs(performance['max_loss'])
            
            if current_max_loss > max_loss_threshold:
                adaptation_needs['risk'] = {
                    'current_max_loss': current_max_loss,
                    'threshold': max_loss_threshold,
                    'consecutive_losses': performance['consecutive_losses'],
                    'action': 'strengthen_risk_control',
                    'urgency': 'high' if performance['consecutive_losses'] >= 3 else 'medium'
                }
            
            # 4. 信号数量自适应检测
            signal_rule = rules['signal_count_adaptation']
            daily_trades = performance['total_trades'] / max(1, performance.get('days_analyzed', 20))
            target_signals = signal_rule['target_daily_signals']
            
            if daily_trades < signal_rule['min_signals']:
                adaptation_needs['signal_count'] = {
                    'current': daily_trades,
                    'target': target_signals,
                    'action': 'relax_criteria',
                    'urgency': 'medium'
                }
            elif daily_trades > signal_rule['max_signals']:
                adaptation_needs['signal_count'] = {
                    'current': daily_trades,
                    'target': target_signals,
                    'action': 'tighten_criteria',
                    'urgency': 'medium'
                }
            
            logger.info(f"检测到 {len(adaptation_needs)} 个自适应需求")
            
            return adaptation_needs
            
        except Exception as e:
            logger.error(f"检测自适应需求失败: {e}")
            return {}
    
    def generate_parameter_adjustments(self, adaptation_needs):
        """生成参数调整建议"""
        logger.info("生成参数调整建议...")
        
        adjustments = {}
        
        try:
            for need_type, need_info in adaptation_needs.items():
                if need_type == 'win_rate':
                    adjustments.update(self._adjust_for_win_rate(need_info))
                elif need_type == 'return':
                    adjustments.update(self._adjust_for_return(need_info))
                elif need_type == 'risk':
                    adjustments.update(self._adjust_for_risk(need_info))
                elif need_type == 'signal_count':
                    adjustments.update(self._adjust_for_signal_count(need_info))
            
            logger.info(f"生成了 {len(adjustments)} 个参数调整建议")
            
            return adjustments
            
        except Exception as e:
            logger.error(f"生成参数调整失败: {e}")
            return {}
    
    def _adjust_for_win_rate(self, need_info):
        """基于胜率调整参数"""
        adjustments = {}
        
        deviation = need_info['deviation']
        adjustment_strength = self.adaptation_rules['win_rate_adaptation']['adjustment_strength']
        
        if need_info['action'] == 'tighten_criteria':
            # 胜率低，收紧筛选条件
            adjustments['min_combined_score'] = {
                'current': 0.50,
                'adjustment': +0.05 * adjustment_strength,
                'reason': f'胜率{need_info["current"]:.2%}低于目标{need_info["target"]:.2%}'
            }
            adjustments['atr_min_threshold'] = {
                'current': 3.0,
                'adjustment': +0.3 * adjustment_strength,
                'reason': '提升ATR阈值增强信号质量'
            }
            adjustments['adx_min_threshold'] = {
                'current': 28,
                'adjustment': +2 * adjustment_strength,
                'reason': '提升ADX阈值确保趋势强度'
            }
        else:
            # 胜率高，可以放宽条件获得更多信号
            adjustments['min_combined_score'] = {
                'current': 0.50,
                'adjustment': -0.03 * adjustment_strength,
                'reason': f'胜率{need_info["current"]:.2%}高于目标，可适当放宽'
            }
        
        return adjustments
    
    def _adjust_for_return(self, need_info):
        """基于收益率调整参数"""
        adjustments = {}
        
        adjustment_strength = self.adaptation_rules['return_adaptation']['adjustment_strength']
        
        if need_info['action'] == 'increase_selectivity':
            # 收益率低，增加选择性
            adjustments['cci_weight'] = {
                'current': 0.10,
                'adjustment': +0.02 * adjustment_strength,
                'reason': f'平均收益{need_info["current"]:.2%}低于目标，增强CCI权重'
            }
            adjustments['fundamental_score_weight'] = {
                'current': 0.25,
                'adjustment': +0.05 * adjustment_strength,
                'reason': '增强基本面权重提升收益质量'
            }
        else:
            # 收益率高，可以增加激进性
            adjustments['volume_breakthrough_weight'] = {
                'current': 0.06,
                'adjustment': +0.02 * adjustment_strength,
                'reason': f'收益表现良好，增强成交量突破权重'
            }
        
        return adjustments
    
    def _adjust_for_risk(self, need_info):
        """基于风险调整参数"""
        adjustments = {}
        
        adjustment_strength = self.adaptation_rules['risk_adaptation']['adjustment_strength']
        
        if need_info['action'] == 'strengthen_risk_control':
            # 加强风险控制
            adjustments['min_combined_score'] = {
                'current': 0.50,
                'adjustment': +0.08 * adjustment_strength,
                'reason': f'最大亏损{need_info["current_max_loss"]:.2%}超过阈值，加强风险控制'
            }
            adjustments['max_signals_per_day'] = {
                'current': 10,
                'adjustment': -2 * adjustment_strength,
                'reason': '减少信号数量降低风险暴露'
            }
            adjustments['stop_loss_threshold'] = {
                'current': 0.05,
                'adjustment': -0.01 * adjustment_strength,
                'reason': '收紧止损阈值'
            }
        
        return adjustments
    
    def _adjust_for_signal_count(self, need_info):
        """基于信号数量调整参数"""
        adjustments = {}
        
        if need_info['action'] == 'relax_criteria':
            # 信号太少，放宽条件
            adjustments['min_combined_score'] = {
                'current': 0.50,
                'adjustment': -0.05,
                'reason': f'日均信号{need_info["current"]:.1f}个过少，放宽筛选条件'
            }
            adjustments['cci_max_threshold'] = {
                'current': 120,
                'adjustment': +10,
                'reason': '放宽CCI上限增加信号'
            }
        else:
            # 信号太多，收紧条件
            adjustments['min_combined_score'] = {
                'current': 0.50,
                'adjustment': +0.05,
                'reason': f'日均信号{need_info["current"]:.1f}个过多，收紧筛选条件'
            }
        
        return adjustments
    
    def apply_parameter_adjustments(self, adjustments):
        """应用参数调整"""
        logger.info("应用参数调整...")
        
        applied_adjustments = {}
        
        try:
            for param_name, adjustment_info in adjustments.items():
                current_value = adjustment_info['current']
                adjustment = adjustment_info['adjustment']
                new_value = current_value + adjustment
                
                # 应用合理性检查
                new_value = self._validate_parameter_value(param_name, new_value)
                
                applied_adjustments[param_name] = {
                    'old_value': current_value,
                    'new_value': new_value,
                    'adjustment': adjustment,
                    'reason': adjustment_info['reason']
                }
                
                logger.info(f"调整 {param_name}: {current_value:.3f} → {new_value:.3f}")
            
            # 记录调整历史
            self.parameter_history.append({
                'timestamp': datetime.now(),
                'adjustments': applied_adjustments,
                'trigger': 'adaptive_optimization'
            })
            
            logger.info(f"✅ 应用了 {len(applied_adjustments)} 个参数调整")
            
            return applied_adjustments
            
        except Exception as e:
            logger.error(f"应用参数调整失败: {e}")
            return {}
    
    def _validate_parameter_value(self, param_name, value):
        """验证参数值的合理性"""
        # 参数范围限制
        param_limits = {
            'min_combined_score': (0.3, 0.8),
            'atr_min_threshold': (1.0, 6.0),
            'adx_min_threshold': (15, 45),
            'cci_max_threshold': (80, 200),
            'max_signals_per_day': (1, 20),
            'stop_loss_threshold': (0.02, 0.10)
        }
        
        if param_name in param_limits:
            min_val, max_val = param_limits[param_name]
            return max(min_val, min(max_val, value))
        
        return value
    
    def generate_optimization_report(self):
        """生成优化报告"""
        if not self.performance_history:
            return "没有足够的历史数据生成报告"
        
        report = "🔄 自适应策略优化报告\n"
        report += "=" * 60 + "\n\n"
        
        # 最近表现分析
        performance = self.analyze_recent_performance()
        if performance:
            report += "📊 最近表现分析:\n"
            report += f"   总交易次数: {performance['total_trades']}\n"
            report += f"   胜率: {performance['win_rate']:.2%}\n"
            report += f"   平均收益: {performance['avg_return']:.2%}\n"
            report += f"   最大亏损: {performance['max_loss']:.2%}\n"
            report += f"   夏普比率: {performance['sharpe_ratio']:.3f}\n"
            report += f"   连续亏损: {performance['consecutive_losses']}次\n"
        
        # 自适应需求分析
        adaptation_needs = self.detect_adaptation_needs()
        if adaptation_needs:
            report += f"\n🎯 检测到的优化需求:\n"
            for need_type, need_info in adaptation_needs.items():
                report += f"   {need_type}: {need_info['action']} (紧急度: {need_info['urgency']})\n"
        
        # 参数调整历史
        if self.parameter_history:
            recent_adjustments = self.parameter_history[-3:]  # 最近3次调整
            report += f"\n🔧 最近参数调整:\n"
            for i, adjustment in enumerate(recent_adjustments, 1):
                report += f"   {i}. {adjustment['timestamp'].strftime('%Y-%m-%d %H:%M')}: "
                report += f"{len(adjustment['adjustments'])}个参数调整\n"
        
        # 优化建议
        report += f"\n💡 优化建议:\n"
        report += f"   1. 持续监控胜率和收益率指标\n"
        report += f"   2. 根据市场环境动态调整参数\n"
        report += f"   3. 保持风险控制优先原则\n"
        report += f"   4. 定期回顾和验证调整效果\n"
        
        return report
    
    def save_optimization_state(self, filename=None):
        """保存优化状态"""
        if filename is None:
            filename = f"adaptive_optimizer_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            state_data = {
                'performance_history': [
                    {**record, 'timestamp': record['timestamp'].isoformat()}
                    for record in self.performance_history[-100:]  # 保存最近100条
                ],
                'parameter_history': [
                    {**record, 'timestamp': record['timestamp'].isoformat()}
                    for record in self.parameter_history[-20:]  # 保存最近20次调整
                ],
                'adaptation_rules': self.adaptation_rules,
                'current_parameters': self.current_parameters
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 优化状态已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存优化状态失败: {e}")
            return None

def main():
    """主函数"""
    print("🔄 自适应策略优化器")
    print("=" * 60)
    
    # 创建优化器
    optimizer = AdaptiveStrategyOptimizer()
    
    # 初始化自适应规则
    optimizer.initialize_adaptation_rules()
    
    # 模拟一些交易记录
    print("📊 模拟交易记录...")
    sample_trades = [
        {'symbol': 'SZSE.000001', 'return_pct': 0.025, 'holding_days': 3},
        {'symbol': 'SZSE.000002', 'return_pct': -0.015, 'holding_days': 2},
        {'symbol': 'SHSE.600000', 'return_pct': 0.018, 'holding_days': 4},
        {'symbol': 'SHSE.600036', 'return_pct': -0.008, 'holding_days': 1},
        {'symbol': 'SZSE.300015', 'return_pct': 0.032, 'holding_days': 5},
        {'symbol': 'SZSE.000858', 'return_pct': -0.022, 'holding_days': 2},
        {'symbol': 'SHSE.600519', 'return_pct': 0.041, 'holding_days': 6},
        {'symbol': 'SHSE.600887', 'return_pct': 0.012, 'holding_days': 3},
    ]
    
    for trade in sample_trades:
        optimizer.record_performance(trade)
    
    # 检测自适应需求
    adaptation_needs = optimizer.detect_adaptation_needs()
    
    if adaptation_needs:
        print(f"\n🎯 检测到 {len(adaptation_needs)} 个优化需求")
        
        # 生成参数调整
        adjustments = optimizer.generate_parameter_adjustments(adaptation_needs)
        
        if adjustments:
            print(f"🔧 生成了 {len(adjustments)} 个参数调整建议")
            
            # 应用调整
            applied = optimizer.apply_parameter_adjustments(adjustments)
            
            print(f"✅ 应用了 {len(applied)} 个参数调整")
    
    # 生成优化报告
    report = optimizer.generate_optimization_report()
    print(f"\n{report}")
    
    # 保存状态
    saved_file = optimizer.save_optimization_state()
    
    print(f"\n✅ 自适应策略优化完成")
    print(f"📊 分析了 {len(sample_trades)} 笔交易")
    print(f"🔄 建立了自适应优化机制")
    print(f"🎯 实现了参数动态调整")
    if saved_file:
        print(f"💾 状态已保存到: {saved_file}")
    
    return optimizer

if __name__ == '__main__':
    main()
