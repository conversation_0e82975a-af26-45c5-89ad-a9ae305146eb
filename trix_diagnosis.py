#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TRIX反转筛选诊断脚本
深入分析TRIX反转筛选无法正常工作的原因
"""

import numpy as np
import pandas as pd
import talib
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_trix_reversal_issues():
    """分析TRIX反转筛选问题"""
    
    print("🔍 TRIX反转筛选问题诊断")
    print("=" * 60)
    
    # 问题1：检查TRIX计算方式差异
    print("\n📊 问题1：TRIX计算方式差异分析")
    print("-" * 40)
    
    # 模拟价格数据
    prices = np.array([10.0, 10.1, 10.0, 9.9, 9.8, 9.7, 9.6, 9.5, 9.4, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8])
    
    # 自定义TRIX计算
    ema1 = talib.EMA(prices, timeperiod=3)
    ema2 = talib.EMA(ema1, timeperiod=3)
    ema3 = talib.EMA(ema2, timeperiod=3)
    
    custom_trix = np.zeros_like(prices)
    for i in range(1, len(ema3)):
        if ema3[i-1] != 0:
            custom_trix[i] = (ema3[i] - ema3[i-1]) / ema3[i-1] * 100
    
    # talib直接计算TRIX
    talib_trix = talib.TRIX(prices, timeperiod=3)
    
    print(f"价格数据: {prices[-5:]}")
    print(f"自定义TRIX: {custom_trix[-5:]}")
    print(f"talib TRIX: {talib_trix[-5:]}")
    print(f"差异: {np.abs(custom_trix - talib_trix)[-5:]}")
    
    # 问题2：检查反转判断逻辑
    print("\n📊 问题2：反转判断逻辑分析")
    print("-" * 40)
    
    # 测试不同的反转判断逻辑
    current_trix = talib_trix[-1]
    prev_trix = talib_trix[-2]
    prev2_trix = talib_trix[-3]
    
    print(f"TRIX序列: {prev2_trix:.6f} -> {prev_trix:.6f} -> {current_trix:.6f}")
    
    # 原逻辑
    original_logic = current_trix > prev_trix or abs(current_trix - prev_trix) < 0.0001
    print(f"原逻辑 (current > prev OR |diff| < 0.0001): {original_logic}")
    
    # 新逻辑
    new_logic = current_trix > prev_trix + 0.001
    print(f"新逻辑 (current > prev + 0.001): {new_logic}")
    
    # 问题3：检查预筛选条件
    print("\n📊 问题3：预筛选条件分析")
    print("-" * 40)
    
    trix_trend_down_original = prev_trix <= prev2_trix or abs(prev_trix - prev2_trix) < 0.0001
    trix_trend_down_new = prev_trix <= prev2_trix + 0.001
    trix_reversal_up = current_trix > prev_trix + 0.001
    
    print(f"下降趋势判断:")
    print(f"  原逻辑: {trix_trend_down_original}")
    print(f"  新逻辑: {trix_trend_down_new}")
    print(f"反转信号: {trix_reversal_up}")
    
    # 问题4：检查参数敏感性
    print("\n📊 问题4：参数敏感性分析")
    print("-" * 40)
    
    thresholds = [0.0001, 0.0005, 0.001, 0.002, 0.005]
    periods = [3, 4, 5]
    
    print("不同阈值下的反转判断:")
    for threshold in thresholds:
        reversal = current_trix > prev_trix + threshold
        print(f"  阈值 {threshold:.4f}: {reversal}")
    
    print("\n不同周期下的TRIX值:")
    for period in periods:
        trix_period = talib.TRIX(prices, timeperiod=period)
        if len(trix_period) >= 3:
            current = trix_period[-1]
            prev = trix_period[-2]
            prev2 = trix_period[-3]
            print(f"  周期 {period}: {prev2:.6f} -> {prev:.6f} -> {current:.6f}")

def suggest_optimizations():
    """建议优化方案"""
    
    print("\n🎯 优化建议")
    print("=" * 60)
    
    print("1. 🚀 立即优化方案:")
    print("   - 使用talib直接计算TRIX (USE_TALIB_TRIX = True)")
    print("   - 调整TRIX周期为4 (TRIX_EMA_PERIOD = 4)")
    print("   - 设置合理的反转阈值 (TRIX_REVERSAL_THRESHOLD = 0.001)")
    
    print("\n2. 📊 预筛选逻辑优化:")
    print("   - 放宽预筛选条件：下降趋势 OR 当日反转")
    print("   - 严格反转确认：必须满足阈值要求")
    
    print("\n3. 🔧 调试建议:")
    print("   - 增加详细的TRIX值日志")
    print("   - 监控预筛选和反转确认的通过率")
    print("   - 定期检查参数敏感性")
    
    print("\n4. 📈 预期效果:")
    print("   - 减少假信号，提高筛选精度")
    print("   - 保持合理的交易频率")
    print("   - 提高策略的稳定性")

def create_test_scenarios():
    """创建测试场景"""
    
    print("\n🧪 测试场景")
    print("=" * 60)
    
    # 场景1：明显的反转信号
    scenario1 = {
        'name': '明显反转',
        'prices': [10.0, 9.9, 9.8, 9.7, 9.6, 9.5, 9.4, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9, 10.0],
        'expected': True
    }
    
    # 场景2：微弱反转信号
    scenario2 = {
        'name': '微弱反转',
        'prices': [10.0, 10.0, 10.0, 9.9, 9.9, 9.9, 9.8, 9.8, 9.8, 9.8, 9.8, 9.8, 9.9, 9.9, 9.9],
        'expected': False
    }
    
    # 场景3：持续下降
    scenario3 = {
        'name': '持续下降',
        'prices': [10.0, 9.9, 9.8, 9.7, 9.6, 9.5, 9.4, 9.3, 9.2, 9.1, 9.0, 8.9, 8.8, 8.7, 8.6],
        'expected': False
    }
    
    scenarios = [scenario1, scenario2, scenario3]
    
    for scenario in scenarios:
        print(f"\n📊 场景: {scenario['name']}")
        prices = np.array(scenario['prices'])
        trix = talib.TRIX(prices, timeperiod=4)
        
        if len(trix) >= 3:
            current = trix[-1]
            prev = trix[-2]
            prev2 = trix[-3]
            
            reversal = current > prev + 0.001
            trend_down = prev <= prev2 + 0.001
            passed = trend_down or reversal
            
            print(f"  价格变化: {prices[0]:.2f} -> {prices[-1]:.2f}")
            print(f"  TRIX: {prev2:.6f} -> {prev:.6f} -> {current:.6f}")
            print(f"  反转: {reversal}, 下降: {trend_down}, 通过: {passed}")
            print(f"  预期: {scenario['expected']}, 结果: {'✅' if passed == scenario['expected'] else '❌'}")

if __name__ == "__main__":
    analyze_trix_reversal_issues()
    suggest_optimizations()
    create_test_scenarios()
    
    print("\n🎯 总结:")
    print("TRIX反转筛选问题主要源于:")
    print("1. 计算方式不一致导致信号差异")
    print("2. 反转判断阈值设置不当")
    print("3. 预筛选条件过于严格")
    print("4. 参数敏感性过高") 