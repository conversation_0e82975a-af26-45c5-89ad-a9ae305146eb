# coding=utf-8
"""
高级因子挖掘分析
基于133个存储因子进行深度挖掘，找到影响盈利的关键因子
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy import stats
from datetime import datetime

def advanced_factor_analysis():
    """高级因子分析"""
    print('🔍 高级因子挖掘分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配数据，修复之前的查询问题
        query = """
        WITH buy_sell_pairs AS (
            SELECT 
                b.id as buy_id,
                b.timestamp as buy_time,
                b.symbol,
                b.price as buy_price,
                s.timestamp as sell_time,
                s.price as sell_price,
                s.net_profit_pct_sell,
                s.sell_reason,
                s.holding_hours,
                ROW_NUMBER() OVER (PARTITION BY b.id ORDER BY s.timestamp) as rn
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT 
            b.*,
            p.net_profit_pct_sell,
            p.sell_reason,
            p.holding_hours
        FROM trades b
        JOIN buy_sell_pairs p ON b.id = p.buy_id AND p.rn = 1
        WHERE b.action = 'BUY'
        ORDER BY b.timestamp DESC
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 成功匹配: {len(df)} 条买入-卖出记录')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的交易记录')
            return None
        
        # 创建盈利标识
        df['is_profitable'] = df['net_profit_pct_sell'] > 0
        df['profit_level'] = pd.cut(df['net_profit_pct_sell'], 
                                   bins=[-np.inf, -5, -2, 0, 2, 5, np.inf],
                                   labels=['大亏', '中亏', '小亏', '小盈', '中盈', '大盈'])
        
        profitable_count = df['is_profitable'].sum()
        total_count = len(df)
        win_rate = profitable_count / total_count * 100
        
        print(f'   盈利交易: {profitable_count}/{total_count} ({win_rate:.1f}%)')
        print(f'   平均收益: {df["net_profit_pct_sell"].mean():.2f}%')
        print(f'   收益标准差: {df["net_profit_pct_sell"].std():.2f}%')
        
        return df
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None

def comprehensive_factor_ic_analysis(df):
    """全面的因子IC分析"""
    print(f'\n🎯 全面因子IC分析 (133个因子)')
    print('=' * 50)
    
    # 排除非因子列
    exclude_columns = [
        'id', 'timestamp', 'symbol', 'action', 'price', 'volume', 'cost_price_sell',
        'confirmed_high_sell', 'confirmed_high_time', 'max_profit_pct', 'final_drawdown_pct',
        'status', 'sell_reason', 'net_profit_pct_sell', 'holding_hours', 'is_profitable', 'profit_level'
    ]
    
    factor_columns = [col for col in df.columns if col not in exclude_columns]
    
    print(f'📊 分析因子数量: {len(factor_columns)}个')
    
    factor_results = []
    
    for factor in factor_columns:
        try:
            factor_values = df[factor].dropna()
            profit_values = df.loc[df[factor].notna(), 'net_profit_pct_sell']
            is_profitable = df.loc[df[factor].notna(), 'is_profitable']
            
            if len(factor_values) > 50:  # 确保样本量足够
                # 计算信息系数 (IC)
                ic, ic_pvalue = stats.pearsonr(factor_values, profit_values)
                
                # 计算Spearman相关系数 (更稳健)
                spearman_ic, spearman_pvalue = stats.spearmanr(factor_values, profit_values)
                
                # 分组分析
                q75 = factor_values.quantile(0.75)
                q25 = factor_values.quantile(0.25)
                
                high_group = df[df[factor] >= q75]
                low_group = df[df[factor] <= q25]
                
                if len(high_group) > 10 and len(low_group) > 10:
                    high_win_rate = (high_group['is_profitable']).mean() * 100
                    low_win_rate = (low_group['is_profitable']).mean() * 100
                    win_rate_diff = high_win_rate - low_win_rate
                    
                    high_avg_profit = high_group['net_profit_pct_sell'].mean()
                    low_avg_profit = low_group['net_profit_pct_sell'].mean()
                    profit_diff = high_avg_profit - low_avg_profit
                    
                    # t检验
                    t_stat, t_pvalue = stats.ttest_ind(
                        high_group['net_profit_pct_sell'].dropna(),
                        low_group['net_profit_pct_sell'].dropna()
                    )
                    
                    factor_results.append({
                        'factor': factor,
                        'ic': ic,
                        'ic_pvalue': ic_pvalue,
                        'spearman_ic': spearman_ic,
                        'spearman_pvalue': spearman_pvalue,
                        'ic_abs': abs(ic),
                        'spearman_abs': abs(spearman_ic),
                        'win_rate_diff': win_rate_diff,
                        'profit_diff': profit_diff,
                        'high_win_rate': high_win_rate,
                        'low_win_rate': low_win_rate,
                        'high_avg_profit': high_avg_profit,
                        'low_avg_profit': low_avg_profit,
                        't_stat': t_stat,
                        't_pvalue': t_pvalue,
                        'sample_size': len(factor_values),
                        'unique_values': factor_values.nunique(),
                        'factor_std': factor_values.std()
                    })
        
        except Exception as e:
            print(f'⚠️ 因子{factor}分析失败: {e}')
            continue
    
    # 按Spearman IC绝对值排序 (更稳健)
    factor_results.sort(key=lambda x: x['spearman_abs'], reverse=True)
    
    print(f'\n🏆 最有效因子排序 (前20名):')
    print(f'   排名  因子名称                      Spearman IC  胜率差异   收益差异   显著性')
    print(f'   ' + '-' * 85)
    
    top_factors = []
    
    for i, result in enumerate(factor_results[:20], 1):
        ic_str = f"{result['spearman_ic']:+.4f}"
        win_diff_str = f"{result['win_rate_diff']:+.1f}%"
        profit_diff_str = f"{result['profit_diff']:+.2f}%"
        
        # 显著性判断
        if result['spearman_pvalue'] < 0.001:
            significance = "🔥极显著"
        elif result['spearman_pvalue'] < 0.01:
            significance = "🚀显著"
        elif result['spearman_pvalue'] < 0.05:
            significance = "📊一般"
        else:
            significance = "🔹微弱"
        
        print(f'   {i:2d}.  {result["factor"]:<28} {ic_str:>10} {win_diff_str:>9} {profit_diff_str:>9} {significance}')
        
        if i <= 10:
            top_factors.append(result)
    
    return factor_results, top_factors

def analyze_factor_categories(factor_results):
    """分析因子类别效果"""
    print(f'\n📊 因子类别效果分析')
    print('=' * 50)
    
    # 定义因子类别
    factor_categories = {
        '价格动量': ['price_momentum_3d', 'price_momentum_5d', 'price_momentum_10d', 'price_change_pct'],
        '成交量': ['volume_ma5_ratio', 'volume_ma10_ratio', 'volume_ma20_ratio', 'volume_change_pct', 
                 'volume_momentum_3d', 'volume_momentum_5d', 'relative_volume', 'volume_change_rate'],
        '技术指标': ['rsi', 'rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d', 'macd_12_26', 'macd_signal_9', 
                   'macd_histogram', 'adx_14', 'cci_14', 'kdj_k', 'kdj_d', 'kdj_j'],
        '布林带': ['bb_upper_20', 'bb_middle_20', 'bb_lower_20', 'bb_width_20', 'bb_position_20', 'bb_squeeze'],
        '移动平均': ['ma5', 'ma10', 'ma20', 'ma60', 'ma120', 'ma5_distance_pct', 'ma10_distance_pct', 
                   'ma20_distance_pct', 'ma5_slope', 'ma10_slope', 'ma20_slope'],
        '波动率': ['volatility_3d', 'volatility_5d', 'volatility_10d', 'volatility_20d', 'volatility_ratio_5_20',
                 'atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized'],
        '时间因子': ['hour_of_day', 'minute_of_hour', 'day_of_week', 'day_of_month', 'week_of_year', 
                   'month_of_year', 'quarter_of_year', 'market_session', 'time_to_close_minutes'],
        '风险指标': ['max_drawdown_3d', 'max_drawdown_5d', 'max_drawdown_10d', 'sharpe_ratio_5d', 
                   'beta_market', 'correlation_market'],
        '形态识别': ['pattern_doji', 'pattern_hammer', 'pattern_engulfing', 'pattern_morning_star', 'pattern_evening_star'],
        '基本面': ['pe_ratio', 'pb_ratio', 'roe', 'industry_relative_strength']
    }
    
    category_performance = {}
    
    for category, factors in factor_categories.items():
        category_factors = [f for f in factor_results if f['factor'] in factors]
        
        if category_factors:
            avg_ic = np.mean([f['spearman_abs'] for f in category_factors])
            max_ic = max([f['spearman_abs'] for f in category_factors])
            significant_count = len([f for f in category_factors if f['spearman_pvalue'] < 0.05])
            total_count = len(category_factors)
            
            category_performance[category] = {
                'avg_ic': avg_ic,
                'max_ic': max_ic,
                'significant_count': significant_count,
                'total_count': total_count,
                'significant_ratio': significant_count / total_count if total_count > 0 else 0
            }
    
    # 按平均IC排序
    sorted_categories = sorted(category_performance.items(), 
                              key=lambda x: x[1]['avg_ic'], 
                              reverse=True)
    
    print(f'📈 因子类别效果排序:')
    print(f'   类别           平均IC    最大IC    显著因子  总因子  显著率')
    print(f'   ' + '-' * 65)
    
    for category, perf in sorted_categories:
        avg_ic_str = f"{perf['avg_ic']:.4f}"
        max_ic_str = f"{perf['max_ic']:.4f}"
        significant_str = f"{perf['significant_count']}/{perf['total_count']}"
        ratio_str = f"{perf['significant_ratio']:.1%}"
        
        effectiveness = "🔥" if perf['avg_ic'] > 0.02 else "📊" if perf['avg_ic'] > 0.01 else "🔹"
        
        print(f'   {category:<12} {avg_ic_str:>8} {max_ic_str:>8} {significant_str:>8} {ratio_str:>8} {effectiveness}')

def generate_factor_selection_strategy(top_factors):
    """生成因子选择策略"""
    print(f'\n🎯 因子选择策略')
    print('=' * 50)
    
    print(f'🏆 推荐的核心因子组合:')
    
    # 选择最有效的因子
    tier1_factors = [f for f in top_factors if f['spearman_abs'] > 0.03 and f['spearman_pvalue'] < 0.01]
    tier2_factors = [f for f in top_factors if 0.02 < f['spearman_abs'] <= 0.03 and f['spearman_pvalue'] < 0.05]
    tier3_factors = [f for f in top_factors if 0.01 < f['spearman_abs'] <= 0.02 and f['spearman_pvalue'] < 0.05]
    
    print(f'\n🔥 一级因子 (IC>0.03, p<0.01): {len(tier1_factors)}个')
    for i, factor in enumerate(tier1_factors, 1):
        print(f'   {i}. {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    print(f'\n🚀 二级因子 (IC>0.02, p<0.05): {len(tier2_factors)}个')
    for i, factor in enumerate(tier2_factors, 1):
        print(f'   {i}. {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    print(f'\n📊 三级因子 (IC>0.01, p<0.05): {len(tier3_factors)}个')
    for i, factor in enumerate(tier3_factors, 1):
        print(f'   {i}. {factor["factor"]}: IC={factor["spearman_ic"]:+.4f}, 胜率差异={factor["win_rate_diff"]:+.1f}%')
    
    # 生成因子权重建议
    print(f'\n⚖️ 建议的因子权重分配:')
    
    all_effective_factors = tier1_factors + tier2_factors + tier3_factors
    total_ic = sum([abs(f['spearman_ic']) for f in all_effective_factors])
    
    if total_ic > 0:
        print(f'   基于IC值的权重分配:')
        for factor in all_effective_factors[:10]:  # 显示前10个
            weight = abs(factor['spearman_ic']) / total_ic
            print(f'     {factor["factor"]}: {weight:.1%}')
    
    return tier1_factors, tier2_factors, tier3_factors

def create_factor_monitoring_system():
    """创建因子监控系统"""
    print(f'\n📊 因子监控系统设计')
    print('=' * 50)
    
    monitoring_config = '''
# 因子监控系统配置

FACTOR_MONITORING_CONFIG = {
    'enable': True,
    
    # IC监控
    'ic_monitoring': {
        'calculation_window': 252,      # 计算窗口252天
        'update_frequency': 'daily',    # 每日更新
        'alert_threshold': 0.01,        # IC低于0.01报警
        'decay_threshold': 0.5,         # IC衰减50%报警
    },
    
    # 因子稳定性监控
    'stability_monitoring': {
        'rolling_window': 60,           # 60天滚动窗口
        'stability_threshold': 0.7,     # 稳定性阈值
        'correlation_threshold': 0.8,   # 相关性阈值
    },
    
    # 因子有效性评级
    'effectiveness_rating': {
        'tier1_ic_threshold': 0.03,     # 一级因子IC阈值
        'tier2_ic_threshold': 0.02,     # 二级因子IC阈值
        'tier3_ic_threshold': 0.01,     # 三级因子IC阈值
        'significance_threshold': 0.05, # 显著性阈值
    },
    
    # 自动因子选择
    'auto_selection': {
        'enable': True,
        'max_factors': 20,              # 最多选择20个因子
        'rebalance_frequency': 'weekly', # 每周重新平衡
        'min_sample_size': 100,         # 最小样本量
    }
}

# 推荐的核心因子配置
RECOMMENDED_CORE_FACTORS = {
    'tier1_factors': [
        # 基于实际分析结果填入一级因子
    ],
    'tier2_factors': [
        # 基于实际分析结果填入二级因子
    ],
    'tier3_factors': [
        # 基于实际分析结果填入三级因子
    ]
}
'''
    
    print(monitoring_config)

def main():
    """主函数"""
    print('🚀 高级因子挖掘分析')
    print('=' * 60)
    
    # 获取数据
    df = advanced_factor_analysis()
    
    if df is not None:
        # 全面因子IC分析
        factor_results, top_factors = comprehensive_factor_ic_analysis(df)
        
        # 分析因子类别效果
        analyze_factor_categories(factor_results)
        
        # 生成因子选择策略
        tier1, tier2, tier3 = generate_factor_selection_strategy(top_factors)
        
        # 创建因子监控系统
        create_factor_monitoring_system()
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print(f'✅ 成功分析{len(factor_results)}个因子')
        print(f'🔥 一级因子: {len(tier1)}个 (IC>0.03)')
        print(f'🚀 二级因子: {len(tier2)}个 (IC>0.02)')
        print(f'📊 三级因子: {len(tier3)}个 (IC>0.01)')
        
        effective_factors = len(tier1) + len(tier2) + len(tier3)
        print(f'💎 总有效因子: {effective_factors}个')
        
        if effective_factors > 0:
            print(f'\n🚀 下一步建议:')
            print(f'   1. 基于{effective_factors}个有效因子重构策略')
            print(f'   2. 实施因子权重优化')
            print(f'   3. 建立因子监控系统')
            print(f'   4. 进行因子组合优化')
        else:
            print(f'\n⚠️ 未发现高效因子，建议:')
            print(f'   1. 检查因子计算逻辑')
            print(f'   2. 扩展因子库')
            print(f'   3. 优化数据质量')
    else:
        print('❌ 数据获取失败，无法进行分析')

if __name__ == '__main__':
    main()
