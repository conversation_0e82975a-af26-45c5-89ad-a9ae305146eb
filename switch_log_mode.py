# coding=utf-8
"""
日志模式切换工具
快速切换策略的日志输出模式
"""

import os
import re

def switch_log_mode(mode=1):
    """
    切换日志模式

    参数:
    - mode: 1=极简模式 | 2=详细模式 | 3=调试模式
    """

    config_file = 'config.py'

    if not os.path.exists(config_file):
        print(f'❌ 配置文件不存在: {config_file}')
        return False

    # 验证模式
    if mode not in [1, 2, 3]:
        print(f'❌ 无效的模式: {mode}，有效模式: 1, 2, 3')
        return False

    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 更新日志模式（匹配数字格式）
    pattern = r"'mode':\s*\d+"
    replacement = f"'mode': {mode}"

    if re.search(pattern, content):
        new_content = re.sub(pattern, replacement, content)

        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)

        print(f'✅ 日志模式已切换为: {mode}')

        # 显示当前模式的特点
        mode_descriptions = {
            1: '🚀 极简模式 - 最高性能，最少日志输出',
            2: '📊 详细模式 - 平衡性能和调试信息',
            3: '🔍 调试模式 - 完整调试信息，性能较低'
        }

        print(f'📋 {mode_descriptions.get(mode, "未知模式")}')
        return True
    else:
        print('❌ 未找到日志模式配置')
        return False

def show_current_mode():
    """显示当前日志模式"""
    config_file = 'config.py'
    
    if not os.path.exists(config_file):
        print(f'❌ 配置文件不存在: {config_file}')
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找当前模式（数字格式）
    pattern = r"'mode':\s*(\d+)"
    match = re.search(pattern, content)

    if match:
        current_mode = int(match.group(1))
        print(f'📊 当前日志模式: {current_mode}')

        # 显示模式详情
        mode_details = {
            1: {
                'description': '🚀 极简模式',
                'features': [
                    '✅ 显示进度信息',
                    '❌ 不显示调试详情',
                    '❌ 不显示数据格式检查',
                    '❌ 不显示TRIX计算详情',
                    '❌ 不显示个股筛选结果',
                    '📊 进度间隔: 100只股票',
                    '🎯 最高性能模式'
                ]
            },
            2: {
                'description': '📊 详细模式',
                'features': [
                    '✅ 显示进度信息',
                    '✅ 显示调试详情',
                    '✅ 显示数据格式检查',
                    '✅ 显示TRIX计算详情',
                    '✅ 显示个股筛选结果',
                    '📊 进度间隔: 50只股票',
                    '⚖️ 平衡性能和信息'
                ]
            },
            3: {
                'description': '🔍 调试模式',
                'features': [
                    '✅ 显示所有调试信息',
                    '✅ 显示详细计算过程',
                    '✅ 显示异常详情',
                    '✅ 显示缓存命中情况',
                    '📊 进度间隔: 25只股票',
                    '🐛 完整调试信息'
                ]
            }
        }
        
        if current_mode in mode_details:
            details = mode_details[current_mode]
            print(f'\n{details["description"]}:')
            for feature in details['features']:
                print(f'  {feature}')
        
        return current_mode
    else:
        print('❌ 未找到日志模式配置')
        return None

def show_performance_comparison():
    """显示性能对比"""
    print('\n📈 性能对比:')
    print('=' * 50)
    
    comparison = [
        ('模式', '性能', '日志量', '调试信息', '适用场景'),
        ('1-极简', '🚀🚀🚀', '⭐', '❌', '生产环境/回测'),
        ('2-详细', '🚀🚀', '⭐⭐⭐', '✅', '策略开发/调试'),
        ('3-调试', '🚀', '⭐⭐⭐⭐⭐', '✅✅', '问题排查/深度调试')
    ]
    
    for row in comparison:
        print(f'{row[0]:<10} {row[1]:<8} {row[2]:<8} {row[3]:<8} {row[4]}')

def main():
    """主函数"""
    print('🔧 日志模式切换工具')
    print('=' * 40)
    
    # 显示当前模式
    current_mode = show_current_mode()
    
    # 显示性能对比
    show_performance_comparison()
    
    print('\n💡 使用方法:')
    print('python switch_log_mode.py 1   # 切换到极简模式（最高性能）')
    print('python switch_log_mode.py 2   # 切换到详细模式（平衡）')
    print('python switch_log_mode.py 3   # 切换到调试模式（完整信息）')

    # 如果有命令行参数，执行切换
    import sys
    if len(sys.argv) > 1:
        try:
            new_mode = int(sys.argv[1])
            if new_mode in [1, 2, 3]:
                print(f'\n🔄 切换日志模式到: {new_mode}')
                if switch_log_mode(new_mode):
                    print('✅ 切换成功！请重启策略以应用新设置。')
                else:
                    print('❌ 切换失败！')
            else:
                print(f'❌ 无效的模式: {new_mode}')
                print('有效模式: 1, 2, 3')
        except ValueError:
            print(f'❌ 无效的模式: {sys.argv[1]}')
            print('有效模式: 1, 2, 3')

if __name__ == '__main__':
    main()
