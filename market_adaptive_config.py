
# 市场自适应策略配置
# 基于市场特征和因子有效性，而非强制分散

# ==================== 市场自适应核心配置 ====================

# 启用市场自适应策略
ENABLE_MARKET_ADAPTIVE_STRATEGY = True

# 基于CCI因子特征的自适应调整 (CCI是最有效因子，开盘时段+35.9%差异)
CCI_ADAPTIVE_CONFIG = {
    'enable': True,
    'opening_cci_avg': 23.7,           # 开盘时段CCI平均值
    'other_cci_avg': 17.4,             # 其他时段CCI平均值
    
    # 基于CCI值动态调整阈值
    'dynamic_thresholds': {
        'high_cci_zone': {             # CCI > 25 (类似开盘特征)
            'cci_min': 25,
            'other_factor_multiplier': 1.1,  # 其他因子要求提高10%
        },
        'medium_cci_zone': {           # CCI 15-25
            'cci_min': 15,
            'cci_max': 25,
            'other_factor_multiplier': 1.0,  # 其他因子正常要求
        },
        'low_cci_zone': {              # CCI < 15 (其他时段特征)
            'cci_max': 15,
            'other_factor_multiplier': 0.9,  # 其他因子要求降低10%
        }
    }
}

# 基于实际表现的质量优化
QUALITY_BASED_OPTIMIZATION = {
    'enable': True,
    'focus_on_quality': True,          # 专注质量而非数量
    'adaptive_thresholds': True,       # 自适应阈值调整
    
    # 高效因子组合 (基于IC分析结果)
    'effective_factors': {
        'cci': {
            'weight': 0.25,            # 最高权重
            'adaptive_threshold': True, # 启用自适应阈值
        },
        'adx': {
            'weight': 0.20,
            'min_threshold': 25,       # ADX最小要求
        },
        'bb_position': {
            'weight': 0.18,
            'optimal_range': [0.3, 0.8],
        },
        'rsi': {
            'weight': 0.15,
            'optimal_range': [40, 70],
        },
        'atr_pct': {
            'weight': 0.12,
            'min_threshold': 2.0,
        },
        'bb_width': {
            'weight': 0.10,
            'min_threshold': 8.0,
        }
    }
}

# 禁用强制时间分散 (违背市场规律)
DISABLE_FORCED_TIME_DISTRIBUTION = True

# 启用基于表现的动态优化
PERFORMANCE_BASED_OPTIMIZATION = {
    'enable': True,
    'monitor_hourly_performance': True,
    'adjust_based_on_results': True,
    'respect_market_patterns': True,
}
