# coding=utf-8
"""
搜索日志中的因子相关信息
"""

import re

def search_factor_logs():
    """搜索因子相关日志"""
    print('🔍 搜索日志中的因子相关信息')
    print('=' * 60)
    
    try:
        # 尝试多个可能的日志文件位置
        log_files = ['logs/strategy.log', 'strategy.log', 'logs/data_manager.log']

        lines = []
        used_file = None

        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    used_file = log_file
                    break
            except FileNotFoundError:
                continue

        if not lines:
            print('❌ 未找到可读取的日志文件')
            return

        print(f'📄 使用日志文件: {used_file}')
        
        print(f'📊 日志总行数: {len(lines):,}')
        
        # 搜索关键词
        keywords = [
            'enhanced',
            'factor',
            'EnhancedFactorEngine',
            'calculate_all_factors',
            '增强因子',
            '因子计算',
            'factor_engine'
        ]
        
        matches = []
        for i, line in enumerate(lines):
            for keyword in keywords:
                if keyword.lower() in line.lower():
                    matches.append((i+1, line.strip()))
                    break
        
        print(f'📈 找到 {len(matches)} 条相关日志:')
        
        if matches:
            print(f'\n前20条匹配日志:')
            for line_num, content in matches[:20]:
                print(f'L{line_num}: {content}')
            
            if len(matches) > 20:
                print(f'\n... 还有{len(matches)-20}条')
        else:
            print('❌ 没有找到任何因子相关的日志')
        
        # 搜索错误信息
        print(f'\n🔍 搜索增强因子相关错误:')
        error_patterns = [
            r'enhanced.*error',
            r'factor.*error',
            r'EnhancedFactorEngine.*error',
            r'增强因子.*异常',
            r'因子计算.*失败'
        ]
        
        error_matches = []
        for i, line in enumerate(lines):
            for pattern in error_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    error_matches.append((i+1, line.strip()))
                    break
        
        if error_matches:
            print(f'找到 {len(error_matches)} 条错误日志:')
            for line_num, content in error_matches:
                print(f'L{line_num}: {content}')
        else:
            print('✅ 没有找到增强因子相关的错误日志')
        
        # 搜索买入记录相关
        print(f'\n🔍 搜索买入记录相关信息:')
        buy_patterns = [
            r'买入记录包含.*字段',
            r'save_original_buy_record',
            r'signal_data',
            r'buy_record'
        ]
        
        buy_matches = []
        for i, line in enumerate(lines):
            for pattern in buy_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    buy_matches.append((i+1, line.strip()))
                    break
        
        if buy_matches:
            print(f'找到 {len(buy_matches)} 条买入记录日志 (显示最后10条):')
            for line_num, content in buy_matches[-10:]:
                print(f'L{line_num}: {content}')
        
    except Exception as e:
        print(f'❌ 搜索失败: {e}')

if __name__ == '__main__':
    search_factor_logs()
