@echo off
chcp 65001 > nul
echo 正在启动万和策略分析系统...
echo.

REM 设置Python路径
set PYTHON_PATH=python

REM 检查是否存在Python
%PYTHON_PATH% --version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

REM 检查必要的Python包
echo 检查必要的Python包...
%PYTHON_PATH% -c "import pandas" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到pandas包，尝试安装...
    %PYTHON_PATH% -m pip install pandas
)

%PYTHON_PATH% -c "import numpy" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到numpy包，尝试安装...
    %PYTHON_PATH% -m pip install numpy
)

%PYTHON_PATH% -c "import matplotlib" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到matplotlib包，尝试安装...
    %PYTHON_PATH% -m pip install matplotlib
)

%PYTHON_PATH% -c "import seaborn" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到seaborn包，尝试安装...
    %PYTHON_PATH% -m pip install seaborn
)

%PYTHON_PATH% -c "import sklearn" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到scikit-learn包，尝试安装...
    %PYTHON_PATH% -m pip install scikit-learn
)

%PYTHON_PATH% -c "import jinja2" > nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到jinja2包，尝试安装...
    %PYTHON_PATH% -m pip install jinja2
)

echo.
echo ===============================================================================
echo                           万和策略分析系统 - 启动器                            
echo ===============================================================================
echo.
echo 请选择要执行的操作:
echo.
echo 1. 运行完整分析流程
echo 2. 仅运行交易分析
echo 3. 仅运行策略优化
echo 4. 仅生成HTML报告
echo 0. 退出
echo.

set /p choice=请输入选项 (0-4): 

if "%choice%"=="1" (
    echo.
    echo 正在运行完整分析流程...
    %PYTHON_PATH% analysis_manager.py --all
) else if "%choice%"=="2" (
    echo.
    echo 正在运行交易分析...
    %PYTHON_PATH% analysis_manager.py --analyze
) else if "%choice%"=="3" (
    echo.
    echo 正在运行策略优化...
    %PYTHON_PATH% analysis_manager.py --optimize
) else if "%choice%"=="4" (
    echo.
    echo 正在生成HTML报告...
    %PYTHON_PATH% analysis_manager.py --html
) else if "%choice%"=="0" (
    echo.
    echo 退出系统...
    exit /b 0
) else (
    echo.
    echo 无效的选项，请重新运行并选择正确的选项
)

echo.
echo 操作完成
pause 