# coding=utf-8
"""
深度分析开盘信号集中的真实原因
基于市场数据和因子有效性，而非强制分散
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_market_characteristics_by_time():
    """分析各时段的市场特征"""
    print('🔍 各时段市场特征分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入记录和对应的因子数据
        query = """
        SELECT 
            timestamp, symbol, price,
            atr_pct, bb_width, rsi, cci, adx, bb_position, macd_hist,
            volatility_3d, volatility_5d, volume_change_pct,
            overall_score, technical_score, momentum_score
        FROM trades 
        WHERE action = 'BUY' 
        ORDER BY timestamp DESC 
        LIMIT 2000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📊 分析样本: {len(df)} 条买入记录')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['minute'] = df['timestamp'].dt.minute
        
        # 按时段分析市场特征
        print(f'\n📈 各时段市场特征对比:')
        
        time_analysis = {}
        
        for hour in sorted(df['hour'].unique()):
            hour_data = df[df['hour'] == hour]
            if len(hour_data) > 10:  # 确保样本量足够
                
                # 计算各因子的平均值
                analysis = {
                    'signal_count': len(hour_data),
                    'signal_percentage': len(hour_data) / len(df) * 100,
                    'avg_atr': hour_data['atr_pct'].mean() if 'atr_pct' in hour_data.columns else 0,
                    'avg_bb_width': hour_data['bb_width'].mean() if 'bb_width' in hour_data.columns else 0,
                    'avg_rsi': hour_data['rsi'].mean() if 'rsi' in hour_data.columns else 0,
                    'avg_cci': hour_data['cci'].mean() if 'cci' in hour_data.columns else 0,
                    'avg_adx': hour_data['adx'].mean() if 'adx' in hour_data.columns else 0,
                    'avg_volatility': hour_data['volatility_3d'].mean() if 'volatility_3d' in hour_data.columns else 0,
                    'avg_volume_change': hour_data['volume_change_pct'].mean() if 'volume_change_pct' in hour_data.columns else 0,
                    'avg_overall_score': hour_data['overall_score'].mean() if 'overall_score' in hour_data.columns else 0,
                }
                
                time_analysis[hour] = analysis
        
        # 显示分析结果
        print(f'时段  信号数  占比    ATR    BB宽度   RSI    CCI    ADX   波动率  成交量变化')
        print(f'-' * 85)
        
        for hour, analysis in time_analysis.items():
            print(f'{hour:02d}:00 {analysis["signal_count"]:5d} {analysis["signal_percentage"]:5.1f}% '
                  f'{analysis["avg_atr"]:6.2f} {analysis["avg_bb_width"]:7.1f} '
                  f'{analysis["avg_rsi"]:6.1f} {analysis["avg_cci"]:6.1f} '
                  f'{analysis["avg_adx"]:5.1f} {analysis["avg_volatility"]:7.3f} '
                  f'{analysis["avg_volume_change"]:9.2f}')
        
        return time_analysis
        
    except Exception as e:
        print(f'❌ 市场特征分析失败: {e}')
        return None

def analyze_why_opening_signals_dominate(time_analysis):
    """分析为什么开盘信号占主导"""
    print(f'\n🎯 开盘信号占主导的原因分析')
    print('=' * 50)
    
    if not time_analysis or 9 not in time_analysis:
        print('⚠️ 缺少开盘时段数据')
        return
    
    opening_data = time_analysis[9]  # 09:00时段
    other_hours = {h: data for h, data in time_analysis.items() if h != 9}
    
    print(f'📊 开盘时段 vs 其他时段对比:')
    print(f'   开盘信号占比: {opening_data["signal_percentage"]:.1f}%')
    
    if len(other_hours) > 0:
        # 计算其他时段的平均值
        other_avg = {
            'avg_atr': np.mean([data['avg_atr'] for data in other_hours.values()]),
            'avg_bb_width': np.mean([data['avg_bb_width'] for data in other_hours.values()]),
            'avg_rsi': np.mean([data['avg_rsi'] for data in other_hours.values()]),
            'avg_cci': np.mean([data['avg_cci'] for data in other_hours.values()]),
            'avg_adx': np.mean([data['avg_adx'] for data in other_hours.values()]),
            'avg_volatility': np.mean([data['avg_volatility'] for data in other_hours.values()]),
            'avg_volume_change': np.mean([data['avg_volume_change'] for data in other_hours.values()]),
        }
        
        print(f'\n🔍 关键因子对比:')
        
        factors = [
            ('ATR波动率', 'avg_atr', '高波动更容易触发买入'),
            ('BB宽度', 'avg_bb_width', '宽度大表示波动性强'),
            ('RSI', 'avg_rsi', 'RSI值影响买入条件'),
            ('CCI', 'avg_cci', 'CCI是最有效因子'),
            ('ADX', 'avg_adx', 'ADX是第二有效因子'),
            ('波动率', 'avg_volatility', '历史波动率'),
            ('成交量变化', 'avg_volume_change', '成交量异动'),
        ]
        
        significant_differences = []
        
        for factor_name, factor_key, description in factors:
            opening_val = opening_data[factor_key]
            other_val = other_avg[factor_key]
            diff = opening_val - other_val
            diff_pct = (diff / other_val * 100) if other_val != 0 else 0
            
            if abs(diff_pct) > 10:  # 差异超过10%
                significant_differences.append({
                    'factor': factor_name,
                    'opening': opening_val,
                    'other': other_val,
                    'diff_pct': diff_pct,
                    'description': description
                })
            
            status = "🔥显著" if abs(diff_pct) > 20 else "📊明显" if abs(diff_pct) > 10 else "🔹微小"
            direction = "📈" if diff > 0 else "📉"
            
            print(f'   {factor_name}: 开盘{opening_val:.2f} vs 其他{other_val:.2f} '
                  f'({direction}{diff_pct:+.1f}%) [{status}]')
        
        # 分析显著差异
        if significant_differences:
            print(f'\n🎯 开盘信号集中的可能原因:')
            
            for i, diff in enumerate(significant_differences, 1):
                print(f'   {i}. {diff["factor"]}: 开盘时段{diff["diff_pct"]:+.1f}%差异')
                print(f'      {diff["description"]}')
                
                if diff['factor'] in ['ATR波动率', 'BB宽度', '波动率'] and diff['diff_pct'] > 0:
                    print(f'      → 开盘时段波动性更高，更容易满足买入条件')
                elif diff['factor'] in ['CCI', 'ADX'] and abs(diff['diff_pct']) > 10:
                    print(f'      → 开盘时段技术指标特征明显不同')
        
        return significant_differences
    
    return None

def analyze_intraday_factor_effectiveness():
    """分析日内因子有效性"""
    print(f'\n📊 日内因子有效性分析')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配数据，按时段分析
        query = """
        WITH buy_sell_pairs AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.atr_pct, b.bb_width, b.rsi, b.cci, b.adx,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_pairs
        ORDER BY buy_time DESC
        LIMIT 1500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 成功匹配: {len(df)} 条交易')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的交易数据')
            return
        
        # 按时段分析胜率和因子有效性
        hourly_performance = {}
        
        for hour in sorted(df['buy_hour'].unique()):
            hour_data = df[df['buy_hour'] == hour]
            
            if len(hour_data) > 20:  # 确保样本量足够
                win_rate = (hour_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = hour_data['net_profit_pct_sell'].mean()
                
                # 分析该时段的因子特征
                factor_analysis = {}
                for factor in ['atr_pct', 'bb_width', 'rsi', 'cci', 'adx']:
                    if factor in hour_data.columns:
                        factor_values = hour_data[factor].dropna()
                        if len(factor_values) > 10:
                            # 计算因子与收益的相关性
                            profit_values = hour_data.loc[hour_data[factor].notna(), 'net_profit_pct_sell']
                            if len(profit_values) > 10:
                                correlation = np.corrcoef(factor_values, profit_values)[0, 1]
                                factor_analysis[factor] = {
                                    'avg_value': factor_values.mean(),
                                    'correlation': correlation
                                }
                
                hourly_performance[hour] = {
                    'trade_count': len(hour_data),
                    'win_rate': win_rate,
                    'avg_profit': avg_profit,
                    'factors': factor_analysis
                }
        
        # 显示结果
        print(f'\n📊 各时段交易表现:')
        print(f'时段  交易数  胜率    平均收益  ATR相关性  CCI相关性  ADX相关性')
        print(f'-' * 70)
        
        for hour, perf in hourly_performance.items():
            atr_corr = perf['factors'].get('atr_pct', {}).get('correlation', 0)
            cci_corr = perf['factors'].get('cci', {}).get('correlation', 0)
            adx_corr = perf['factors'].get('adx', {}).get('correlation', 0)
            
            print(f'{hour:02d}:00 {perf["trade_count"]:5d} {perf["win_rate"]:5.1f}% '
                  f'{perf["avg_profit"]:8.2f}% {atr_corr:8.3f} {cci_corr:8.3f} {adx_corr:8.3f}')
        
        # 分析最佳交易时段
        best_hours = sorted(hourly_performance.items(), 
                           key=lambda x: x[1]['win_rate'], 
                           reverse=True)
        
        print(f'\n🏆 最佳交易时段排序 (按胜率):')
        for i, (hour, perf) in enumerate(best_hours[:5], 1):
            print(f'   {i}. {hour:02d}:00时段: 胜率{perf["win_rate"]:.1f}%, '
                  f'平均收益{perf["avg_profit"]:.2f}%, 交易数{perf["trade_count"]}')
        
        return hourly_performance
        
    except Exception as e:
        print(f'❌ 日内因子有效性分析失败: {e}')
        return None

def generate_data_driven_strategy():
    """生成基于数据驱动的策略建议"""
    print(f'\n💡 基于数据驱动的策略建议')
    print('=' * 50)
    
    strategy_recommendations = '''
🎯 正确的策略优化方向:

1. 📊 基于市场特征优化，而非强制分散:
   - 如果开盘时段确实具有更好的市场特征（高波动、大成交量）
   - 那么信号集中在开盘是合理的市场反应
   - 不应该强制分散，而应该优化因子筛选质量

2. 🔍 深度分析开盘信号质量:
   - 检查开盘时段的实际胜率和收益
   - 如果开盘信号质量好，应该保持
   - 如果开盘信号质量差，需要提高筛选标准

3. 🎯 因子有效性时段分析:
   - 分析各因子在不同时段的预测能力
   - 在因子有效性强的时段降低阈值
   - 在因子有效性弱的时段提高阈值

4. 📈 动态阈值策略:
   - 根据时段调整因子阈值，而非强制分散
   - 开盘时段如果波动大，可以提高ATR要求
   - 其他时段根据历史表现调整阈值

5. 🔧 优化建议:
   - 如果开盘胜率高：保持开盘信号，优化其他时段
   - 如果开盘胜率低：提高开盘时段的筛选标准
   - 如果其他时段胜率高：降低其他时段阈值
   - 基于实际表现，而非主观分散

⚠️ 避免的错误做法:
   - 强制时间分散（违背市场规律）
   - 忽略市场特征差异
   - 主观设定时段权重
   - 不基于历史表现优化
'''
    
    print(strategy_recommendations)

def main():
    """主函数"""
    print('🚀 开盘信号集中的真实原因分析')
    print('=' * 60)
    
    print('🎯 分析目标:')
    print('   理解为什么98.2%信号集中在开盘')
    print('   基于市场数据而非主观判断')
    print('   找到真正的优化方向')
    print('   避免强制分散的错误做法')
    
    # 分析各时段市场特征
    time_analysis = analyze_market_characteristics_by_time()
    
    # 分析开盘信号占主导的原因
    if time_analysis:
        significant_diffs = analyze_why_opening_signals_dominate(time_analysis)
    
    # 分析日内因子有效性
    hourly_performance = analyze_intraday_factor_effectiveness()
    
    # 生成数据驱动的策略建议
    generate_data_driven_strategy()
    
    print(f'\n🎯 分析总结')
    print('=' * 40)
    print('✅ 完成市场特征分析')
    print('✅ 完成因子有效性分析')
    print('✅ 生成数据驱动建议')
    print('')
    print('💡 关键洞察: 策略应该基于市场数据自动优化')
    print('⚠️ 避免强制分散，应该基于实际表现调整')

if __name__ == '__main__':
    main()
