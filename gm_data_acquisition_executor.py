# coding=utf-8
"""
掘金平台数据获取执行器
完整执行数据获取和存储
"""

import pandas as pd
import numpy as np
import sqlite3
import time
import os
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GMDataAcquisitionExecutor:
    """掘金数据获取执行器"""
    
    def __init__(self):
        self.db_path = 'data/enhanced_market_data.db'
        self.ensure_data_directory()
        
    def ensure_data_directory(self):
        """确保数据目录存在"""
        if not os.path.exists('data'):
            os.makedirs('data')
            logger.info("创建数据目录")
    
    def get_stock_list(self):
        """获取股票列表 - 模拟实现"""
        logger.info("获取股票列表...")
        
        # 模拟股票列表 (实际应该从掘金API获取)
        # 这里使用一些主要股票作为示例
        stock_list = [
            'SZSE.000001', 'SZSE.000002', 'SZSE.000858', 'SZSE.002415',
            'SHSE.600000', 'SHSE.600036', 'SHSE.600519', 'SHSE.600887',
            'SZSE.300015', 'SZSE.300059', 'SZSE.300750', 'SZSE.300760'
        ]
        
        logger.info(f"获取到 {len(stock_list)} 只股票")
        return stock_list
    
    def simulate_history_data(self, symbol, start_date, end_date):
        """模拟历史数据获取 - 实际应该调用掘金API"""
        try:
            # 生成模拟数据
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            # 过滤掉周末
            date_range = [d for d in date_range if d.weekday() < 5]
            
            if len(date_range) == 0:
                return None
            
            # 模拟价格数据
            np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
            base_price = 10 + np.random.uniform(5, 50)
            
            data = []
            current_price = base_price
            
            for date in date_range:
                # 模拟价格变动
                change_pct = np.random.normal(0, 0.02)  # 2%日波动
                current_price *= (1 + change_pct)
                
                # 生成OHLC
                open_price = current_price * (1 + np.random.uniform(-0.01, 0.01))
                high_price = max(open_price, current_price) * (1 + np.random.uniform(0, 0.03))
                low_price = min(open_price, current_price) * (1 - np.random.uniform(0, 0.03))
                close_price = current_price
                
                # 生成成交量
                volume = int(np.random.uniform(1000000, 10000000))
                amount = volume * close_price
                turnover_rate = np.random.uniform(0.5, 5.0)
                amplitude = (high_price - low_price) / low_price * 100
                
                data.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume,
                    'amount': round(amount, 2),
                    'turn': round(turnover_rate, 2),
                    'pct_chg': round(change_pct * 100, 2),
                    'amplitude': round(amplitude, 2)
                })
            
            df = pd.DataFrame(data)
            df.set_index('date', inplace=True)
            return df
            
        except Exception as e:
            logger.error(f"模拟数据生成失败 {symbol}: {e}")
            return None
    
    def get_comprehensive_history_data(self):
        """获取全面历史数据"""
        logger.info("开始获取历史行情数据...")
        
        symbols = self.get_stock_list()
        start_date = '2021-01-01'
        end_date = '2024-12-31'
        
        conn = sqlite3.connect(self.db_path)
        success_count = 0
        total_count = len(symbols)
        
        for i, symbol in enumerate(symbols):
            try:
                logger.info(f"获取 {symbol} 数据 ({i+1}/{total_count})")
                
                # 模拟获取历史数据 (实际应该调用掘金API)
                data = self.simulate_history_data(symbol, start_date, end_date)
                
                if data is not None and len(data) > 0:
                    # 保存到数据库
                    cursor = conn.cursor()
                    for date, row in data.iterrows():
                        cursor.execute("""
                            INSERT OR REPLACE INTO daily_market_data 
                            (symbol, trade_date, open_price, high_price, low_price, close_price, 
                             volume, amount, turnover_rate, pct_change, amplitude)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            symbol, date.date(), row['open'], row['high'], row['low'], 
                            row['close'], row['volume'], row['amount'], row['turn'], 
                            row['pct_chg'], row['amplitude']
                        ))
                    
                    conn.commit()
                    success_count += 1
                    logger.info(f"✅ {symbol} 数据保存成功，共 {len(data)} 条记录")
                
                # 避免请求过快
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"❌ 获取 {symbol} 数据失败: {e}")
                continue
        
        conn.close()
        logger.info(f"✅ 历史数据获取完成: {success_count}/{total_count}")
        return success_count, total_count
    
    def simulate_financial_data(self, symbol):
        """模拟财务数据"""
        try:
            # 生成模拟财务数据
            np.random.seed(hash(symbol) % 2**32)
            
            # 生成季度数据
            quarters = pd.date_range(start='2021-03-31', end='2024-09-30', freq='Q')
            
            data = []
            for quarter in quarters:
                # 模拟财务指标
                pe_ttm = np.random.uniform(10, 50)
                pb_lf = np.random.uniform(1, 8)
                ps_ttm = np.random.uniform(2, 15)
                pcf_ttm = np.random.uniform(8, 30)
                roe_ttm = np.random.uniform(5, 25)
                roa_ttm = np.random.uniform(2, 15)
                
                # 模拟财务报表数据
                revenue = np.random.uniform(1000000, 10000000)  # 营收
                net_profit = revenue * np.random.uniform(0.05, 0.25)  # 净利润
                gross_profit = revenue * np.random.uniform(0.2, 0.5)  # 毛利润
                total_assets = revenue * np.random.uniform(2, 8)  # 总资产
                total_liabilities = total_assets * np.random.uniform(0.3, 0.7)  # 总负债
                
                data.append({
                    'report_date': quarter.date(),
                    'pe_ttm': round(pe_ttm, 2),
                    'pb_lf': round(pb_lf, 2),
                    'ps_ttm': round(ps_ttm, 2),
                    'pcf_ttm': round(pcf_ttm, 2),
                    'roe_ttm': round(roe_ttm, 2),
                    'roa_ttm': round(roa_ttm, 2),
                    'revenue': round(revenue, 2),
                    'net_profit': round(net_profit, 2),
                    'gross_profit': round(gross_profit, 2),
                    'total_assets': round(total_assets, 2),
                    'total_liabilities': round(total_liabilities, 2)
                })
            
            return data
            
        except Exception as e:
            logger.error(f"模拟财务数据生成失败 {symbol}: {e}")
            return None
    
    def get_comprehensive_financial_data(self):
        """获取全面财务数据"""
        logger.info("开始获取财务数据...")
        
        symbols = self.get_stock_list()
        conn = sqlite3.connect(self.db_path)
        success_count = 0
        
        for i, symbol in enumerate(symbols):
            try:
                logger.info(f"获取 {symbol} 财务数据 ({i+1}/{len(symbols)})")
                
                # 模拟获取财务数据
                financial_data = self.simulate_financial_data(symbol)
                
                if financial_data:
                    cursor = conn.cursor()
                    for record in financial_data:
                        cursor.execute("""
                            INSERT OR REPLACE INTO financial_data 
                            (symbol, report_date, pe_ttm, pb_lf, ps_ttm, pcf_ttm, roe_ttm, roa_ttm,
                             revenue, net_profit, gross_profit, total_assets, total_liabilities)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            symbol, record['report_date'], record['pe_ttm'], record['pb_lf'],
                            record['ps_ttm'], record['pcf_ttm'], record['roe_ttm'], record['roa_ttm'],
                            record['revenue'], record['net_profit'], record['gross_profit'],
                            record['total_assets'], record['total_liabilities']
                        ))
                    
                    conn.commit()
                    success_count += 1
                    logger.info(f"✅ {symbol} 财务数据保存成功，共 {len(financial_data)} 条记录")
                
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"❌ 获取 {symbol} 财务数据失败: {e}")
                continue
        
        conn.close()
        logger.info(f"✅ 财务数据获取完成: {success_count}/{len(symbols)}")
        return success_count, len(symbols)
    
    def simulate_money_flow_data(self, symbol):
        """模拟资金流向数据"""
        try:
            # 生成模拟资金流向数据
            np.random.seed(hash(symbol) % 2**32)
            
            date_range = pd.date_range(start='2021-01-01', end='2024-12-31', freq='D')
            date_range = [d for d in date_range if d.weekday() < 5]  # 过滤周末
            
            data = []
            for date in date_range[-252:]:  # 最近一年的数据
                # 模拟资金流向
                net_amount_main = np.random.normal(0, 1000000)  # 主力净流入
                net_pct_main = np.random.normal(0, 2)  # 主力净流入占比
                net_amount_xl = np.random.normal(0, 500000)  # 超大单净流入
                net_pct_xl = np.random.normal(0, 1.5)
                net_amount_l = np.random.normal(0, 300000)  # 大单净流入
                net_pct_l = np.random.normal(0, 1)
                net_amount_m = np.random.normal(0, 200000)  # 中单净流入
                net_pct_m = np.random.normal(0, 0.8)
                net_amount_s = np.random.normal(0, 100000)  # 小单净流入
                net_pct_s = np.random.normal(0, 0.5)
                
                data.append({
                    'trade_date': date.date(),
                    'net_amount_main': round(net_amount_main, 2),
                    'net_pct_main': round(net_pct_main, 2),
                    'net_amount_xl': round(net_amount_xl, 2),
                    'net_pct_xl': round(net_pct_xl, 2),
                    'net_amount_l': round(net_amount_l, 2),
                    'net_pct_l': round(net_pct_l, 2),
                    'net_amount_m': round(net_amount_m, 2),
                    'net_pct_m': round(net_pct_m, 2),
                    'net_amount_s': round(net_amount_s, 2),
                    'net_pct_s': round(net_pct_s, 2)
                })
            
            return data
            
        except Exception as e:
            logger.error(f"模拟资金流向数据生成失败 {symbol}: {e}")
            return None
    
    def get_money_flow_data(self):
        """获取资金流向数据"""
        logger.info("开始获取资金流向数据...")
        
        symbols = self.get_stock_list()
        conn = sqlite3.connect(self.db_path)
        success_count = 0
        
        for i, symbol in enumerate(symbols):
            try:
                logger.info(f"获取 {symbol} 资金流向数据 ({i+1}/{len(symbols)})")
                
                money_flow_data = self.simulate_money_flow_data(symbol)
                
                if money_flow_data:
                    cursor = conn.cursor()
                    for record in money_flow_data:
                        cursor.execute("""
                            INSERT OR REPLACE INTO money_flow_data 
                            (symbol, trade_date, net_amount_main, net_pct_main, net_amount_xl, net_pct_xl,
                             net_amount_l, net_pct_l, net_amount_m, net_pct_m, net_amount_s, net_pct_s)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            symbol, record['trade_date'], record['net_amount_main'], record['net_pct_main'],
                            record['net_amount_xl'], record['net_pct_xl'], record['net_amount_l'], record['net_pct_l'],
                            record['net_amount_m'], record['net_pct_m'], record['net_amount_s'], record['net_pct_s']
                        ))
                    
                    conn.commit()
                    success_count += 1
                    logger.info(f"✅ {symbol} 资金流向数据保存成功，共 {len(money_flow_data)} 条记录")
                
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"❌ 获取 {symbol} 资金流向数据失败: {e}")
                continue
        
        conn.close()
        logger.info(f"✅ 资金流向数据获取完成: {success_count}/{len(symbols)}")
        return success_count, len(symbols)
    
    def get_industry_concept_data(self):
        """获取行业概念数据"""
        logger.info("开始获取行业概念数据...")
        
        symbols = self.get_stock_list()
        conn = sqlite3.connect(self.db_path)
        
        # 模拟行业概念数据
        industries = ['银行', '证券', '保险', '房地产', '食品饮料', '医药生物', '电子', '计算机', '通信', '新能源']
        concepts = ['人工智能', '新能源汽车', '5G', '芯片', '医疗器械', '云计算', '区块链', '物联网']
        
        success_count = 0
        
        for i, symbol in enumerate(symbols):
            try:
                # 随机分配行业和概念
                np.random.seed(hash(symbol) % 2**32)
                industry = np.random.choice(industries)
                concept_list = np.random.choice(concepts, size=np.random.randint(1, 4), replace=False)
                concept_names = ','.join(concept_list)
                
                market_cap = np.random.uniform(1000000, 100000000)  # 市值
                
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO industry_concept_mapping 
                    (symbol, sec_name, industry_sw1, concept_names, market_cap)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    symbol, f"股票{i+1}", industry, concept_names, round(market_cap, 2)
                ))
                
                conn.commit()
                success_count += 1
                logger.info(f"✅ {symbol} 行业概念数据保存成功")
                
            except Exception as e:
                logger.error(f"❌ 获取 {symbol} 行业概念数据失败: {e}")
                continue
        
        conn.close()
        logger.info(f"✅ 行业概念数据获取完成: {success_count}/{len(symbols)}")
        return success_count, len(symbols)
    
    def verify_data_quality(self):
        """验证数据质量"""
        logger.info("开始数据质量验证...")
        
        conn = sqlite3.connect(self.db_path)
        
        # 检查各表的数据量
        tables = [
            'daily_market_data',
            'financial_data', 
            'money_flow_data',
            'industry_concept_mapping'
        ]
        
        for table in tables:
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            logger.info(f"📊 {table}: {count} 条记录")
        
        # 检查数据完整性
        cursor.execute("""
            SELECT symbol, COUNT(*) as record_count 
            FROM daily_market_data 
            GROUP BY symbol 
            ORDER BY record_count DESC
        """)
        
        results = cursor.fetchall()
        logger.info(f"📈 行情数据覆盖情况:")
        for symbol, count in results[:5]:
            logger.info(f"   {symbol}: {count} 条记录")
        
        conn.close()
        logger.info("✅ 数据质量验证完成")
    
    def execute_full_data_acquisition(self):
        """执行完整数据获取"""
        logger.info("🚀 开始执行完整数据获取...")
        
        start_time = datetime.now()
        
        # 第1步: 获取历史行情数据
        logger.info("=" * 60)
        logger.info("第1步: 获取历史行情数据")
        hist_success, hist_total = self.get_comprehensive_history_data()
        
        # 第2步: 获取财务数据
        logger.info("=" * 60)
        logger.info("第2步: 获取财务数据")
        fin_success, fin_total = self.get_comprehensive_financial_data()
        
        # 第3步: 获取资金流向数据
        logger.info("=" * 60)
        logger.info("第3步: 获取资金流向数据")
        flow_success, flow_total = self.get_money_flow_data()
        
        # 第4步: 获取行业概念数据
        logger.info("=" * 60)
        logger.info("第4步: 获取行业概念数据")
        ind_success, ind_total = self.get_industry_concept_data()
        
        # 第5步: 验证数据质量
        logger.info("=" * 60)
        logger.info("第5步: 验证数据质量")
        self.verify_data_quality()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 总结报告
        logger.info("=" * 60)
        logger.info("🎉 数据获取完成总结")
        logger.info(f"⏰ 总耗时: {duration}")
        logger.info(f"📊 历史行情: {hist_success}/{hist_total}")
        logger.info(f"💰 财务数据: {fin_success}/{fin_total}")
        logger.info(f"🌊 资金流向: {flow_success}/{flow_total}")
        logger.info(f"🏢 行业概念: {ind_success}/{ind_total}")
        
        total_success = hist_success + fin_success + flow_success + ind_success
        total_attempts = hist_total + fin_total + flow_total + ind_total
        success_rate = total_success / total_attempts * 100 if total_attempts > 0 else 0
        
        logger.info(f"✅ 总体成功率: {success_rate:.1f}%")
        
        return {
            'duration': duration,
            'success_rate': success_rate,
            'details': {
                'history': (hist_success, hist_total),
                'financial': (fin_success, fin_total),
                'money_flow': (flow_success, flow_total),
                'industry': (ind_success, ind_total)
            }
        }

def main():
    """主函数"""
    print("🚀 掘金平台数据获取执行器启动")
    print("=" * 60)
    
    executor = GMDataAcquisitionExecutor()
    result = executor.execute_full_data_acquisition()
    
    print("\n🎉 数据获取执行完成!")
    print(f"⏰ 耗时: {result['duration']}")
    print(f"✅ 成功率: {result['success_rate']:.1f}%")
    
    return result

if __name__ == '__main__':
    main()
