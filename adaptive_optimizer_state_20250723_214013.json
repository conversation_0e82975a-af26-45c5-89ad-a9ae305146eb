{"performance_history": [{"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SZSE.000001", "entry_price": 0, "exit_price": 0, "return_pct": 0.025, "holding_days": 3, "win": true, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SZSE.000002", "entry_price": 0, "exit_price": 0, "return_pct": -0.015, "holding_days": 2, "win": false, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SHSE.600000", "entry_price": 0, "exit_price": 0, "return_pct": 0.018, "holding_days": 4, "win": true, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SHSE.600036", "entry_price": 0, "exit_price": 0, "return_pct": -0.008, "holding_days": 1, "win": false, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SZSE.300015", "entry_price": 0, "exit_price": 0, "return_pct": 0.032, "holding_days": 5, "win": true, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SZSE.000858", "entry_price": 0, "exit_price": 0, "return_pct": -0.022, "holding_days": 2, "win": false, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SHSE.600519", "entry_price": 0, "exit_price": 0, "return_pct": 0.041, "holding_days": 6, "win": true, "factors_used": {}, "market_conditions": {}}, {"timestamp": "2025-07-23T21:40:13.868149", "symbol": "SHSE.600887", "entry_price": 0, "exit_price": 0, "return_pct": 0.012, "holding_days": 3, "win": true, "factors_used": {}, "market_conditions": {}}], "parameter_history": [{"timestamp": "2025-07-23T21:40:13.870656", "adjustments": {"min_combined_score": {"old_value": 0.5, "new_value": 0.45, "adjustment": -0.05, "reason": "日均信号0.4个过少，放宽筛选条件"}, "cci_max_threshold": {"old_value": 120, "new_value": 130, "adjustment": 10, "reason": "放宽CCI上限增加信号"}}, "trigger": "adaptive_optimization"}], "adaptation_rules": {"win_rate_adaptation": {"target_win_rate": 0.6, "tolerance": 0.05, "adjustment_strength": 0.1, "lookback_period": 20}, "return_adaptation": {"target_avg_return": 0.015, "tolerance": 0.005, "adjustment_strength": 0.15, "lookback_period": 15}, "risk_adaptation": {"max_drawdown": 0.08, "max_single_loss": 0.05, "adjustment_strength": 0.2, "lookback_period": 10}, "signal_count_adaptation": {"target_daily_signals": 3, "min_signals": 1, "max_signals": 8, "adjustment_strength": 0.1}, "market_adaptation": {"volatility_threshold": 0.02, "trend_threshold": 0.01, "adaptation_speed": 0.2}}, "current_parameters": {}}