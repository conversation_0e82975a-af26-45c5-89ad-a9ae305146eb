# coding=utf-8
"""
调试字段映射问题
检查增强因子引擎生成的字段名与数据库字段名的匹配
"""

import sqlite3
import pandas as pd
import numpy as np

def check_database_fields():
    """检查数据库实际字段"""
    print('🔍 检查数据库实际字段')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        
        all_fields = [col[1] for col in columns]
        
        # 查找技术指标相关字段
        tech_fields = []
        for field in all_fields:
            if any(keyword in field.lower() for keyword in ['ma', 'rsi', 'macd', 'adx', 'cci', 'atr', 'bb', 'trix', 'volume', 'price']):
                tech_fields.append(field)
        
        print(f'📊 数据库技术指标字段 ({len(tech_fields)}个):')
        for field in sorted(tech_fields):
            print(f'  • {field}')
        
        conn.close()
        return tech_fields
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return []

def test_factor_engine_output():
    """测试因子引擎输出"""
    print(f'\n🧪 测试因子引擎输出')
    print('=' * 50)
    
    try:
        from enhanced_factor_engine import EnhancedFactorEngine
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [10.0]
        for _ in range(49):
            prices.append(prices[-1] * (1 + np.random.normal(0.001, 0.02)))
        
        test_data = pd.DataFrame({
            'open': np.array(prices) * 1.01,
            'high': np.array(prices) * 1.02,
            'low': np.array(prices) * 0.98,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 50)
        }, index=dates)
        
        engine = EnhancedFactorEngine()
        factors = engine.calculate_all_factors(test_data, 'TEST.000001')
        
        print(f'✅ 因子引擎生成了 {len(factors)} 个因子')
        
        # 查找技术指标相关因子
        tech_factors = []
        for field, value in factors.items():
            if any(keyword in field.lower() for keyword in ['ma', 'rsi', 'macd', 'adx', 'cci', 'atr', 'bb', 'trix', 'volume', 'price']):
                tech_factors.append((field, value))
        
        print(f'\n📈 技术指标因子 ({len(tech_factors)}个):')
        for field, value in sorted(tech_factors):
            if isinstance(value, (int, float)) and not np.isnan(value) and np.isfinite(value):
                print(f'  ✅ {field}: {value:.6f}')
            else:
                print(f'  ⚠️ {field}: {value}')
        
        return factors
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return {}

def compare_field_mapping(db_fields, factor_fields):
    """比较字段映射"""
    print(f'\n🔍 比较字段映射')
    print('=' * 50)
    
    if not db_fields or not factor_fields:
        print('❌ 缺少比较数据')
        return
    
    # 查找匹配的字段
    matched_fields = []
    unmatched_db_fields = []
    unmatched_factor_fields = []
    
    for db_field in db_fields:
        if db_field in factor_fields:
            matched_fields.append(db_field)
        else:
            unmatched_db_fields.append(db_field)
    
    for factor_field in factor_fields:
        if factor_field not in db_fields:
            unmatched_factor_fields.append(factor_field)
    
    print(f'📊 字段映射统计:')
    print(f'  数据库技术指标字段: {len(db_fields)}个')
    print(f'  因子引擎技术指标字段: {len(factor_fields)}个')
    print(f'  匹配字段: {len(matched_fields)}个')
    print(f'  数据库独有: {len(unmatched_db_fields)}个')
    print(f'  因子引擎独有: {len(unmatched_factor_fields)}个')
    
    if matched_fields:
        print(f'\n✅ 匹配的字段 ({len(matched_fields)}个):')
        for field in sorted(matched_fields):
            print(f'  • {field}')
    
    if unmatched_db_fields:
        print(f'\n❌ 数据库有但因子引擎没有的字段 ({len(unmatched_db_fields)}个):')
        for field in sorted(unmatched_db_fields)[:10]:
            print(f'  • {field}')
        if len(unmatched_db_fields) > 10:
            print(f'  ... 还有{len(unmatched_db_fields)-10}个')
    
    if unmatched_factor_fields:
        print(f'\n❌ 因子引擎有但数据库没有的字段 ({len(unmatched_factor_fields)}个):')
        for field in sorted(unmatched_factor_fields)[:10]:
            print(f'  • {field}')
        if len(unmatched_factor_fields) > 10:
            print(f'  ... 还有{len(unmatched_factor_fields)-10}个')

def check_specific_field_mapping():
    """检查特定字段映射"""
    print(f'\n🎯 检查特定关键字段映射')
    print('=' * 50)
    
    # 关键技术指标的预期映射
    expected_mappings = [
        ('rsi_14', 'rsi'),
        ('macd', 'macd'),
        ('macd_signal', 'macd_signal'),
        ('macd_hist', 'macd_hist'),
        ('adx', 'adx'),
        ('cci', 'cci'),
        ('atr_14_pct', 'atr_pct'),
        ('bb_width', 'bb_width'),
        ('bb_position', 'bb_position'),
        ('ma20', 'ma20'),
        ('trix', 'trix_buy'),
        ('volume_ratio_20', 'relative_volume'),
        ('volume_change_pct', 'volume_change_rate')
    ]
    
    print('📋 预期的关键字段映射:')
    for factor_name, db_field in expected_mappings:
        print(f'  {factor_name} → {db_field}')
    
    # 测试映射是否正确
    try:
        from enhanced_factor_engine import EnhancedFactorEngine
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        prices = [10.0]
        for _ in range(49):
            prices.append(prices[-1] * (1 + np.random.normal(0.001, 0.02)))
        
        test_data = pd.DataFrame({
            'open': np.array(prices) * 1.01,
            'high': np.array(prices) * 1.02,
            'low': np.array(prices) * 0.98,
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 50)
        }, index=dates)
        
        engine = EnhancedFactorEngine()
        factors = engine.calculate_all_factors(test_data, 'TEST.000001')
        
        print(f'\n🧪 测试映射结果:')
        
        mapping_success = 0
        for factor_name, db_field in expected_mappings:
            if db_field in factors:
                value = factors[db_field]
                if isinstance(value, (int, float)) and not np.isnan(value) and np.isfinite(value):
                    print(f'  ✅ {factor_name} → {db_field}: {value:.6f}')
                    mapping_success += 1
                else:
                    print(f'  ⚠️ {factor_name} → {db_field}: {value} (无效值)')
            else:
                print(f'  ❌ {factor_name} → {db_field}: 缺失')
        
        print(f'\n📊 映射成功率: {mapping_success}/{len(expected_mappings)} ({mapping_success/len(expected_mappings)*100:.1f}%)')
        
        if mapping_success < len(expected_mappings) * 0.8:
            print('❌ 字段映射存在严重问题')
        else:
            print('✅ 字段映射基本正常')
            
    except Exception as e:
        print(f'❌ 测试映射失败: {e}')

def main():
    """主函数"""
    print('🔍 字段映射调试分析')
    print('=' * 60)
    
    # 检查数据库字段
    db_fields = check_database_fields()
    
    # 测试因子引擎输出
    factors = test_factor_engine_output()
    
    # 提取技术指标因子字段名
    factor_fields = []
    if factors:
        for field in factors.keys():
            if any(keyword in field.lower() for keyword in ['ma', 'rsi', 'macd', 'adx', 'cci', 'atr', 'bb', 'trix', 'volume', 'price']):
                factor_fields.append(field)
    
    # 比较字段映射
    compare_field_mapping(db_fields, factor_fields)
    
    # 检查特定字段映射
    check_specific_field_mapping()
    
    print(f'\n🎯 调试结论')
    print('=' * 40)
    
    if len(factor_fields) > 0 and len(db_fields) > 0:
        matched_count = len(set(factor_fields) & set(db_fields))
        if matched_count < len(factor_fields) * 0.5:
            print('❌ 字段映射存在严重问题')
            print('🔧 需要修复enhanced_factor_engine.py中的字段映射')
        else:
            print('✅ 字段映射基本正常')
            print('🔧 问题可能在于数据传递或保存环节')
    else:
        print('❌ 无法进行字段映射比较')

if __name__ == '__main__':
    main()
