# coding=utf-8
"""
因子有效性分析工具
分析各个因子对策略胜率的贡献，识别最有效的因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class FactorEffectivenessAnalyzer:
    """因子有效性分析器"""
    
    def __init__(self, db_path='data/trades.db'):
        self.db_path = db_path
        
    def analyze_factor_effectiveness(self):
        """分析因子有效性"""
        print('🔬 因子有效性分析')
        print('=' * 60)
        print(f'分析时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        try:
            # 获取交易数据
            buy_df, sell_df = self._load_trading_data()
            
            if buy_df is None or len(buy_df) == 0:
                print('❌ 没有买入数据可分析')
                return
            
            # 计算交易收益
            trade_returns = self._calculate_trade_returns(buy_df, sell_df)
            
            if len(trade_returns) == 0:
                print('❌ 没有完整的交易对可分析')
                return
            
            # 分析因子与收益的关系
            factor_analysis = self._analyze_factor_returns(trade_returns)
            
            # 生成因子排名
            factor_ranking = self._rank_factors(factor_analysis)
            
            # 显示分析结果
            self._display_results(factor_ranking, trade_returns)
            
            return factor_ranking
            
        except Exception as e:
            print(f'❌ 分析失败: {e}')
            return None
    
    def _load_trading_data(self):
        """加载交易数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取买入数据
            buy_df = pd.read_sql_query("""
                SELECT * FROM trades 
                WHERE action = 'BUY' 
                ORDER BY timestamp
            """, conn)
            
            # 获取卖出数据
            sell_df = pd.read_sql_query("""
                SELECT * FROM trades 
                WHERE action = 'SELL' 
                ORDER BY timestamp
            """, conn)
            
            conn.close()
            
            print(f'📊 数据加载完成:')
            print(f'  买入记录: {len(buy_df)}条')
            print(f'  卖出记录: {len(sell_df)}条')
            
            return buy_df, sell_df
            
        except Exception as e:
            print(f'❌ 数据加载失败: {e}')
            return None, None
    
    def _calculate_trade_returns(self, buy_df, sell_df):
        """计算交易收益"""
        trade_returns = []
        
        try:
            # 按股票分组计算收益
            for symbol in buy_df['symbol'].unique():
                symbol_buys = buy_df[buy_df['symbol'] == symbol].copy()
                symbol_sells = sell_df[sell_df['symbol'] == symbol].copy()
                
                if len(symbol_sells) == 0:
                    continue
                
                # 简化处理：使用最早买入和最晚卖出
                if len(symbol_buys) > 0 and len(symbol_sells) > 0:
                    buy_record = symbol_buys.iloc[0]  # 第一次买入
                    sell_record = symbol_sells.iloc[-1]  # 最后一次卖出
                    
                    buy_price = float(buy_record['price'])
                    sell_price = float(sell_record['price'])
                    
                    # 计算收益率
                    return_pct = (sell_price - buy_price) / buy_price * 100
                    
                    # 构建交易记录
                    trade_record = {
                        'symbol': symbol,
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'return_pct': return_pct,
                        'profitable': 1 if return_pct > 0 else 0
                    }
                    
                    # 添加买入时的因子数据
                    for col in buy_record.index:
                        if col not in ['timestamp', 'symbol', 'action', 'price', 'volume']:
                            try:
                                value = float(buy_record[col])
                                if not np.isnan(value) and np.isfinite(value):
                                    trade_record[col] = value
                            except (ValueError, TypeError):
                                pass
                    
                    trade_returns.append(trade_record)
            
            print(f'📈 计算完成交易收益: {len(trade_returns)}笔交易')
            
            return trade_returns
            
        except Exception as e:
            print(f'❌ 收益计算失败: {e}')
            return []
    
    def _analyze_factor_returns(self, trade_returns):
        """分析因子与收益的关系"""
        if len(trade_returns) == 0:
            return {}
        
        df = pd.DataFrame(trade_returns)
        factor_analysis = {}
        
        # 获取所有因子列
        factor_columns = [col for col in df.columns 
                         if col not in ['symbol', 'buy_price', 'sell_price', 'return_pct', 'profitable']]
        
        print(f'🔍 分析{len(factor_columns)}个因子与收益的关系')
        
        for factor in factor_columns:
            try:
                factor_values = df[factor].dropna()
                returns = df.loc[factor_values.index, 'return_pct']
                profitable = df.loc[factor_values.index, 'profitable']
                
                if len(factor_values) < 5:  # 数据太少跳过
                    continue
                
                # 计算相关性
                correlation = stats.pearsonr(factor_values, returns)[0] if len(factor_values) > 1 else 0
                
                # 计算因子分组收益
                factor_quartiles = pd.qcut(factor_values, q=4, labels=['Q1', 'Q2', 'Q3', 'Q4'], duplicates='drop')
                group_returns = returns.groupby(factor_quartiles).mean()
                group_win_rates = profitable.groupby(factor_quartiles).mean()
                
                # 计算因子有效性指标
                effectiveness_score = 0
                
                # 1. 相关性得分 (30%)
                correlation_score = abs(correlation) * 0.3
                
                # 2. 单调性得分 (40%) - 因子值越高，收益是否单调变化
                if len(group_returns) >= 2:
                    monotonicity = stats.spearmanr(range(len(group_returns)), group_returns.values)[0]
                    monotonicity_score = abs(monotonicity) * 0.4
                else:
                    monotonicity_score = 0
                
                # 3. 胜率差异得分 (30%) - 高低分组胜率差异
                if len(group_win_rates) >= 2:
                    win_rate_diff = abs(group_win_rates.iloc[-1] - group_win_rates.iloc[0])
                    win_rate_score = win_rate_diff * 0.3
                else:
                    win_rate_score = 0
                
                effectiveness_score = correlation_score + monotonicity_score + win_rate_score
                
                factor_analysis[factor] = {
                    'correlation': correlation,
                    'effectiveness_score': effectiveness_score,
                    'group_returns': group_returns.to_dict() if len(group_returns) > 0 else {},
                    'group_win_rates': group_win_rates.to_dict() if len(group_win_rates) > 0 else {},
                    'sample_size': len(factor_values),
                    'mean_value': factor_values.mean(),
                    'std_value': factor_values.std()
                }
                
            except Exception as e:
                continue
        
        return factor_analysis
    
    def _rank_factors(self, factor_analysis):
        """对因子进行排名"""
        if not factor_analysis:
            return []
        
        # 按有效性得分排序
        factor_ranking = []
        for factor, analysis in factor_analysis.items():
            factor_ranking.append({
                'factor': factor,
                'effectiveness_score': analysis['effectiveness_score'],
                'correlation': analysis['correlation'],
                'sample_size': analysis['sample_size'],
                'group_returns': analysis['group_returns'],
                'group_win_rates': analysis['group_win_rates']
            })
        
        factor_ranking.sort(key=lambda x: x['effectiveness_score'], reverse=True)
        
        return factor_ranking
    
    def _display_results(self, factor_ranking, trade_returns):
        """显示分析结果"""
        print(f'\n📊 因子有效性分析结果')
        print('=' * 60)
        
        if not factor_ranking:
            print('❌ 没有有效的因子分析结果')
            return
        
        # 总体统计
        df = pd.DataFrame(trade_returns)
        total_trades = len(df)
        profitable_trades = len(df[df['profitable'] == 1])
        overall_win_rate = profitable_trades / total_trades * 100
        avg_return = df['return_pct'].mean()
        
        print(f'📈 总体交易统计:')
        print(f'  总交易数: {total_trades}笔')
        print(f'  盈利交易: {profitable_trades}笔')
        print(f'  整体胜率: {overall_win_rate:.1f}%')
        print(f'  平均收益: {avg_return:.2f}%')
        
        # 显示最有效的因子
        print(f'\n🏆 最有效的因子 (Top 20):')
        print(f'{"排名":<4} {"因子名称":<25} {"有效性得分":<12} {"相关性":<10} {"样本数":<8} {"评级"}')
        print('-' * 75)
        
        for i, factor in enumerate(factor_ranking[:20], 1):
            effectiveness = factor['effectiveness_score']
            correlation = factor['correlation']
            sample_size = factor['sample_size']
            
            # 评级
            if effectiveness >= 0.3:
                rating = '⭐⭐⭐'
            elif effectiveness >= 0.2:
                rating = '⭐⭐'
            elif effectiveness >= 0.1:
                rating = '⭐'
            else:
                rating = '❌'
            
            print(f'{i:<4} {factor["factor"]:<25} {effectiveness:<12.4f} {correlation:<10.4f} {sample_size:<8} {rating}')
        
        # 显示因子分类统计
        self._display_factor_categories(factor_ranking)
        
        # 推荐使用的因子
        self._recommend_factors(factor_ranking)
    
    def _display_factor_categories(self, factor_ranking):
        """显示因子分类统计"""
        print(f'\n📊 因子分类有效性统计:')
        
        categories = {
            '价格因子': ['price', 'momentum', 'reversal', 'position'],
            '移动平均': ['ma', 'ema'],
            '趋势指标': ['macd', 'adx', 'dmi', 'trix', 'aroon'],
            '超买超卖': ['rsi', 'cci', 'willr', 'stoch', 'ultosc'],
            '成交量': ['volume', 'obv', 'mfi'],
            '波动率': ['atr', 'bb_', 'volatility', 'kc_'],
            '自定义': ['score', 'consistency', 'confirmation', 'signal']
        }
        
        category_stats = {}
        for category, keywords in categories.items():
            category_factors = []
            for factor in factor_ranking:
                factor_name = factor['factor'].lower()
                if any(keyword in factor_name for keyword in keywords):
                    category_factors.append(factor)
            
            if category_factors:
                avg_effectiveness = np.mean([f['effectiveness_score'] for f in category_factors])
                category_stats[category] = {
                    'count': len(category_factors),
                    'avg_effectiveness': avg_effectiveness,
                    'top_factor': category_factors[0]['factor'] if category_factors else None
                }
        
        for category, stats in sorted(category_stats.items(), key=lambda x: x[1]['avg_effectiveness'], reverse=True):
            print(f'  {category:<12}: {stats["count"]:>2}个因子, 平均有效性: {stats["avg_effectiveness"]:.4f}, 最佳: {stats["top_factor"]}')
    
    def _recommend_factors(self, factor_ranking):
        """推荐使用的因子"""
        print(f'\n💡 因子使用建议:')
        
        # 高效因子 (有效性得分 >= 0.2)
        high_effective = [f for f in factor_ranking if f['effectiveness_score'] >= 0.2]
        
        # 中效因子 (有效性得分 >= 0.1)
        medium_effective = [f for f in factor_ranking if 0.1 <= f['effectiveness_score'] < 0.2]
        
        # 低效因子 (有效性得分 < 0.1)
        low_effective = [f for f in factor_ranking if f['effectiveness_score'] < 0.1]
        
        print(f'🌟 高效因子 ({len(high_effective)}个): 强烈推荐使用')
        for factor in high_effective[:5]:
            print(f'  • {factor["factor"]} (得分: {factor["effectiveness_score"]:.4f})')
        
        print(f'\n⭐ 中效因子 ({len(medium_effective)}个): 可以考虑使用')
        for factor in medium_effective[:3]:
            print(f'  • {factor["factor"]} (得分: {factor["effectiveness_score"]:.4f})')
        
        print(f'\n❌ 低效因子 ({len(low_effective)}个): 建议移除或优化')
        
        # 因子组合建议
        print(f'\n🎯 因子组合建议:')
        if len(high_effective) >= 3:
            print(f'  建议使用前3-5个高效因子构建综合评分模型')
            print(f'  权重分配: 按有效性得分比例分配')
        else:
            print(f'  当前高效因子较少，建议增加更多有效因子')

def main():
    """主函数"""
    print('🔬 策略因子有效性分析工具')
    print('=' * 60)
    
    analyzer = FactorEffectivenessAnalyzer()
    factor_ranking = analyzer.analyze_factor_effectiveness()
    
    if factor_ranking:
        print(f'\n🎯 分析完成!')
        print(f'✅ 成功分析了{len(factor_ranking)}个因子的有效性')
        print(f'💡 请根据分析结果优化策略中的因子使用')
    else:
        print(f'\n❌ 分析失败，请检查数据质量')

if __name__ == '__main__':
    main()
