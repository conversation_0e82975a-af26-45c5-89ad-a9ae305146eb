#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和交易策略 - 配置管理器模块
实现动态加载配置的功能，允许在不重启策略的情况下更新参数
"""

import os
import sys
import time
import importlib
import logging
import json
import traceback
from pathlib import Path
from datetime import datetime
from threading import Thread, Event

class ConfigManager:
    """配置管理器类，用于动态加载和管理配置"""
    
    def __init__(self, config_file='config.py', check_interval=60, logger=None):
        """
        初始化配置管理器
        
        参数:
        - config_file: 配置文件路径，默认为'config.py'
        - check_interval: 检查配置文件变化的时间间隔（秒），默认为60秒
        - logger: 日志记录器，如果为None则创建新的日志记录器
        """
        # 配置文件路径
        self.config_file = Path(config_file)
        self.module_name = self.config_file.stem
        
        # 确保配置文件在Python路径中
        self.config_dir = self.config_file.parent
        if str(self.config_dir) not in sys.path:
            sys.path.insert(0, str(self.config_dir))
        
        # 检查间隔
        self.check_interval = check_interval
        
        # 初始化日志记录器
        self.logger = logger or self._setup_logger()
        
        # 加载初始配置
        self.config = None
        self.config_backup = None
        self.last_modified_time = None
        self.last_reload_time = None
        self.reload_count = 0
        
        # 初始化文件监控线程
        self.monitor_thread = None
        self.stop_event = Event()
        
        # 首次加载配置
        self.load_config()
        
        self.logger.info(f"配置管理器初始化完成，将监控 {self.config_file} 的变化")
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('config_manager')
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def load_config(self):
        """加载配置文件"""
        try:
            # 获取文件修改时间
            mtime = os.path.getmtime(self.config_file)
            
            # 如果文件没有变化，则不重新加载
            if self.last_modified_time == mtime:
                return False
            
            # 备份当前配置
            if self.config is not None:
                self.config_backup = self.config
            
            # 重新加载模块
            if self.module_name in sys.modules:
                self.config = importlib.reload(sys.modules[self.module_name])
            else:
                self.config = importlib.import_module(self.module_name)
            
            # 更新时间戳
            self.last_modified_time = mtime
            self.last_reload_time = datetime.now()
            self.reload_count += 1
            
            self.logger.info(f"配置已重新加载 ({self.reload_count}次) - {self.last_reload_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 记录变化的参数
            if self.config_backup is not None:
                self._log_config_changes()
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件异常: {str(e)}")
            self.logger.error(traceback.format_exc())
            
            # 如果加载失败且有备份，则恢复备份
            if self.config is None and self.config_backup is not None:
                self.logger.warning("加载失败，恢复到上一个可用配置")
                self.config = self.config_backup
            
            return False
    
    def _log_config_changes(self):
        """记录配置变化"""
        changes = []
        
        # 获取旧配置的所有公共属性
        old_attrs = {attr: getattr(self.config_backup, attr) 
                     for attr in dir(self.config_backup) 
                     if not attr.startswith('_') and not callable(getattr(self.config_backup, attr))}
        
        # 获取新配置的所有公共属性
        new_attrs = {attr: getattr(self.config, attr) 
                     for attr in dir(self.config) 
                     if not attr.startswith('_') and not callable(getattr(self.config, attr))}
        
        # 检查变化的参数
        for attr, new_value in new_attrs.items():
            if attr in old_attrs:
                old_value = old_attrs[attr]
                if new_value != old_value:
                    changes.append({
                        'param': attr,
                        'old_value': old_value,
                        'new_value': new_value
                    })
            else:
                changes.append({
                    'param': attr,
                    'old_value': None,
                    'new_value': new_value
                })
        
        # 检查删除的参数
        for attr in old_attrs:
            if attr not in new_attrs:
                changes.append({
                    'param': attr,
                    'old_value': old_attrs[attr],
                    'new_value': None
                })
        
        # 记录变化
        if changes:
            self.logger.info(f"检测到 {len(changes)} 个参数变化:")
            for change in changes:
                self.logger.info(f"  - {change['param']}: {change['old_value']} => {change['new_value']}")
        else:
            self.logger.info("配置文件已重新加载，但没有参数变化")
    
    def get_config(self):
        """获取当前配置"""
        return self.config
    
    def get_param(self, param_name, default=None):
        """
        获取指定参数的值
        
        参数:
        - param_name: 参数名称
        - default: 如果参数不存在，返回的默认值
        
        返回:
        - 参数值或默认值
        """
        if self.config is None:
            return default
        
        return getattr(self.config, param_name, default)
    
    def start_monitoring(self):
        """启动配置文件监控线程"""
        if self.monitor_thread is not None and self.monitor_thread.is_alive():
            self.logger.warning("监控线程已在运行")
            return
        
        self.stop_event.clear()
        self.monitor_thread = Thread(target=self._monitor_config_file, daemon=True)
        self.monitor_thread.start()
        self.logger.info(f"配置文件监控已启动，检查间隔为 {self.check_interval} 秒")
    
    def stop_monitoring(self):
        """停止配置文件监控线程"""
        if self.monitor_thread is None or not self.monitor_thread.is_alive():
            self.logger.warning("监控线程未运行")
            return
        
        self.stop_event.set()
        self.monitor_thread.join(timeout=5.0)
        if self.monitor_thread.is_alive():
            self.logger.warning("监控线程未正常结束")
        else:
            self.logger.info("配置文件监控已停止")
        
        self.monitor_thread = None
    
    def _monitor_config_file(self):
        """监控配置文件变化的线程函数"""
        self.logger.info(f"开始监控配置文件 {self.config_file}")
        
        while not self.stop_event.is_set():
            try:
                # 检查文件是否存在
                if not os.path.exists(self.config_file):
                    self.logger.warning(f"配置文件 {self.config_file} 不存在")
                else:
                    # 检查文件是否有变化
                    self.load_config()
                
                # 等待下一次检查
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"监控配置文件异常: {str(e)}")
                self.logger.error(traceback.format_exc())
                # 继续监控，不要因为一次异常而停止
                self.stop_event.wait(self.check_interval)
        
        self.logger.info("配置文件监控线程已结束")
    
    def get_config_summary(self):
        """获取配置摘要"""
        if self.config is None:
            return "配置未加载"
        
        result = {
            "文件路径": str(self.config_file),
            "最后修改时间": datetime.fromtimestamp(self.last_modified_time).strftime('%Y-%m-%d %H:%M:%S') if self.last_modified_time else "未知",
            "最后加载时间": self.last_reload_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_reload_time else "未知",
            "重新加载次数": self.reload_count,
            "参数": {}
        }
        
        # 获取所有公共属性
        for attr in dir(self.config):
            if not attr.startswith('_') and not callable(getattr(self.config, attr)):
                result["参数"][attr] = getattr(self.config, attr)
        
        return result
    
    def save_config_backup(self, backup_dir='config_backups'):
        """保存当前配置的备份"""
        try:
            # 确保备份目录存在
            os.makedirs(backup_dir, exist_ok=True)
            
            # 创建带时间戳的备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(backup_dir, f"config_backup_{timestamp}.py")
            
            # 复制配置文件
            with open(self.config_file, 'r', encoding='utf-8') as src:
                content = src.read()
                
            with open(backup_file, 'w', encoding='utf-8') as dst:
                dst.write(content)
            
            self.logger.info(f"配置文件已备份到 {backup_file}")
            return backup_file
            
        except Exception as e:
            self.logger.error(f"备份配置文件异常: {str(e)}")
            self.logger.error(traceback.format_exc())
            return None

# 创建全局配置管理器实例
config_manager = ConfigManager()

# 便捷函数：获取配置参数
def get_param(param_name, default=None):
    """获取配置参数的快捷方法"""
    return config_manager.get_param(param_name, default)

# 测试代码
if __name__ == "__main__":
    print("配置管理器测试")
    config_manager.start_monitoring()
    
    # 打印初始配置
    print("\n初始配置摘要:")
    print(json.dumps(config_manager.get_config_summary(), indent=4, ensure_ascii=False))
    
    # 等待用户手动修改配置文件
    print("\n请修改配置文件，然后按回车键查看变化...")
    input()
    
    # 强制重新加载配置并打印变化
    config_manager.load_config()
    print("\n当前配置摘要:")
    print(json.dumps(config_manager.get_config_summary(), indent=4, ensure_ascii=False))
    
    # 停止监控
    print("\n按回车键停止监控...")
    input()
    config_manager.stop_monitoring()
    print("测试完成") 