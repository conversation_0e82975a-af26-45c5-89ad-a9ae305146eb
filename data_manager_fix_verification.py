# coding=utf-8
"""
数据管理器修复验证脚本
验证数据管理器初始化修复是否正确
"""

import re

def verify_data_manager_fix():
    """验证数据管理器修复"""
    print('🔧 数据管理器修复验证')
    print('=' * 60)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print('📊 数据管理器初始化检查:')
        
        # 1. 检查全局数据管理器初始化
        global_init = re.findall(r'data_manager\s*=\s*get_data_manager\(\)', content)
        print(f'  1. 全局数据管理器初始化: {len(global_init)}处')
        
        # 2. 检查context数据管理器赋值
        context_assignment = re.findall(r'context\.data_manager\s*=\s*data_manager', content)
        print(f'  2. context数据管理器赋值: {len(context_assignment)}处')
        
        # 3. 检查数据管理器可用性检查
        availability_check = re.findall(r'hasattr\(context, [\'"]data_manager[\'"].*context\.data_manager', content)
        print(f'  3. 数据管理器可用性检查: {len(availability_check)}处')
        
        # 4. 检查数据管理器调用
        data_manager_calls = re.findall(r'context\.data_manager\.save_trade\(', content)
        print(f'  4. 数据管理器保存调用: {len(data_manager_calls)}处')
        
        # 5. 检查备用保存调用
        backup_save_calls = re.findall(r'save_analysis\(', content)
        print(f'  5. 备用保存调用: {len(backup_save_calls)}处')
        
        # 验证修复是否正确
        if len(context_assignment) > 0:
            print('\n✅ 数据管理器初始化修复成功！')
            print('  - context.data_manager 已正确赋值')
            print('  - 买入记录应该能正确保存到数据库')
        else:
            print('\n❌ 数据管理器初始化仍有问题')
            print('  - 缺少 context.data_manager 赋值')
        
        return len(context_assignment) > 0
        
    except Exception as e:
        print(f'❌ 验证失败: {e}')
        return False

def check_buy_record_save_logic():
    """检查买入记录保存逻辑"""
    print('\n📊 买入记录保存逻辑检查')
    print('=' * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找save_original_buy_record函数
        save_function_pattern = r'def save_original_buy_record\(.*?\):(.*?)(?=def|\Z)'
        save_function_match = re.search(save_function_pattern, content, re.DOTALL)
        
        if save_function_match:
            function_body = save_function_match.group(1)
            
            print('📝 买入记录保存逻辑分析:')
            
            # 检查数据管理器路径
            if 'context.data_manager' in function_body and 'save_trade' in function_body:
                print('  ✅ 主要保存路径: 使用数据管理器')
            else:
                print('  ❌ 主要保存路径: 数据管理器调用缺失')
            
            # 检查备用保存路径
            if 'save_analysis' in function_body:
                print('  ✅ 备用保存路径: save_analysis函数')
            else:
                print('  ❌ 备用保存路径: save_analysis调用缺失')
            
            # 检查条件判断
            if 'hasattr(context, \'data_manager\')' in function_body:
                print('  ✅ 条件判断: 检查数据管理器可用性')
            else:
                print('  ❌ 条件判断: 缺少数据管理器可用性检查')
            
            # 检查日志记录
            log_patterns = ['买入记录已保存', '买入记录已直接保存', '保存买入记录异常']
            for pattern in log_patterns:
                if pattern in function_body:
                    print(f'  ✅ 日志记录: {pattern}')
        else:
            print('❌ 未找到save_original_buy_record函数')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def predict_fix_effectiveness():
    """预测修复效果"""
    print('\n🔮 修复效果预测')
    print('=' * 50)
    
    print('📊 修复前后对比:')
    
    print('\n修复前:')
    print('  ❌ context.data_manager = None')
    print('  ❌ 买入记录保存走备用路径')
    print('  ❌ save_analysis可能失败或保存位置错误')
    print('  ❌ 数据库中没有买入记录')
    
    print('\n修复后:')
    print('  ✅ context.data_manager = 有效的数据管理器实例')
    print('  ✅ 买入记录保存走主要路径')
    print('  ✅ 使用data_manager.save_trade正确保存')
    print('  ✅ 买入记录应该能正确写入数据库')
    
    print('\n🎯 预期效果:')
    print('  1. 策略执行买入操作时，买入记录能正确保存')
    print('  2. 数据库中应该出现action=\'BUY\'的记录')
    print('  3. 胜率分析器应该能找到买入记录')
    print('  4. 买入卖出记录应该能正确配对')

def suggest_testing_steps():
    """建议测试步骤"""
    print('\n📋 建议测试步骤')
    print('=' * 50)
    
    steps = [
        {
            'step': '1. 运行策略测试',
            'description': '运行策略进行买入操作测试',
            'command': '运行main.py进行回测或实盘测试',
            'expected': '应该看到"数据管理器已初始化"的日志'
        },
        {
            'step': '2. 监控买入日志',
            'description': '观察买入操作的日志输出',
            'command': '查看策略日志中的买入相关信息',
            'expected': '应该看到"买入记录已保存到数据库"的日志'
        },
        {
            'step': '3. 检查数据库记录',
            'description': '检查数据库中是否出现买入记录',
            'command': 'SELECT * FROM trades WHERE action = "BUY"',
            'expected': '应该能找到买入记录'
        },
        {
            'step': '4. 运行胜率分析器',
            'description': '验证胜率分析器能否正常工作',
            'command': 'python 胜率分析器.py',
            'expected': '应该能分析买入卖出记录的配对'
        },
        {
            'step': '5. 数据完整性验证',
            'description': '验证买入卖出记录的完整性',
            'command': '检查买入卖出数量和时间的合理性',
            'expected': '买入卖出记录应该基本配对'
        }
    ]
    
    for step in steps:
        print(f'{step["step"]}: {step["description"]}')
        print(f'   操作: {step["command"]}')
        print(f'   预期: {step["expected"]}')
        print()

def main():
    """主函数"""
    print('🔧 数据管理器修复验证报告')
    print('=' * 60)
    
    # 验证数据管理器修复
    fix_success = verify_data_manager_fix()
    
    # 检查买入记录保存逻辑
    check_buy_record_save_logic()
    
    # 预测修复效果
    predict_fix_effectiveness()
    
    # 建议测试步骤
    suggest_testing_steps()
    
    print(f'\n🎯 修复验证总结:')
    print('=' * 40)
    
    if fix_success:
        print('✅ 数据管理器初始化修复成功')
        print('✅ 买入记录保存逻辑完整')
        print('✅ 预期能解决买入记录缺失问题')
        print('🔄 建议立即运行策略测试验证修复效果')
    else:
        print('❌ 数据管理器初始化仍有问题')
        print('❌ 需要进一步检查和修复')
        print('🔧 建议重新检查数据管理器赋值逻辑')

if __name__ == '__main__':
    main()
