# coding=utf-8
"""
因子组合实施工具
将胜率分析结果转化为可执行的策略代码
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib as mpl
mpl.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
mpl.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_trading_data():
    """加载交易数据"""
    print('📊 加载交易数据')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 加载所有交易记录
        query = """
        SELECT * FROM trades 
        ORDER BY timestamp
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'✅ 成功加载 {len(df)} 条交易记录')
        
        # 分析买卖记录分布
        buy_records = df[df['action'] == 'BUY']
        sell_records = df[df['action'] == 'SELL']
        
        print(f'📈 买入记录: {len(buy_records)} 条')
        print(f'📉 卖出记录: {len(sell_records)} 条')
        
        return df, buy_records, sell_records
        
    except Exception as e:
        print(f'❌ 加载数据失败: {e}')
        return None, None, None

def implement_factor_combinations(buy_records, sell_records):
    """实施因子组合策略"""
    print(f'\n🚀 实施因子组合策略')
    print('=' * 50)
    
    # 定义高胜率组合
    combinations = [
        {
            'name': '高胜率组合A',
            'description': '高波动 + 宽布林带 + MACD金叉',
            'conditions': [
                ('atr_pct', '>', 2.7),
                ('bb_width', '>', 10.8),
                ('macd_hist', '>', 0)
            ],
            'expected_win_rate': '35%+'
        },
        {
            'name': '稳健组合B',
            'description': '中等波动 + 强趋势 + RSI中性',
            'conditions': [
                ('atr_pct', '>', 2.2),
                ('adx', '>', 25),
                ('rsi', '>=', 30),
                ('rsi', '<=', 70)
            ],
            'expected_win_rate': '28%+'
        },
        {
            'name': '反转组合C',
            'description': 'MACD负值 + 布林带下轨 + 高成交量',
            'conditions': [
                ('macd', '<', -0.12),
                ('bb_position', '<', 20),
                ('relative_volume', '>', 1.5)
            ],
            'expected_win_rate': '30%+'
        },
        {
            'name': '突破组合D',
            'description': '远离均线 + TRIX买入 + 控制波动',
            'conditions': [
                ('ma20', '>', 23),
                ('trix_buy', '>', 0),
                ('atr_pct', '<', 3)
            ],
            'expected_win_rate': '26%+'
        }
    ]
    
    results = []
    
    for combo in combinations:
        print(f'\n📊 分析: {combo["name"]} ({combo["description"]})')
        
        # 应用组合条件
        filtered_buys = buy_records.copy()
        valid_combo = True
        
        for field, operator, value in combo['conditions']:
            if field not in buy_records.columns:
                print(f'   ⚠️ 缺少字段: {field}')
                valid_combo = False
                break
            
            # 过滤有效数据
            valid_data = (filtered_buys[field].notna()) & (filtered_buys[field] != 0)
            filtered_buys = filtered_buys[valid_data]
            
            # 应用条件
            if operator == '>':
                filtered_buys = filtered_buys[filtered_buys[field] > value]
            elif operator == '<':
                filtered_buys = filtered_buys[filtered_buys[field] < value]
            elif operator == '>=':
                filtered_buys = filtered_buys[filtered_buys[field] >= value]
            elif operator == '<=':
                filtered_buys = filtered_buys[filtered_buys[field] <= value]
        
        if not valid_combo:
            continue
        
        print(f'   符合条件的买入: {len(filtered_buys)} 条')
        
        if len(filtered_buys) > 0:
            # 计算实际胜率
            wins = 0
            total_matched = 0
            profit_sum = 0
            loss_sum = 0
            
            for _, buy in filtered_buys.iterrows():
                # 查找对应的卖出记录
                matching_sells = sell_records[
                    (sell_records['symbol'] == buy['symbol']) &
                    (sell_records['timestamp'] > buy['timestamp'])
                ]
                
                if len(matching_sells) > 0:
                    sell = matching_sells.iloc[0]
                    if 'net_profit_pct_sell' in sell and pd.notna(sell['net_profit_pct_sell']):
                        profit = sell['net_profit_pct_sell']
                        if profit > 0:
                            wins += 1
                            profit_sum += profit
                        else:
                            loss_sum += abs(profit)
                        total_matched += 1
            
            actual_win_rate = wins / total_matched * 100 if total_matched > 0 else 0
            avg_profit = profit_sum / wins if wins > 0 else 0
            avg_loss = loss_sum / (total_matched - wins) if (total_matched - wins) > 0 else 0
            profit_factor = profit_sum / loss_sum if loss_sum > 0 else float('inf')
            
            combo_result = {
                'name': combo['name'],
                'description': combo['description'],
                'expected_win_rate': combo['expected_win_rate'],
                'actual_win_rate': actual_win_rate,
                'total_trades': len(filtered_buys),
                'matched_trades': total_matched,
                'wins': wins,
                'losses': total_matched - wins,
                'avg_profit': avg_profit,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'symbols': filtered_buys['symbol'].unique().tolist()[:5]
            }
            
            results.append(combo_result)
            
            print(f'   实际胜率: {actual_win_rate:.1f}% ({wins}/{total_matched})')
            print(f'   平均盈利: {avg_profit:.2f}%')
            print(f'   平均亏损: {avg_loss:.2f}%')
            print(f'   盈亏比: {profit_factor:.2f}')
            print(f'   样本股票: {", ".join(combo_result["symbols"])}')
    
    return results

def generate_implementation_code(best_combo):
    """生成实施代码"""
    print(f'\n💻 生成实施代码')
    print('=' * 50)
    
    if not best_combo:
        print('⚠️ 没有有效的组合策略')
        return
    
    combo_name = best_combo['name']
    description = best_combo['description']
    conditions = []
    
    if combo_name == '高胜率组合A':
        conditions = [
            ('atr_pct', '>', 2.7),
            ('bb_width', '>', 10.8),
            ('macd_hist', '>', 0)
        ]
    elif combo_name == '稳健组合B':
        conditions = [
            ('atr_pct', '>', 2.2),
            ('adx', '>', 25),
            ('rsi', '>=', 30),
            ('rsi', '<=', 70)
        ]
    elif combo_name == '反转组合C':
        conditions = [
            ('macd', '<', -0.12),
            ('bb_position', '<', 20),
            ('relative_volume', '>', 1.5)
        ]
    elif combo_name == '突破组合D':
        conditions = [
            ('ma20', '>', 23),
            ('trix_buy', '>', 0),
            ('atr_pct', '<', 3)
        ]
    
    code = f"""
# 基于因子分析的{combo_name}策略实现
# {description}
# 预期胜率: {best_combo['expected_win_rate']}, 实际胜率: {best_combo['actual_win_rate']:.1f}%

def apply_{combo_name.lower().replace(' ', '_')}_strategy(data, context):
    \"\"\"
    应用{combo_name}策略
    {description}
    \"\"\"
    # 初始化信号
    buy_signal = False
    signal_reason = ""
    
    # 检查数据有效性
    if data is None or len(data) < 30:
        return False, "数据不足"
    
    try:
        # 计算技术指标
        from enhanced_factor_engine import EnhancedFactorEngine
        factor_engine = EnhancedFactorEngine(context)
        factors = factor_engine.calculate_all_factors(data, data.index[-1])
        
        # 应用{combo_name}条件
"""
    
    for field, operator, value in conditions:
        code += f"""        # 检查{field} {operator} {value}条件
        if "{field}" not in factors:
            return False, "{field}指标缺失"
        
        if not (factors["{field}"] {operator} {value}):
            return False, "{field} = {{:.2f}} 不满足 {operator} {value}".format(factors["{field}"])
        
"""
    
    code += f"""        # 所有条件满足，生成买入信号
        buy_signal = True
        signal_reason = "{description}信号触发"
        
        # 记录关键指标值
"""
    
    for field, _, _ in conditions:
        code += f"""        signal_reason += ", {field}={{:.2f}}".format(factors["{field}"])
        
"""
    
    code += f"""        return buy_signal, signal_reason
        
    except Exception as e:
        context.log.error(f"{{context.now.strftime('%Y-%m-%d %H:%M:%S')}} - ❌ 应用{combo_name}策略失败: {{str(e)}}")
        return False, f"策略应用异常: {{str(e)}}"
"""
    
    print(f'✅ 已生成{combo_name}策略实现代码')
    print(f'📋 代码片段:')
    print('-' * 50)
    print(code[:500] + '...')  # 只显示前500个字符
    
    return code

def visualize_combination_results(results):
    """可视化组合结果"""
    print(f'\n📊 可视化组合结果')
    print('=' * 50)
    
    if not results:
        print('⚠️ 没有有效的组合策略结果')
        return
    
    try:
        # 创建胜率对比图
        plt.figure(figsize=(12, 6))
        
        names = [r['name'] for r in results]
        win_rates = [r['actual_win_rate'] for r in results]
        trades = [r['total_trades'] for r in results]
        
        # 胜率条形图
        ax1 = plt.subplot(121)
        bars = ax1.bar(names, win_rates, color='skyblue')
        ax1.set_title('各组合策略胜率对比')
        ax1.set_ylabel('胜率 (%)')
        ax1.set_ylim(0, max(win_rates) * 1.2)
        
        # 添加数据标签
        for bar, win_rate in zip(bars, win_rates):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{win_rate:.1f}%',
                    ha='center', va='bottom')
        
        # 交易次数条形图
        ax2 = plt.subplot(122)
        bars = ax2.bar(names, trades, color='lightgreen')
        ax2.set_title('各组合策略交易次数')
        ax2.set_ylabel('交易次数')
        
        # 添加数据标签
        for bar, trade in zip(bars, trades):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{trade}',
                    ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('factor_combination_results.png')
        print('✅ 已生成组合结果可视化图表: factor_combination_results.png')
        
    except Exception as e:
        print(f'❌ 可视化失败: {e}')

def main():
    """主函数"""
    print('🚀 因子组合实施工具')
    print('=' * 60)
    
    # 加载数据
    df, buy_records, sell_records = load_trading_data()
    
    if df is None:
        return
    
    # 实施因子组合
    combo_results = implement_factor_combinations(buy_records, sell_records)
    
    # 找出最佳组合
    if combo_results:
        best_combo = max(combo_results, key=lambda x: x['actual_win_rate'])
        print(f'\n🏆 最佳组合: {best_combo["name"]} (胜率: {best_combo["actual_win_rate"]:.1f}%)')
        
        # 生成实施代码
        implementation_code = generate_implementation_code(best_combo)
        
        # 保存实施代码
        if implementation_code:
            file_name = f'{best_combo["name"].lower().replace(" ", "_")}_strategy.py'
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(implementation_code)
            print(f'✅ 已保存策略实现代码: {file_name}')
        
        # 可视化结果
        visualize_combination_results(combo_results)
    else:
        print('⚠️ 没有找到有效的组合策略')
    
    print(f'\n🎯 实施总结')
    print('=' * 40)
    
    if combo_results:
        print(f'✅ 分析了{len(combo_results)}个组合策略')
        print(f'✅ 找到最佳组合: {best_combo["name"]}')
        print(f'✅ 实际胜率: {best_combo["actual_win_rate"]:.1f}%')
        print(f'✅ 生成了可执行的策略代码')
        print(f'✅ 创建了结果可视化')
    else:
        print('⚠️ 未能找到有效的组合策略')
    
    print(f'\n📈 下一步建议:')
    print(f'1. 将生成的策略代码集成到主策略中')
    print(f'2. 进行回测验证实际效果')
    print(f'3. 根据回测结果微调参数')

if __name__ == '__main__':
    main()
