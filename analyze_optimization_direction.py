# coding=utf-8
"""
分析优化方向是否错误
深度检查胜率下降的根本原因
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

def analyze_latest_backtest_performance():
    """分析最新回测表现"""
    print("📊 分析最新回测表现")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('trading_data.db')
        
        # 检查是否有新的交易数据
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 数据库表状态:")
        for table in tables:
            print(f"   - {table[0]}")
        
        if ('trades',) in tables:
            # 分析交易记录
            trades_query = '''
                SELECT 
                    symbol,
                    buy_date,
                    sell_date,
                    buy_price,
                    sell_price,
                    return_pct,
                    hold_days,
                    sell_reason
                FROM trades 
                WHERE sell_date IS NOT NULL
                ORDER BY sell_date DESC
                LIMIT 100
            '''
            
            trades_df = pd.read_sql_query(trades_query, conn)
            
            if not trades_df.empty:
                # 计算最新胜率
                total_trades = len(trades_df)
                winning_trades = (trades_df['return_pct'] > 0).sum()
                current_win_rate = winning_trades / total_trades
                avg_return = trades_df['return_pct'].mean()
                
                print(f"\n📈 最新交易表现:")
                print(f"   交易总数: {total_trades}")
                print(f"   盈利交易: {winning_trades}")
                print(f"   当前胜率: {current_win_rate:.2%}")
                print(f"   平均收益率: {avg_return:.2%}")
                print(f"   最大收益: {trades_df['return_pct'].max():.2%}")
                print(f"   最大亏损: {trades_df['return_pct'].min():.2%}")
                print(f"   平均持仓天数: {trades_df['hold_days'].mean():.1f}天")
                
                # 胜率变化分析
                baseline_win_rate = 0.42  # 原始基线
                if current_win_rate < baseline_win_rate:
                    decline = (baseline_win_rate - current_win_rate) * 100
                    print(f"\n   🚨 胜率下降: -{decline:.1f}个百分点 (从42%基线)")
                    print(f"   ❌ 优化方向可能有问题")
                else:
                    improvement = (current_win_rate - baseline_win_rate) * 100
                    print(f"\n   ✅ 胜率改善: +{improvement:.1f}个百分点")
                
                # 时间序列分析
                trades_df['sell_date'] = pd.to_datetime(trades_df['sell_date'])
                trades_df['date'] = trades_df['sell_date'].dt.date
                
                # 按日期分析胜率趋势
                daily_stats = trades_df.groupby('date').agg({
                    'return_pct': ['count', lambda x: (x > 0).mean(), 'mean']
                }).round(4)
                
                print(f"\n   📅 最近胜率趋势:")
                for date, stats in daily_stats.tail(10).iterrows():
                    count = int(stats[('return_pct', 'count')])
                    win_rate = stats[('return_pct', '<lambda>')]
                    avg_ret = stats[('return_pct', 'mean')]
                    print(f"     {date}: {count}笔, 胜率{win_rate:.2%}, 平均{avg_ret:.2%}")
                
                return current_win_rate, avg_return, trades_df
            else:
                print("   ❌ 没有交易记录")
                return None, None, None
        else:
            print("   ❌ 没有trades表，可能仍在回测模式")
            return None, None, None
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None, None, None

def analyze_signal_quality_changes():
    """分析信号质量变化"""
    print("\n🎯 分析信号质量变化")
    print("=" * 80)
    
    try:
        with open('logs/strategy.log', 'r', encoding='utf-8', errors='ignore') as f:
            log_content = f.read()
        
        # 提取所有综合评分
        import re
        score_matches = re.findall(r'综合评分([\d.]+)', log_content)
        
        if score_matches:
            all_scores = [float(score) for score in score_matches]
            
            # 分析评分分布
            print(f"📊 信号评分分析:")
            print(f"   信号总数: {len(all_scores)}")
            print(f"   平均评分: {np.mean(all_scores):.4f}")
            print(f"   评分范围: {np.min(all_scores):.4f} - {np.max(all_scores):.4f}")
            print(f"   标准差: {np.std(all_scores):.4f}")
            
            # 评分分布统计
            high_quality = len([s for s in all_scores if s >= 0.52])
            medium_quality = len([s for s in all_scores if 0.45 <= s < 0.52])
            low_quality = len([s for s in all_scores if s < 0.45])
            
            print(f"\n   📈 评分分布:")
            print(f"     高质量(≥0.52): {high_quality} ({high_quality/len(all_scores)*100:.1f}%)")
            print(f"     中等质量(0.45-0.52): {medium_quality} ({medium_quality/len(all_scores)*100:.1f}%)")
            print(f"     低质量(<0.45): {low_quality} ({low_quality/len(all_scores)*100:.1f}%)")
            
            # 检查新配置是否生效
            if np.min(all_scores) >= 0.52:
                print(f"\n   ✅ 新筛选标准完全生效 (所有信号≥0.52)")
            elif high_quality / len(all_scores) > 0.8:
                print(f"\n   ⚠️ 新筛选标准部分生效 ({high_quality/len(all_scores)*100:.1f}%高质量)")
            else:
                print(f"\n   ❌ 新筛选标准未生效 (仍有大量低质量信号)")
            
            # 分析信号数量变化
            recent_scores = all_scores[-100:] if len(all_scores) > 100 else all_scores
            early_scores = all_scores[:100] if len(all_scores) > 100 else []
            
            if early_scores:
                print(f"\n   📊 信号数量变化:")
                print(f"     早期100个信号平均评分: {np.mean(early_scores):.4f}")
                print(f"     最近100个信号平均评分: {np.mean(recent_scores):.4f}")
                
                if np.mean(recent_scores) > np.mean(early_scores):
                    print(f"     ✅ 信号质量有提升")
                else:
                    print(f"     ❌ 信号质量未提升")
            
            return all_scores
        else:
            print("   ❌ 未找到信号评分数据")
            return []
            
    except Exception as e:
        print(f"❌ 信号质量分析失败: {e}")
        return []

def analyze_optimization_problems():
    """分析优化问题"""
    print("\n🔍 分析优化问题")
    print("=" * 80)
    
    problems = []
    insights = []
    
    # 1. 过度筛选问题
    print("1. 检查过度筛选问题:")
    try:
        from config import EFFECTIVE_FACTORS_CONFIG
        conditions = EFFECTIVE_FACTORS_CONFIG.get('buy_conditions', {})
        min_score = conditions.get('min_combined_score', 0)
        min_factors = conditions.get('min_factors_count', 0)
        
        print(f"   当前筛选标准:")
        print(f"     min_combined_score: {min_score}")
        print(f"     min_factors_count: {min_factors}")
        
        if min_score >= 0.52:
            problems.append("筛选阈值可能过高 (0.52)")
            insights.append("过高的阈值可能导致信号过少，错失机会")
        
        if min_factors >= 4:
            problems.append("因子数量要求可能过高 (4)")
            insights.append("过多的因子要求可能过度限制信号生成")
            
    except Exception as e:
        problems.append(f"配置检查失败: {e}")
    
    # 2. 权重配置问题
    print(f"\n2. 检查权重配置问题:")
    try:
        weights = EFFECTIVE_FACTORS_CONFIG.get('scoring_weights', {})
        sentiment_weight = weights.get('sentiment_score', 0)
        technical_weight = weights.get('technical_score', 0)
        
        print(f"   当前权重配置:")
        for key, value in weights.items():
            if key != 'optimization_note':
                print(f"     {key}: {value}")
        
        if sentiment_weight <= 0.20:
            problems.append("sentiment_score权重可能过低")
            insights.append("过度降低某些因子权重可能丢失有效信息")
        
        if technical_weight >= 0.35:
            problems.append("technical_score权重可能过高")
            insights.append("过度依赖单一维度可能降低策略稳健性")
            
    except Exception as e:
        problems.append(f"权重检查失败: {e}")
    
    # 3. 市场环境变化
    print(f"\n3. 检查市场环境因素:")
    insights.append("市场环境可能发生变化，历史优化参数不适用")
    insights.append("需要考虑当前市场特征和波动性")
    insights.append("可能需要动态调整策略参数")
    
    # 4. 过拟合问题
    print(f"\n4. 检查过拟合问题:")
    insights.append("过度优化可能导致策略过拟合历史数据")
    insights.append("在样本外数据上表现可能不佳")
    insights.append("需要平衡复杂度和泛化能力")
    
    return problems, insights

def suggest_alternative_directions():
    """建议替代优化方向"""
    print("\n💡 建议替代优化方向")
    print("=" * 80)
    
    alternatives = '''
🔄 可能的优化方向调整:

方向1: 降低筛选标准
   - min_combined_score: 0.52 → 0.40
   - min_factors_count: 4 → 3
   - 理由: 当前标准可能过严，错失有效信号

方向2: 重新平衡权重
   - 增加sentiment_score权重: 0.20 → 0.25
   - 降低technical_score权重: 0.35 → 0.30
   - 理由: 可能过度调整了权重配置

方向3: 简化策略
   - 回到更简单的筛选逻辑
   - 减少复杂的多维度筛选
   - 理由: 复杂度可能导致过拟合

方向4: 动态调整
   - 根据市场环境动态调整参数
   - 实施自适应筛选标准
   - 理由: 固定参数可能不适应市场变化

方向5: 回归基线
   - 暂时回到原始配置
   - 重新分析问题根源
   - 理由: 重新评估优化假设

🎯 建议的验证方法:
1. A/B测试不同参数组合
2. 分析不同市场环境下的表现
3. 检查策略在不同时间段的稳定性
4. 评估信号质量与胜率的真实关系
'''
    
    print(alternatives)

def generate_diagnosis_report():
    """生成诊断报告"""
    print("\n📋 优化方向诊断报告")
    print("=" * 80)
    
    # 执行所有分析
    win_rate, avg_return, trades_df = analyze_latest_backtest_performance()
    signal_scores = analyze_signal_quality_changes()
    problems, insights = analyze_optimization_problems()
    
    print(f"\n🏆 诊断结果总结:")
    print("=" * 50)
    
    if win_rate is not None:
        if win_rate < 0.42:
            print(f"❌ 胜率下降: {win_rate:.2%} < 42%基线")
            print(f"🚨 优化方向可能确实有问题")
        else:
            print(f"✅ 胜率维持或改善: {win_rate:.2%}")
    
    if signal_scores:
        avg_score = np.mean(signal_scores)
        if avg_score > 0.52:
            print(f"✅ 信号质量提升: 平均{avg_score:.3f}")
        else:
            print(f"⚠️ 信号质量一般: 平均{avg_score:.3f}")
    
    print(f"\n🔍 发现的问题:")
    for i, problem in enumerate(problems, 1):
        print(f"   {i}. {problem}")
    
    print(f"\n💡 关键洞察:")
    for i, insight in enumerate(insights, 1):
        print(f"   {i}. {insight}")
    
    # 给出明确建议
    if win_rate is not None and win_rate < 0.40:
        print(f"\n🎯 建议:")
        print("1. 🔄 立即调整优化方向")
        print("2. 📉 降低筛选标准")
        print("3. 🔧 重新平衡权重配置")
        print("4. 📊 考虑回归更简单的策略")
    elif win_rate is not None and win_rate < 0.42:
        print(f"\n🎯 建议:")
        print("1. ⚠️ 谨慎调整参数")
        print("2. 📊 延长观察期")
        print("3. 🔍 深入分析失败交易")
    else:
        print(f"\n🎯 建议:")
        print("1. ✅ 继续观察当前策略")
        print("2. 📈 可能需要更长时间验证")

def main():
    """主函数"""
    print("🚨 优化方向诊断分析")
    print("=" * 80)
    
    print("🎯 目标: 确定优化方向是否错误，胜率下降的根本原因")
    
    # 生成完整诊断报告
    generate_diagnosis_report()
    
    # 建议替代方向
    suggest_alternative_directions()
    
    print(f"\n🏆 诊断完成")
    print("=" * 40)
    print("📊 请根据诊断结果决定是否调整优化方向")
    print("🔧 如果胜率持续下降，建议立即调整策略")
    print("📈 如果胜率稳定，可能需要更长观察期")

if __name__ == '__main__':
    main()
