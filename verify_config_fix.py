# coding=utf-8
"""
验证配置修复
确认配置键名匹配和强制时间分散配置已正确应用
"""

from config import get_config_value

def verify_config_fix():
    """验证配置修复"""
    print('✅ 验证配置修复')
    print('=' * 60)
    
    print('🎯 修复目标:')
    print('   解决配置键名不匹配问题')
    print('   添加强制时间分散逻辑')
    print('   确保策略代码能正确读取配置')
    print('   解决98.2%开盘信号集中问题')
    
    # 验证多因子策略开关
    print(f'\n🔧 多因子策略开关验证:')
    
    enable_multifactor = get_config_value('ENABLE_MULTIFACTOR_STRATEGY', False)
    enable_effective = get_config_value('ENABLE_EFFECTIVE_FACTORS_STRATEGY', False)
    
    if enable_multifactor == True:
        print(f'   ✅ ENABLE_MULTIFACTOR_STRATEGY: {enable_multifactor} (代码能读取)')
    else:
        print(f'   ❌ ENABLE_MULTIFACTOR_STRATEGY: {enable_multifactor} (期望: True)')
    
    if enable_effective == True:
        print(f'   ✅ ENABLE_EFFECTIVE_FACTORS_STRATEGY: {enable_effective} (备用)')
    else:
        print(f'   ❌ ENABLE_EFFECTIVE_FACTORS_STRATEGY: {enable_effective} (期望: True)')
    
    # 验证多因子阈值修复
    print(f'\n📊 多因子阈值修复验证:')
    
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    expected_low_thresholds = {
        'min_overall_score': 0.01,
        'min_technical_score': 0.01,
        'min_momentum_score': 0.01,
        'min_volatility_score': 0.01,
        'min_trend_score': 0.01,
        'min_buy_signal_strength': 0.01,
        'min_risk_adjusted_score': 0.01
    }
    
    threshold_correct = True
    for key, expected in expected_low_thresholds.items():
        actual = thresholds.get(key, 'NOT_FOUND')
        if actual == expected:
            print(f'   ✅ {key}: {actual} (极低阈值，主要依靠技术指标)')
        else:
            print(f'   ❌ {key}: {actual} (期望: {expected})')
            threshold_correct = False
    
    # 验证强制时间分散配置
    print(f'\n🕐 强制时间分散配置验证:')
    
    force_time_dist = get_config_value('FORCE_TIME_DISTRIBUTION', False)
    opening_limit = get_config_value('OPENING_HOUR_LIMIT', {})
    other_hours_boost = get_config_value('OTHER_HOURS_BOOST', {})
    
    if force_time_dist == True:
        print(f'   ✅ 强制时间分散: 已启用')
    else:
        print(f'   ❌ 强制时间分散: {force_time_dist} (期望: True)')
    
    if opening_limit.get('enable', False):
        max_signals = opening_limit.get('max_signals_per_hour', 0)
        max_ratio = opening_limit.get('max_opening_ratio', 0)
        force_reject = opening_limit.get('force_reject_excess', False)
        
        print(f'   ✅ 开盘限制: 已启用')
        print(f'     最大信号/小时: {max_signals}')
        print(f'     最大开盘比例: {max_ratio*100}%')
        print(f'     强制拒绝超额: {force_reject}')
    else:
        print(f'   ❌ 开盘限制: 未启用')
    
    if other_hours_boost.get('enable', False):
        boost_mult = other_hours_boost.get('boost_multiplier', 1.0)
        priority_hours = other_hours_boost.get('priority_hours', [])
        
        print(f'   ✅ 其他时段提升: 已启用')
        print(f'     提升倍数: {boost_mult} (阈值降低{(1-boost_mult)*100}%)')
        print(f'     优先时段: {priority_hours}')
    else:
        print(f'   ❌ 其他时段提升: 未启用')
    
    # 验证高效因子配置
    print(f'\n🎯 高效因子配置验证:')
    
    effective_config = get_config_value('EFFECTIVE_FACTORS_CONFIG', {})
    
    if effective_config.get('enable', False):
        factors = effective_config.get('factors', {})
        print(f'   ✅ 高效因子配置: 已启用')
        print(f'   ✅ 配置因子数量: {len(factors)}个')
        
        # 检查关键因子
        key_factors = ['cci', 'adx', 'bb_position', 'rsi']
        for factor in key_factors:
            if factor in factors:
                weight = factors[factor].get('weight', 0)
                ic = factors[factor].get('ic', 0)
                print(f'     ✅ {factor}: 权重{weight:.3f}, IC{ic:.4f}')
            else:
                print(f'     ❌ {factor}: 未配置')
    else:
        print(f'   ❌ 高效因子配置: 未启用')
    
    # 总体验证结果
    all_correct = (
        enable_multifactor == True and
        threshold_correct and
        force_time_dist == True and
        opening_limit.get('enable', False) and
        other_hours_boost.get('enable', False)
    )
    
    print(f'\n🎯 验证总结:')
    if all_correct:
        print('✅ 所有配置修复已正确应用')
        print('🚀 策略应该能正确读取配置')
        print('💡 重启后应该解决98.2%开盘集中问题')
        return True
    else:
        print('❌ 部分配置修复未正确应用')
        print('💡 请检查config.py文件')
        return False

def show_fix_summary():
    """显示修复总结"""
    print(f'\n📋 配置修复总结')
    print('=' * 50)
    
    summary = '''
🎯 核心问题解决:
   1. ✅ 配置键名不匹配: ENABLE_MULTIFACTOR_STRATEGY已修复
   2. ✅ 多因子阈值过高: 全部降低到0.01 (极低阈值)
   3. ✅ 时间分散缺失: 添加强制时间分散配置
   4. ✅ 开盘限制缺失: 每小时最多3个信号，最多15%

🔧 修复策略:
   - 保持原有配置键名，确保代码能读取
   - 极低多因子阈值，主要依靠技术指标筛选
   - 强制时间分散，硬性限制开盘时段信号
   - 其他时段阈值降低50%，优先生成信号

📊 预期效果:
   - 开盘信号比例: 98.2% → 15%以下
   - 信号分布: 强制分散到全天各时段
   - 技术指标筛选: 依靠CCI, ADX, RSI等有效因子
   - 策略稳定性: 配置与代码完全匹配

⚠️ 关键改变:
   - 多因子评分阈值极低 (0.01)
   - 主要筛选依靠技术指标条件
   - 强制时间分散机制
   - 开盘时段严格限制
'''
    
    print(summary)

def create_monitoring_checklist():
    """创建监控检查清单"""
    print(f'\n📋 修复后监控检查清单')
    print('=' * 50)
    
    checklist = '''
🔍 重启后立即检查 (前30分钟):
   □ 策略是否正常启动
   □ 配置是否正确加载 (检查日志)
   □ 多因子策略是否启用
   □ 开盘时段信号是否明显减少

📊 2小时内关键监控:
   □ 09:00时段信号数量 (应该<15%)
   □ 10-11点和13-14点信号是否增加
   □ 技术指标筛选是否正常工作
   □ 总信号数量是否合理

📈 24小时内效果评估:
   □ 信号时间分布是否均匀
   □ 开盘集中问题是否解决
   □ 胜率是否保持或提升
   □ 交易质量是否改善

🎯 一周内综合评估:
   □ 策略稳定性是否提升
   □ 各时段表现是否均衡
   □ 技术指标策略效果如何
   □ 是否需要进一步微调

🚨 异常情况处理:
   □ 如果仍然集中开盘: 检查代码实现
   □ 如果信号过少: 适当降低技术指标阈值
   □ 如果出现错误: 检查配置语法
   □ 如果效果不佳: 考虑回退配置
'''
    
    print(checklist)

def main():
    """主函数"""
    print('🚀 配置修复验证')
    print('=' * 60)
    
    # 验证配置修复
    success = verify_config_fix()
    
    # 显示修复总结
    show_fix_summary()
    
    # 创建监控检查清单
    create_monitoring_checklist()
    
    if success:
        print(f'\n🏆 配置修复验证成功!')
        print('🚀 策略已完全修复，准备重启程序!')
        print('')
        print('🎯 下一步: python main.py')
        print('📈 目标: 解决98.2%开盘信号集中问题')
        print('💎 从配置不匹配 → 完全匹配的关键修复!')
    else:
        print(f'\n⚠️ 配置修复验证失败!')
        print('💡 请检查并修正配置文件')

if __name__ == '__main__':
    main()
