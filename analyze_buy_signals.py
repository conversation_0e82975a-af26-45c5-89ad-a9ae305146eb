# coding=utf-8
"""
分析买入信号质量
基于1857条买入记录优化多因子策略
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def analyze_buy_signal_quality():
    """分析买入信号质量"""
    print('📊 买入信号质量分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 加载所有买入记录
        query = """
        SELECT 
            timestamp, symbol, 
            overall_score, technical_score, momentum_score, volume_score,
            volatility_score, trend_score, buy_signal_strength, risk_adjusted_score,
            atr_pct, bb_width, macd_hist, rsi, trix_buy,
            price, volume
        FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 买入记录总数: {len(df)} 条')
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 分析时间分布
        print(f'\n⏰ 时间分布:')
        print(f'   最早记录: {df["timestamp"].min()}')
        print(f'   最新记录: {df["timestamp"].max()}')
        print(f'   时间跨度: {(df["timestamp"].max() - df["timestamp"].min()).days} 天')
        
        # 分析最近的买入活动
        recent_24h = df[df['timestamp'] >= (datetime.now() - timedelta(hours=24))]
        recent_7d = df[df['timestamp'] >= (datetime.now() - timedelta(days=7))]
        
        print(f'   最近24小时: {len(recent_24h)} 条')
        print(f'   最近7天: {len(recent_7d)} 条')
        
        return df
        
    except Exception as e:
        print(f'❌ 分析失败: {e}')
        return None

def analyze_score_distributions(df):
    """分析评分分布"""
    print(f'\n📊 多因子评分分布分析')
    print('=' * 50)
    
    score_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                    'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    print(f'📋 评分统计 (基于{len(df)}条记录):')
    
    score_stats = {}
    for col in score_columns:
        if col in df.columns:
            values = df[col].dropna()
            if len(values) > 0:
                stats = {
                    'count': len(values),
                    'mean': values.mean(),
                    'median': values.median(),
                    'std': values.std(),
                    'min': values.min(),
                    'max': values.max(),
                    'q25': values.quantile(0.25),
                    'q75': values.quantile(0.75)
                }
                score_stats[col] = stats
                
                print(f'\n   {col}:')
                print(f'     样本数: {stats["count"]}')
                print(f'     平均值: {stats["mean"]:.3f}')
                print(f'     中位数: {stats["median"]:.3f}')
                print(f'     标准差: {stats["std"]:.3f}')
                print(f'     范围: {stats["min"]:.3f} - {stats["max"]:.3f}')
                print(f'     四分位: {stats["q25"]:.3f} - {stats["q75"]:.3f}')
    
    return score_stats

def analyze_current_thresholds_effectiveness(df):
    """分析当前阈值的有效性"""
    print(f'\n🎯 当前阈值有效性分析')
    print('=' * 50)
    
    from config import get_config_value
    
    # 获取当前配置
    thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    confirmations = get_config_value('MULTIFACTOR_CONFIRMATIONS', {})
    
    print(f'📋 当前阈值配置:')
    for key, value in thresholds.items():
        print(f'   {key}: {value}')
    
    # 分析有多少记录满足当前阈值
    score_mapping = {
        'min_overall_score': 'overall_score',
        'min_technical_score': 'technical_score',
        'min_momentum_score': 'momentum_score',
        'min_volume_score': 'volume_score',
        'min_volatility_score': 'volatility_score',
        'min_trend_score': 'trend_score',
        'min_buy_signal_strength': 'buy_signal_strength',
        'min_risk_adjusted_score': 'risk_adjusted_score'
    }
    
    print(f'\n📊 阈值满足情况:')
    
    total_records = len(df)
    threshold_satisfaction = {}
    
    for threshold_key, score_col in score_mapping.items():
        if threshold_key in thresholds and score_col in df.columns:
            threshold_value = thresholds[threshold_key]
            values = df[score_col].dropna()
            
            if len(values) > 0:
                satisfied_count = len(values[values >= threshold_value])
                satisfaction_rate = satisfied_count / len(values) * 100
                
                threshold_satisfaction[score_col] = {
                    'threshold': threshold_value,
                    'satisfied_count': satisfied_count,
                    'total_count': len(values),
                    'satisfaction_rate': satisfaction_rate
                }
                
                print(f'   {score_col}: {satisfied_count}/{len(values)} ({satisfaction_rate:.1f}%) >= {threshold_value}')
    
    # 分析多重条件满足情况
    min_count = confirmations.get('min_score_count', 2)
    print(f'\n🎯 多重条件分析 (要求至少{min_count}个条件满足):')
    
    # 计算每条记录满足多少个条件
    satisfied_counts = []
    for idx, row in df.iterrows():
        satisfied = 0
        for threshold_key, score_col in score_mapping.items():
            if threshold_key in thresholds and score_col in df.columns:
                threshold_value = thresholds[threshold_key]
                score_value = row[score_col]
                
                if pd.notna(score_value) and score_value >= threshold_value:
                    satisfied += 1
        
        satisfied_counts.append(satisfied)
    
    df['satisfied_conditions'] = satisfied_counts
    
    # 统计满足不同条件数的记录
    condition_dist = pd.Series(satisfied_counts).value_counts().sort_index()
    print(f'   满足条件数分布:')
    for count, records in condition_dist.items():
        percentage = records / total_records * 100
        status = "✅" if count >= min_count else "❌"
        print(f'     {status} {count}个条件: {records}条 ({percentage:.1f}%)')
    
    # 计算符合多因子策略的记录数
    qualified_records = len(df[df['satisfied_conditions'] >= min_count])
    qualification_rate = qualified_records / total_records * 100
    
    print(f'\n📈 多因子策略符合率:')
    print(f'   符合条件记录: {qualified_records}/{total_records} ({qualification_rate:.1f}%)')
    
    return threshold_satisfaction, qualified_records

def analyze_technical_indicators(df):
    """分析技术指标分布"""
    print(f'\n📋 技术指标分布分析')
    print('=' * 50)
    
    tech_columns = ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    
    for col in tech_columns:
        if col in df.columns:
            values = df[col].dropna()
            if len(values) > 0:
                print(f'\n   {col}:')
                print(f'     样本数: {len(values)}')
                print(f'     平均值: {values.mean():.3f}')
                print(f'     中位数: {values.median():.3f}')
                print(f'     标准差: {values.std():.3f}')
                print(f'     范围: {values.min():.3f} - {values.max():.3f}')
                
                # 分析分布
                q10 = values.quantile(0.1)
                q25 = values.quantile(0.25)
                q75 = values.quantile(0.75)
                q90 = values.quantile(0.9)
                
                print(f'     分位数: 10%={q10:.3f}, 25%={q25:.3f}, 75%={q75:.3f}, 90%={q90:.3f}')

def suggest_threshold_optimization(threshold_satisfaction, qualified_records, total_records):
    """建议阈值优化"""
    print(f'\n💡 阈值优化建议')
    print('=' * 50)
    
    qualification_rate = qualified_records / total_records * 100
    
    print(f'📊 当前状况:')
    print(f'   总买入记录: {total_records}')
    print(f'   符合多因子条件: {qualified_records} ({qualification_rate:.1f}%)')
    
    if qualification_rate < 10:
        print(f'\n🚨 阈值过于严格 (符合率 < 10%):')
        print(f'   建议: 大幅降低阈值，增加买入机会')
        
        suggested_adjustments = {
            'min_overall_score': 0.15,
            'min_technical_score': 0.10,
            'min_momentum_score': 0.05,
            'min_volume_score': 0.00,
            'min_volatility_score': 0.00,
            'min_trend_score': 0.30,
            'min_buy_signal_strength': 0.00,
            'min_risk_adjusted_score': 0.05
        }
        
    elif qualification_rate < 25:
        print(f'\n⚠️ 阈值较为严格 (符合率 < 25%):')
        print(f'   建议: 适度降低阈值')
        
        suggested_adjustments = {
            'min_overall_score': 0.18,
            'min_technical_score': 0.12,
            'min_momentum_score': 0.08,
            'min_volume_score': 0.00,
            'min_volatility_score': 0.00,
            'min_trend_score': 0.40,
            'min_buy_signal_strength': 0.00,
            'min_risk_adjusted_score': 0.08
        }
        
    elif qualification_rate > 60:
        print(f'\n📈 阈值较为宽松 (符合率 > 60%):')
        print(f'   建议: 适度提高阈值，提升信号质量')
        
        suggested_adjustments = {
            'min_overall_score': 0.25,
            'min_technical_score': 0.20,
            'min_momentum_score': 0.15,
            'min_volume_score': 0.10,
            'min_volatility_score': 0.10,
            'min_trend_score': 0.60,
            'min_buy_signal_strength': 0.10,
            'min_risk_adjusted_score': 0.15
        }
        
    else:
        print(f'\n✅ 阈值设置合理 (符合率 25-60%):')
        print(f'   建议: 保持当前设置，微调即可')
        return
    
    print(f'\n⚙️ 建议的新阈值配置:')
    print(f'```python')
    print(f'MULTIFACTOR_THRESHOLDS = {{')
    for key, value in suggested_adjustments.items():
        print(f"    '{key}': {value:.3f},")
    print(f'}}')
    print(f'```')
    
    # 估算新配置的符合率
    print(f'\n📈 预期效果:')
    print(f'   预期符合率: 30-50%')
    print(f'   预期买入信号增加: {(0.4 * total_records - qualified_records):.0f} 条')

def analyze_recent_trends(df):
    """分析最近趋势"""
    print(f'\n📈 最近买入趋势分析')
    print('=' * 50)
    
    # 按天分组分析
    df['date'] = df['timestamp'].dt.date
    daily_counts = df.groupby('date').size().sort_index()
    
    print(f'📊 每日买入数量 (最近10天):')
    for date, count in daily_counts.tail(10).items():
        print(f'   {date}: {count} 条')
    
    # 分析评分趋势
    recent_7d = df[df['timestamp'] >= (datetime.now() - timedelta(days=7))]
    
    if len(recent_7d) > 0:
        print(f'\n📊 最近7天评分趋势:')
        score_columns = ['overall_score', 'technical_score', 'momentum_score']
        
        for col in score_columns:
            if col in recent_7d.columns:
                values = recent_7d[col].dropna()
                if len(values) > 0:
                    avg_score = values.mean()
                    print(f'   {col}: 平均 {avg_score:.3f}')

def main():
    """主函数"""
    print('🚀 基于1857条买入记录的策略优化')
    print('=' * 60)
    
    # 分析买入信号质量
    df = analyze_buy_signal_quality()
    
    if df is not None and len(df) > 0:
        # 分析评分分布
        score_stats = analyze_score_distributions(df)
        
        # 分析当前阈值有效性
        threshold_satisfaction, qualified_records = analyze_current_thresholds_effectiveness(df)
        
        # 分析技术指标
        analyze_technical_indicators(df)
        
        # 建议阈值优化
        suggest_threshold_optimization(threshold_satisfaction, qualified_records, len(df))
        
        # 分析最近趋势
        analyze_recent_trends(df)
        
        print(f'\n🎯 优化总结')
        print('=' * 40)
        print('✅ 基于1857条实际买入记录的分析完成')
        print('📊 已识别当前阈值的有效性')
        print('🎯 已生成针对性的优化建议')
        print('')
        print('🚀 建议应用优化配置，提高多因子策略效果')
    else:
        print('❌ 数据加载失败')

if __name__ == '__main__':
    main()
