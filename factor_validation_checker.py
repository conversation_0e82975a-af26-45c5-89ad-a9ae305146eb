# coding=utf-8
"""
因子验证检查器
检查因子是否正确计算和保存，验证无未来函数引用
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def check_factor_data_quality():
    """检查因子数据质量"""
    print('🔍 因子数据质量检查')
    print('=' * 60)
    print(f'检查时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 1. 检查trades表的因子数据
        print('\n📊 trades表因子数据检查:')
        
        # 获取买入记录
        buy_df = pd.read_sql_query("SELECT * FROM trades WHERE action = 'BUY' LIMIT 5", conn)
        
        if len(buy_df) == 0:
            print('❌ 没有买入记录可检查')
            return
        
        print(f'  检查样本: {len(buy_df)}条买入记录')
        
        # 获取所有字段
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(trades)")
        columns = cursor.fetchall()
        all_fields = [col[1] for col in columns]
        
        # 分类检查因子数据
        factor_categories = {
            '增强因子': [],
            '原有因子': [],
            '空值因子': []
        }
        
        # 检查每个字段的数据质量
        for field in all_fields:
            if field in ['id', 'timestamp', 'symbol', 'action', 'price', 'volume']:
                continue
                
            # 检查字段数据
            sample_values = buy_df[field].dropna()
            
            if len(sample_values) == 0:
                factor_categories['空值因子'].append(field)
            elif any(keyword in field.lower() for keyword in ['ma', 'rsi', 'macd', 'atr', 'bb_', 'cci', 'willr', 'stoch']):
                factor_categories['增强因子'].append(field)
            else:
                factor_categories['原有因子'].append(field)
        
        # 显示分类结果
        for category, fields in factor_categories.items():
            print(f'\n  📈 {category} ({len(fields)}个):')
            if len(fields) <= 10:
                for field in fields:
                    sample_value = buy_df[field].iloc[0] if not buy_df[field].isna().all() else 'NULL'
                    print(f'    {field}: {sample_value}')
            else:
                for field in fields[:5]:
                    sample_value = buy_df[field].iloc[0] if not buy_df[field].isna().all() else 'NULL'
                    print(f'    {field}: {sample_value}')
                print(f'    ... 还有{len(fields)-5}个字段')
        
        # 2. 检查analysis表的因子数据
        print('\n📊 analysis表因子数据检查:')
        
        analysis_df = pd.read_sql_query("SELECT * FROM analysis LIMIT 5", conn)
        print(f'  analysis表记录数: {len(analysis_df)}条')
        
        if len(analysis_df) > 0:
            print('  analysis表字段数据:')
            for col in analysis_df.columns:
                if col not in ['id', 'timestamp', 'symbol']:
                    sample_value = analysis_df[col].iloc[0] if not analysis_df[col].isna().all() else 'NULL'
                    print(f'    {col}: {sample_value}')
        else:
            print('  ⚠️ analysis表为空，增强因子可能没有保存到analysis表')
        
        conn.close()
        return factor_categories
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        return None

def check_enhanced_factor_integration():
    """检查增强因子集成情况"""
    print('\n🔍 增强因子集成检查')
    print('=' * 50)
    
    # 1. 检查enhanced_factor_engine.py是否被正确导入
    print('📊 增强因子引擎集成检查:')
    
    try:
        # 检查signal_generator.py中的导入
        with open('signal_generator.py', 'r', encoding='utf-8') as f:
            signal_content = f.read()
        
        if 'EnhancedFactorEngine' in signal_content:
            print('  ✅ signal_generator.py已导入EnhancedFactorEngine')
            
            if 'self.factor_engine = EnhancedFactorEngine' in signal_content:
                print('  ✅ 已初始化factor_engine实例')
            else:
                print('  ❌ 未初始化factor_engine实例')
            
            if 'enhanced_factors = self.factor_engine.calculate_all_factors' in signal_content:
                print('  ✅ 已调用calculate_all_factors方法')
            else:
                print('  ❌ 未调用calculate_all_factors方法')
                
            if 'analysis_data.update(enhanced_factors)' in signal_content:
                print('  ✅ 已将增强因子合并到analysis_data')
            else:
                print('  ❌ 未将增强因子合并到analysis_data')
        else:
            print('  ❌ signal_generator.py未导入EnhancedFactorEngine')
        
        # 2. 检查main.py中的因子保存
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        print('\n📊 买入记录因子保存检查:')
        
        if 'enhanced_factors' in main_content:
            print('  ✅ main.py中有enhanced_factors处理')
        else:
            print('  ❌ main.py中没有enhanced_factors处理')
        
        if 'buy_record.update(enhanced_factors)' in main_content:
            print('  ✅ 已将增强因子合并到买入记录')
        else:
            print('  ❌ 未将增强因子合并到买入记录')
            
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def check_future_function_references():
    """检查未来函数引用"""
    print('\n🔍 未来函数引用检查')
    print('=' * 50)
    
    try:
        # 检查enhanced_factor_engine.py中的计算逻辑
        with open('enhanced_factor_engine.py', 'r', encoding='utf-8') as f:
            engine_content = f.read()
        
        print('📊 时间序列数据使用检查:')
        
        # 检查可能的未来函数引用模式
        future_patterns = [
            (r'close\[.*\+.*\]', '使用未来价格数据'),
            (r'high\[.*\+.*\]', '使用未来最高价数据'),
            (r'low\[.*\+.*\]', '使用未来最低价数据'),
            (r'volume\[.*\+.*\]', '使用未来成交量数据'),
            (r'\.shift\(-', '向前移位（未来数据）'),
            (r'\.iloc\[.*\+.*\]', '访问未来索引位置')
        ]
        
        future_issues = []
        for pattern, description in future_patterns:
            import re
            matches = re.findall(pattern, engine_content)
            if matches:
                future_issues.append((description, len(matches)))
        
        if future_issues:
            print('  ⚠️ 发现潜在的未来函数引用:')
            for issue, count in future_issues:
                print(f'    {issue}: {count}处')
        else:
            print('  ✅ 未发现明显的未来函数引用')
        
        # 检查数据访问模式
        print('\n📊 数据访问模式检查:')
        
        safe_patterns = [
            (r'close\[-1\]', '使用当前价格'),
            (r'close\[-\d+:\]', '使用历史到当前的数据'),
            (r'talib\.\w+\(.*\)\[-1\]', '使用talib指标的当前值'),
            (r'np\.max\(.*\[-\d+:\]\)', '使用历史数据的最大值'),
            (r'np\.min\(.*\[-\d+:\]\)', '使用历史数据的最小值')
        ]
        
        safe_count = 0
        for pattern, description in safe_patterns:
            import re
            matches = re.findall(pattern, engine_content)
            if matches:
                safe_count += len(matches)
                print(f'  ✅ {description}: {len(matches)}处')
        
        print(f'\n  📊 安全数据访问总计: {safe_count}处')
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def test_factor_calculation_timing():
    """测试因子计算时机"""
    print('\n🔍 因子计算时机测试')
    print('=' * 50)
    
    try:
        # 模拟历史数据
        dates = pd.date_range('2024-01-01', periods=50, freq='D')
        np.random.seed(42)
        
        # 创建模拟股价数据
        base_price = 10.0
        returns = np.random.normal(0.001, 0.02, 50)
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        test_data = pd.DataFrame({
            'open': np.array(prices) * (1 + np.random.normal(0, 0.005, 50)),
            'high': np.array(prices) * (1 + np.abs(np.random.normal(0, 0.01, 50))),
            'low': np.array(prices) * (1 - np.abs(np.random.normal(0, 0.01, 50))),
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, 50)
        }, index=dates)
        
        print('📊 模拟数据测试:')
        print(f'  数据长度: {len(test_data)}天')
        print(f'  价格范围: ¥{test_data["close"].min():.2f} - ¥{test_data["close"].max():.2f}')
        
        # 测试因子计算
        try:
            from enhanced_factor_engine import EnhancedFactorEngine
            
            engine = EnhancedFactorEngine()
            
            # 测试不同时间点的因子计算
            test_points = [20, 30, 40, 49]  # 不同的历史时间点
            
            print('\n📊 不同时间点因子计算测试:')
            
            for point in test_points:
                # 只使用到该时间点的数据
                historical_data = test_data.iloc[:point+1]
                
                factors = engine.calculate_all_factors(historical_data, 'TEST.000001')
                
                print(f'  时间点 {point} (数据长度{len(historical_data)}): 计算了{len(factors)}个因子')
                
                # 检查关键因子
                key_factors = ['current_price', 'ma20', 'rsi_14', 'macd', 'atr_14']
                for factor in key_factors:
                    if factor in factors:
                        print(f'    {factor}: {factors[factor]:.4f}')
                
                # 验证当前价格是否正确
                if 'current_price' in factors:
                    expected_price = historical_data['close'].iloc[-1]
                    actual_price = factors['current_price']
                    if abs(expected_price - actual_price) < 0.0001:
                        print(f'    ✅ 当前价格正确: {actual_price:.4f}')
                    else:
                        print(f'    ❌ 当前价格错误: 期望{expected_price:.4f}, 实际{actual_price:.4f}')
        
        except ImportError:
            print('  ❌ 无法导入EnhancedFactorEngine，请检查文件路径')
        except Exception as e:
            print(f'  ❌ 因子计算测试失败: {e}')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')

def diagnose_factor_issues():
    """诊断因子问题"""
    print('\n💡 因子问题诊断')
    print('=' * 50)
    
    issues = []
    
    # 基于前面的检查结果进行诊断
    print('📊 可能的问题和解决方案:')
    
    potential_issues = [
        {
            'issue': 'analysis表为空',
            'description': '增强因子没有保存到analysis表',
            'possible_causes': [
                'EnhancedFactorEngine导入失败',
                'factor_engine未正确初始化',
                'calculate_all_factors方法未被调用',
                'analysis_data.update()未执行'
            ],
            'solutions': [
                '检查enhanced_factor_engine.py文件是否存在',
                '验证signal_generator.py中的导入语句',
                '确认factor_engine初始化代码',
                '检查analyze_signals函数中的因子计算调用'
            ]
        },
        {
            'issue': 'trades表因子数据大部分为空',
            'description': '买入记录中的增强因子数据为NULL',
            'possible_causes': [
                '因子计算返回空字典',
                'signal_data中没有因子数据',
                '字段名转换问题',
                '数据类型转换失败'
            ],
            'solutions': [
                '检查enhanced_factors是否正确计算',
                '验证signal_data传递过程',
                '检查字段名转换逻辑',
                '添加详细的调试日志'
            ]
        },
        {
            'issue': '因子计算可能有未来函数',
            'description': '使用了未来时间点的数据',
            'possible_causes': [
                '数组索引使用正数',
                '使用了shift(-n)向前移位',
                'talib函数使用不当'
            ],
            'solutions': [
                '确保只使用[-1]或[-n:]访问数据',
                '避免使用正数索引',
                '验证talib函数的输出时机'
            ]
        }
    ]
    
    for i, issue in enumerate(potential_issues, 1):
        print(f'\n{i}. {issue["issue"]}')
        print(f'   描述: {issue["description"]}')
        print(f'   可能原因:')
        for cause in issue['possible_causes']:
            print(f'     • {cause}')
        print(f'   解决方案:')
        for solution in issue['solutions']:
            print(f'     • {solution}')

def main():
    """主函数"""
    print('🔍 因子计算和保存验证报告')
    print('=' * 60)
    
    # 检查因子数据质量
    factor_categories = check_factor_data_quality()
    
    # 检查增强因子集成
    check_enhanced_factor_integration()
    
    # 检查未来函数引用
    check_future_function_references()
    
    # 测试因子计算时机
    test_factor_calculation_timing()
    
    # 诊断因子问题
    diagnose_factor_issues()
    
    print(f'\n🎯 验证结论')
    print('=' * 40)
    
    if factor_categories:
        empty_factors = len(factor_categories.get('空值因子', []))
        enhanced_factors = len(factor_categories.get('增强因子', []))
        
        if empty_factors > enhanced_factors:
            print('❌ 大部分因子数据为空，增强因子系统可能未正常工作')
            print('🔧 建议检查EnhancedFactorEngine的集成和调用')
        elif enhanced_factors > 0:
            print('⚠️ 部分增强因子有数据，但可能不完整')
            print('🔧 建议检查因子计算和保存的完整性')
        else:
            print('✅ 因子数据基本正常')
    
    print(f'\n📋 下一步建议:')
    print('1. 🔍 检查策略运行日志中的因子计算信息')
    print('2. 🔧 修复发现的集成问题')
    print('3. 🧪 重新运行策略测试因子保存')
    print('4. ✅ 验证因子数据的时间正确性')

if __name__ == '__main__':
    main()
