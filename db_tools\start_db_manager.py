#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
万和策略分析系统 - 数据库管理工具启动脚本
"""

import os
import sys
import subprocess
import platform

# 添加父目录到系统路径，以便能够导入scripts目录中的模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def find_python_executable():
    """查找Python可执行文件路径"""
    # 首先尝试使用当前Python解释器
    current_python = sys.executable
    if current_python and os.path.exists(current_python):
        return current_python
    
    # 尝试查找特定版本的Python
    python_paths = [
        # Python 3.9 路径
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe",
        # 其他可能的Python路径
        r"C:\Python39\python.exe",
        r"C:\Program Files\Python39\python.exe",
        r"C:\Program Files (x86)\Python39\python.exe",
        # 系统Python路径
        "python3.9",
        "python3",
        "python"
    ]
    
    for path in python_paths:
        try:
            # 检查Python可执行文件是否存在并可用
            if os.path.exists(path):
                return path
            elif platform.system() != "Windows":
                # 在非Windows系统上尝试使用which命令
                result = subprocess.run(["which", path], capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    return result.stdout.strip()
        except Exception:
            continue
    
    return None

def check_dependencies(python_path):
    """检查必要的依赖库是否已安装"""
    dependencies = ["seaborn", "pandas", "numpy", "matplotlib", "colorama"]
    missing_deps = []
    
    for dep in dependencies:
        try:
            cmd = [python_path, "-c", f"import {dep}"]
            result = subprocess.run(cmd, capture_output=True)
            if result.returncode != 0:
                missing_deps.append(dep)
        except Exception:
            missing_deps.append(dep)
    
    return missing_deps

def install_dependencies(python_path, dependencies):
    """安装缺失的依赖库"""
    print(f"正在安装缺失的依赖库: {', '.join(dependencies)}")
    try:
        cmd = [python_path, "-m", "pip", "install"] + dependencies
        subprocess.run(cmd, check=True)
        print("依赖库安装完成！")
        return True
    except Exception as e:
        print(f"安装依赖库时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("万和策略分析系统 - 数据库管理工具启动器")
    print("=" * 60)
    
    # 获取项目根目录
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    # 查找Python可执行文件
    python_path = find_python_executable()
    if not python_path:
        print("错误: 无法找到Python可执行文件。请确保已安装Python 3.6+。")
        input("按回车键退出...")
        return
    
    print(f"找到Python解释器: {python_path}")
    
    # 检查脚本文件是否存在
    script_path = os.path.join(root_dir, "scripts", "db_advanced_tools.py")
    if not os.path.exists(script_path):
        print(f"错误: 找不到数据库管理工具脚本 '{script_path}'")
        input("按回车键退出...")
        return
    
    # 检查依赖
    missing_deps = check_dependencies(python_path)
    if missing_deps:
        print(f"发现缺失的依赖库: {', '.join(missing_deps)}")
        install = input("是否自动安装这些依赖库? (y/n): ").lower()
        if install == 'y':
            if not install_dependencies(python_path, missing_deps):
                print("无法安装依赖库，请手动安装后再试。")
                input("按回车键退出...")
                return
        else:
            print("请手动安装缺失的依赖库后再试。")
            input("按回车键退出...")
            return
    
    # 启动数据库管理工具
    print("\n正在启动数据库管理工具...")
    try:
        # 切换到项目根目录，确保相对路径正确
        os.chdir(root_dir)
        cmd = [python_path, script_path, "--interactive"]
        subprocess.run(cmd)
    except Exception as e:
        print(f"启动数据库管理工具时出错: {str(e)}")
    
    print("\n数据库管理工具已关闭。")
    input("按回车键退出...")

if __name__ == "__main__":
    main() 