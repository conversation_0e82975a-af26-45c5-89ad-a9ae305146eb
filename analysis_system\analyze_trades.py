# coding=utf-8
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

# 文件路径
TRADE_LOG_FILE = 'data/trade_log.csv'
ANALYSIS_LOG_FILE = 'data/analysis_log.csv'
OUTPUT_FILE = 'reports/trade_analysis_results.csv'

def analyze_trades(progress_callback=None):
    """
    分析交易数据
    
    参数:
    progress_callback (function): 进度回调函数，接受三个参数：当前步骤、总步骤数和状态消息
    """
    print("开始分析交易数据...")
    
    # 定义总步骤数
    total_steps = 10
    current_step = 0
    
    # 更新进度
    if progress_callback:
        progress_callback(current_step, total_steps, "正在开始分析...")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "已创建输出目录")
    
    # 读取交易日志
    try:
        trade_df = pd.read_csv(TRADE_LOG_FILE)
        print(f"成功读取交易日志，共{len(trade_df)}条记录")
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已读取交易日志，共{len(trade_df)}条记录")
    except Exception as e:
        print(f"读取交易日志失败: {e}")
        if progress_callback:
            progress_callback(total_steps, total_steps, f"错误: 读取交易日志失败 - {e}")
        return
    
    # 读取分析日志（可选，如果需要更多指标数据）
    try:
        analysis_df = pd.read_csv(ANALYSIS_LOG_FILE)
        print(f"成功读取分析日志，共{len(analysis_df)}条记录")
        has_analysis_data = True
    except Exception as e:
        print(f"读取分析日志失败: {e}，将仅使用交易日志数据")
        has_analysis_data = False
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在处理交易数据...")
    
    # 分离买入和卖出记录
    buy_records = trade_df[trade_df['Action'] == 'BUY']
    sell_records = trade_df[trade_df['Action'] == 'SELL']
    
    print(f"买入记录: {len(buy_records)}条")
    print(f"卖出记录: {len(sell_records)}条")
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, f"已分离买入({len(buy_records)}条)和卖出({len(sell_records)}条)记录")
    
    # 创建完整交易记录
    complete_trades = []
    
    # 更新进度
    if progress_callback:
        progress_callback(current_step, total_steps, "正在匹配买卖记录...")
    
    # 遍历卖出记录，查找对应的买入记录
    for _, sell in sell_records.iterrows():
        symbol = sell['Symbol']
        sell_time = pd.to_datetime(sell['Timestamp'])
        
        # 查找该股票在卖出前的最近一次买入记录
        matching_buys = buy_records[buy_records['Symbol'] == symbol]
        if len(matching_buys) == 0:
            continue
            
        matching_buys['Timestamp'] = pd.to_datetime(matching_buys['Timestamp'])
        matching_buys = matching_buys[matching_buys['Timestamp'] < sell_time]
        
        if len(matching_buys) == 0:
            continue
            
        # 获取最近的买入记录
        buy = matching_buys.sort_values('Timestamp', ascending=False).iloc[0]
        
        # 合并买入和卖出数据
        buy_amount = buy['Price'] * buy['Volume']
        sell_amount = sell['Price'] * buy['Volume']
        profit_amount = sell_amount - buy_amount
        actual_profit_pct = (profit_amount / buy_amount * 100) if buy_amount > 0 else 0
        
        trade_record = {
            # 交易基本信息
            'Symbol': symbol,
            'Buy_Time': buy['Timestamp'],
            'Sell_Time': sell_time,
            'Holding_Hours': sell['Holding_Hours'],
            'Buy_Price': buy['Price'],
            'Sell_Price': sell['Price'],
            'Volume': buy['Volume'],
            'Buy_Amount': buy_amount,
            'Sell_Amount': sell_amount,
            'Profit_Amount': profit_amount,
            'Profit_Pct': sell['Net_Profit_Pct_Sell'],  # 保留原始收益率
            'Actual_Profit_Pct': actual_profit_pct,     # 添加实际计算的收益率
            'Max_Profit_Pct': sell['Max_Profit_Pct'],
            'Final_Drawdown_Pct': sell['Final_Drawdown_Pct'],
            'Sell_Reason': sell['Sell_Reason'],
            
            # 买入点指标
            'TRIX_Buy': buy.get('TRIX_Buy', None),
            'Volatility_Buy': buy.get('Volatility', None),
            'ATR_Pct_Buy': buy.get('ATR_Pct', None),
            'Volatility_Score_Buy': buy.get('Volatility_Score', None),
            'Allocation_Factor_Buy': buy.get('Allocation_Factor', None),
            
            # 计算其他指标
            'Trade_Result': 'Win' if actual_profit_pct > 0 else 'Loss',
        }
        
        complete_trades.append(trade_record)
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "完成买卖记录匹配")
    
    # 创建完整交易DataFrame
    if complete_trades:
        complete_df = pd.DataFrame(complete_trades)
        print(f"成功匹配{len(complete_df)}笔完整交易")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"成功匹配{len(complete_df)}笔完整交易，正在保存结果...")
        
        # 保存完整交易记录
        complete_df.to_csv(OUTPUT_FILE, index=False)
        print(f"交易分析结果已保存到 {OUTPUT_FILE}")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, "已保存交易分析结果，开始分析交易表现...")
        
        # 进行交易分析
        analyze_trade_performance(complete_df, progress_callback, current_step, total_steps)
    else:
        print("没有找到匹配的完整交易记录")
        if progress_callback:
            progress_callback(total_steps, total_steps, "没有找到匹配的完整交易记录")

def analyze_trade_performance(df, progress_callback=None, current_step=0, total_steps=10):
    """
    分析交易表现
    
    参数:
    df (DataFrame): 交易数据
    progress_callback (function): 进度回调函数
    current_step (int): 当前进度步骤
    total_steps (int): 总步骤数
    """
    print("\n========== 交易表现分析 ==========")
    
    # 基本统计
    total_trades = len(df)
    winning_trades = len(df[df['Actual_Profit_Pct'] > 0])
    losing_trades = len(df[df['Actual_Profit_Pct'] <= 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    
    avg_profit = df['Actual_Profit_Pct'].mean()
    avg_win = df[df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].mean() if winning_trades > 0 else 0
    avg_loss = df[df['Actual_Profit_Pct'] <= 0]['Actual_Profit_Pct'].mean() if losing_trades > 0 else 0
    
    profit_factor = abs(df[df['Actual_Profit_Pct'] > 0]['Actual_Profit_Pct'].sum() / df[df['Actual_Profit_Pct'] < 0]['Actual_Profit_Pct'].sum()) if df[df['Actual_Profit_Pct'] < 0]['Actual_Profit_Pct'].sum() != 0 else float('inf')
    
    print(f"总交易次数: {total_trades}")
    print(f"盈利交易: {winning_trades} ({win_rate:.2f}%)")
    print(f"亏损交易: {losing_trades} ({100-win_rate:.2f}%)")
    print(f"平均收益率: {avg_profit:.4f}%")
    print(f"平均盈利: {avg_win:.4f}%")
    print(f"平均亏损: {avg_loss:.4f}%")
    
    # 计算并格式化盈亏比
    profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 else float('inf')
    print(f"盈亏比: {profit_loss_ratio:.2f}")
    
    print(f"利润因子: {profit_factor:.2f}")
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在分析交易统计数据...")
    
    # 按卖出原因分析
    print("\n按卖出原因分析:")
    reason_stats = df.groupby('Sell_Reason').agg({
        'Actual_Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    reason_stats.columns = ['Sell_Reason', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(reason_stats.sort_values('Count', ascending=False))
    
    # 按持仓时间分析
    df['Holding_Days'] = df['Holding_Hours'] / 24
    print("\n按持仓时间分析:")
    df['Holding_Group'] = pd.cut(df['Holding_Days'], 
                                bins=[0, 1, 2, 3, 5, 10, float('inf')],
                                labels=['0-1天', '1-2天', '2-3天', '3-5天', '5-10天', '10天以上'])
    holding_stats = df.groupby('Holding_Group').agg({
        'Actual_Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    holding_stats.columns = ['Holding_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(holding_stats)
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在分析交易分组数据...")
    
    # 波动性分析
    if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
        print("\n按波动性分析:")
        df['Volatility_Group'] = pd.qcut(df['Volatility_Buy'], 4, labels=['低波动', '中低波动', '中高波动', '高波动'])
        vol_stats = df.groupby('Volatility_Group').agg({
            'Actual_Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        vol_stats.columns = ['Volatility_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(vol_stats)
    
    # TRIX指标分析
    if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
        print("\n按TRIX值分析:")
        df['TRIX_Group'] = pd.qcut(df['TRIX_Buy'], 4, labels=['低TRIX', '中低TRIX', '中高TRIX', '高TRIX'])
        trix_stats = df.groupby('TRIX_Group').agg({
            'Actual_Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        trix_stats.columns = ['TRIX_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(trix_stats)
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "开始生成可视化分析...")
    
    # 可视化分析
    try:
        print("\n生成可视化分析...")
        
        # 设置绘图风格
        plt.style.use('ggplot')
        
        # 确保输出目录存在
        os.makedirs('reports', exist_ok=True)
        
        # 图1: 收益分布
        plt.figure(figsize=(10, 6))
        sns.histplot(df['Actual_Profit_Pct'], bins=30, kde=True)
        plt.title('交易收益率分布')
        plt.xlabel('收益率 (%)')
        plt.ylabel('频率')
        plt.axvline(x=0, color='r', linestyle='--')
        plt.savefig('reports/profit_distribution.png')
        
        # 更新进度
        if progress_callback:
            progress_callback(current_step, total_steps, "正在生成收益分布图表...")
        
        # 图2: 各指标与收益率的关系
        if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x='Volatility_Buy', y='Actual_Profit_Pct', data=df, hue='Trade_Result')
            plt.title('波动性与收益率关系')
            plt.xlabel('买入时波动性')
            plt.ylabel('收益率 (%)')
            plt.axhline(y=0, color='r', linestyle='--')
            plt.savefig('reports/volatility_vs_profit.png')
        
        if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x='TRIX_Buy', y='Actual_Profit_Pct', data=df, hue='Trade_Result')
            plt.title('TRIX与收益率关系')
            plt.xlabel('买入时TRIX值')
            plt.ylabel('收益率 (%)')
            plt.axhline(y=0, color='r', linestyle='--')
            plt.savefig('reports/trix_vs_profit.png')
        
        # 图3: 持仓时间与收益率关系
        plt.figure(figsize=(10, 6))
        sns.boxplot(x='Holding_Group', y='Actual_Profit_Pct', data=df)
        plt.title('持仓时间与收益率关系')
        plt.xlabel('持仓时间')
        plt.ylabel('收益率 (%)')
        plt.xticks(rotation=45)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.savefig('reports/holding_time_vs_profit.png')
        
        print("可视化分析已保存为PNG文件")
        
        # 更新进度
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, "已完成可视化分析，开始寻找最佳参数...")
        
    except Exception as e:
        print(f"生成可视化分析时出错: {e}")
        if progress_callback:
            progress_callback(current_step, total_steps, f"生成可视化分析时出错: {e}")
    
    # 寻找最佳参数组合
    find_best_parameters(df, progress_callback, current_step, total_steps)

def find_best_parameters(df, progress_callback=None, current_step=0, total_steps=10):
    """
    寻找最佳参数组合
    
    参数:
    df (DataFrame): 交易数据
    progress_callback (function): 进度回调函数
    current_step (int): 当前进度步骤
    total_steps (int): 总步骤数
    """
    print("\n========== 寻找最佳参数组合 ==========")
    
    # 检查是否有足够的数据进行分析
    if len(df) < 30:
        print("交易数据不足，无法进行可靠的参数优化分析")
        if progress_callback:
            progress_callback(total_steps, total_steps, "交易数据不足，无法进行可靠的参数优化分析")
        return
    
    # 分析波动性指标
    if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
        # 创建不同波动性阈值
        thresholds = np.linspace(df['Volatility_Buy'].min(), df['Volatility_Buy'].max(), 10)
        results = []
        
        for threshold in thresholds:
            # 过滤高波动性交易
            high_vol_trades = df[df['Volatility_Buy'] >= threshold]
            if len(high_vol_trades) < 5:
                continue
                
            # 计算胜率和平均收益
            win_rate = (high_vol_trades['Actual_Profit_Pct'] > 0).mean() * 100
            avg_profit = high_vol_trades['Actual_Profit_Pct'].mean()
            count = len(high_vol_trades)
            
            results.append({
                'Threshold': threshold,
                'Count': count,
                'Win_Rate': win_rate,
                'Avg_Profit': avg_profit,
                'Score': win_rate * avg_profit / 100
            })
        
        # 找出最佳阈值
        if results:
            best_result = max(results, key=lambda x: x['Score'])
            
            print("\n最佳波动性阈值:")
            print(f"阈值: >= {best_result['Threshold']:.2f}")
            print(f"交易数量: {best_result['Count']}")
            print(f"胜率: {best_result['Win_Rate']:.2f}%")
            print(f"平均收益: {best_result['Avg_Profit']:.2f}%")
            print(f"综合得分: {best_result['Score']:.2f}")
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "完成参数优化分析")
    
    # 最终更新进度
    if progress_callback:
        progress_callback(total_steps, total_steps, "分析完成！")
    
    print("\n分析完成！")

if __name__ == "__main__":
    analyze_trades() 