# coding=utf-8
"""
基于TRIX_EMA_PERIOD=3的最优策略分析
分析最新回测数据，得出最优因子组合
"""

def show_latest_analysis_summary():
    """显示最新分析总结"""
    print('📊 TRIX_EMA_PERIOD=3 最新胜率分析总结')
    print('=' * 60)
    
    print('📈 数据质量:')
    print('   总交易记录: 5,126条')
    print('   买入记录: 2,603条')
    print('   卖出记录: 2,523条')
    print('   买卖匹配率: 96.9%')
    print('   交易股票数: 245只 (100%匹配)')
    
    print(f'\n🎯 回到TRIX_EMA_PERIOD=3的效果:')
    comparison_data = [
        ('总交易记录', '4,262条 (周期=4)', '5,126条 (周期=3)', '+20.3%'),
        ('买入记录', '2,160条 (周期=4)', '2,603条 (周期=3)', '+20.5%'),
        ('卖出记录', '2,102条 (周期=4)', '2,523条 (周期=3)', '+20.0%'),
        ('买卖匹配率', '97.3% (周期=4)', '96.9% (周期=3)', '-0.4%'),
        ('交易频率', '较低 (周期=4)', '较高 (周期=3)', '更多机会')
    ]
    
    print(f'{"指标":<12} | {"周期=4":<18} | {"周期=3":<18} | {"变化"}')
    print('-' * 65)
    
    for metric, period4, period3, change in comparison_data:
        print(f'{metric:<12} | {period4:<18} | {period3:<18} | {change}')

def show_top_factors_ranking():
    """显示顶级因子排名"""
    print(f'\n🏆 最新因子有效性排名 (TOP 10)')
    print('=' * 50)
    
    top_factors = [
        ('atr_pct', 1, '13.21%', '28.0% vs 14.8%', '高波动股票胜率显著更高'),
        ('macd_hist', 2, '11.33%', '26.8% vs 15.5%', 'MACD金叉信号非常有效'),
        ('macd_signal', 3, '10.22%', '26.5% vs 16.2%', 'MACD信号线负值区间优秀'),
        ('bb_width', 4, '10.17%', '24.4% vs 14.2%', '布林带宽度预测能力强'),
        ('ma20', 5, '9.69%', '24.6% vs 14.9%', '远离均线的股票表现更好'),
        ('macd', 6, '7.95%', '24.8% vs 16.9%', 'MACD主线负值区间效果好'),
        ('bb_position', 7, '6.90%', '24.5% vs 17.6%', '布林带位置有一定预测性'),
        ('trix_buy', 8, '6.69%', '24.4% vs 17.7%', 'TRIX买入信号稳定有效'),
        ('volume_change_rate', 9, '6.24%', '24.8% vs 18.5%', '成交量变化率有效'),
        ('cci', 10, '6.13%', '23.8% vs 17.7%', 'CCI指标有一定区分度')
    ]
    
    print(f'{"排名":<4} | {"因子":<18} | {"得分":<8} | {"胜率差异":<15} | {"关键洞察"}')
    print('-' * 80)
    
    for factor, rank, score, win_rate_diff, insight in top_factors:
        print(f'{rank:<4} | {factor:<18} | {score:<8} | {win_rate_diff:<15} | {insight}')

def show_optimal_combinations():
    """显示最优组合"""
    print(f'\n🎯 最优因子组合分析')
    print('=' * 50)
    
    combinations = [
        {
            'name': '高胜率组合A',
            'description': '高波动 + 宽布林带 + MACD金叉',
            'conditions': 'ATR > 2.7 + BB_width > 10.8 + MACD_hist > 0',
            'trades': 389,
            'matched': 381,
            'win_rate': 25.7,
            'avg_profit': 5.60,
            'avg_loss': 1.52,
            'profit_factor': 1.27,
            'rank': 1,
            'recommendation': '最佳选择'
        },
        {
            'name': '稳健组合B',
            'description': '中等波动 + 强趋势 + RSI中性',
            'conditions': 'ATR > 2.2 + ADX > 25 + RSI 30-70',
            'trades': 454,
            'matched': 447,
            'win_rate': 24.6,
            'avg_profit': 4.60,
            'avg_loss': 1.24,
            'profit_factor': 1.21,
            'rank': 2,
            'recommendation': '稳健选择'
        },
        {
            'name': '突破组合D',
            'description': '远离均线 + TRIX买入 + 控制波动',
            'conditions': 'MA20距离 > 23 + TRIX > 0 + ATR < 3',
            'trades': 228,
            'matched': 220,
            'win_rate': 20.5,
            'avg_profit': 4.57,
            'avg_loss': 1.18,
            'profit_factor': 1.00,
            'rank': 3,
            'recommendation': '趋势选择'
        },
        {
            'name': '反转组合C',
            'description': 'MACD负值 + 布林带下轨 + 高成交量',
            'conditions': 'MACD < -0.12 + BB_position < 20 + Volume > 1.5',
            'trades': 29,
            'matched': 28,
            'win_rate': 3.6,
            'avg_profit': 5.25,
            'avg_loss': 1.35,
            'profit_factor': 0.14,
            'rank': 4,
            'recommendation': '不推荐'
        }
    ]
    
    print(f'{"组合":<12} | {"胜率":<6} | {"交易数":<6} | {"盈亏比":<6} | {"推荐度":<8} | {"描述"}')
    print('-' * 75)
    
    for combo in combinations:
        print(f'{combo["name"]:<12} | {combo["win_rate"]:<6.1f}% | {combo["trades"]:<6} | {combo["profit_factor"]:<6.2f} | {combo["recommendation"]:<8} | {combo["description"]}')

def show_key_insights():
    """显示关键洞察"""
    print(f'\n💡 关键策略洞察')
    print('=' * 50)
    
    insights = [
        {
            'category': '波动率效应',
            'finding': 'ATR高波动股票胜率28.0% vs 低波动14.8%',
            'explanation': '高波动股票有更大的获利空间和突破潜力',
            'strategy_implication': '优先选择ATR > 2.7的高波动股票'
        },
        {
            'category': 'MACD系列优势',
            'finding': 'MACD_hist、MACD_signal、MACD主线都进入前6',
            'explanation': 'MACD系列指标在当前市场环境下表现优异',
            'strategy_implication': '强化MACD相关条件的权重'
        },
        {
            'category': '布林带预测力',
            'finding': 'BB_width排名第4，宽布林带胜率24.4% vs 窄带14.2%',
            'explanation': '宽布林带表示市场活跃，突破概率更高',
            'strategy_implication': '添加BB_width > 10.8作为筛选条件'
        },
        {
            'category': '均线偏离效应',
            'finding': 'MA20距离排名第5，远离均线胜率24.6% vs 贴近14.9%',
            'explanation': '可能是均值回归或突破效应',
            'strategy_implication': '优先选择偏离MA20较远的股票'
        },
        {
            'category': '反转策略失效',
            'finding': '反转组合C胜率仅3.6%，大幅下降',
            'explanation': '当前市场环境可能不适合深度反转策略',
            'strategy_implication': '避免使用极端反转条件'
        }
    ]
    
    for insight in insights:
        print(f'\n📊 {insight["category"]}:')
        print(f'   发现: {insight["finding"]}')
        print(f'   解释: {insight["explanation"]}')
        print(f'   策略含义: {insight["strategy_implication"]}')

def show_optimization_recommendations():
    """显示优化建议"""
    print(f'\n🚀 策略优化建议')
    print('=' * 50)
    
    recommendations = [
        {
            'priority': '高优先级',
            'action': '实施高胜率组合A',
            'details': [
                'ATR_pct > 2.7 (高波动)',
                'BB_width > 10.8 (宽布林带)',
                'MACD_hist > 0 (金叉)',
                '预期胜率: 25.7%，盈亏比: 1.27'
            ],
            'expected_impact': '胜率提升至25%+，风险调整收益显著改善'
        },
        {
            'priority': '中优先级',
            'action': '强化MACD系列指标',
            'details': [
                '增加MACD_signal < 0的条件权重',
                '优化MACD_hist金叉时机判断',
                '考虑MACD主线负值区间机会',
                '构建MACD多维度评分体系'
            ],
            'expected_impact': 'MACD相关信号质量提升，减少假信号'
        },
        {
            'priority': '中优先级',
            'action': '优化波动率筛选',
            'details': [
                '提高ATR阈值至2.7以上',
                '过滤ATR < 2.2的低波动股票',
                '结合相对波动率指标',
                '动态调整波动率阈值'
            ],
            'expected_impact': '选股精度提升，高胜率股票比例增加'
        },
        {
            'priority': '低优先级',
            'action': '放弃极端反转策略',
            'details': [
                '暂停使用反转组合C',
                '避免极端超跌条件',
                '重新评估反转信号有效性',
                '考虑温和反转条件'
            ],
            'expected_impact': '避免低胜率陷阱，提升整体策略效果'
        }
    ]
    
    for rec in recommendations:
        print(f'\n🎯 {rec["priority"]} - {rec["action"]}:')
        print(f'   具体措施:')
        for detail in rec['details']:
            print(f'     • {detail}')
        print(f'   预期影响: {rec["expected_impact"]}')

def show_implementation_plan():
    """显示实施计划"""
    print(f'\n📋 实施计划')
    print('=' * 50)
    
    phases = [
        {
            'phase': '第一阶段：核心优化 (1周)',
            'tasks': [
                '集成高胜率组合A到主策略',
                '调整ATR阈值至2.7',
                '添加BB_width > 10.8条件',
                '强化MACD_hist金叉过滤'
            ],
            'target': '胜率提升至25%+',
            'risk': '需要验证新条件的稳定性'
        },
        {
            'phase': '第二阶段：精细调优 (2周)',
            'tasks': [
                '优化MACD系列指标权重',
                '测试不同ATR阈值效果',
                '评估布林带宽度动态阈值',
                '完善因子组合逻辑'
            ],
            'target': '盈亏比提升至1.3+',
            'risk': '过度优化可能降低适应性'
        },
        {
            'phase': '第三阶段：监控优化 (持续)',
            'tasks': [
                '建立因子有效性监控',
                '定期重新分析因子排名',
                '根据市场变化调整策略',
                '实施自适应参数调整'
            ],
            'target': '策略长期稳定性',
            'risk': '需要持续的监控和调整'
        }
    ]
    
    for phase in phases:
        print(f'\n📅 {phase["phase"]}:')
        print(f'   任务:')
        for task in phase['tasks']:
            print(f'     • {task}')
        print(f'   目标: {phase["target"]}')
        print(f'   风险: {phase["risk"]}')

def main():
    """主函数"""
    print('📊 TRIX_EMA_PERIOD=3 最优策略分析报告')
    print('=' * 60)
    
    # 显示最新分析总结
    show_latest_analysis_summary()
    
    # 显示顶级因子排名
    show_top_factors_ranking()
    
    # 显示最优组合
    show_optimal_combinations()
    
    # 显示关键洞察
    show_key_insights()
    
    # 显示优化建议
    show_optimization_recommendations()
    
    # 显示实施计划
    show_implementation_plan()
    
    print(f'\n🎉 总结')
    print('=' * 40)
    print('✅ TRIX_EMA_PERIOD=3恢复了更高的交易频率')
    print('✅ ATR波动率仍是最强预测因子 (13.21%得分)')
    print('✅ MACD系列指标表现优异 (前6名占3个)')
    print('✅ 高胜率组合A实际胜率25.7%，盈亏比1.27')
    print('✅ 反转策略在当前环境下失效')
    print('')
    print('🎯 最优策略:')
    print('   组合: 高胜率组合A')
    print('   条件: ATR > 2.7 + BB_width > 10.8 + MACD_hist > 0')
    print('   胜率: 25.7%')
    print('   盈亏比: 1.27')
    print('   交易数: 389次')
    print('')
    print('🚀 立即行动: 实施高胜率组合A，预期胜率提升至25%+')

if __name__ == '__main__':
    main()
