# coding=utf-8
"""
第2天优化：BB位置和RSI因子深度分析
基于CCI优化成功，继续优化其他高效因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_bb_position_effectiveness():
    """分析BB位置因子有效性"""
    print('📊 BB位置因子深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配的BB位置数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.bb_position,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.bb_position IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 BB位置匹配数据: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的BB位置数据')
            return None
        
        # BB位置值分布分析
        bb_stats = df['bb_position'].describe()
        print(f'\n📊 BB位置值分布:')
        print(f'   均值: {bb_stats["mean"]:.3f}')
        print(f'   中位数: {bb_stats["50%"]:.3f}')
        print(f'   标准差: {bb_stats["std"]:.3f}')
        print(f'   范围: [{bb_stats["min"]:.3f}, {bb_stats["max"]:.3f}]')
        
        # 收益分布分析
        profit_stats = df['net_profit_pct_sell'].describe()
        print(f'\n💰 收益分布:')
        print(f'   均值: {profit_stats["mean"]:.2f}%')
        print(f'   胜率: {(df["net_profit_pct_sell"] > 0).mean() * 100:.1f}%')
        
        # BB位置区间效果分析
        bb_ranges = [
            ('极低位置 [0.0-0.2]', (df['bb_position'] >= 0.0) & (df['bb_position'] <= 0.2)),
            ('低位置 [0.2-0.4]', (df['bb_position'] > 0.2) & (df['bb_position'] <= 0.4)),
            ('中位置 [0.4-0.6]', (df['bb_position'] > 0.4) & (df['bb_position'] <= 0.6)),
            ('高位置 [0.6-0.8]', (df['bb_position'] > 0.6) & (df['bb_position'] <= 0.8)),
            ('极高位置 [0.8-1.0]', (df['bb_position'] > 0.8) & (df['bb_position'] <= 1.0)),
            ('当前策略 [0.3-0.8]', (df['bb_position'] >= 0.3) & (df['bb_position'] <= 0.8)),
        ]
        
        print(f'\n🎯 BB位置区间效果分析:')
        print(f'BB位置区间        交易数  胜率%   平均收益%')
        print(f'-' * 45)
        
        best_range = None
        best_score = -999
        
        for range_name, condition in bb_ranges:
            range_data = df[condition]
            
            if len(range_data) > 20:  # 确保样本量足够
                win_rate = (range_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = range_data['net_profit_pct_sell'].mean()
                trade_count = len(range_data)
                
                # 综合评分
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                print(f'{range_name:<15} {trade_count:6d} {win_rate:6.1f} {avg_profit:9.2f}')
                
                if score > best_score and 'current' not in range_name.lower():
                    best_score = score
                    best_range = (range_name, win_rate, avg_profit, trade_count)
        
        if best_range:
            print(f'\n🏆 最优BB位置区间: {best_range[0]}')
            print(f'   胜率: {best_range[1]:.1f}%')
            print(f'   平均收益: {best_range[2]:.2f}%')
            print(f'   样本数: {best_range[3]}')
        
        return df, best_range
        
    except Exception as e:
        print(f'❌ BB位置分析失败: {e}')
        return None, None

def analyze_rsi_effectiveness():
    """分析RSI因子有效性"""
    print(f'\n📊 RSI因子深度分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入-卖出匹配的RSI数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.rsi,
                s.net_profit_pct_sell,
                CAST(strftime('%H', b.timestamp) AS INTEGER) as buy_hour,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.rsi IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1500
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 RSI匹配数据: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的RSI数据')
            return None
        
        # RSI值分布分析
        rsi_stats = df['rsi'].describe()
        print(f'\n📊 RSI值分布:')
        print(f'   均值: {rsi_stats["mean"]:.2f}')
        print(f'   中位数: {rsi_stats["50%"]:.2f}')
        print(f'   标准差: {rsi_stats["std"]:.2f}')
        print(f'   范围: [{rsi_stats["min"]:.2f}, {rsi_stats["max"]:.2f}]')
        
        # RSI区间效果分析
        rsi_ranges = [
            ('超卖区 [0-30]', (df['rsi'] >= 0) & (df['rsi'] <= 30)),
            ('偏低区 [30-40]', (df['rsi'] > 30) & (df['rsi'] <= 40)),
            ('中性区 [40-60]', (df['rsi'] > 40) & (df['rsi'] <= 60)),
            ('偏高区 [60-70]', (df['rsi'] > 60) & (df['rsi'] <= 70)),
            ('超买区 [70-100]', (df['rsi'] > 70) & (df['rsi'] <= 100)),
            ('当前策略 [40-70]', (df['rsi'] >= 40) & (df['rsi'] <= 70)),
        ]
        
        print(f'\n🎯 RSI区间效果分析:')
        print(f'RSI区间           交易数  胜率%   平均收益%')
        print(f'-' * 45)
        
        best_range = None
        best_score = -999
        
        for range_name, condition in rsi_ranges:
            range_data = df[condition]
            
            if len(range_data) > 20:  # 确保样本量足够
                win_rate = (range_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = range_data['net_profit_pct_sell'].mean()
                trade_count = len(range_data)
                
                # 综合评分
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                print(f'{range_name:<15} {trade_count:6d} {win_rate:6.1f} {avg_profit:9.2f}')
                
                if score > best_score and 'current' not in range_name.lower():
                    best_score = score
                    best_range = (range_name, win_rate, avg_profit, trade_count)
        
        if best_range:
            print(f'\n🏆 最优RSI区间: {best_range[0]}')
            print(f'   胜率: {best_range[1]:.1f}%')
            print(f'   平均收益: {best_range[2]:.2f}%')
            print(f'   样本数: {best_range[3]}')
        
        return df, best_range
        
    except Exception as e:
        print(f'❌ RSI分析失败: {e}')
        return None, None

def analyze_four_factor_combination():
    """分析CCI+ADX+BB+RSI四因子组合"""
    print(f'\n🔗 四因子组合分析 (CCI+ADX+BB+RSI)')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取同时有四个因子的数据
        query = """
        WITH buy_sell_matched AS (
            SELECT 
                b.timestamp as buy_time,
                b.symbol,
                b.cci, b.adx, b.bb_position, b.rsi,
                s.net_profit_pct_sell,
                ROW_NUMBER() OVER (PARTITION BY b.symbol ORDER BY b.timestamp) as buy_rank,
                ROW_NUMBER() OVER (PARTITION BY s.symbol ORDER BY s.timestamp) as sell_rank
            FROM trades b
            JOIN trades s ON b.symbol = s.symbol 
            WHERE b.action = 'BUY' 
            AND s.action = 'SELL'
            AND s.net_profit_pct_sell IS NOT NULL
            AND b.cci IS NOT NULL
            AND b.adx IS NOT NULL
            AND b.bb_position IS NOT NULL
            AND b.rsi IS NOT NULL
            AND b.timestamp < s.timestamp
        )
        SELECT * FROM buy_sell_matched
        WHERE buy_rank = sell_rank
        ORDER BY buy_time DESC
        LIMIT 1000
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if len(df) == 0:
            print('⚠️ 没有四因子组合数据')
            return
        
        print(f'📊 四因子组合数据: {len(df)}条')
        
        # 基于之前的最优发现分析组合
        combinations = [
            ('最优CCI+高ADX', (df['cci'] >= 20) & (df['cci'] <= 30) & (df['adx'] >= 35)),
            ('最优CCI+中ADX', (df['cci'] >= 20) & (df['cci'] <= 30) & (df['adx'] >= 25) & (df['adx'] < 35)),
            ('最优CCI+低ADX', (df['cci'] >= 20) & (df['cci'] <= 30) & (df['adx'] < 25)),
            ('最优CCI+最优BB', (df['cci'] >= 20) & (df['cci'] <= 30) & (df['bb_position'] >= 0.3) & (df['bb_position'] <= 0.8)),
            ('最优CCI+最优RSI', (df['cci'] >= 20) & (df['cci'] <= 30) & (df['rsi'] >= 40) & (df['rsi'] <= 70)),
            ('四因子最优组合', (df['cci'] >= 20) & (df['cci'] <= 30) & 
                              (df['adx'] >= 25) & 
                              (df['bb_position'] >= 0.3) & (df['bb_position'] <= 0.8) &
                              (df['rsi'] >= 40) & (df['rsi'] <= 70)),
        ]
        
        print(f'\n🎯 四因子组合效果:')
        print(f'组合类型              胜率%   收益%   样本数')
        print(f'-' * 50)
        
        best_combination = None
        best_score = -999
        
        for combo_name, condition in combinations:
            combo_data = df[condition]
            
            if len(combo_data) > 5:
                win_rate = (combo_data['net_profit_pct_sell'] > 0).mean() * 100
                avg_profit = combo_data['net_profit_pct_sell'].mean()
                sample_count = len(combo_data)
                
                score = win_rate * 0.6 + avg_profit * 10 * 0.4
                
                print(f'{combo_name:<20} {win_rate:5.1f} {avg_profit:7.2f} {sample_count:6d}')
                
                if score > best_score:
                    best_score = score
                    best_combination = (combo_name, win_rate, avg_profit, sample_count)
        
        if best_combination:
            print(f'\n🏆 最优四因子组合: {best_combination[0]}')
            print(f'   胜率: {best_combination[1]:.1f}%')
            print(f'   平均收益: {best_combination[2]:.2f}%')
            print(f'   样本数: {best_combination[3]}')
        
        return best_combination
        
    except Exception as e:
        print(f'❌ 四因子组合分析失败: {e}')
        return None

def generate_optimization_recommendations(bb_result, rsi_result, combo_result):
    """生成优化建议"""
    print(f'\n🚀 第2天优化建议')
    print('=' * 50)
    
    recommendations = []
    
    # BB位置优化建议
    if bb_result and bb_result[1]:
        bb_range, bb_win_rate, bb_profit, bb_count = bb_result[1]
        current_bb_win_rate = 50.0  # 假设当前胜率
        
        if bb_win_rate > current_bb_win_rate + 5:
            recommendations.append({
                'factor': 'BB位置',
                'current': '[0.3, 0.8]',
                'recommended': bb_range,
                'improvement': f'+{bb_win_rate - current_bb_win_rate:.1f}%',
                'priority': 'HIGH'
            })
    
    # RSI优化建议
    if rsi_result and rsi_result[1]:
        rsi_range, rsi_win_rate, rsi_profit, rsi_count = rsi_result[1]
        current_rsi_win_rate = 50.0  # 假设当前胜率
        
        if rsi_win_rate > current_rsi_win_rate + 5:
            recommendations.append({
                'factor': 'RSI',
                'current': '[40, 70]',
                'recommended': rsi_range,
                'improvement': f'+{rsi_win_rate - current_rsi_win_rate:.1f}%',
                'priority': 'HIGH'
            })
    
    # 组合优化建议
    if combo_result:
        combo_name, combo_win_rate, combo_profit, combo_count = combo_result
        
        if combo_win_rate > 60:
            recommendations.append({
                'factor': '四因子组合',
                'current': '独立因子',
                'recommended': combo_name,
                'improvement': f'胜率{combo_win_rate:.1f}%',
                'priority': 'HIGHEST'
            })
    
    print(f'📊 优化建议汇总:')
    print(f'因子         当前配置      建议配置           提升幅度    优先级')
    print(f'-' * 70)
    
    for rec in recommendations:
        print(f'{rec["factor"]:<10} {rec["current"]:<12} {rec["recommended"]:<15} {rec["improvement"]:<10} {rec["priority"]}')
    
    return recommendations

def main():
    """主函数"""
    print('🚀 第2天优化：BB位置和RSI因子分析')
    print('=' * 60)
    
    print('🎯 基于CCI优化成功 (胜率+22.1%)，继续优化其他因子')
    print('📊 目标: 完成前4个最有效因子的全面优化')
    
    # 分析BB位置因子
    bb_df, bb_best = analyze_bb_position_effectiveness()
    
    # 分析RSI因子
    rsi_df, rsi_best = analyze_rsi_effectiveness()
    
    # 分析四因子组合
    combo_best = analyze_four_factor_combination()
    
    # 生成优化建议
    recommendations = generate_optimization_recommendations(
        (bb_df, bb_best), (rsi_df, rsi_best), combo_best
    )
    
    print(f'\n🎯 第2天分析总结')
    print('=' * 40)
    print('✅ BB位置因子分析完成')
    print('✅ RSI因子分析完成')
    print('✅ 四因子组合分析完成')
    print('✅ 优化建议已生成')
    
    if len(recommendations) > 0:
        print(f'🚀 发现 {len(recommendations)} 个优化机会')
        print('💡 建议立即实施高优先级优化')
    else:
        print('📊 当前配置已相对优化')
        print('💡 建议继续第3天MACD等因子分析')

if __name__ == '__main__':
    main()
