# coding=utf-8
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 文件路径
TRADE_LOG_FILE = 'data/trade_log.csv'
ANALYSIS_LOG_FILE = 'data/analysis_log.csv'
OUTPUT_FILE = 'reports/trade_analysis_results.csv'

def analyze_trades():
    print("开始分析交易数据...")
    
    # 读取交易日志
    try:
        trade_df = pd.read_csv(TRADE_LOG_FILE)
        print(f"成功读取交易日志，共{len(trade_df)}条记录")
    except Exception as e:
        print(f"读取交易日志失败: {e}")
        return
    
    # 读取分析日志（可选，如果需要更多指标数据）
    try:
        analysis_df = pd.read_csv(ANALYSIS_LOG_FILE)
        print(f"成功读取分析日志，共{len(analysis_df)}条记录")
        has_analysis_data = True
    except Exception as e:
        print(f"读取分析日志失败: {e}，将仅使用交易日志数据")
        has_analysis_data = False
    
    # 分离买入和卖出记录
    buy_records = trade_df[trade_df['Action'] == 'BUY']
    sell_records = trade_df[trade_df['Action'] == 'SELL']
    
    print(f"买入记录: {len(buy_records)}条")
    print(f"卖出记录: {len(sell_records)}条")
    
    # 创建完整交易记录
    complete_trades = []
    
    # 遍历卖出记录，查找对应的买入记录
    for _, sell in sell_records.iterrows():
        symbol = sell['Symbol']
        sell_time = pd.to_datetime(sell['Timestamp'])
        
        # 查找该股票在卖出前的最近一次买入记录
        matching_buys = buy_records[buy_records['Symbol'] == symbol]
        if len(matching_buys) == 0:
            continue
            
        matching_buys['Timestamp'] = pd.to_datetime(matching_buys['Timestamp'])
        matching_buys = matching_buys[matching_buys['Timestamp'] < sell_time]
        
        if len(matching_buys) == 0:
            continue
            
        # 获取最近的买入记录
        buy = matching_buys.sort_values('Timestamp', ascending=False).iloc[0]
        
        # 合并买入和卖出数据
        trade_record = {
            # 交易基本信息
            'Symbol': symbol,
            'Buy_Time': buy['Timestamp'],
            'Sell_Time': sell_time,
            'Holding_Hours': sell['Holding_Hours'],
            'Buy_Price': buy['Price'],
            'Sell_Price': sell['Price'],
            'Volume': buy['Volume'],
            'Profit_Pct': sell['Net_Profit_Pct_Sell'],
            'Max_Profit_Pct': sell['Max_Profit_Pct'],
            'Final_Drawdown_Pct': sell['Final_Drawdown_Pct'],
            'Sell_Reason': sell['Sell_Reason'],
            
            # 买入点指标
            'TRIX_Buy': buy.get('TRIX_Buy', None),
            'Volatility_Buy': buy.get('Volatility', None),
            'ATR_Pct_Buy': buy.get('ATR_Pct', None),
            'Volatility_Score_Buy': buy.get('Volatility_Score', None),
            'Allocation_Factor_Buy': buy.get('Allocation_Factor', None),
            
            # 计算其他指标
            'Trade_Result': 'Win' if sell['Net_Profit_Pct_Sell'] > 0 else 'Loss',
            'Profit_Amount': (sell['Price'] - buy['Price']) * buy['Volume'],
        }
        
        complete_trades.append(trade_record)
    
    # 创建完整交易DataFrame
    if complete_trades:
        complete_df = pd.DataFrame(complete_trades)
        print(f"成功匹配{len(complete_df)}笔完整交易")
        
        # 保存完整交易记录
        complete_df.to_csv(OUTPUT_FILE, index=False)
        print(f"交易分析结果已保存到 {OUTPUT_FILE}")
        
        # 进行交易分析
        analyze_trade_performance(complete_df)
    else:
        print("没有找到匹配的完整交易记录")

def analyze_trade_performance(df):
    """分析交易表现"""
    print("\n========== 交易表现分析 ==========")
    
    # 基本统计
    total_trades = len(df)
    winning_trades = len(df[df['Profit_Pct'] > 0])
    losing_trades = len(df[df['Profit_Pct'] <= 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    
    avg_profit = df['Profit_Pct'].mean()
    avg_win = df[df['Profit_Pct'] > 0]['Profit_Pct'].mean() if winning_trades > 0 else 0
    avg_loss = df[df['Profit_Pct'] <= 0]['Profit_Pct'].mean() if losing_trades > 0 else 0
    
    profit_factor = abs(df[df['Profit_Pct'] > 0]['Profit_Pct'].sum() / df[df['Profit_Pct'] < 0]['Profit_Pct'].sum()) if df[df['Profit_Pct'] < 0]['Profit_Pct'].sum() != 0 else float('inf')
    
    print(f"总交易次数: {total_trades}")
    print(f"盈利交易: {winning_trades} ({win_rate:.2f}%)")
    print(f"亏损交易: {losing_trades} ({100-win_rate:.2f}%)")
    print(f"平均收益率: {avg_profit:.2f}%")
    print(f"平均盈利: {avg_win:.2f}%")
    print(f"平均亏损: {avg_loss:.2f}%")
    print(f"盈亏比: {abs(avg_win/avg_loss) if avg_loss != 0 else float('inf'):.2f}")
    print(f"利润因子: {profit_factor:.2f}")
    
    # 按卖出原因分析
    print("\n按卖出原因分析:")
    reason_stats = df.groupby('Sell_Reason').agg({
        'Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    reason_stats.columns = ['Sell_Reason', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(reason_stats.sort_values('Count', ascending=False))
    
    # 按持仓时间分析
    df['Holding_Days'] = df['Holding_Hours'] / 24
    print("\n按持仓时间分析:")
    df['Holding_Group'] = pd.cut(df['Holding_Days'], 
                                bins=[0, 1, 2, 3, 5, 10, float('inf')],
                                labels=['0-1天', '1-2天', '2-3天', '3-5天', '5-10天', '10天以上'])
    holding_stats = df.groupby('Holding_Group').agg({
        'Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    holding_stats.columns = ['Holding_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(holding_stats)
    
    # 波动性分析
    if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
        print("\n按波动性分析:")
        df['Volatility_Group'] = pd.qcut(df['Volatility_Buy'], 4, labels=['低波动', '中低波动', '中高波动', '高波动'])
        vol_stats = df.groupby('Volatility_Group').agg({
            'Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        vol_stats.columns = ['Volatility_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(vol_stats)
    
    # TRIX指标分析
    if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
        print("\n按TRIX值分析:")
        df['TRIX_Group'] = pd.qcut(df['TRIX_Buy'], 4, labels=['低TRIX', '中低TRIX', '中高TRIX', '高TRIX'])
        trix_stats = df.groupby('TRIX_Group').agg({
            'Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        trix_stats.columns = ['TRIX_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(trix_stats)
    
    # 可视化分析
    try:
        print("\n生成可视化分析...")
        
        # 设置绘图风格
        plt.style.use('ggplot')
        
        # 图1: 收益分布
        plt.figure(figsize=(10, 6))
        sns.histplot(df['Profit_Pct'], bins=30, kde=True)
        plt.title('交易收益率分布')
        plt.xlabel('收益率 (%)')
        plt.ylabel('频率')
        plt.axvline(x=0, color='r', linestyle='--')
        plt.savefig('reports/profit_distribution.png')
        
        # 图2: 各指标与收益率的关系
        if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x='Volatility_Buy', y='Profit_Pct', data=df, hue='Trade_Result')
            plt.title('波动性与收益率关系')
            plt.xlabel('买入时波动性')
            plt.ylabel('收益率 (%)')
            plt.axhline(y=0, color='r', linestyle='--')
            plt.savefig('reports/volatility_vs_profit.png')
        
        if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
            plt.figure(figsize=(10, 6))
            sns.scatterplot(x='TRIX_Buy', y='Profit_Pct', data=df, hue='Trade_Result')
            plt.title('TRIX与收益率关系')
            plt.xlabel('买入时TRIX值')
            plt.ylabel('收益率 (%)')
            plt.axhline(y=0, color='r', linestyle='--')
            plt.savefig('reports/trix_vs_profit.png')
        
        # 图3: 持仓时间与收益率关系
        plt.figure(figsize=(10, 6))
        sns.boxplot(x='Holding_Group', y='Profit_Pct', data=df)
        plt.title('持仓时间与收益率关系')
        plt.xlabel('持仓时间')
        plt.ylabel('收益率 (%)')
        plt.xticks(rotation=45)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.savefig('reports/holding_time_vs_profit.png')
        
        print("可视化分析已保存为PNG文件")
    except Exception as e:
        print(f"生成可视化分析时出错: {e}")
    
    # 寻找最佳参数组合
    find_best_parameters(df)

def find_best_parameters(df):
    """寻找最佳参数组合"""
    print("\n========== 寻找最佳参数组合 ==========")
    
    # 检查是否有足够的数据进行分析
    if len(df) < 30:
        print("交易数据不足，无法进行可靠的参数优化分析")
        return
    
    # 分析波动性指标
    if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
        # 创建不同波动性阈值
        thresholds = np.linspace(df['Volatility_Buy'].min(), df['Volatility_Buy'].max(), 10)
        results = []
        
        for threshold in thresholds:
            subset = df[df['Volatility_Buy'] >= threshold]
            if len(subset) < 10:  # 确保有足够的样本
                continue
                
            win_rate = (subset['Profit_Pct'] > 0).mean() * 100
            avg_profit = subset['Profit_Pct'].mean()
            count = len(subset)
            
            results.append({
                'Threshold': threshold,
                'Count': count,
                'Win_Rate': win_rate,
                'Avg_Profit': avg_profit,
                'Score': win_rate * avg_profit / 100  # 综合得分
            })
        
        if results:
            results_df = pd.DataFrame(results)
            best_result = results_df.sort_values('Score', ascending=False).iloc[0]
            
            print(f"最佳波动性阈值: >= {best_result['Threshold']:.2f}")
            print(f"样本数: {best_result['Count']}")
            print(f"胜率: {best_result['Win_Rate']:.2f}%")
            print(f"平均收益: {best_result['Avg_Profit']:.2f}%")
            print(f"综合得分: {best_result['Score']:.2f}")
    
    # 分析TRIX指标
    if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
        # 创建不同TRIX阈值
        thresholds = np.linspace(df['TRIX_Buy'].min(), df['TRIX_Buy'].max(), 10)
        results = []
        
        for threshold in thresholds:
            subset = df[df['TRIX_Buy'] >= threshold]
            if len(subset) < 10:  # 确保有足够的样本
                continue
                
            win_rate = (subset['Profit_Pct'] > 0).mean() * 100
            avg_profit = subset['Profit_Pct'].mean()
            count = len(subset)
            
            results.append({
                'Threshold': threshold,
                'Count': count,
                'Win_Rate': win_rate,
                'Avg_Profit': avg_profit,
                'Score': win_rate * avg_profit / 100  # 综合得分
            })
        
        if results:
            results_df = pd.DataFrame(results)
            best_result = results_df.sort_values('Score', ascending=False).iloc[0]
            
            print(f"最佳TRIX阈值: >= {best_result['Threshold']:.6f}")
            print(f"样本数: {best_result['Count']}")
            print(f"胜率: {best_result['Win_Rate']:.2f}%")
            print(f"平均收益: {best_result['Avg_Profit']:.2f}%")
            print(f"综合得分: {best_result['Score']:.2f}")
    
    # 多因子组合分析
    print("\n多因子组合分析:")
    try:
        # 选择可用的指标
        features = []
        for feature in ['Volatility_Buy', 'ATR_Pct_Buy', 'TRIX_Buy', 'Volatility_Score_Buy']:
            if feature in df.columns and not df[feature].isna().all():
                features.append(feature)
        
        if not features:
            print("没有足够的指标数据进行多因子分析")
            return
            
        # 对每个特征进行分箱
        for feature in features:
            df[f'{feature}_bin'] = pd.qcut(df[feature], 4, labels=[1, 2, 3, 4])
        
        # 创建组合特征
        bin_features = [f'{feature}_bin' for feature in features]
        
        # 分析每种组合的表现
        combination_results = df.groupby(bin_features).agg({
            'Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        
        # 重命名列
        new_columns = list(bin_features)
        new_columns.extend(['Count', 'Avg_Profit', 'Win_Rate'])
        combination_results.columns = new_columns
        
        # 计算综合得分
        combination_results['Score'] = combination_results['Win_Rate'] * combination_results['Avg_Profit'] / 100
        
        # 筛选样本量足够的组合
        valid_combinations = combination_results[combination_results['Count'] >= 5]
        
        if len(valid_combinations) > 0:
            # 找出最佳组合
            best_combination = valid_combinations.sort_values('Score', ascending=False).iloc[0]
            
            print("最佳指标组合:")
            for i, feature in enumerate(features):
                feature_bin = f'{feature}_bin'
                bin_value = best_combination[feature_bin]
                if bin_value == 1:
                    description = "最低25%"
                elif bin_value == 2:
                    description = "25%-50%"
                elif bin_value == 3:
                    description = "50%-75%"
                else:
                    description = "最高25%"
                print(f"- {feature}: {description}")
            
            print(f"样本数: {best_combination['Count']}")
            print(f"胜率: {best_combination['Win_Rate']:.2f}%")
            print(f"平均收益: {best_combination['Avg_Profit']:.2f}%")
            print(f"综合得分: {best_combination['Score']:.2f}")
            
            # 保存最佳组合结果
            best_combinations = valid_combinations.sort_values('Score', ascending=False).head(10)
            best_combinations.to_csv('reports/best_parameter_combinations.csv', index=False)
            print("前10个最佳参数组合已保存到 best_parameter_combinations.csv")
        else:
            print("没有足够样本的参数组合")
    except Exception as e:
        print(f"进行多因子组合分析时出错: {e}")

if __name__ == "__main__":
    analyze_trades() 