# coding=utf-8
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import sqlite3

# 文件路径
DB_FILE = 'data/trades.db'
OUTPUT_FILE = 'reports/trade_analysis_results.csv'

def analyze_trades_db(progress_callback=None):
    """
    从SQLite数据库分析交易数据
    
    参数:
    progress_callback (function): 进度回调函数，接受三个参数：当前步骤、总步骤数和状态消息
    """
    print("开始从数据库分析交易数据...")
    
    # 定义总步骤数
    total_steps = 10
    current_step = 0
    
    # 更新进度
    if progress_callback:
        progress_callback(current_step, total_steps, "正在开始分析...")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "已创建输出目录")
    
    # 检查数据库文件是否存在
    if not os.path.exists(DB_FILE):
        error_msg = f"数据库文件不存在: {DB_FILE}"
        print(error_msg)
        if progress_callback:
            progress_callback(total_steps, total_steps, f"错误: {error_msg}")
        return
    
    # 从数据库读取交易记录
    try:
        # 连接到SQLite数据库
        conn = sqlite3.connect(DB_FILE)
        
        # 读取交易表
        query = "SELECT * FROM trades"
        trade_df = pd.read_sql_query(query, conn)
        
        # 关闭数据库连接
        conn.close()
        
        # 规范化列名 (将所有列名转换为与原CSV格式兼容的格式)
        column_map = {
            'timestamp': 'Timestamp',
            'symbol': 'Symbol',
            'action': 'Action',
            'price': 'Price',
            'volume': 'Volume',
            'sell_reason': 'Sell_Reason',
            'net_profit_pct_sell': 'Net_Profit_Pct_Sell',
            'holding_hours': 'Holding_Hours',
            'max_profit_pct': 'Max_Profit_Pct',
            'final_drawdown_pct': 'Final_Drawdown_Pct',
            
            # 买入时指标 - 基础指标
            'trix_buy': 'TRIX_Buy',
            'volatility': 'Volatility',
            'atr_pct': 'ATR_Pct',
            'volatility_score': 'Volatility_Score',
            'allocation_factor': 'Allocation_Factor',
            
            # 买入时指标 - 价格趋势指标
            'ma3_buy': 'MA3_Buy',
            'ma5_buy': 'MA5_Buy',
            'ma7_buy': 'MA7_Buy',
            'ma10_buy': 'MA10_Buy',
            'ma20_buy': 'MA20_Buy',
            'ma30_buy': 'MA30_Buy',
            'ma60_buy': 'MA60_Buy',
            'ema5_buy': 'EMA5_Buy',
            'ema10_buy': 'EMA10_Buy',
            'ema20_buy': 'EMA20_Buy',
            'ema30_buy': 'EMA30_Buy',
            'price_change_pct_buy': 'Price_Change_Pct_Buy',
            'trend_strength_buy': 'Trend_Strength_Buy',
            
            # 买入时指标 - 波动性指标
            'atr_buy': 'ATR_Buy',
            'true_range_buy': 'True_Range_Buy',
            'natr_buy': 'NATR_Buy',
            
            # 买入时指标 - 布林带指标
            'boll_upper_buy': 'BOLL_Upper_Buy',
            'boll_middle_buy': 'BOLL_Middle_Buy',
            'boll_lower_buy': 'BOLL_Lower_Buy',
            'boll_width_buy': 'BOLL_Width_Buy',
            'bollinger_width_buy': 'Bollinger_Width_Buy',
            
            # 买入时指标 - 动量指标
            'rsi_buy': 'RSI_Buy',
            'cci_buy': 'CCI_Buy',
            'wr_buy': 'WR_Buy',
            'williams_r_buy': 'Williams_R_Buy',
            'roc_buy': 'ROC_Buy',
            'momentum_buy': 'Momentum_Buy',
            'stoch_k_buy': 'Stoch_K_Buy',
            'stoch_d_buy': 'Stoch_D_Buy',
            'stochastic_rsi_buy': 'Stochastic_RSI_Buy',
            
            # 买入时指标 - 震荡指标
            'macd_buy': 'MACD_Buy',
            'macd_signal_buy': 'MACD_Signal_Buy',
            'macd_hist_buy': 'MACD_Hist_Buy',
            'kdj_k_buy': 'KDJ_K_Buy',
            'kdj_d_buy': 'KDJ_D_Buy',
            'kdj_j_buy': 'KDJ_J_Buy',
            
            # 买入时指标 - 趋势指标
            'dmi_plus_buy': 'DMI_Plus_Buy',
            'dmi_minus_buy': 'DMI_Minus_Buy',
            'adx_buy': 'ADX_Buy',
            
            # 买入时指标 - 成交量指标
            'volume_ratio_buy': 'Volume_Ratio_Buy',
            'obv_buy': 'OBV_Buy',
            'emv_buy': 'EMV_Buy',
            
            # 买入时指标 - 其他技术指标
            'psy_buy': 'PSY_Buy',
            
            # 买入时指标 - 资金管理指标
            'risk_ratio_buy': 'Risk_Ratio_Buy',
            
            # 卖出时指标
            'rsi_sell': 'RSI_Sell',
            'macd_sell': 'MACD_Sell',
            'volume_ratio_sell': 'Volume_Ratio_Sell'
        }
        
        # 重命名列
        for old_col, new_col in column_map.items():
            if old_col in trade_df.columns:
                trade_df.rename(columns={old_col: new_col}, inplace=True)
        
        print(f"成功从数据库读取交易记录，共{len(trade_df)}条记录")
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"已读取交易记录，共{len(trade_df)}条记录")
    except Exception as e:
        error_msg = f"从数据库读取交易记录失败: {e}"
        print(error_msg)
        if progress_callback:
            progress_callback(total_steps, total_steps, f"错误: {error_msg}")
        return
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在处理交易数据...")
    
    # 分离买入和卖出记录
    buy_records = trade_df[trade_df['Action'] == 'BUY']
    sell_records = trade_df[trade_df['Action'] == 'SELL']
    
    print(f"买入记录: {len(buy_records)}条")
    print(f"卖出记录: {len(sell_records)}条")
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, f"已分离买入({len(buy_records)}条)和卖出({len(sell_records)}条)记录")
    
    # 创建完整交易记录
    complete_trades = []
    
    # 更新进度
    if progress_callback:
        progress_callback(current_step, total_steps, "正在匹配买卖记录...")
    
    # 遍历卖出记录，查找对应的买入记录
    for _, sell in sell_records.iterrows():
        symbol = sell['Symbol']
        sell_time = pd.to_datetime(sell['Timestamp'])
        
        # 查找该股票在卖出前的最近一次买入记录
        matching_buys = buy_records[buy_records['Symbol'] == symbol]
        if len(matching_buys) == 0:
            continue
            
        matching_buys['Timestamp'] = pd.to_datetime(matching_buys['Timestamp'])
        matching_buys = matching_buys[matching_buys['Timestamp'] < sell_time]
        
        if len(matching_buys) == 0:
            continue
            
        # 获取最近的买入记录
        buy = matching_buys.sort_values('Timestamp', ascending=False).iloc[0]
        
        # 合并买入和卖出数据
        trade_record = {
            # 交易基本信息
            'Symbol': symbol,
            'Buy_Time': buy['Timestamp'],
            'Sell_Time': sell_time,
            'Holding_Hours': sell['Holding_Hours'],
            'Buy_Price': buy['Price'],
            'Sell_Price': sell['Price'],
            'Volume': buy['Volume'],
            'Profit_Pct': sell['Net_Profit_Pct_Sell'],
            'Max_Profit_Pct': sell['Max_Profit_Pct'],
            'Final_Drawdown_Pct': sell['Final_Drawdown_Pct'],
            'Sell_Reason': sell['Sell_Reason'],
            
            # 买入点指标 - 基础指标
            'TRIX_Buy': buy.get('TRIX_Buy', None),
            'Volatility_Buy': buy.get('Volatility', None),
            'ATR_Pct_Buy': buy.get('ATR_Pct', None),
            'Volatility_Score_Buy': buy.get('Volatility_Score', None),
            'Allocation_Factor_Buy': buy.get('Allocation_Factor', None),
            
            # 买入点指标 - 价格趋势指标
            'MA3_Buy': buy.get('MA3_Buy', None),
            'MA5_Buy': buy.get('MA5_Buy', None),
            'MA7_Buy': buy.get('MA7_Buy', None),
            'MA10_Buy': buy.get('MA10_Buy', None),
            'MA20_Buy': buy.get('MA20_Buy', None),
            'MA30_Buy': buy.get('MA30_Buy', None),
            'MA60_Buy': buy.get('MA60_Buy', None),
            'EMA5_Buy': buy.get('EMA5_Buy', None),
            'EMA10_Buy': buy.get('EMA10_Buy', None),
            'EMA20_Buy': buy.get('EMA20_Buy', None),
            'EMA30_Buy': buy.get('EMA30_Buy', None),
            'Price_Change_Pct_Buy': buy.get('Price_Change_Pct_Buy', None),
            'Trend_Strength_Buy': buy.get('Trend_Strength_Buy', None),
            
            # 买入点指标 - 波动性指标
            'ATR_Buy': buy.get('ATR_Buy', None),
            'True_Range_Buy': buy.get('True_Range_Buy', None),
            'NATR_Buy': buy.get('NATR_Buy', None),
            
            # 买入点指标 - 布林带指标
            'BOLL_Upper_Buy': buy.get('BOLL_Upper_Buy', None),
            'BOLL_Middle_Buy': buy.get('BOLL_Middle_Buy', None),
            'BOLL_Lower_Buy': buy.get('BOLL_Lower_Buy', None),
            'BOLL_Width_Buy': buy.get('BOLL_Width_Buy', None),
            'Bollinger_Width_Buy': buy.get('Bollinger_Width_Buy', None),
            
            # 买入点指标 - 动量指标
            'RSI_Buy': buy.get('RSI_Buy', None),
            'CCI_Buy': buy.get('CCI_Buy', None),
            'WR_Buy': buy.get('WR_Buy', None),
            'Williams_R_Buy': buy.get('Williams_R_Buy', None),
            'ROC_Buy': buy.get('ROC_Buy', None),
            'Momentum_Buy': buy.get('Momentum_Buy', None),
            'Stoch_K_Buy': buy.get('Stoch_K_Buy', None),
            'Stoch_D_Buy': buy.get('Stoch_D_Buy', None),
            'Stochastic_RSI_Buy': buy.get('Stochastic_RSI_Buy', None),
            
            # 买入点指标 - 震荡指标
            'MACD_Buy': buy.get('MACD_Buy', None),
            'MACD_Signal_Buy': buy.get('MACD_Signal_Buy', None),
            'MACD_Hist_Buy': buy.get('MACD_Hist_Buy', None),
            'KDJ_K_Buy': buy.get('KDJ_K_Buy', None),
            'KDJ_D_Buy': buy.get('KDJ_D_Buy', None),
            'KDJ_J_Buy': buy.get('KDJ_J_Buy', None),
            
            # 买入点指标 - 趋势指标
            'DMI_Plus_Buy': buy.get('DMI_Plus_Buy', None),
            'DMI_Minus_Buy': buy.get('DMI_Minus_Buy', None),
            'ADX_Buy': buy.get('ADX_Buy', None),
            
            # 买入点指标 - 成交量指标
            'Volume_Ratio_Buy': buy.get('Volume_Ratio_Buy', None),
            'OBV_Buy': buy.get('OBV_Buy', None),
            'EMV_Buy': buy.get('EMV_Buy', None),
            
            # 买入点指标 - 其他技术指标
            'PSY_Buy': buy.get('PSY_Buy', None),
            
            # 买入点指标 - 资金管理指标
            'Risk_Ratio_Buy': buy.get('Risk_Ratio_Buy', None),
            
            # 卖出时指标
            'RSI_Sell': sell.get('RSI_Sell', None),
            'MACD_Sell': sell.get('MACD_Sell', None),
            'Volume_Ratio_Sell': sell.get('Volume_Ratio_Sell', None),
            
            # 计算其他指标
            'Trade_Result': 'Win' if sell['Net_Profit_Pct_Sell'] > 0 else 'Loss',
            'Profit_Amount': (sell['Price'] - buy['Price']) * buy['Volume'],
        }
        
        complete_trades.append(trade_record)
    
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "完成买卖记录匹配")
    
    # 创建完整交易DataFrame
    if complete_trades:
        complete_df = pd.DataFrame(complete_trades)
        print(f"成功匹配{len(complete_df)}笔完整交易")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, f"成功匹配{len(complete_df)}笔完整交易，正在保存结果...")
        
        # 保存完整交易记录
        complete_df.to_csv(OUTPUT_FILE, index=False)
        print(f"交易分析结果已保存到 {OUTPUT_FILE}")
        
        current_step += 1
        if progress_callback:
            progress_callback(current_step, total_steps, "已保存交易分析结果，开始分析交易表现...")
        
        # 进行交易分析
        analyze_trade_performance(complete_df, progress_callback, current_step, total_steps)
    else:
        print("没有找到匹配的完整交易记录")
        if progress_callback:
            progress_callback(total_steps, total_steps, "没有找到匹配的完整交易记录")

def analyze_trade_performance(df, progress_callback=None, current_step=0, total_steps=10):
    """
    分析交易表现
    
    参数:
    df (DataFrame): 交易数据
    progress_callback (function): 进度回调函数
    current_step (int): 当前进度步骤
    total_steps (int): 总步骤数
    """
    print("\n========== 交易表现分析 ==========")
    
    # 基本统计
    total_trades = len(df)
    winning_trades = len(df[df['Profit_Pct'] > 0])
    losing_trades = len(df[df['Profit_Pct'] <= 0])
    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
    
    avg_profit = df['Profit_Pct'].mean()
    avg_win = df[df['Profit_Pct'] > 0]['Profit_Pct'].mean() if winning_trades > 0 else 0
    avg_loss = df[df['Profit_Pct'] <= 0]['Profit_Pct'].mean() if losing_trades > 0 else 0
    
    profit_factor = abs(df[df['Profit_Pct'] > 0]['Profit_Pct'].sum() / df[df['Profit_Pct'] < 0]['Profit_Pct'].sum()) if df[df['Profit_Pct'] < 0]['Profit_Pct'].sum() != 0 else float('inf')
    
    print(f"总交易次数: {total_trades}")
    print(f"盈利交易: {winning_trades} ({win_rate:.2f}%)")
    print(f"亏损交易: {losing_trades} ({100-win_rate:.2f}%)")
    print(f"平均收益率: {avg_profit:.2f}%")
    print(f"平均盈利: {avg_win:.2f}%")
    print(f"平均亏损: {avg_loss:.2f}%")
    
    # 计算并格式化盈亏比
    profit_loss_ratio = abs(avg_win/avg_loss) if avg_loss != 0 else float('inf')
    print(f"盈亏比: {profit_loss_ratio:.2f}")
    
    print(f"利润因子: {profit_factor:.2f}")
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在分析交易统计数据...")
    
    # 按卖出原因分析
    print("\n按卖出原因分析:")
    reason_stats = df.groupby('Sell_Reason').agg({
        'Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    reason_stats.columns = ['Sell_Reason', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(reason_stats.sort_values('Count', ascending=False))
    
    # 按持仓时间分析
    df['Holding_Days'] = df['Holding_Hours'] / 24
    print("\n按持仓时间分析:")
    df['Holding_Group'] = pd.cut(df['Holding_Days'], 
                                bins=[0, 1, 2, 3, 5, 10, float('inf')],
                                labels=['0-1天', '1-2天', '2-3天', '3-5天', '5-10天', '10天以上'])
    holding_stats = df.groupby('Holding_Group').agg({
        'Profit_Pct': ['count', 'mean'],
        'Trade_Result': lambda x: (x == 'Win').mean() * 100
    }).reset_index()
    holding_stats.columns = ['Holding_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
    print(holding_stats)
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在分析交易分组数据...")
    
    # 波动性分析
    if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
        print("\n按波动性分析:")
        df['Volatility_Group'] = pd.qcut(df['Volatility_Buy'], 4, labels=['低波动', '中低波动', '中高波动', '高波动'])
        vol_stats = df.groupby('Volatility_Group').agg({
            'Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        vol_stats.columns = ['Volatility_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(vol_stats)
    
    # TRIX指标分析
    if 'TRIX_Buy' in df.columns and not df['TRIX_Buy'].isna().all():
        print("\n按TRIX值分析:")
        df['TRIX_Group'] = pd.qcut(df['TRIX_Buy'], 4, labels=['低TRIX', '中低TRIX', '中高TRIX', '高TRIX'])
        trix_stats = df.groupby('TRIX_Group').agg({
            'Profit_Pct': ['count', 'mean'],
            'Trade_Result': lambda x: (x == 'Win').mean() * 100
        }).reset_index()
        trix_stats.columns = ['TRIX_Group', 'Count', 'Avg_Profit_Pct', 'Win_Rate']
        print(trix_stats)
    
    # 更新进度
    current_step += 1
    if progress_callback:
        progress_callback(current_step, total_steps, "正在生成分析图表...")
    
    # 绘制图表
    generate_charts(df)
    
    # 最终进度
    if progress_callback:
        progress_callback(total_steps, total_steps, "交易分析完成!")
    
    print("交易分析完成!")
    return df

def generate_charts(df):
    """
    生成交易分析图表
    
    参数:
    df (DataFrame): 交易数据
    """
    # 确保输出目录存在
    os.makedirs('reports', exist_ok=True)
    
    try:
        # 收益分布图
        plt.figure(figsize=(10, 6))
        sns.histplot(df['Profit_Pct'], kde=True)
        plt.axvline(x=0, color='red', linestyle='--')
        plt.title('交易收益分布')
        plt.xlabel('收益率 (%)')
        plt.ylabel('频率')
        plt.savefig('reports/profit_distribution.png')
        plt.close()
        
        # 持仓时间与收益关系图
        plt.figure(figsize=(10, 6))
        sns.boxplot(x='Holding_Group', y='Profit_Pct', data=df)
        plt.title('持仓时间与收益关系')
        plt.xlabel('持仓时间')
        plt.ylabel('收益率 (%)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('reports/holding_time_profit.png')
        plt.close()
        
        # 累积收益曲线
        plt.figure(figsize=(12, 6))
        df_sorted = df.sort_values('Buy_Time')
        df_sorted['Cumulative_Profit'] = df_sorted['Profit_Pct'].cumsum()
        plt.plot(range(len(df_sorted)), df_sorted['Cumulative_Profit'])
        plt.title('累积收益曲线')
        plt.xlabel('交易次数')
        plt.ylabel('累积收益率 (%)')
        plt.grid(True)
        plt.savefig('reports/cumulative_profit_curve.png')
        plt.close()
        
        # 如果有波动率数据，绘制波动率与收益关系图
        if 'Volatility_Buy' in df.columns and not df['Volatility_Buy'].isna().all():
            plt.figure(figsize=(10, 6))
            plt.scatter(df['Volatility_Buy'], df['Profit_Pct'])
            plt.title('波动率与收益关系')
            plt.xlabel('波动率')
            plt.ylabel('收益率 (%)')
            plt.grid(True)
            plt.savefig('reports/volatility_profit.png')
            plt.close()
        
        print("交易分析图表已生成")
    except Exception as e:
        print(f"生成图表时出错: {e}")

# 如果直接运行此脚本，执行分析
if __name__ == "__main__":
    analyze_trades_db() 