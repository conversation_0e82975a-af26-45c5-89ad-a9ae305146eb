#!/usr/bin/env python
# -*- coding: utf-8 -*-
from gm.api import *
import datetime
import random
import time
import pandas as pd

def get_account_info():
    """获取账户信息，包括最大订阅数量"""
    try:
        # 尝试获取账户信息
        account = get_account()
        if account:
            # 创建一个包含账户信息的字典
            account_info = {}
            
            # 尝试获取最大订阅数量
            # 注意：这里假设API提供了这样的信息，实际可能需要根据具体平台调整
            try:
                # 对于不同的API，获取最大订阅数量的方法可能不同
                # 这里提供几种可能的尝试方式
                
                # 方式1：直接从账户对象获取
                if hasattr(account, 'max_subscribe'):
                    account_info['max_subscribe'] = account.max_subscribe
                
                # 方式2：通过API调用获取
                user_info = get_user_info() if 'get_user_info' in globals() else None
                if user_info and 'max_subscribe' in user_info:
                    account_info['max_subscribe'] = user_info['max_subscribe']
                
                # 方式3：从账户权限中获取
                permissions = get_permissions() if 'get_permissions' in globals() else None
                if permissions and 'max_subscribe' in permissions:
                    account_info['max_subscribe'] = permissions['max_subscribe']
                
                # 如果以上方法都失败，使用默认安全值
                if 'max_subscribe' not in account_info:
                    account_info['max_subscribe'] = 50
            except:
                account_info['max_subscribe'] = 50
                
            return account_info
    except:
        # 如果获取账户信息失败，返回默认值
        return {'max_subscribe': 50}
    
    # 如果以上都失败，返回默认值
    return {'max_subscribe': 50}

class DataFetcher:
    def __init__(self, context):
        """
        初始化数据获取器
        
        参数:
        - context: 策略上下文
        """
        self.context = context
        
        # 初始化成分股列表
        self.constituents = []
        
        # 初始化批次相关变量
        self.stock_batches = []
        self.current_batch_index = 0
        self.current_subscribed_batch = []
        self.last_rotation_time = None
        
        # 获取模式自适应设置
        mode_adaptive_setting = self._get_config_value('ENABLE_MODE_ADAPTIVE', 0)
        
        # 处理模式自适应设置
        if mode_adaptive_setting == 0:
            # 自动模式自适应，根据运行模式自动判断
            self.enable_mode_adaptive = True
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - DataFetcher: 使用自动模式自适应")
        elif mode_adaptive_setting == 1:
            # 强制使用回测模式订阅策略
            self.enable_mode_adaptive = False
            self.is_backtest = True
            self.context.run_mode = 'backtest'
            self.backtest_full_subscription = True
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - DataFetcher: 强制使用回测模式订阅策略")
        elif mode_adaptive_setting == 2:
            # 强制使用模拟盘模式订阅策略
            self.enable_mode_adaptive = False
            self.is_backtest = False
            self.context.run_mode = 'live'
            self.subscription_rotation_enabled = True
            self.live_mode_batch_size = 100  # 模拟盘使用较大批次
            self.subscription_rotation_interval = 2  # 模拟盘使用较长轮换间隔
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - DataFetcher: 强制使用模拟盘模式订阅策略")
        elif mode_adaptive_setting == 3:
            # 强制使用实盘模式订阅策略
            self.enable_mode_adaptive = False
            self.is_backtest = False
            self.context.run_mode = 'live'
            self.subscription_rotation_enabled = True
            self.live_mode_batch_size = 50   # 实盘使用较小批次
            self.subscription_rotation_interval = 1  # 实盘使用较短轮换间隔
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - DataFetcher: 强制使用实盘模式订阅策略")
        else:
            # 未知设置，使用默认值
            self.enable_mode_adaptive = True
            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - DataFetcher: 未知的模式自适应设置: {mode_adaptive_setting}，使用默认自动模式")
        
        # 初始化其他订阅参数
        self.subscription_rotation_enabled = self._get_config_value('SUBSCRIPTION_ROTATION_ENABLED', True)
        self.subscription_rotation_interval = self._get_config_value('SUBSCRIPTION_ROTATION_INTERVAL', 1)
        self.live_mode_batch_size = self._get_config_value('LIVE_MODE_BATCH_SIZE', 50)
        self.max_subscription_limit = self._get_config_value('MAX_SUBSCRIPTION_LIMIT', 50)
        self.max_subscription_per_batch = self._get_config_value('MAX_SUBSCRIPTION_PER_BATCH', 50)
        self.sync_buy_check_with_rotation = self._get_config_value('SYNC_BUY_CHECK_WITH_ROTATION', True)
        self.backtest_full_subscription = self._get_config_value('BACKTEST_FULL_SUBSCRIPTION', True)
        
        # 初始化股票过滤参数
        self.filter_st_stocks = self._get_config_value('FILTER_ST_STOCKS', True)
        self.filter_startup_board = self._get_config_value('FILTER_STARTUP_BOARD', False)
        self.filter_science_board = self._get_config_value('FILTER_SCIENCE_BOARD', False)
        self.filter_beijing_board = self._get_config_value('FILTER_BEIJING_BOARD', False)
        self.price_filter_enabled = self._get_config_value('PRICE_FILTER_ENABLED', True)
        self.min_price_filter = self._get_config_value('MIN_PRICE_FILTER', 5.0)
        self.max_price_filter = self._get_config_value('MAX_PRICE_FILTER', 100.0)
        
        # 检测运行模式
        self.is_backtest = self.detect_run_mode()
        
        # 根据运行模式设置订阅模式
        if self.is_backtest:
            if self.backtest_full_subscription:
                self.subscribe_mode = 'full'
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式，使用全量订阅")
            else:
                self.subscribe_mode = 'batch'
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式，使用批次订阅")
        else:
            # 实盘/模拟盘模式
            self.subscribe_mode = 'batch'
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，使用批次订阅")
        
        # 获取最大订阅数量
        self.max_subscribe_count = self.get_max_subscribe_count()
        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 最大订阅数量: {self.max_subscribe_count}")
        
        # 如果是实盘/模拟盘模式，确保启用批次订阅
        if not self.is_backtest:
            self.subscription_rotation_enabled = True
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，强制启用批次订阅")
        elif self.enable_mode_adaptive:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 启用自适应模式，根据运行模式自动调整订阅方式")
        else:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 不使用自适应模式，默认批次订阅")
        
        # 日志输出初始化信息
        self.context.log.info(f"""
        ========== 订阅管理初始化 ==========
        模式自适应: {'启用' if self.enable_mode_adaptive else '禁用'}
        订阅轮换: {'启用' if self.subscription_rotation_enabled else '禁用'}
        轮换间隔: {self.subscription_rotation_interval}分钟
        实盘批次大小: {self.live_mode_batch_size}
        最大订阅限制: {self.max_subscription_limit}
        每批次最大订阅: {self.max_subscription_per_batch}
        买入检查与轮换同步: {'启用' if self.sync_buy_check_with_rotation else '禁用'}
        回测全量订阅: {'启用' if self.backtest_full_subscription else '禁用'}
        运行模式: {'回测' if self.is_backtest else '实盘/模拟盘'}
        订阅模式: {self.subscribe_mode}
        最大订阅数: {self.max_subscribe_count}
        
        ========== 股票过滤参数 ==========
        过滤ST股票: {'启用' if self.filter_st_stocks else '禁用'}
        过滤创业板: {'启用' if self.filter_startup_board else '禁用'}
        过滤科创板: {'启用' if self.filter_science_board else '禁用'}
        过滤北交所: {'启用' if self.filter_beijing_board else '禁用'}
        价格过滤: {'启用' if self.price_filter_enabled else '禁用'}
        最低价格: {self.min_price_filter}元
        最高价格: {self.max_price_filter}元
        ======================================
        """)
        
        # 如果设置了轮换间隔，立即设置上次轮换时间为一个较早的时间，确保首次检查时能触发轮换
        if self.subscription_rotation_enabled and self.subscription_rotation_interval > 0:
            # 设置上次轮换时间为当前时间减去两倍轮换间隔，确保首次检查时能触发轮换
            self.last_rotation_time = self.context.now - datetime.timedelta(minutes=self.subscription_rotation_interval*2)
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置初始轮换时间为: {self.last_rotation_time}，确保首次检查时能触发轮换")
        
    def detect_run_mode(self):
        """
        检测当前运行模式，返回是否为回测模式
        
        修改说明:
        1. 优化运行模式检测逻辑
        2. 在检测到实盘/模拟盘模式后立即设置相关属性
        3. 增加运行模式检测结果的日志记录
        4. 如果模式已经被强制设置，则直接返回当前设置
        
        返回:
        - bool: 是否为回测模式
        """
        # 如果模式已经被强制设置（通过ENABLE_MODE_ADAPTIVE参数），则直接返回当前设置
        if hasattr(self, 'is_backtest') and not self.enable_mode_adaptive:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用已强制设置的运行模式: {'回测' if self.is_backtest else '实盘/模拟盘'}")
            return self.is_backtest
            
        try:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始检测运行模式")
            
            # 方法1: 尝试使用API直接获取运行模式
            if 'get_run_mode' in globals():
                try:
                    mode = get_run_mode()
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过API获取运行模式: {mode}")
                    is_backtest = (mode == 0)
                    if not is_backtest:
                        # 立即设置为实盘/模拟盘模式
                        self._set_live_mode_properties()
                    return is_backtest
                except Exception as e:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 通过API获取运行模式失败: {str(e)}")
            
            # 方法2: 通过订阅沪深A股检查
            try:
                # 获取沪深A股列表
                sh_symbols = get_instruments(exchanges='SHSE', sec_types=1, fields='symbol', df=True)
                sz_symbols = get_instruments(exchanges='SZSE', sec_types=1, fields='symbol', df=True)
                
                if sh_symbols is not None and not sh_symbols.empty and sz_symbols is not None and not sz_symbols.empty:
                    # 合并沪深A股
                    all_symbols = pd.concat([sh_symbols, sz_symbols])
                    stock_list = all_symbols['symbol'].tolist()
                    
                    # 选取前100只进行测试
                    test_symbols = stock_list[:100]
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试订阅100只A股测试运行模式")
                    
                    # 尝试订阅
                    subscribe(symbols=test_symbols, frequency='60s', count=1)
                    
                    # 如果能订阅100只不同股票，判断为回测模式
                    unsubscribe(symbols=test_symbols)
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 能够订阅大量A股，判断为回测模式")
                    return True
            except Exception as e:
                error_msg = str(e)
                if "订阅代码数量超过用户权限" in error_msg:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - A股订阅受限，判断为实盘/模拟盘模式")
                    
                    # 确保清理所有可能的订阅
                    try:
                        if 'test_symbols' in locals():
                            unsubscribe(symbols=test_symbols)
                    except:
                        pass
                    
                    # 立即设置为实盘/模拟盘模式
                    self._set_live_mode_properties()
                    return False
                else:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - A股订阅测试异常: {error_msg}")
            
            # 方法3: 检查账户属性
            try:
                account = get_account()
                if hasattr(account, 'account_id') and account.account_id:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到账户ID: {account.account_id}，判断为实盘/模拟盘模式")
                    # 立即设置为实盘/模拟盘模式
                    self._set_live_mode_properties()
                    return False
            except Exception as e:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查账户属性异常: {str(e)}")
                
            # 方法4: 检查环境变量或其他特定标志
            try:
                if hasattr(self.context, 'mode') and self.context.mode == 0:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 从context.mode判断为回测模式")
                    return True
                elif hasattr(self.context, 'mode') and self.context.mode in [1, 2]:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 从context.mode判断为实盘/模拟盘模式")
                    # 立即设置为实盘/模拟盘模式
                    self._set_live_mode_properties()
                    return False
            except Exception as e:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检查context.mode异常: {str(e)}")
                
            # 默认假设为实盘/模拟盘模式（更安全的默认值）
            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法确定运行模式，默认设置为实盘/模拟盘模式")
            # 立即设置为实盘/模拟盘模式
            self._set_live_mode_properties()
            return False
            
        except Exception as e:
            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 运行模式检测异常: {str(e)}，默认设置为实盘/模拟盘模式")
            # 立即设置为实盘/模拟盘模式
            self._set_live_mode_properties()
            return False
            
    def _set_live_mode_properties(self):
        """
        设置实盘/模拟盘模式相关属性
        """
        self.is_backtest = False
        self.context.run_mode = 'live'
        # 设置订阅相关属性
        self.subscribe_mode = 'batch'
        self.subscription_rotation_enabled = True
        # 更新最大订阅数
        self.max_subscribe_count = self.get_max_subscribe_count()
        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置为实盘/模拟盘模式，最大订阅数: {self.max_subscribe_count}，使用批次订阅模式")
        
        # 初始化实盘/模拟盘模式
        self.initialize_live_mode()
        
    def initialize_live_mode(self):
        """
        初始化实盘/模拟盘模式的设置
        
        该方法在检测到实盘/模拟盘模式时调用，用于进行必要的初始化设置
        """
        try:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 初始化实盘/模拟盘模式设置")
            
            # 确保批次大小不超过最大订阅限制
            self.live_mode_batch_size = min(self.live_mode_batch_size, self.max_subscribe_count)
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘批次大小设置为: {self.live_mode_batch_size}")
            
            # 确保轮换间隔合理
            if self.subscription_rotation_interval <= 0:
                self.subscription_rotation_interval = 1
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换间隔设置为: {self.subscription_rotation_interval}分钟")
            
            # 优先订阅持仓股票
            if hasattr(self.context, 'positions') and self.context.positions:
                holding_symbols = list(self.context.positions.keys())
                if holding_symbols:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 优先订阅 {len(holding_symbols)} 只持仓股票")
                    try:
                        subscribe(symbols=holding_symbols, frequency='60s', count=5)
                        self.current_subscribed_batch = holding_symbols
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅持仓股票")
                    except Exception as e:
                        self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅持仓股票时发生错误: {str(e)}")
            
            # 初始化股票批次
            index_symbol = getattr(self.context, 'index_symbol', 'SHSE.000300')
            self.initialize_stock_batches(index_symbol, self.live_mode_batch_size)
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 初始化股票批次完成，共 {len(self.stock_batches)} 个批次")
            
            # 设置上次轮换时间为当前时间减去轮换间隔，确保首次检查时能触发轮换
            self.last_rotation_time = self.context.now - datetime.timedelta(minutes=self.subscription_rotation_interval*2)
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 设置初始轮换时间为: {self.last_rotation_time}，确保首次检查时能触发轮换")
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 初始化实盘/模拟盘模式设置异常: {str(e)}")
        
    def get_max_subscribe_count(self):
        """获取最大订阅数量"""
        # 根据运行模式返回不同的订阅数量
        try:
            is_backtest_mode = getattr(self, 'is_backtest', False)
            if is_backtest_mode:
                # 回测模式下使用较大的订阅数量
                max_count = 500
                self.context.log.info(f"回测模式，使用最大订阅数量: {max_count}")
            else:
                # 实盘/模拟盘模式下使用配置中的值或从账户信息获取
                account_info = get_account_info()
                max_count = min(
                    account_info.get('max_subscribe', 50),
                    self._get_config_value('MAX_SUBSCRIPTION_LIMIT', 50)
                )
                self.context.log.info(f"实盘/模拟盘模式，使用最大订阅数量: {max_count}")
        except Exception as e:
            # 如果发生任何错误，使用安全的默认值
            max_count = 50
            self.context.log.warning(f"获取最大订阅数量时发生错误: {str(e)}，使用默认值: {max_count}")
            
        return max_count
        
    def filter_stocks(self, stock_list):
        """
        根据配置的过滤条件过滤股票列表
        
        参数:
        - stock_list: 要过滤的股票列表
        
        返回:
        - 过滤后的股票列表
        """
        try:
            if not stock_list:
                return []
                
            filtered_list = stock_list.copy()
            original_count = len(filtered_list)
            filter_stats = {
                'st_filtered': 0,
                'startup_filtered': 0,
                'science_filtered': 0,
                'beijing_filtered': 0,
                'price_filtered': 0
            }
            
            # 过滤ST股票
            if self.filter_st_stocks:
                # 获取股票名称
                try:
                    stock_info = get_instruments(symbols=filtered_list, df=True)
                    if stock_info is not None and not stock_info.empty:
                        st_stocks = stock_info[stock_info['sec_name'].str.contains('ST|st|S|s\\*t|S\\*T|退', na=False)]['symbol'].tolist()
                        filter_stats['st_filtered'] = len(st_stocks)
                        filtered_list = [s for s in filtered_list if s not in st_stocks]
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤掉 {len(st_stocks)} 只ST股票")
                except Exception as e:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤ST股票异常: {str(e)}")
            
            # 过滤创业板股票 (股票代码以3开头)
            if self.filter_startup_board:
                startup_stocks = [s for s in filtered_list if s.startswith('SZSE.3')]
                filter_stats['startup_filtered'] = len(startup_stocks)
                filtered_list = [s for s in filtered_list if not s.startswith('SZSE.3')]
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤掉 {len(startup_stocks)} 只创业板股票")
            
            # 过滤科创板股票 (股票代码以688开头)
            if self.filter_science_board:
                science_stocks = [s for s in filtered_list if s.startswith('SHSE.688')]
                filter_stats['science_filtered'] = len(science_stocks)
                filtered_list = [s for s in filtered_list if not s.startswith('SHSE.688')]
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤掉 {len(science_stocks)} 只科创板股票")
            
            # 过滤北交所股票 (股票代码以8开头)
            if self.filter_beijing_board:
                beijing_stocks = [s for s in filtered_list if s.startswith('SHSE.8') or s.startswith('SZSE.8')]
                filter_stats['beijing_filtered'] = len(beijing_stocks)
                filtered_list = [s for s in filtered_list if not (s.startswith('SHSE.8') or s.startswith('SZSE.8'))]
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤掉 {len(beijing_stocks)} 只北交所股票")
            
            # 价格过滤
            if self.price_filter_enabled:
                try:
                    # 获取当前价格
                    current_prices = current(symbols=filtered_list)
                    if current_prices:
                        price_dict = {item['symbol']: item['price'] for item in current_prices}
                        price_filtered_stocks = [s for s in filtered_list if s in price_dict and (price_dict[s] < self.min_price_filter or price_dict[s] > self.max_price_filter)]
                        filter_stats['price_filtered'] = len(price_filtered_stocks)
                        filtered_list = [s for s in filtered_list if s not in price_filtered_stocks]
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 过滤掉 {len(price_filtered_stocks)} 只价格不符合条件的股票")
                except Exception as e:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 价格过滤异常: {str(e)}")
            
            # 记录过滤统计信息
            total_filtered = original_count - len(filtered_list)
            self.context.log.info(f"""
            ========== 股票过滤统计 ==========
            原始股票数量: {original_count}
            过滤后股票数量: {len(filtered_list)}
            总过滤数量: {total_filtered}
            
            ST股票过滤: {filter_stats['st_filtered']}
            创业板过滤: {filter_stats['startup_filtered']}
            科创板过滤: {filter_stats['science_filtered']}
            北交所过滤: {filter_stats['beijing_filtered']}
            价格过滤: {filter_stats['price_filtered']}
            ===================================
            """)
            
            return filtered_list
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票过滤异常: {str(e)}")
            return stock_list  # 发生异常时返回原始列表
    
    def get_all_constituents(self, index_symbol):
        """获取指数所有成分股"""
        try:
            constituents = stk_get_index_constituents(index=index_symbol)
            if constituents is None or constituents.empty:
                self.context.log.error("获取成分股失败")
                return []
            
            # 获取成分股列表
            stock_list = constituents['symbol'].tolist()
            
            # 应用股票过滤
            filtered_stock_list = self.filter_stocks(stock_list)
            
            self.all_constituents = filtered_stock_list
            return filtered_stock_list
        except Exception as e:
            self.context.log.error(f"获取成分股失败: {str(e)}")
            return []
    
    def get_all_stocks(self):
        """
        获取全市场股票列表
        
        返回:
        - 所有A股股票代码列表
        """
        try:
            # 获取上证所有股票
            sh_symbols = get_instruments(exchanges='SHSE', sec_types=1, fields='symbol', df=True)
            # 获取深证所有股票
            sz_symbols = get_instruments(exchanges='SZSE', sec_types=1, fields='symbol', df=True)
            
            # 合并上证和深证股票
            if sh_symbols is not None and not sh_symbols.empty and sz_symbols is not None and not sz_symbols.empty:
                all_symbols = pd.concat([sh_symbols, sz_symbols])
                stock_list = all_symbols['symbol'].tolist()
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功获取全市场股票，共{len(stock_list)}只")
                
                # 应用股票过滤
                filtered_stock_list = self.filter_stocks(stock_list)
                
                return filtered_stock_list
            else:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取全市场股票失败，返回空列表")
                return []
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取全市场股票异常: {str(e)}")
            return []
    
    def initialize_stock_batches(self, index_symbol, batch_size=None):
        """初始化股票批次"""
        try:
            # 如果未指定批次大小，使用配置中的值
            if batch_size is None:
                batch_size = self.live_mode_batch_size
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用配置中的批次大小: {batch_size}")
            else:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 使用传入的批次大小: {batch_size}")
                
            # 记录配置参数
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前配置: live_mode_batch_size={self.live_mode_batch_size}, max_subscription_per_batch={self.max_subscription_per_batch}")
                
            # 获取所有成分股
            if not self.all_constituents:
                self.all_constituents = self.get_all_constituents(index_symbol)
                
            if not self.all_constituents:
                self.context.log.error("没有可用的成分股，无法初始化批次")
                return False
                
            # 获取当前持仓
            positions = self.context.account().positions()
            holding_symbols = [p['symbol'] for p in positions] if positions else []
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前持仓数量: {len(holding_symbols)}")
            
            # 从所有成分股中排除已持仓的股票
            available_stocks = [s for s in self.all_constituents if s not in holding_symbols]
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 可用股票数量: {len(available_stocks)}")
            
            # 计算每批次的股票数量
            original_batch_size = batch_size
            batch_size = min(batch_size, self.max_subscription_per_batch)
            if original_batch_size != batch_size:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 批次大小已调整: {original_batch_size} -> {batch_size} (受max_subscription_per_batch限制)")
            
            # 计算可用于轮换的槽位数量
            available_slots = batch_size - len(holding_symbols)
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 每批次可用槽位数: {available_slots} (批次大小{batch_size} - 持仓数{len(holding_symbols)})")
            
            if available_slots <= 0:
                self.context.log.warning(f"持仓数量({len(holding_symbols)})已占满或超过批次大小({batch_size})，只能订阅持仓股票")
                self.stock_batches = [holding_symbols[:batch_size]]
                return True
                
            # 将可用股票分成多个批次
            num_batches = max(1, len(available_stocks) // available_slots)
            self.stock_batches = []
            
            for i in range(num_batches):
                start_idx = i * available_slots
                end_idx = min((i + 1) * available_slots, len(available_stocks))
                batch = holding_symbols + available_stocks[start_idx:end_idx]
                self.stock_batches.append(batch)
                
            # 记录批次详情
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功初始化{len(self.stock_batches)}个股票批次，每批次包含约{batch_size}只股票")
            for i, batch in enumerate(self.stock_batches):
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 批次{i+1}: {len(batch)}只股票")
                
            return True
            
        except Exception as e:
            self.context.log.error(f"初始化股票批次失败: {str(e)}")
            import traceback
            self.context.log.error(f"异常堆栈: {traceback.format_exc()}")
            return False
        
    def subscribe_constituents(self, index_symbol='SHSE.000300'):
        """
        订阅指数成分股行情
        
        参数:
        - index_symbol: 指数代码，默认为沪深300
        
        返回:
        - bool: 是否成功订阅
        """
        try:
            # 检查是否在交易时间段
            if not self._is_trading_hour_or_backtest():
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过订阅操作")
                return False
            
            # 获取模式自适应设置
            mode_adaptive_setting = self._get_config_value('ENABLE_MODE_ADAPTIVE', 0)
            
            # 检查是否是强制回测模式且已经订阅过
            if mode_adaptive_setting == 1 and hasattr(self.context, 'subscribed_today') and self.context.subscribed_today:
                # 检查是否是同一天的订阅
                if hasattr(self.context, 'last_subscription_date') and self.context.last_subscription_date == self.context.now.date():
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下已经订阅过，跳过重复订阅")
                    return True
            
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始订阅 {index_symbol} 成分股")
            
            # 更新订阅状态
            if hasattr(self.context, 'subscribed_today'):
                self.context.subscribed_today = True
                self.context.last_subscription_date = self.context.now.date()
                self.context.subscription_errors = 0
                self.context.subscription_error_cooldown = False
            
            # 获取成分股列表
            if index_symbol == 'ALL':
                # 全市场股票
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取全市场股票列表")
                all_stocks = self.get_all_stocks()
                self.constituents = all_stocks
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取到 {len(all_stocks)} 只全市场股票")
            else:
                # 指数成分股
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取 {index_symbol} 成分股列表")
                constituents = self.get_all_constituents(index_symbol)
                if constituents:
                    self.constituents = constituents
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取到 {len(constituents)} 只 {index_symbol} 成分股")
                else:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取 {index_symbol} 成分股失败")
                    return False
            
            # 检查是否是回测模式且启用了全量订阅
            is_backtest_full_subscription = (self.context.run_mode == 'backtest' and self.backtest_full_subscription)
            
            # 根据模式自适应设置选择订阅方式
            if mode_adaptive_setting == 1:
                # 强制使用回测模式订阅策略 - 全量订阅
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用回测模式订阅策略，执行全量订阅")
                result = self._subscribe_full()
                
                # 在强制回测模式下，记录全局订阅状态，避免后续重复订阅
                if result and hasattr(self.context, 'subscribed_today'):
                    self.context.subscribed_today = True
                    self.context.last_subscription_date = self.context.now.date()
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制回测模式下已完成全量订阅，后续将跳过重复订阅")
                
                return result
            elif mode_adaptive_setting == 2:
                # 强制使用模拟盘模式订阅策略 - 批次订阅，较大批次
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用模拟盘模式订阅策略，执行批次订阅（较大批次）")
                self.subscription_rotation_enabled = True
                self.subscribe_mode = 'batch'
                # 初始化股票批次，使用较大批次
                self.initialize_stock_batches(index_symbol, 100)
                # 执行第一次轮换订阅
                return self.rotate_subscription()
            elif mode_adaptive_setting == 3:
                # 强制使用实盘模式订阅策略 - 批次订阅，较小批次
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 强制使用实盘模式订阅策略，执行批次订阅（较小批次）")
                self.subscription_rotation_enabled = True
                self.subscribe_mode = 'batch'
                # 初始化股票批次，使用较小批次
                self.initialize_stock_batches(index_symbol, 50)
                # 执行第一次轮换订阅
                return self.rotate_subscription()
            
            # 自动模式（mode_adaptive_setting == 0）或其他情况下，使用原有逻辑
            # 根据运行模式和配置选择订阅方式
            elif is_backtest_full_subscription:
                # 回测模式下一次性订阅所有股票
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式全量订阅，跳过批次初始化")
                return self._subscribe_full()
            elif not self.is_backtest:
                # 实盘/模拟盘模式下直接使用批次订阅
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，直接使用批次订阅")
                # 确保启用轮换订阅
                self.subscription_rotation_enabled = True
                self.subscribe_mode = 'batch'
                # 初始化股票批次
                self.initialize_stock_batches(index_symbol)
                # 执行第一次轮换订阅
                return self.rotate_subscription()
            elif self.subscription_rotation_enabled:
                # 使用轮换订阅模式
                # 初始化股票批次
                self.initialize_stock_batches(index_symbol)
                # 执行第一次轮换订阅
                return self.rotate_subscription()
            else:
                # 不使用轮换，一次性订阅
                return self._subscribe_full()
            
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅成分股异常: {str(e)}")
            return False
    
    def _subscribe_full(self):
        """
        一次性订阅所有成分股
        
        修改说明:
        1. 在订阅失败时不再自动减小批次大小
        2. 使用配置中的原始批次大小进行重试
        3. 在实盘/模拟盘模式下直接使用批次订阅，避免不必要的全量订阅尝试
        
        返回:
        - bool: 是否成功订阅
        """
        try:
            # 检查是否在交易时间段
            if not self._is_trading_hour_or_backtest():
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过全量订阅操作")
                return False
            
            # 如果是实盘/模拟盘模式，直接使用批次订阅
            if not self.is_backtest:
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 实盘/模拟盘模式，跳过全量订阅，直接使用批次订阅")
                self.subscribe_mode = 'batch'
                self.subscription_rotation_enabled = True
                
                # 初始化批次
                self.initialize_stock_batches(self.context.index_symbol)
                
                # 执行第一次轮换订阅
                return self.rotate_subscription()
            
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始一次性订阅所有成分股")
            
            # 更新订阅状态
            if hasattr(self.context, 'subscribed_today'):
                self.context.subscribed_today = True
                self.context.last_subscription_date = self.context.now.date()
            
            # 检查成分股列表是否存在
            if not self.constituents:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成分股列表为空，无法订阅")
                return False
            
            # 订阅所有成分股
            try:
                subscribe(symbols=self.constituents, frequency='60s', count=5)
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅所有 {len(self.constituents)} 只成分股")
                return True
            except Exception as e:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅所有成分股时发生异常: {str(e)}")
                
                # 订阅失败时，尝试使用批次订阅
                try:
                    # 使用配置中的批次大小，不自动减小
                    batch_size = self.live_mode_batch_size
                    
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 全量订阅失败，切换到批次订阅模式")
                    self.subscribe_mode = 'batch'
                    self.subscription_rotation_enabled = True
                    
                    # 初始化批次
                    self.initialize_stock_batches(self.context.index_symbol, batch_size)
                    
                    # 执行第一次轮换订阅
                    return self.rotate_subscription()
                except Exception as e2:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 切换到批次订阅模式也失败: {str(e2)}")
                    return False
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 一次性订阅所有成分股异常: {str(e)}")
            return False
    
    def check_rotation_time(self):
        """检查是否需要进行订阅轮换"""
        # 如果未启用订阅轮换，不进行轮换
        if not self.subscription_rotation_enabled:
            return False
            
        # 如果是回测模式且启用了全量订阅，不进行轮换
        if hasattr(self.context, 'run_mode') and self.context.run_mode == 'backtest' and self.backtest_full_subscription:
            return False
            
        # 如果是首次轮换或已经到达轮换时间
        if self.last_rotation_time is None:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 首次轮换检查，需要执行轮换")
            return True
            
        # 计算距离上次轮换的时间（分钟）
        elapsed_minutes = (self.context.now - self.last_rotation_time).total_seconds() / 60
        
        # 如果已经过了轮换间隔，需要进行轮换
        should_rotate = elapsed_minutes >= self.subscription_rotation_interval
        
        if should_rotate:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已过{elapsed_minutes:.1f}分钟，超过设定的{self.subscription_rotation_interval}分钟间隔，需要执行轮换")
        
        return should_rotate
    
    def get_current_batch(self):
        """获取当前批次的股票"""
        if not self.stock_batches:
            return []
            
        return self.stock_batches[self.current_batch_index]
    
    def rotate_subscription(self, index_symbol=None):
        """
        轮换订阅批次
        
        修改说明：
        1. 在订阅失败时不再自动减小批次大小
        2. 使用配置中的原始批次大小进行重试
        3. 优化持仓股票的订阅逻辑，确保持仓股票能够被优先订阅
        4. 支持BACKTEST_SUBSCRIBE_ONCE参数，回测模式下只订阅一次
        
        参数:
        - index_symbol: 指数代码，如果为None则使用当前批次
        
        返回:
        - bool: 是否成功轮换
        """
        try:
            # 检查是否在交易时间段
            if not self._is_trading_hour_or_backtest():
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过轮换订阅操作")
                return False
            
            # 检查回测模式下是否启用了只订阅一次的功能
            if self.context.run_mode == 'backtest':
                backtest_subscribe_once = self._get_config_value('BACKTEST_SUBSCRIBE_ONCE', True)
                if backtest_subscribe_once and hasattr(self.context, 'subscribed_today') and self.context.subscribed_today:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 回测模式已启用BACKTEST_SUBSCRIBE_ONCE，跳过轮换订阅")
                    return True  # 返回True以保持策略的正常运行
            
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始轮换订阅批次")
            
            # 更新订阅状态
            if hasattr(self.context, 'subscribed_today'):
                self.context.subscribed_today = True
                self.context.last_subscription_date = self.context.now.date()
            
            # 如果指定了新的指数，重新初始化批次
            if index_symbol is not None:
                self.initialize_stock_batches(index_symbol)
            
            # 检查是否有批次可用
            if not hasattr(self, 'stock_batches') or not self.stock_batches:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 没有可用的股票批次，尝试重新初始化")
                self.initialize_stock_batches(self.context.index_symbol)
                if not hasattr(self, 'stock_batches') or not self.stock_batches:
                    self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 无法初始化股票批次")
                    return False
            
            # 更新当前批次索引
            if not hasattr(self, 'current_batch_index'):
                self.current_batch_index = 0
            else:
                self.current_batch_index = (self.current_batch_index + 1) % len(self.stock_batches)
            
            # 获取当前批次
            current_batch = self.stock_batches[self.current_batch_index]
            
            # 记录当前批次信息
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前轮换到第 {self.current_batch_index + 1}/{len(self.stock_batches)} 批，包含 {len(current_batch)} 只股票")
            
            # 更新最近轮换时间
            self.last_rotation_time = self.context.now
            
            # 先取消之前的订阅，但保留持仓股票的订阅
            try:
                # 获取持仓股票列表
                holding_symbols = []
                if hasattr(self.context, 'positions') and self.context.positions:
                    holding_symbols = list(self.context.positions.keys())
                
                # 取消非持仓股票的订阅
                if hasattr(self, 'current_subscribed_batch') and self.current_subscribed_batch:
                    # 过滤出需要取消订阅的股票（非持仓股票）
                    to_unsubscribe = [s for s in self.current_subscribed_batch if s not in holding_symbols]
                    if to_unsubscribe:
                        unsubscribe(symbols=to_unsubscribe)
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 已取消 {len(to_unsubscribe)} 只非持仓股票的订阅")
            except Exception as e:
                self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 取消上一批次订阅时发生错误: {str(e)}")
            
            # 订阅新批次
            try:
                # 优先订阅持仓股票
                if hasattr(self.context, 'positions') and self.context.positions:
                    holding_symbols = list(self.context.positions.keys())
                    if holding_symbols:
                        self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 优先订阅 {len(holding_symbols)} 只持仓股票")
                        try:
                            subscribe(symbols=holding_symbols, frequency='60s', count=5)
                            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅持仓股票")
                        except Exception as e:
                            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅持仓股票时发生错误: {str(e)}")
                
                # 计算剩余可订阅数量
                subscribed_count = len(holding_symbols) if holding_symbols else 0
                remaining_slots = self.max_subscribe_count - subscribed_count
                
                # 从当前批次中过滤掉已经订阅的持仓股票
                filtered_batch = [s for s in current_batch if s not in holding_symbols]
                
                # 如果剩余槽位不足以订阅整个批次，则截取部分
                if remaining_slots > 0 and filtered_batch:
                    batch_to_subscribe = filtered_batch[:remaining_slots]
                    if batch_to_subscribe:
                        try:
                            subscribe(symbols=batch_to_subscribe, frequency='60s', count=5)
                            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅当前批次中 {len(batch_to_subscribe)} 只非持仓股票")
                        except Exception as e:
                            self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅当前批次非持仓股票时发生错误: {str(e)}")
                else:
                    self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 没有剩余订阅槽位，跳过非持仓股票订阅")
                
                # 更新当前已订阅批次
                self.current_subscribed_batch = (holding_symbols if holding_symbols else []) + (batch_to_subscribe if 'batch_to_subscribe' in locals() else [])
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前共订阅 {len(self.current_subscribed_batch)} 只股票")
                
                return True
            except Exception as e:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅批次时发生异常: {str(e)}")
                return False
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 轮换订阅批次异常: {str(e)}")
            return False
    
    def get_history_data(self, symbol, frequency, count, fields):
        """获取历史数据"""
        try:
            # 如果传入的count参数为None或小于等于0，则使用配置文件中的默认值
            if count is None or count <= 0:
                # 根据用途选择不同的默认天数
                if isinstance(fields, str) and ('volatility' in fields.lower() or any(f in fields.lower() for f in ['atr', 'tr'])):
                    # 用于波动性计算的历史数据
                    count = self._get_config_value('HISTORY_DATA_DAYS_VOLATILITY', 60)
                else:
                    # 用于一般技术指标的历史数据
                    count = self._get_config_value('HISTORY_DATA_DAYS', 50)
            
            # 处理fields参数，确保它是正确的格式
            if isinstance(fields, list):
                # 如果已经是列表，直接使用
                fields_param = fields
                # 为日志记录转换为字符串
                fields_str = ','.join(fields)
            else:
                # 如果是字符串，检查是否需要分割
                fields_str = fields
                if isinstance(fields, str) and ',' in fields:
                    fields_param = [f.strip() for f in fields.split(',')]
                else:
                    fields_param = [fields] if isinstance(fields, str) else fields
            
            # 获取历史数据
            hist_data = history_n(
                symbol=symbol,
                frequency=frequency,
                count=count,
                fields=fields_param,  # 使用处理后的fields参数
                skip_suspended=True,
                fill_missing='Last',
                adjust=ADJUST_PREV,
                df=True
            )
            
            # 验证并修复返回的数据格式
            if hist_data is not None and not hist_data.empty:
                # 检查是否有合并的列名
                merged_columns = [col for col in hist_data.columns if isinstance(col, str) and ',' in col]
                if merged_columns:
                    self.context.log.warning(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 检测到 {symbol} 的历史数据有合并列名: {merged_columns}")
                    
                    # 尝试修复合并的列名
                    for merged_col in merged_columns:
                        # 分割合并的列名
                        split_cols = [c.strip() for c in merged_col.split(',')]
                        
                        # 如果数据是一列，但列名包含多个字段，尝试分割数据
                        if len(hist_data.columns) == 1:
                            # 检查第一行数据是否为字符串且包含逗号
                            first_row = hist_data.iloc[0, 0]
                            if isinstance(first_row, str) and ',' in first_row:
                                # 数据也是逗号分隔的，需要完全重新解析
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 尝试重新解析 {symbol} 的历史数据")
                                
                                # 将每行数据分割成多列
                                new_data = []
                                for i in range(len(hist_data)):
                                    row_values = hist_data.iloc[i, 0].split(',')
                                    if len(row_values) == len(split_cols):
                                        new_data.append([v.strip() for v in row_values])
                                
                                # 创建新的DataFrame
                                hist_data = pd.DataFrame(new_data, columns=split_cols)
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功重新解析 {symbol} 的历史数据")
                            else:
                                # 只有列名有问题，数据本身格式正确
                                # 重命名列
                                hist_data.columns = split_cols
                                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功修复 {symbol} 的历史数据列名")
            
            return hist_data
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 获取{symbol}的{frequency}历史数据异常: {str(e)}")
            return None
            
    def refresh_max_subscribe_count(self):
        """刷新最大订阅数量（可在运行过程中调用）"""
        self.max_subscribe_count = self.get_max_subscribe_count()
        return self.max_subscribe_count
        
    def _get_config_value(self, param_name, default=None):
        """
        从context获取配置参数值，支持动态配置和静态配置
        """
        # 优先从context的get_config_value方法获取
        if hasattr(self.context, 'get_config_value'):
            return self.context.get_config_value(param_name, default)
        
        # 其次从context.config获取
        elif hasattr(self.context, 'config') and hasattr(self.context.config, param_name):
            return getattr(self.context.config, param_name)
        
        # 最后返回默认值
        return default

    def _is_trading_hour_or_backtest(self):
        """
        检查当前是否在交易时间段或回测模式
        
        修改说明:
        1. 在股票池为空的情况下忽略交易时间判断，直接返回True
        2. 确保在需要订阅时能够执行轮换订阅操作
        
        返回:
        - bool: 是否在交易时间段或回测模式或股票池为空
        """
        # 回测模式下始终返回True
        if self.context.run_mode == 'backtest':
            return True
            
        # 检查是否股票池为空，如果为空则忽略交易时间判断，直接返回True
        if not hasattr(self, 'constituents') or not self.constituents:
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 股票池为空，忽略交易时间判断")
            return True
        
        # 实盘/模拟盘模式下检查是否在交易时间段
        try:
            from main import is_trading_hour
            return is_trading_hour(self.context.now)
        except ImportError:
            # 如果无法导入is_trading_hour函数，使用简单的时间判断
            current_time = self.context.now.time()
            morning_session = (datetime.time(9, 30) <= current_time <= datetime.time(11, 30))
            afternoon_session = (datetime.time(13, 0) <= current_time <= datetime.time(15, 0))
            return morning_session or afternoon_session

    def _subscribe_batch(self, batch):
        """
        订阅一个批次的股票
        
        参数:
        - batch: 股票列表
        
        返回:
        - bool: 是否成功订阅
        """
        try:
            # 检查是否在交易时间段
            if not self._is_trading_hour_or_backtest():
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 当前不在交易时间段，跳过批次订阅操作")
                return False
            
            self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 开始订阅 {len(batch)} 只股票的批次")
            
            # 更新订阅状态
            if hasattr(self.context, 'subscribed_today'):
                self.context.subscribed_today = True
                self.context.last_subscription_date = self.context.now.date()
            
            # 订阅批次
            try:
                subscribe(symbols=batch, frequency='60s', count=5)
                self.context.log.info(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 成功订阅 {len(batch)} 只股票的批次")
                return True
            except Exception as e:
                self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅批次时发生异常: {str(e)}")
                return False
        except Exception as e:
            self.context.log.error(f"{self.context.now.strftime('%Y-%m-%d %H:%M:%S')} - 订阅批次异常: {str(e)}")
            return False
