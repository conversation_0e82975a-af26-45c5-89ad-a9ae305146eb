# coding=utf-8
"""
当前优化进程分析
验证回退配置后的胜率和收益，挖掘有效指标因子
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import itertools

def analyze_current_performance_after_rollback():
    """分析回退配置后的当前表现"""
    print('📊 回退配置后表现分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取最新的交易数据
        query = """
        SELECT * FROM trades 
        WHERE action = 'SELL' 
        AND net_profit_pct_sell IS NOT NULL
        ORDER BY timestamp DESC 
        LIMIT 2000
        """
        
        sell_df = pd.read_sql_query(query, conn)
        
        # 获取买入数据
        buy_query = """
        SELECT * FROM trades 
        WHERE action = 'BUY'
        ORDER BY timestamp DESC 
        LIMIT 3000
        """
        
        buy_df = pd.read_sql_query(buy_query, conn)
        conn.close()
        
        print(f'📈 卖出记录: {len(sell_df)} 条')
        print(f'📈 买入记录: {len(buy_df)} 条')
        
        # 转换时间戳
        sell_df['timestamp'] = pd.to_datetime(sell_df['timestamp'])
        buy_df['timestamp'] = pd.to_datetime(buy_df['timestamp'])
        
        # 分析不同时间段的表现
        segments = [50, 100, 200, 500, 1000]
        
        print(f'\n📊 当前表现分段分析:')
        print(f'样本数    胜率%    平均收益%   中位收益%   最大收益%   最小收益%')
        print(f'-' * 70)
        
        segment_results = {}
        
        for segment_size in segments:
            if segment_size > len(sell_df):
                segment_size = len(sell_df)
            
            segment_data = sell_df.head(segment_size)
            win_rate = (segment_data['net_profit_pct_sell'] > 0).mean() * 100
            avg_profit = segment_data['net_profit_pct_sell'].mean()
            median_profit = segment_data['net_profit_pct_sell'].median()
            max_profit = segment_data['net_profit_pct_sell'].max()
            min_profit = segment_data['net_profit_pct_sell'].min()
            
            segment_results[segment_size] = {
                'win_rate': win_rate,
                'avg_profit': avg_profit,
                'median_profit': median_profit,
                'max_profit': max_profit,
                'min_profit': min_profit,
                'data': segment_data
            }
            
            print(f'{segment_size:6d}   {win_rate:6.1f}   {avg_profit:8.2f}   {median_profit:8.2f}   {max_profit:8.2f}   {min_profit:8.2f}')
        
        # 分析趋势
        print(f'\n📈 表现趋势分析:')
        recent_50 = segment_results[50]['win_rate']
        recent_200 = segment_results[200]['win_rate']
        recent_500 = segment_results[500]['win_rate']
        
        print(f'   最新50条 vs 最新200条: {recent_50 - recent_200:+.1f}%')
        print(f'   最新200条 vs 最新500条: {recent_200 - recent_500:+.1f}%')
        
        if recent_50 > recent_200 > recent_500:
            trend = 'IMPROVING'
            print(f'   📈 趋势: 持续改善')
        elif recent_50 > recent_200:
            trend = 'RECENT_IMPROVEMENT'
            print(f'   📊 趋势: 近期改善')
        elif recent_50 < recent_200:
            trend = 'RECENT_DECLINE'
            print(f'   📉 趋势: 近期下降')
        else:
            trend = 'STABLE'
            print(f'   📊 趋势: 相对稳定')
        
        return segment_results, buy_df, trend
        
    except Exception as e:
        print(f'❌ 数据获取失败: {e}')
        return None, None, None

def analyze_signal_quantity_recovery(buy_df):
    """分析信号数量恢复情况"""
    print(f'\n📊 信号数量恢复分析')
    print('=' * 60)
    
    # 分析最新买入记录的因子分布
    latest_1000 = buy_df.head(1000)
    
    print(f'📈 分析最新 {len(latest_1000)} 条买入信号')
    
    # 检查各因子的分布和配置符合情况
    factors_analysis = {}
    
    # CCI分析 [0, 100]
    if 'cci' in latest_1000.columns:
        cci_data = latest_1000['cci'].dropna()
        cci_in_range = latest_1000[(latest_1000['cci'] >= 0) & (latest_1000['cci'] <= 100)]['cci'].count()
        cci_stats = {
            'total': len(cci_data),
            'in_range': cci_in_range,
            'in_range_pct': cci_in_range / len(cci_data) * 100 if len(cci_data) > 0 else 0,
            'mean': cci_data.mean(),
            'median': cci_data.median(),
            'std': cci_data.std()
        }
        factors_analysis['cci'] = cci_stats
        print(f'   CCI [0,100]: {cci_in_range}条 ({cci_stats["in_range_pct"]:.1f}%) | 均值:{cci_stats["mean"]:.1f}')
    
    # ATR分析 > 1.5%
    if 'atr_pct' in latest_1000.columns:
        atr_data = latest_1000['atr_pct'].dropna()
        atr_above_threshold = latest_1000[latest_1000['atr_pct'] > 1.5]['atr_pct'].count()
        atr_stats = {
            'total': len(atr_data),
            'above_threshold': atr_above_threshold,
            'above_threshold_pct': atr_above_threshold / len(atr_data) * 100 if len(atr_data) > 0 else 0,
            'mean': atr_data.mean(),
            'median': atr_data.median(),
            'std': atr_data.std()
        }
        factors_analysis['atr_pct'] = atr_stats
        print(f'   ATR >1.5%: {atr_above_threshold}条 ({atr_stats["above_threshold_pct"]:.1f}%) | 均值:{atr_stats["mean"]:.1f}%')
    
    # 其他因子分析
    other_factors = ['rsi', 'adx', 'macd_hist', 'bb_width', 'overall_score']
    
    for factor in other_factors:
        if factor in latest_1000.columns:
            factor_data = latest_1000[factor].dropna()
            if len(factor_data) > 0:
                factor_stats = {
                    'total': len(factor_data),
                    'mean': factor_data.mean(),
                    'median': factor_data.median(),
                    'std': factor_data.std(),
                    'min': factor_data.min(),
                    'max': factor_data.max()
                }
                factors_analysis[factor] = factor_stats
                print(f'   {factor.upper()}: 均值{factor_stats["mean"]:.2f} | 中位{factor_stats["median"]:.2f} | 范围[{factor_stats["min"]:.2f}, {factor_stats["max"]:.2f}]')
    
    return factors_analysis

def deep_mine_effective_factors(segment_results, buy_df):
    """深度挖掘有效因子"""
    print(f'\n🔍 深度挖掘有效因子')
    print('=' * 60)
    
    # 使用最新500条数据进行分析
    latest_500_sells = segment_results[500]['data']
    
    # 匹配买入数据
    matched_trades = []
    
    for _, sell_row in latest_500_sells.iterrows():
        symbol = sell_row['symbol']
        sell_time = sell_row['timestamp']
        
        # 找对应的买入记录
        symbol_buys = buy_df[buy_df['symbol'] == symbol].copy()
        if len(symbol_buys) > 0:
            symbol_buys['timestamp'] = pd.to_datetime(symbol_buys['timestamp'])
            valid_buys = symbol_buys[symbol_buys['timestamp'] < sell_time]
            
            if len(valid_buys) > 0:
                recent_buy = valid_buys.loc[valid_buys['timestamp'].idxmax()]
                
                matched_trades.append({
                    'symbol': symbol,
                    'profit': sell_row['net_profit_pct_sell'],
                    'profitable': sell_row['net_profit_pct_sell'] > 0,
                    'cci': recent_buy.get('cci'),
                    'rsi': recent_buy.get('rsi'),
                    'adx': recent_buy.get('adx'),
                    'macd_hist': recent_buy.get('macd_hist'),
                    'atr_pct': recent_buy.get('atr_pct'),
                    'bb_width': recent_buy.get('bb_width'),
                    'overall_score': recent_buy.get('overall_score'),
                    'bb_position': recent_buy.get('bb_position'),
                })
    
    matched_df = pd.DataFrame(matched_trades)
    print(f'📊 成功匹配: {len(matched_df)} 条交易')
    
    if len(matched_df) == 0:
        print('⚠️ 没有匹配的交易数据')
        return None
    
    # 分析盈利vs亏损交易的因子特征
    profitable_trades = matched_df[matched_df['profitable'] == True]
    losing_trades = matched_df[matched_df['profitable'] == False]
    
    current_win_rate = len(profitable_trades) / len(matched_df) * 100
    
    print(f'\n📈 交易分布:')
    print(f'   盈利交易: {len(profitable_trades)} 条 ({len(profitable_trades)/len(matched_df)*100:.1f}%)')
    print(f'   亏损交易: {len(losing_trades)} 条 ({len(losing_trades)/len(matched_df)*100:.1f}%)')
    print(f'   当前胜率: {current_win_rate:.1f}%')
    
    # 分析各因子在盈利交易中的特征
    factors = ['cci', 'rsi', 'adx', 'macd_hist', 'atr_pct', 'bb_width', 'overall_score']
    
    print(f'\n🔍 因子有效性分析:')
    print(f'因子        盈利均值    亏损均值    差异      t统计量   有效性评分')
    print(f'-' * 70)
    
    factor_effectiveness = {}
    
    for factor in factors:
        if factor in matched_df.columns:
            profit_data = profitable_trades[factor].dropna()
            loss_data = losing_trades[factor].dropna()
            
            if len(profit_data) >= 10 and len(loss_data) >= 10:
                profit_mean = profit_data.mean()
                loss_mean = loss_data.mean()
                difference = profit_mean - loss_mean
                
                # 计算t统计量
                from scipy import stats
                try:
                    t_stat, p_value = stats.ttest_ind(profit_data, loss_data)
                    effectiveness_score = abs(t_stat) * (1 - p_value) if p_value < 0.1 else 0
                except:
                    t_stat = 0
                    effectiveness_score = 0
                
                factor_effectiveness[factor] = {
                    'profit_mean': profit_mean,
                    'loss_mean': loss_mean,
                    'difference': difference,
                    't_stat': t_stat,
                    'effectiveness_score': effectiveness_score,
                    'profit_samples': len(profit_data),
                    'loss_samples': len(loss_data)
                }
                
                print(f'{factor:<10} {profit_mean:8.2f} {loss_mean:8.2f} {difference:8.2f} {t_stat:8.2f} {effectiveness_score:8.2f}')
    
    return factor_effectiveness, matched_df, current_win_rate

def identify_optimal_factor_ranges(factor_effectiveness, matched_df):
    """识别最优因子范围"""
    print(f'\n🎯 最优因子范围识别')
    print('=' * 60)
    
    # 按有效性评分排序
    sorted_factors = sorted(
        factor_effectiveness.items(),
        key=lambda x: x[1]['effectiveness_score'],
        reverse=True
    )
    
    optimization_opportunities = []
    
    print(f'因子        最优范围        胜率%   样本数  当前配置      建议调整')
    print(f'-' * 80)
    
    for factor, stats in sorted_factors[:5]:  # 分析前5个最有效的因子
        if stats['effectiveness_score'] > 0.5:  # 只分析有效性评分较高的因子
            
            factor_data = matched_df.dropna(subset=[factor])
            if len(factor_data) < 30:
                continue
            
            # 分析不同分位数区间的胜率
            percentiles = [0, 20, 40, 60, 80, 100]
            thresholds = [factor_data[factor].quantile(p/100) for p in percentiles]
            
            best_range = None
            best_win_rate = 0
            
            for i in range(len(thresholds)-1):
                lower = thresholds[i]
                upper = thresholds[i+1]
                
                range_data = factor_data[(factor_data[factor] >= lower) & (factor_data[factor] <= upper)]
                
                if len(range_data) >= 15:
                    win_rate = range_data['profitable'].mean() * 100
                    
                    if win_rate > best_win_rate:
                        best_win_rate = win_rate
                        best_range = {
                            'lower': lower,
                            'upper': upper,
                            'win_rate': win_rate,
                            'sample_count': len(range_data),
                            'percentile_range': f'P{percentiles[i]}-P{percentiles[i+1]}'
                        }
            
            if best_range and best_range['win_rate'] > 50:  # 只考虑胜率>50%的范围
                
                # 生成配置建议
                current_config = "当前配置"
                suggested_config = "建议配置"
                
                if factor == 'cci':
                    current_config = "[0, 100]"
                    if best_range['lower'] > 10:
                        suggested_config = f"[{best_range['lower']:.0f}, {best_range['upper']:.0f}]"
                    else:
                        suggested_config = f"[0, {best_range['upper']:.0f}]"
                
                elif factor == 'rsi':
                    current_config = "无限制"
                    suggested_config = f"[{best_range['lower']:.0f}, {best_range['upper']:.0f}]"
                
                elif factor == 'atr_pct':
                    current_config = "> 1.5%"
                    if best_range['lower'] > 1.5:
                        suggested_config = f"> {best_range['lower']:.1f}%"
                    else:
                        suggested_config = f"[{best_range['lower']:.1f}%, {best_range['upper']:.1f}%]"
                
                elif factor == 'adx':
                    current_config = "无限制"
                    suggested_config = f"[{best_range['lower']:.0f}, {best_range['upper']:.0f}]"
                
                else:
                    suggested_config = f"[{best_range['lower']:.2f}, {best_range['upper']:.2f}]"
                
                optimization_opportunities.append({
                    'factor': factor,
                    'best_range': best_range,
                    'current_config': current_config,
                    'suggested_config': suggested_config,
                    'effectiveness_score': stats['effectiveness_score']
                })
                
                print(f'{factor:<10} {best_range["percentile_range"]:<12} {best_range["win_rate"]:6.1f} {best_range["sample_count"]:6d}  {current_config:<12} {suggested_config}')
    
    return optimization_opportunities

def evaluate_optimization_progress(segment_results, trend):
    """评估优化进程"""
    print(f'\n📊 优化进程评估')
    print('=' * 60)
    
    # 基准数据
    baseline_win_rate = 46.2  # 之前分析的基准
    current_win_rate = segment_results[500]['win_rate']
    recent_win_rate = segment_results[100]['win_rate']
    
    progress_evaluation = f'''
📈 优化进程评估:

🎯 胜率变化:
   基准胜率 (500条历史): {baseline_win_rate:.1f}%
   当前胜率 (500条最新): {current_win_rate:.1f}%
   近期胜率 (100条最新): {recent_win_rate:.1f}%
   
   整体变化: {current_win_rate - baseline_win_rate:+.1f}%
   近期变化: {recent_win_rate - current_win_rate:+.1f}%

📊 收益表现:
   当前平均收益: {segment_results[500]['avg_profit']:.2f}%
   当前中位收益: {segment_results[500]['median_profit']:.2f}%
   近期平均收益: {segment_results[100]['avg_profit']:.2f}%

🔍 趋势分析: {trend}
'''
    
    print(progress_evaluation)
    
    # 评估优化效果
    if current_win_rate >= baseline_win_rate + 2:
        progress_status = 'EXCELLENT'
        print(f'   🏆 优化效果: 优秀 (胜率提升{current_win_rate - baseline_win_rate:.1f}%)')
    elif current_win_rate >= baseline_win_rate + 1:
        progress_status = 'GOOD'
        print(f'   ✅ 优化效果: 良好 (胜率提升{current_win_rate - baseline_win_rate:.1f}%)')
    elif current_win_rate >= baseline_win_rate - 1:
        progress_status = 'STABLE'
        print(f'   📊 优化效果: 稳定 (胜率变化{current_win_rate - baseline_win_rate:+.1f}%)')
    else:
        progress_status = 'DECLINING'
        print(f'   📉 优化效果: 下降 (胜率下降{baseline_win_rate - current_win_rate:.1f}%)')
    
    return progress_status

def generate_next_optimization_recommendations(optimization_opportunities, progress_status):
    """生成下一步优化建议"""
    print(f'\n🚀 下一步优化建议')
    print('=' * 60)
    
    if progress_status in ['EXCELLENT', 'GOOD']:
        print(f'✅ 当前优化进程良好，可以继续推进')
        
        if optimization_opportunities:
            best_opportunity = max(optimization_opportunities, key=lambda x: x['effectiveness_score'])
            
            print(f'\n🎯 推荐优化: {best_opportunity["factor"].upper()}因子')
            print(f'   当前配置: {best_opportunity["current_config"]}')
            print(f'   建议配置: {best_opportunity["suggested_config"]}')
            print(f'   预期胜率: {best_opportunity["best_range"]["win_rate"]:.1f}%')
            print(f'   样本支撑: {best_opportunity["best_range"]["sample_count"]}条')
            print(f'   有效性评分: {best_opportunity["effectiveness_score"]:.2f}')
            
            return best_opportunity
    
    elif progress_status == 'STABLE':
        print(f'📊 当前表现稳定，可以尝试小幅优化')
        
        if optimization_opportunities:
            # 选择风险较低的优化
            safe_opportunities = [op for op in optimization_opportunities if op['best_range']['sample_count'] >= 30]
            if safe_opportunities:
                best_opportunity = max(safe_opportunities, key=lambda x: x['effectiveness_score'])
                print(f'\n🎯 谨慎优化: {best_opportunity["factor"].upper()}因子')
                print(f'   建议: 小幅调整，密切监控')
                return best_opportunity
    
    else:  # DECLINING
        print(f'⚠️ 表现下降，建议暂停优化或回退')
        print(f'   1. 分析下降原因')
        print(f'   2. 考虑回退到更保守的配置')
        print(f'   3. 重新评估优化策略')
    
    return None

def main():
    """主函数"""
    print('🔍 当前优化进程分析')
    print('=' * 60)
    
    print('🎯 目标: 验证回退配置效果，评估优化进程，挖掘有效因子')
    
    # 分析当前表现
    result = analyze_current_performance_after_rollback()
    
    if result[0] is not None:
        segment_results, buy_df, trend = result
        
        # 分析信号数量恢复
        factors_analysis = analyze_signal_quantity_recovery(buy_df)
        
        # 深度挖掘有效因子
        mining_result = deep_mine_effective_factors(segment_results, buy_df)
        
        if mining_result:
            factor_effectiveness, matched_df, current_win_rate = mining_result
            
            # 识别最优因子范围
            optimization_opportunities = identify_optimal_factor_ranges(factor_effectiveness, matched_df)
            
            # 评估优化进程
            progress_status = evaluate_optimization_progress(segment_results, trend)
            
            # 生成下一步建议
            next_recommendation = generate_next_optimization_recommendations(optimization_opportunities, progress_status)
            
            print(f'\n🎯 分析总结')
            print('=' * 40)
            print(f'📊 当前胜率: {current_win_rate:.1f}%')
            print(f'📈 表现趋势: {trend}')
            print(f'🔍 发现 {len(optimization_opportunities)} 个优化机会')
            print(f'⚙️ 优化进程: {progress_status}')
            
            if next_recommendation:
                print(f'🚀 推荐下一步: 优化{next_recommendation["factor"].upper()}因子')
                return next_recommendation
            else:
                print(f'📊 建议: 保持当前配置，继续观察')
                return None
        
        else:
            print('❌ 因子挖掘失败')
            return None
    
    else:
        print('❌ 数据分析失败，请检查数据库')
        return None

if __name__ == '__main__':
    result = main()
    if result:
        print(f'\n💡 立即行动建议: 实施{result["factor"].upper()}因子优化')
