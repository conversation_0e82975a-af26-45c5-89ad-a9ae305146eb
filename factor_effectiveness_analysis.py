# coding=utf-8
"""
深度因子指标分析
分析哪些因子对胜率产生更大影响，评估策略因子分析的合理性
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt

def analyze_factor_effectiveness():
    """分析因子有效性"""
    print('📊 深度因子指标分析')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 获取买入和卖出匹配的交易数据
        query = """
        SELECT 
            b.timestamp as buy_time,
            s.timestamp as sell_time,
            b.symbol,
            s.net_profit_pct_sell,
            b.overall_score, b.technical_score, b.momentum_score, b.volume_score,
            b.volatility_score, b.trend_score, b.buy_signal_strength, b.risk_adjusted_score,
            b.atr_pct, b.bb_width, b.macd_hist, b.rsi, b.trix_buy,
            b.price as buy_price,
            s.price as sell_price,
            s.sell_reason,
            s.holding_hours
        FROM trades b
        JOIN trades s ON b.symbol = s.symbol 
        WHERE b.action = 'BUY' 
        AND s.action = 'SELL'
        AND s.net_profit_pct_sell IS NOT NULL
        AND b.timestamp < s.timestamp
        ORDER BY b.timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f'📈 成功匹配交易: {len(df)} 条')
        
        if len(df) == 0:
            print('⚠️ 没有匹配的交易数据')
            return None
        
        # 创建盈利标识
        df['is_profitable'] = df['net_profit_pct_sell'] > 0
        df['profit_level'] = pd.cut(df['net_profit_pct_sell'], 
                                   bins=[-np.inf, -2, 0, 2, 5, np.inf],
                                   labels=['大亏', '小亏', '小盈', '中盈', '大盈'])
        
        profitable_trades = df[df['is_profitable']]
        losing_trades = df[~df['is_profitable']]
        
        print(f'   盈利交易: {len(profitable_trades)} 条 ({len(profitable_trades)/len(df)*100:.1f}%)')
        print(f'   亏损交易: {len(losing_trades)} 条 ({len(losing_trades)/len(df)*100:.1f}%)')
        
        return df
        
    except Exception as e:
        print(f'❌ 数据分析失败: {e}')
        return None

def analyze_factor_discrimination(df):
    """分析因子区分度"""
    print(f'\n🎯 因子区分度分析')
    print('=' * 50)
    
    # 定义所有因子
    factor_columns = {
        '评分因子': ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                   'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score'],
        '技术指标': ['atr_pct', 'bb_width', 'macd_hist', 'rsi', 'trix_buy']
    }
    
    profitable_trades = df[df['is_profitable']]
    losing_trades = df[~df['is_profitable']]
    
    factor_analysis = {}
    
    print(f'📊 因子区分度排序 (按统计显著性):')
    
    all_factors = []
    
    for category, factors in factor_columns.items():
        print(f'\n🔍 {category}:')
        
        for factor in factors:
            if factor in df.columns:
                profitable_values = profitable_trades[factor].dropna()
                losing_values = losing_trades[factor].dropna()
                
                if len(profitable_values) > 10 and len(losing_values) > 10:
                    # 计算统计量
                    profit_mean = profitable_values.mean()
                    loss_mean = losing_values.mean()
                    diff = profit_mean - loss_mean
                    
                    # 进行t检验
                    t_stat, p_value = stats.ttest_ind(profitable_values, losing_values)
                    
                    # 计算效应量 (Cohen's d)
                    pooled_std = np.sqrt(((len(profitable_values)-1)*profitable_values.var() + 
                                        (len(losing_values)-1)*losing_values.var()) / 
                                       (len(profitable_values)+len(losing_values)-2))
                    cohens_d = abs(diff) / pooled_std if pooled_std > 0 else 0
                    
                    # 计算信息系数 (IC)
                    correlation, _ = stats.pearsonr(df[factor].dropna(), 
                                                  df.loc[df[factor].notna(), 'net_profit_pct_sell'])
                    
                    factor_analysis[factor] = {
                        'profit_mean': profit_mean,
                        'loss_mean': loss_mean,
                        'diff': diff,
                        'p_value': p_value,
                        'cohens_d': cohens_d,
                        'ic': correlation,
                        'category': category
                    }
                    
                    # 显著性标记
                    significance = "🔥极显著" if p_value < 0.001 else "🚀显著" if p_value < 0.01 else "📊一般" if p_value < 0.05 else "🔹微弱"
                    effect_size = "大" if cohens_d > 0.8 else "中" if cohens_d > 0.5 else "小" if cohens_d > 0.2 else "微"
                    direction = "📈" if diff > 0 else "📉"
                    
                    all_factors.append({
                        'factor': factor,
                        'category': category,
                        'diff': diff,
                        'p_value': p_value,
                        'cohens_d': cohens_d,
                        'ic': correlation,
                        'significance': significance,
                        'effect_size': effect_size,
                        'direction': direction
                    })
                    
                    print(f'   {factor}: 盈利{profit_mean:.3f} vs 亏损{loss_mean:.3f} '
                          f'({direction}{diff:+.3f}) [p={p_value:.3f}, d={cohens_d:.2f}, IC={correlation:.3f}] {significance}')
    
    # 按统计显著性排序
    all_factors_df = pd.DataFrame(all_factors)
    if len(all_factors_df) > 0:
        all_factors_df = all_factors_df.sort_values(['p_value', 'cohens_d'], ascending=[True, False])
        
        print(f'\n🏆 最有效因子排序 (前10名):')
        for i, (_, factor_info) in enumerate(all_factors_df.head(10).iterrows(), 1):
            print(f'   {i}. {factor_info["factor"]} ({factor_info["category"]})')
            print(f'      效应: {factor_info["direction"]}{factor_info["diff"]:+.3f}, '
                  f'显著性: {factor_info["significance"]}, '
                  f'效应量: {factor_info["effect_size"]}, '
                  f'IC: {factor_info["ic"]:.3f}')
    
    return factor_analysis, all_factors_df

def analyze_factor_distribution(df):
    """分析因子分布合理性"""
    print(f'\n📊 因子分布合理性分析')
    print('=' * 50)
    
    # 分析买入信号的分布
    print(f'🎯 买入信号分布分析:')
    
    factor_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                     'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    for factor in factor_columns:
        if factor in df.columns:
            values = df[factor].dropna()
            if len(values) > 0:
                # 计算分布统计
                mean_val = values.mean()
                median_val = values.median()
                std_val = values.std()
                min_val = values.min()
                max_val = values.max()
                
                # 计算分位数
                q25 = values.quantile(0.25)
                q75 = values.quantile(0.75)
                
                # 检查分布是否合理
                distribution_issues = []
                
                # 检查是否过度集中在高分区
                high_score_ratio = (values > 0.8).mean()
                if high_score_ratio > 0.8:
                    distribution_issues.append(f'过度集中在高分区({high_score_ratio:.1%})')
                
                # 检查是否过度集中在低分区
                low_score_ratio = (values < 0.2).mean()
                if low_score_ratio > 0.8:
                    distribution_issues.append(f'过度集中在低分区({low_score_ratio:.1%})')
                
                # 检查标准差是否过小
                if std_val < 0.05:
                    distribution_issues.append(f'标准差过小({std_val:.3f})')
                
                # 检查是否有异常的最值
                if max_val > 1.0 or min_val < 0.0:
                    distribution_issues.append(f'超出正常范围[{min_val:.3f}, {max_val:.3f}]')
                
                status = "⚠️" if distribution_issues else "✅"
                
                print(f'   {factor}: 均值{mean_val:.3f}, 中位{median_val:.3f}, 标准差{std_val:.3f} {status}')
                if distribution_issues:
                    for issue in distribution_issues:
                        print(f'     - {issue}')

def analyze_factor_correlation(df):
    """分析因子相关性"""
    print(f'\n🔗 因子相关性分析')
    print('=' * 50)
    
    factor_columns = ['overall_score', 'technical_score', 'momentum_score', 'volume_score',
                     'volatility_score', 'trend_score', 'buy_signal_strength', 'risk_adjusted_score']
    
    # 计算因子间相关性
    factor_data = df[factor_columns].dropna()
    
    if len(factor_data) > 0:
        correlation_matrix = factor_data.corr()
        
        print(f'📊 因子间相关性矩阵:')
        
        # 找出高相关性的因子对
        high_corr_pairs = []
        
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                factor1 = correlation_matrix.columns[i]
                factor2 = correlation_matrix.columns[j]
                corr_value = correlation_matrix.iloc[i, j]
                
                if abs(corr_value) > 0.7:
                    high_corr_pairs.append((factor1, factor2, corr_value))
        
        if high_corr_pairs:
            print(f'\n⚠️ 高相关性因子对 (|相关系数| > 0.7):')
            for factor1, factor2, corr in sorted(high_corr_pairs, key=lambda x: abs(x[2]), reverse=True):
                print(f'   {factor1} ↔ {factor2}: {corr:.3f}')
                print(f'     建议: 考虑合并或选择其中一个')
        else:
            print(f'✅ 未发现高相关性因子对')
        
        # 分析因子与收益的相关性
        print(f'\n📈 因子与收益相关性:')
        profit_correlations = []
        
        for factor in factor_columns:
            if factor in df.columns:
                factor_values = df[factor].dropna()
                profit_values = df.loc[df[factor].notna(), 'net_profit_pct_sell']
                
                if len(factor_values) > 10:
                    corr, p_value = stats.pearsonr(factor_values, profit_values)
                    profit_correlations.append((factor, corr, p_value))
        
        # 按相关性排序
        profit_correlations.sort(key=lambda x: abs(x[1]), reverse=True)
        
        for factor, corr, p_value in profit_correlations:
            significance = "🔥" if p_value < 0.001 else "🚀" if p_value < 0.01 else "📊" if p_value < 0.05 else "🔹"
            direction = "📈正相关" if corr > 0 else "📉负相关"
            
            print(f'   {factor}: {corr:+.3f} ({direction}) [p={p_value:.3f}] {significance}')

def analyze_threshold_effectiveness(df):
    """分析阈值设置有效性"""
    print(f'\n🎯 阈值设置有效性分析')
    print('=' * 50)
    
    # 获取当前配置的阈值
    from config import get_config_value
    current_thresholds = get_config_value('MULTIFACTOR_THRESHOLDS', {})
    
    print(f'📋 当前阈值设置:')
    for key, value in current_thresholds.items():
        print(f'   {key}: {value}')
    
    # 分析每个阈值的有效性
    print(f'\n🔍 阈值有效性分析:')
    
    threshold_mapping = {
        'min_overall_score': 'overall_score',
        'min_technical_score': 'technical_score',
        'min_momentum_score': 'momentum_score',
        'min_volume_score': 'volume_score',
        'min_volatility_score': 'volatility_score',
        'min_trend_score': 'trend_score',
        'min_buy_signal_strength': 'buy_signal_strength',
        'min_risk_adjusted_score': 'risk_adjusted_score'
    }
    
    for threshold_key, factor_name in threshold_mapping.items():
        if threshold_key in current_thresholds and factor_name in df.columns:
            threshold_value = current_thresholds[threshold_key]
            factor_values = df[factor_name].dropna()
            
            if len(factor_values) > 0:
                # 分析满足阈值的交易表现
                above_threshold = df[df[factor_name] >= threshold_value]
                below_threshold = df[df[factor_name] < threshold_value]
                
                if len(above_threshold) > 0 and len(below_threshold) > 0:
                    above_win_rate = (above_threshold['is_profitable']).mean() * 100
                    below_win_rate = (below_threshold['is_profitable']).mean() * 100
                    
                    above_avg_profit = above_threshold['net_profit_pct_sell'].mean()
                    below_avg_profit = below_threshold['net_profit_pct_sell'].mean()
                    
                    improvement = above_win_rate - below_win_rate
                    
                    effectiveness = "🔥高效" if improvement > 10 else "📊有效" if improvement > 5 else "🔹一般" if improvement > 0 else "❌无效"
                    
                    print(f'   {factor_name} (阈值{threshold_value}):')
                    print(f'     满足阈值: {len(above_threshold)}笔, 胜率{above_win_rate:.1f}%, 平均{above_avg_profit:.2f}%')
                    print(f'     低于阈值: {len(below_threshold)}笔, 胜率{below_win_rate:.1f}%, 平均{below_avg_profit:.2f}%')
                    print(f'     胜率提升: {improvement:+.1f}% {effectiveness}')
                    
                    # 建议优化阈值
                    if improvement < 5:
                        # 寻找更好的阈值
                        best_threshold = threshold_value
                        best_improvement = improvement
                        
                        test_thresholds = np.percentile(factor_values, [10, 20, 30, 40, 50, 60, 70, 80, 90])
                        
                        for test_threshold in test_thresholds:
                            test_above = df[df[factor_name] >= test_threshold]
                            test_below = df[df[factor_name] < test_threshold]
                            
                            if len(test_above) > 50 and len(test_below) > 50:  # 确保样本量足够
                                test_above_rate = (test_above['is_profitable']).mean() * 100
                                test_below_rate = (test_below['is_profitable']).mean() * 100
                                test_improvement = test_above_rate - test_below_rate
                                
                                if test_improvement > best_improvement:
                                    best_threshold = test_threshold
                                    best_improvement = test_improvement
                        
                        if best_threshold != threshold_value:
                            print(f'     💡 建议阈值: {best_threshold:.3f} (预期胜率提升{best_improvement:+.1f}%)')

def generate_factor_optimization_recommendations(factor_analysis, all_factors_df):
    """生成因子优化建议"""
    print(f'\n💡 因子优化建议')
    print('=' * 50)
    
    if len(all_factors_df) == 0:
        print('⚠️ 无足够数据生成建议')
        return
    
    print(f'🎯 基于统计分析的优化建议:')
    
    # 1. 最有效因子
    most_effective = all_factors_df.head(3)
    print(f'\n1. 🏆 重点关注最有效因子:')
    for i, (_, factor) in enumerate(most_effective.iterrows(), 1):
        print(f'   {i}. {factor["factor"]}: IC={factor["ic"]:.3f}, 效应量={factor["effect_size"]}')
        print(f'      建议: 提高该因子在综合评分中的权重')
    
    # 2. 无效因子
    ineffective = all_factors_df[all_factors_df['p_value'] > 0.05]
    if len(ineffective) > 0:
        print(f'\n2. ❌ 考虑移除无效因子:')
        for _, factor in ineffective.iterrows():
            print(f'   {factor["factor"]}: p值={factor["p_value"]:.3f} (不显著)')
            print(f'      建议: 考虑从策略中移除或降低权重')
    
    # 3. 方向性建议
    positive_factors = all_factors_df[all_factors_df['diff'] > 0]
    negative_factors = all_factors_df[all_factors_df['diff'] < 0]
    
    if len(positive_factors) > 0:
        print(f'\n3. 📈 正向因子 (值越高越好):')
        for _, factor in positive_factors.head(5).iterrows():
            print(f'   {factor["factor"]}: 盈利交易平均高{factor["diff"]:.3f}')
    
    if len(negative_factors) > 0:
        print(f'\n4. 📉 负向因子 (值越低越好):')
        for _, factor in negative_factors.head(5).iterrows():
            print(f'   {factor["factor"]}: 盈利交易平均低{abs(factor["diff"]):.3f}')
    
    # 5. 综合建议
    print(f'\n5. 🔧 综合优化建议:')
    
    # 基于IC值给出权重建议
    significant_factors = all_factors_df[all_factors_df['p_value'] < 0.05]
    if len(significant_factors) > 0:
        total_ic = significant_factors['ic'].abs().sum()
        
        print(f'   建议的因子权重分配 (基于IC值):')
        for _, factor in significant_factors.iterrows():
            weight = abs(factor['ic']) / total_ic
            print(f'     {factor["factor"]}: {weight:.1%}')
    
    print(f'\n6. 🎯 策略改进方向:')
    print(f'   - 重新评估因子权重分配')
    print(f'   - 优化阈值设置')
    print(f'   - 考虑因子组合效应')
    print(f'   - 增加因子有效性监控')

def main():
    """主函数"""
    print('🚀 深度因子指标分析')
    print('=' * 60)
    
    # 分析因子有效性
    df = analyze_factor_effectiveness()
    
    if df is not None:
        # 因子区分度分析
        factor_analysis, all_factors_df = analyze_factor_discrimination(df)
        
        # 因子分布分析
        analyze_factor_distribution(df)
        
        # 因子相关性分析
        analyze_factor_correlation(df)
        
        # 阈值有效性分析
        analyze_threshold_effectiveness(df)
        
        # 生成优化建议
        generate_factor_optimization_recommendations(factor_analysis, all_factors_df)
        
        print(f'\n🎯 分析总结')
        print('=' * 40)
        print('✅ 深度因子分析完成')
        print('📊 已识别最有效和无效因子')
        print('🔧 已生成因子优化建议')
        print('')
        print('💡 请根据分析结果优化因子权重和阈值')
    else:
        print('❌ 无法获取交易数据')

if __name__ == '__main__':
    main()
