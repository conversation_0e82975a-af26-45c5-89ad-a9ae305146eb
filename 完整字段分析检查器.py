# coding=utf-8
"""
完整字段分析检查器
检查数据库中的所有字段是否都被应用到胜率分析中
"""

import sqlite3
import pandas as pd
import numpy as np

def check_all_database_fields():
    """检查数据库中的所有字段"""
    print('🔍 完整字段分析检查器')
    print('=' * 80)
    
    try:
        conn = sqlite3.connect('data/trades.db')
        
        # 1. 获取表结构
        structure_query = "PRAGMA table_info(trades);"
        structure = pd.read_sql_query(structure_query, conn)
        
        print(f'📊 trades表字段总数: {len(structure)}')
        print('\n📋 所有字段列表:')
        print('-' * 60)
        
        all_fields = []
        for i, row in structure.iterrows():
            field_name = row['name']
            field_type = row['type']
            all_fields.append(field_name)
            print(f'{i+1:3d}. {field_name:35} {field_type:15}')
        
        # 2. 检查数据样本
        print(f'\n📈 检查字段数据完整性:')
        print('-' * 60)
        
        # 查询买入记录
        buy_query = "SELECT * FROM trades WHERE action = 'BUY' LIMIT 1000"
        buy_data = pd.read_sql_query(buy_query, conn)
        
        # 分析每个字段的数据情况
        field_analysis = {}
        
        for field in all_fields:
            if field in buy_data.columns:
                non_null_count = buy_data[field].notna().sum()
                total_count = len(buy_data)
                completeness = non_null_count / total_count * 100
                
                # 检查数据类型
                if buy_data[field].dtype in ['int64', 'float64']:
                    data_type = 'numeric'
                    unique_values = buy_data[field].nunique()
                    min_val = buy_data[field].min() if non_null_count > 0 else None
                    max_val = buy_data[field].max() if non_null_count > 0 else None
                else:
                    data_type = 'text'
                    unique_values = buy_data[field].nunique()
                    min_val = None
                    max_val = None
                
                field_analysis[field] = {
                    'completeness': completeness,
                    'non_null_count': non_null_count,
                    'data_type': data_type,
                    'unique_values': unique_values,
                    'min_val': min_val,
                    'max_val': max_val
                }
        
        # 3. 按数据类型分类显示
        print(f'\n📊 字段分类分析:')
        print('-' * 60)
        
        numeric_fields = []
        text_fields = []
        empty_fields = []
        
        for field, analysis in field_analysis.items():
            if analysis['completeness'] < 1:
                empty_fields.append(field)
            elif analysis['data_type'] == 'numeric':
                numeric_fields.append(field)
            else:
                text_fields.append(field)
        
        print(f'🔢 数值型字段 ({len(numeric_fields)}个):')
        for field in numeric_fields:
            analysis = field_analysis[field]
            print(f'  {field:35} 完整度: {analysis["completeness"]:5.1f}% 范围: [{analysis["min_val"]:.3f}, {analysis["max_val"]:.3f}]' if analysis["min_val"] is not None else f'  {field:35} 完整度: {analysis["completeness"]:5.1f}%')
        
        print(f'\n📝 文本型字段 ({len(text_fields)}个):')
        for field in text_fields:
            analysis = field_analysis[field]
            print(f'  {field:35} 完整度: {analysis["completeness"]:5.1f}% 唯一值: {analysis["unique_values"]}')
        
        print(f'\n❌ 空字段 ({len(empty_fields)}个):')
        for field in empty_fields:
            print(f'  {field:35} 完整度: {field_analysis[field]["completeness"]:5.1f}%')
        
        # 4. 检查哪些字段可能被遗漏
        print(f'\n🔍 分析遗漏字段:')
        print('-' * 60)
        
        # 定义已知的分析字段类别
        analyzed_categories = {
            '基础信息': ['symbol', 'timestamp', 'action', 'price', 'volume'],
            '技术指标': ['rsi_3d', 'rsi_5d', 'rsi_10d', 'rsi_20d', 'macd_12_26', 'macd_signal_9', 
                       'macd_histogram', 'ma5', 'ma10', 'ma20', 'ma60', 'ma120', 'kdj_k', 'kdj_d', 'kdj_j'],
            '波动率指标': ['atr_3d', 'atr_5d', 'atr_10d', 'atr_normalized', 'volatility_3d', 'volatility_5d', 
                        'volatility_10d', 'volatility_20d'],
            '布林带指标': ['bb_upper_20', 'bb_middle_20', 'bb_lower_20', 'bb_width_20', 'bb_position_20'],
            '高级技术': ['adx_14', 'adx_slope', 'cci_14', 'williams_r', 'stoch_k', 'stoch_d'],
            '价格动量': ['price_change_pct', 'price_momentum_3d', 'price_momentum_5d', 'price_momentum_10d'],
            '均线距离': ['ma5_distance_pct', 'ma10_distance_pct', 'ma20_distance_pct'],
            '时间因子': ['day_of_month', 'day_of_week', 'month_of_year', 'hour_of_day'],
            '成交量': ['volume_ma5_ratio', 'volume_ma10_ratio', 'relative_volume'],
            '增强因子': ['enhanced_overall_score', 'buy_confidence_score', 'market_environment_score']
        }
        
        # 收集所有已分析的字段
        analyzed_fields = set()
        for category_fields in analyzed_categories.values():
            analyzed_fields.update(category_fields)
        
        # 找出可能遗漏的数值型字段
        missing_numeric_fields = []
        for field in numeric_fields:
            if field not in analyzed_fields and field_analysis[field]['completeness'] > 50:
                missing_numeric_fields.append(field)
        
        if missing_numeric_fields:
            print(f'⚠️ 可能遗漏的数值型字段 ({len(missing_numeric_fields)}个):')
            for field in missing_numeric_fields:
                analysis = field_analysis[field]
                print(f'  {field:35} 完整度: {analysis["completeness"]:5.1f}% 唯一值: {analysis["unique_values"]}')
        else:
            print('✅ 所有有效的数值型字段都已被分析')
        
        # 5. 生成补充分析建议
        print(f'\n💡 补充分析建议:')
        print('-' * 60)
        
        if missing_numeric_fields:
            print('建议将以下字段加入胜率分析:')
            for field in missing_numeric_fields[:10]:  # 只显示前10个
                analysis = field_analysis[field]
                if analysis['completeness'] > 80:
                    print(f'  🔥 {field} (完整度: {analysis["completeness"]:.1f}%)')
                elif analysis['completeness'] > 50:
                    print(f'  📊 {field} (完整度: {analysis["completeness"]:.1f}%)')
        
        # 6. 保存完整字段列表
        timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
        filename = f'完整字段分析_{timestamp}.txt'
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('完整字段分析报告\n')
            f.write('=' * 50 + '\n\n')
            f.write(f'分析时间: {pd.Timestamp.now()}\n')
            f.write(f'总字段数: {len(all_fields)}\n')
            f.write(f'数值型字段: {len(numeric_fields)}\n')
            f.write(f'文本型字段: {len(text_fields)}\n')
            f.write(f'空字段: {len(empty_fields)}\n\n')
            
            f.write('所有字段列表:\n')
            f.write('-' * 30 + '\n')
            for i, field in enumerate(all_fields, 1):
                if field in field_analysis:
                    analysis = field_analysis[field]
                    f.write(f'{i:3d}. {field:35} {analysis["data_type"]:8} 完整度: {analysis["completeness"]:5.1f}%\n')
                else:
                    f.write(f'{i:3d}. {field:35} unknown\n')
            
            if missing_numeric_fields:
                f.write('\n可能遗漏的字段:\n')
                f.write('-' * 20 + '\n')
                for field in missing_numeric_fields:
                    analysis = field_analysis[field]
                    f.write(f'{field:35} 完整度: {analysis["completeness"]:5.1f}%\n')
        
        print(f'\n✅ 详细报告已保存: {filename}')
        
        conn.close()
        
        return {
            'total_fields': len(all_fields),
            'numeric_fields': numeric_fields,
            'text_fields': text_fields,
            'empty_fields': empty_fields,
            'missing_fields': missing_numeric_fields,
            'field_analysis': field_analysis
        }
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print('🔍 完整字段分析检查器')
    print('=' * 80)
    
    results = check_all_database_fields()
    
    if results:
        print(f'\n📊 检查完成!')
        print(f'总字段数: {results["total_fields"]}')
        print(f'数值型字段: {len(results["numeric_fields"])}')
        print(f'可能遗漏字段: {len(results["missing_fields"])}')
        
        if results["missing_fields"]:
            print(f'\n⚠️ 发现 {len(results["missing_fields"])} 个可能遗漏的字段')
            print('建议运行补充分析以获得更全面的结果')
        else:
            print('\n✅ 所有有效字段都已被分析')
    else:
        print('❌ 检查失败')

if __name__ == "__main__":
    main()
