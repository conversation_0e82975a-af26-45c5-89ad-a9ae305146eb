#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import logging
import json
from datetime import datetime, timedelta
import shutil
import sys
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("db_advanced_tools.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 统一定义数据路径常量
DATA_DIR = 'data'
BACKUP_DIR = 'backups'
REPORT_DIR = 'reports/db_analysis'

# 确保目录存在
for directory in [DATA_DIR, BACKUP_DIR, REPORT_DIR]:
    os.makedirs(directory, exist_ok=True)

# 定义文件路径
DB_FILE = os.path.join(DATA_DIR, 'trades.db')
TRADE_LOG_FILE = os.path.join(DATA_DIR, 'trade_log.csv')
ANALYSIS_LOG_FILE = os.path.join(DATA_DIR, 'analysis_log.csv')

def check_db_exists():
    """检查数据库文件是否存在"""
    if not os.path.exists(DB_FILE):
        logger.warning(f"数据库文件不存在: {DB_FILE}")
        return False
    return True

def analyze_data_distribution(table_name, columns=None, output_dir=None):
    """分析数据分布并生成图表
    
    Args:
        table_name: 表名
        columns: 要分析的列名列表（为None时分析所有数值列）
        output_dir: 输出目录（默认为REPORT_DIR）
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        
        # 获取表数据
        df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        conn.close()
        
        if df.empty:
            logger.warning(f"表 {table_name} 没有数据")
            return False
        
        if output_dir is None:
            output_dir = os.path.join(REPORT_DIR, f"{table_name}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 如果未指定列，则自动选择数值类型列
        if columns is None:
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            columns = numeric_cols
        else:
            # 确保所选列存在
            columns = [col for col in columns if col in df.columns]
        
        if not columns:
            logger.warning(f"表 {table_name} 中没有可分析的数值列")
            return False
        
        # 生成汇总统计信息
        stats = df[columns].describe().T
        stats.to_csv(os.path.join(output_dir, "statistics.csv"))
        
        # 创建分布图
        plt.figure(figsize=(12, 8))
        for i, col in enumerate(columns):
            plt.subplot(len(columns), 1, i+1)
            sns.histplot(df[col].dropna(), kde=True)
            plt.title(f"{col} 分布")
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "distributions.png"))
        
        # 如果数据包含时间戳，创建时间序列图
        if 'timestamp' in df.columns:
            try:
                df['date'] = pd.to_datetime(df['timestamp'])
                
                # 按日期汇总数据
                for col in columns:
                    if col != 'date':
                        plt.figure(figsize=(12, 6))
                        daily_stats = df.groupby(df['date'].dt.date)[col].mean()
                        daily_stats.plot()
                        plt.title(f"{col} 时间趋势")
                        plt.savefig(os.path.join(output_dir, f"{col}_trend.png"))
            except Exception as e:
                logger.warning(f"生成时间序列图失败: {str(e)}")
        
        # 创建相关性矩阵
        plt.figure(figsize=(10, 8))
        corr = df[columns].corr()
        sns.heatmap(corr, annot=True, cmap='coolwarm')
        plt.title("相关性矩阵")
        plt.savefig(os.path.join(output_dir, "correlation.png"))
        
        logger.info(f"数据分析完成，结果保存在 {output_dir}")
        return output_dir
    except Exception as e:
        logger.error(f"分析数据分布失败: {str(e)}")
        return False

def fix_missing_data(table_name, column, strategy='mean'):
    """修复表中的缺失数据
    
    Args:
        table_name: 表名
        column: 列名
        strategy: 修复策略 (mean, median, mode, zero, previous)
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        
        # 获取表数据
        df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        
        if column not in df.columns:
            logger.warning(f"列 {column} 不存在于表 {table_name} 中")
            conn.close()
            return False
        
        # 获取缺失值数量
        missing_count = df[column].isnull().sum()
        
        if missing_count == 0:
            logger.info(f"表 {table_name} 中的列 {column} 没有缺失值")
            conn.close()
            return True
        
        # 备份数据
        df_backup = df.copy()
        
        # 应用修复策略
        if strategy == 'mean':
            fill_value = df[column].mean()
            df[column].fillna(fill_value, inplace=True)
        elif strategy == 'median':
            fill_value = df[column].median()
            df[column].fillna(fill_value, inplace=True)
        elif strategy == 'mode':
            fill_value = df[column].mode()[0]
            df[column].fillna(fill_value, inplace=True)
        elif strategy == 'zero':
            df[column].fillna(0, inplace=True)
        elif strategy == 'previous':
            df[column].fillna(method='ffill', inplace=True)
            # 处理开头的NaN值
            if df[column].isnull().any():
                df[column].fillna(df[column].mean(), inplace=True)
        else:
            logger.warning(f"不支持的修复策略: {strategy}")
            conn.close()
            return False
        
        # 更新数据库
        # 删除现有数据
        cursor = conn.cursor()
        cursor.execute(f"DELETE FROM {table_name}")
        
        # 插入修复后的数据
        df.to_sql(table_name, conn, if_exists='append', index=False)
        
        conn.commit()
        conn.close()
        
        logger.info(f"已修复表 {table_name} 中列 {column} 的 {missing_count} 个缺失值，使用策略: {strategy}")
        return True
    except Exception as e:
        logger.error(f"修复缺失数据失败: {str(e)}")
        return False

def batch_process_data(table_name, operation, condition=None, params=None):
    """批量处理数据
    
    Args:
        table_name: 表名
        operation: 操作类型 ('update', 'delete')
        condition: SQL WHERE 条件
        params: 操作参数 (对于'update'操作，需要提供{列名: 新值}的字典)
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        if not cursor.fetchone():
            logger.warning(f"表不存在: {table_name}")
            conn.close()
            return False
        
        # 获取操作前的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}" + (f" WHERE {condition}" if condition else ""))
        before_count = cursor.fetchone()[0]
        
        if before_count == 0:
            logger.warning(f"没有符合条件的记录: {condition}")
            conn.close()
            return False
        
        # 执行操作
        if operation == 'delete':
            sql = f"DELETE FROM {table_name}" + (f" WHERE {condition}" if condition else "")
            cursor.execute(sql)
        elif operation == 'update':
            if not params:
                logger.warning("更新操作需要提供参数")
                conn.close()
                return False
            
            set_clause = ", ".join([f"{key} = ?" for key in params])
            values = list(params.values())
            
            sql = f"UPDATE {table_name} SET {set_clause}" + (f" WHERE {condition}" if condition else "")
            cursor.execute(sql, values)
        else:
            logger.warning(f"不支持的操作: {operation}")
            conn.close()
            return False
        
        # 获取操作后的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        after_count = cursor.fetchone()[0]
        
        conn.commit()
        conn.close()
        
        affected_rows = before_count - after_count if operation == 'delete' else cursor.rowcount
        logger.info(f"{operation.capitalize()}操作成功，影响了 {affected_rows} 行记录")
        return affected_rows
    except Exception as e:
        logger.error(f"批量处理数据失败: {str(e)}")
        return False

def merge_databases(target_db, source_db, strategy='replace'):
    """合并两个数据库
    
    Args:
        target_db: 目标数据库路径
        source_db: 源数据库路径
        strategy: 合并策略 ('replace': 覆盖重复记录, 'append': 添加所有记录)
    """
    if not os.path.exists(source_db):
        logger.warning(f"源数据库不存在: {source_db}")
        return False
    
    try:
        # 连接源数据库
        source_conn = sqlite3.connect(source_db)
        source_cursor = source_conn.cursor()
        
        # 获取源数据库中的所有表
        source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in source_cursor.fetchall()]
        
        if not tables:
            logger.warning(f"源数据库中没有表")
            source_conn.close()
            return False
        
        # 如果目标数据库不存在，则复制源数据库
        if not os.path.exists(target_db):
            shutil.copy2(source_db, target_db)
            logger.info(f"源数据库已复制到: {target_db}")
            source_conn.close()
            return True
        
        # 连接目标数据库
        target_conn = sqlite3.connect(target_db)
        target_cursor = target_conn.cursor()
        
        # 获取每个表的主键信息
        primary_keys = {}
        for table in tables:
            source_cursor.execute(f"PRAGMA table_info({table})")
            columns = source_cursor.fetchall()
            pk_columns = [column[1] for column in columns if column[5] > 0]  # 第5个元素是pk标志
            primary_keys[table] = pk_columns if pk_columns else ['id']
        
        # 处理每个表
        for table in tables:
            # 检查目标数据库中是否存在该表
            target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not target_cursor.fetchone():
                # 如果表不存在，创建表
                source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                create_sql = source_cursor.fetchone()[0]
                target_cursor.execute(create_sql)
                logger.info(f"在目标数据库中创建表: {table}")
            
            # 获取源表中的所有数据
            source_data = pd.read_sql_query(f"SELECT * FROM {table}", source_conn)
            
            if source_data.empty:
                logger.info(f"表 {table} 中没有数据")
                continue
            
            if strategy == 'replace':
                # 获取主键列
                pk_cols = primary_keys[table]
                
                # 获取目标表中已有的主键值
                pk_conditions = " AND ".join([f"{col} IS NOT NULL" for col in pk_cols])
                target_pks = pd.read_sql_query(f"SELECT {', '.join(pk_cols)} FROM {table} WHERE {pk_conditions}", target_conn)
                
                if not target_pks.empty:
                    # 遍历源数据，检查是否有重复记录
                    for _, row in source_data.iterrows():
                        # 构建查询条件
                        conditions = []
                        values = []
                        for pk in pk_cols:
                            if pd.isna(row[pk]):
                                continue
                            conditions.append(f"{pk} = ?")
                            values.append(row[pk])
                        
                        if not conditions:
                            continue
                        
                        # 检查记录是否存在
                        check_sql = f"SELECT COUNT(*) FROM {table} WHERE {' AND '.join(conditions)}"
                        target_cursor.execute(check_sql, values)
                        exists = target_cursor.fetchone()[0] > 0
                        
                        if exists:
                            # 更新记录
                            set_clauses = []
                            update_values = []
                            for col in row.index:
                                if col not in pk_cols:
                                    set_clauses.append(f"{col} = ?")
                                    update_values.append(row[col])
                            
                            if set_clauses:
                                update_sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {' AND '.join(conditions)}"
                                target_cursor.execute(update_sql, update_values + values)
                        else:
                            # 插入记录
                            cols = ", ".join(row.index)
                            placeholders = ", ".join(["?"] * len(row))
                            insert_sql = f"INSERT INTO {table} ({cols}) VALUES ({placeholders})"
                            target_cursor.execute(insert_sql, row.tolist())
                else:
                    # 如果目标表为空，直接插入所有数据
                    source_data.to_sql(table, target_conn, if_exists='append', index=False)
            elif strategy == 'append':
                # 直接附加所有记录
                source_data.to_sql(table, target_conn, if_exists='append', index=False)
            else:
                logger.warning(f"不支持的合并策略: {strategy}")
                source_conn.close()
                target_conn.close()
                return False
        
        target_conn.commit()
        source_conn.close()
        target_conn.close()
        
        logger.info(f"数据库合并完成，策略: {strategy}")
        return True
    except Exception as e:
        logger.error(f"合并数据库失败: {str(e)}")
        return False

def generate_db_report(output_file=None):
    """生成数据库完整报告
    
    Args:
        output_file: 输出文件路径
    """
    if not check_db_exists():
        return False
    
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        # 创建报告数据结构
        report = {
            "database_info": {
                "path": os.path.abspath(DB_FILE),
                "size_mb": os.path.getsize(DB_FILE) / (1024 * 1024),
                "created_time": datetime.fromtimestamp(os.path.getctime(DB_FILE)).strftime('%Y-%m-%d %H:%M:%S'),
                "modified_time": datetime.fromtimestamp(os.path.getmtime(DB_FILE)).strftime('%Y-%m-%d %H:%M:%S')
            },
            "tables": {}
        }
        
        # 收集每个表的信息
        for table in tables:
            # 获取表的列信息
            cursor.execute(f"PRAGMA table_info({table})")
            columns = []
            for column in cursor.fetchall():
                columns.append({
                    "name": column[1],
                    "type": column[2],
                    "notnull": column[3] == 1,
                    "default_value": column[4],
                    "primary_key": column[5] == 1
                })
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            record_count = cursor.fetchone()[0]
            
            # 获取表大小（估计值）
            try:
                # 获取表的所有列名
                cursor.execute(f"PRAGMA table_info({table})")
                columns_info = cursor.fetchall()
                column_names = [column[1] for column in columns_info]
                
                # 构建一个查询，计算每列的长度并求和
                length_expressions = []
                for col in column_names:
                    length_expressions.append(f"LENGTH(CAST({col} AS TEXT))")
                
                if length_expressions:
                    length_sum_expr = " + ".join(length_expressions)
                    cursor.execute(f"SELECT SUM({length_sum_expr}) FROM (SELECT * FROM {table} LIMIT 100) as t")
                    row_size_estimate = cursor.fetchone()[0] or 0
                else:
                    row_size_estimate = 0
            except Exception as e:
                logger.warning(f"估计表大小时出错: {str(e)}")
                # 如果上述查询失败，使用另一种方法估计
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                if count > 0:
                    # 获取一行数据的大小
                    cursor.execute(f"SELECT * FROM {table} LIMIT 1")
                    row = cursor.fetchone()
                    if row:
                        # 估算每行平均字节数
                        row_size_estimate = sum(len(str(item)) for item in row) * min(count, 100)
                    else:
                        row_size_estimate = 0
                else:
                    row_size_estimate = 0
                
            if row_size_estimate and record_count > 0:
                table_size_estimate = (row_size_estimate / min(100, record_count)) * record_count / 1024  # KB
            else:
                table_size_estimate = 0
            
            # 获取数据范围（如果有timestamp列）
            time_range = {}
            if any(col["name"] == "timestamp" for col in columns):
                cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                min_time, max_time = cursor.fetchone()
                if min_time and max_time:
                    time_range = {
                        "earliest": min_time,
                        "latest": max_time,
                        "date_range": (datetime.strptime(max_time[:10], '%Y-%m-%d') - 
                                      datetime.strptime(min_time[:10], '%Y-%m-%d')).days
                    }
            
            # 收集数据质量信息
            data_quality = {
                "missing_values": {},
                "duplicates": 0
            }
            
            for col in columns:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {col['name']} IS NULL")
                null_count = cursor.fetchone()[0]
                if null_count > 0:
                    data_quality["missing_values"][col["name"]] = null_count
            
            # 检查主键列的重复值
            pk_columns = [col["name"] for col in columns if col["primary_key"]]
            if pk_columns:
                pk_cols_str = ", ".join(pk_columns)
                cursor.execute(f"""
                    SELECT COUNT(*) - COUNT(DISTINCT {pk_cols_str})
                    FROM {table}
                    WHERE {' AND '.join([f"{col} IS NOT NULL" for col in pk_columns])}
                """)
                data_quality["duplicates"] = cursor.fetchone()[0]
            
            report["tables"][table] = {
                "columns": columns,
                "record_count": record_count,
                "size_estimate_kb": table_size_estimate,
                "time_range": time_range,
                "data_quality": data_quality
            }
        
        conn.close()
        
        # 设置默认输出文件路径
        if not output_file:
            output_file = os.path.join(REPORT_DIR, f"db_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 写入报告
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据库报告已生成: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"生成数据库报告失败: {str(e)}")
        return False

def interactive_mode():
    """交互式模式"""
    try:
        import colorama
        from colorama import Fore, Style
        colorama.init()
        has_color = True
    except ImportError:
        has_color = False
        print("提示: 安装 colorama 包可以获得彩色输出体验 (pip install colorama)")
    
    def print_header(text):
        if has_color:
            print(f"\n{Fore.CYAN}{Style.BRIGHT}{text}{Style.RESET_ALL}")
        else:
            print(f"\n=== {text} ===")
    
    def print_option(key, description):
        if has_color:
            print(f"{Fore.GREEN}{key}{Style.RESET_ALL}: {description}")
        else:
            print(f"{key}: {description}")
    
    def print_success(text):
        if has_color:
            print(f"{Fore.GREEN}{text}{Style.RESET_ALL}")
        else:
            print(f"成功: {text}")
    
    def print_error(text):
        if has_color:
            print(f"{Fore.RED}{text}{Style.RESET_ALL}")
        else:
            print(f"错误: {text}")
    
    def print_warning(text):
        if has_color:
            print(f"{Fore.YELLOW}{text}{Style.RESET_ALL}")
        else:
            print(f"警告: {text}")
    
    def print_menu():
        print_header("万和策略分析系统 - 高级数据库管理工具")
        print("请选择要执行的操作:")
        print_option("1", "数据库信息")
        print_option("2", "数据分析")
        print_option("3", "修复缺失数据")
        print_option("4", "批量处理数据")
        print_option("5", "合并数据库")
        print_option("6", "生成数据库报告")
        print_option("7", "备份数据库")
        print_option("8", "优化数据库")
        print_option("0", "退出")
        print()
    
    def get_tables():
        """获取数据库中的所有表"""
        if not check_db_exists():
            return []
        
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            conn.close()
            return tables
        except Exception as e:
            print_error(f"获取表列表失败: {str(e)}")
            return []
    
    def get_columns(table_name):
        """获取指定表的所有列"""
        if not check_db_exists():
            return []
        
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [column[1] for column in cursor.fetchall()]
            conn.close()
            return columns
        except Exception as e:
            print_error(f"获取列列表失败: {str(e)}")
            return []
    
    def handle_db_info():
        """处理数据库信息查询"""
        print_header("数据库信息")
        
        if not check_db_exists():
            print_error("数据库文件不存在")
            return
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        # 获取数据库文件大小
        db_size = os.path.getsize(DB_FILE) / (1024 * 1024)  # MB
        
        print(f"数据库路径: {os.path.abspath(DB_FILE)}")
        print(f"数据库大小: {db_size:.2f} MB")
        print(f"表数量: {len(tables)}")
        
        print("\n表信息:")
        for table in tables:
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            
            # 获取列数
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # 获取时间范围
            time_range = ""
            if any(column[1] == "timestamp" for column in columns):
                try:
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                    min_time, max_time = cursor.fetchone()
                    if min_time and max_time:
                        time_range = f" (时间范围: {min_time} 至 {max_time})"
                except:
                    pass
            
            print(f"  - {table}: {count} 条记录, {len(columns)} 列{time_range}")
        
        conn.close()
        input("\n按回车键继续...")
    
    def handle_data_analysis():
        """处理数据分析"""
        print_header("数据分析")
        
        tables = get_tables()
        if not tables:
            print_error("数据库中没有表")
            return
        
        print("选择要分析的表:")
        for i, table in enumerate(tables):
            print_option(f"{i+1}", table)
        
        try:
            table_idx = int(input("\n请输入表编号: ")) - 1
            if table_idx < 0 or table_idx >= len(tables):
                print_error("无效的表编号")
                return
            
            table_name = tables[table_idx]
            columns = get_columns(table_name)
            
            print(f"\n选择要分析的列 (多列用逗号分隔，留空分析所有数值列):")
            for i, column in enumerate(columns):
                print_option(f"{i+1}", column)
            
            column_input = input("\n请输入列编号 (例如: 1,3,5): ")
            
            if column_input.strip():
                try:
                    column_indices = [int(idx.strip()) - 1 for idx in column_input.split(",")]
                    selected_columns = [columns[idx] for idx in column_indices if 0 <= idx < len(columns)]
                except:
                    print_error("无效的列编号")
                    return
            else:
                selected_columns = None
            
            print("\n分析中，请稍候...")
            result = analyze_data_distribution(table_name, selected_columns)
            
            if result:
                print_success(f"数据分析完成，结果保存在: {result}")
            else:
                print_error("数据分析失败")
        except ValueError:
            print_error("请输入有效的数字")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    def handle_fix_missing():
        """处理修复缺失数据"""
        print_header("修复缺失数据")
        
        tables = get_tables()
        if not tables:
            print_error("数据库中没有表")
            return
        
        print("选择要修复的表:")
        for i, table in enumerate(tables):
            print_option(f"{i+1}", table)
        
        try:
            table_idx = int(input("\n请输入表编号: ")) - 1
            if table_idx < 0 or table_idx >= len(tables):
                print_error("无效的表编号")
                return
            
            table_name = tables[table_idx]
            columns = get_columns(table_name)
            
            print(f"\n选择要修复的列:")
            for i, column in enumerate(columns):
                print_option(f"{i+1}", column)
            
            column_idx = int(input("\n请输入列编号: ")) - 1
            if column_idx < 0 or column_idx >= len(columns):
                print_error("无效的列编号")
                return
            
            column_name = columns[column_idx]
            
            print("\n选择修复策略:")
            strategies = [
                ("mean", "使用平均值填充"),
                ("median", "使用中位数填充"),
                ("mode", "使用众数填充"),
                ("zero", "使用零填充"),
                ("previous", "使用前一个有效值填充")
            ]
            
            for i, (strategy, desc) in enumerate(strategies):
                print_option(f"{i+1}", f"{strategy} - {desc}")
            
            strategy_idx = int(input("\n请输入策略编号: ")) - 1
            if strategy_idx < 0 or strategy_idx >= len(strategies):
                print_error("无效的策略编号")
                return
            
            strategy = strategies[strategy_idx][0]
            
            print(f"\n将使用 {strategy} 策略修复表 {table_name} 中的列 {column_name}")
            confirm = input("确认操作? (y/n): ").lower()
            
            if confirm == 'y':
                print("\n修复中，请稍候...")
                result = fix_missing_data(table_name, column_name, strategy)
                
                if result:
                    print_success(f"成功修复表 {table_name} 中列 {column_name} 的缺失值")
                else:
                    print_error("修复缺失数据失败")
            else:
                print("操作已取消")
        except ValueError:
            print_error("请输入有效的数字")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    def handle_batch_process():
        """处理批量处理数据"""
        print_header("批量处理数据")
        
        tables = get_tables()
        if not tables:
            print_error("数据库中没有表")
            return
        
        print("选择要处理的表:")
        for i, table in enumerate(tables):
            print_option(f"{i+1}", table)
        
        try:
            table_idx = int(input("\n请输入表编号: ")) - 1
            if table_idx < 0 or table_idx >= len(tables):
                print_error("无效的表编号")
                return
            
            table_name = tables[table_idx]
            
            print("\n选择操作类型:")
            print_option("1", "删除数据")
            print_option("2", "更新数据")
            
            op_idx = int(input("\n请输入操作类型编号: "))
            if op_idx == 1:
                operation = "delete"
                print("\n请输入删除条件 (SQL WHERE 子句，例如: timestamp < '2023-01-01'):")
                condition = input("条件: ")
                params = None
            elif op_idx == 2:
                operation = "update"
                print("\n请输入更新条件 (SQL WHERE 子句，例如: symbol = 'SHSE.600000'):")
                condition = input("条件: ")
                
                print("\n请输入要更新的字段和值 (JSON格式，例如: {\"price\": 10.5, \"volume\": 100}):")
                params_str = input("更新参数: ")
                
                try:
                    params = json.loads(params_str)
                except json.JSONDecodeError:
                    print_error("参数格式无效，必须是有效的JSON")
                    return
            else:
                print_error("无效的操作类型编号")
                return
            
            print(f"\n将在表 {table_name} 上执行 {operation} 操作")
            if condition:
                print(f"条件: {condition}")
            if params:
                print(f"参数: {params}")
            
            confirm = input("确认操作? (y/n): ").lower()
            
            if confirm == 'y':
                print("\n处理中，请稍候...")
                result = batch_process_data(table_name, operation, condition, params)
                
                if result:
                    print_success(f"批量{operation}操作成功，影响了 {result} 行记录")
                else:
                    print_error(f"批量{operation}操作失败")
            else:
                print("操作已取消")
        except ValueError:
            print_error("请输入有效的数字")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    def handle_merge_db():
        """处理合并数据库"""
        print_header("合并数据库")
        
        print("请输入目标数据库路径 (将合并到此数据库):")
        target_db = input("目标数据库: ")
        
        print("\n请输入源数据库路径 (从此数据库合并):")
        source_db = input("源数据库: ")
        
        if not os.path.exists(source_db):
            print_error(f"源数据库不存在: {source_db}")
            input("\n按回车键继续...")
            return
        
        print("\n选择合并策略:")
        print_option("1", "replace - 覆盖重复记录")
        print_option("2", "append - 添加所有记录")
        
        try:
            strategy_idx = int(input("\n请输入策略编号: "))
            if strategy_idx == 1:
                strategy = "replace"
            elif strategy_idx == 2:
                strategy = "append"
            else:
                print_error("无效的策略编号")
                return
            
            print(f"\n将使用 {strategy} 策略将 {source_db} 合并到 {target_db}")
            confirm = input("确认操作? (y/n): ").lower()
            
            if confirm == 'y':
                print("\n合并中，请稍候...")
                result = merge_databases(target_db, source_db, strategy)
                
                if result:
                    print_success(f"数据库合并完成，策略: {strategy}")
                else:
                    print_error("数据库合并失败")
            else:
                print("操作已取消")
        except ValueError:
            print_error("请输入有效的数字")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    def handle_generate_report():
        """处理生成数据库报告"""
        print_header("生成数据库报告")
        
        print("请输入报告输出路径 (留空使用默认路径):")
        report_file = input("报告路径: ")
        
        if not report_file:
            report_file = None
        
        print("\n生成报告中，请稍候...")
        result = generate_db_report(report_file)
        
        if result:
            print_success(f"数据库报告已生成: {result}")
        else:
            print_error("生成数据库报告失败")
        
        input("\n按回车键继续...")
    
    def handle_backup_db():
        """处理备份数据库"""
        print_header("备份数据库")
        
        try:
            from scripts.db_manager import backup_database
            
            print("备份中，请稍候...")
            backup_file = backup_database()
            
            if backup_file:
                print_success(f"数据库已备份到: {backup_file}")
            else:
                print_error("备份数据库失败")
        except ImportError:
            print_error("无法导入备份函数，请确保 db_manager.py 文件存在")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    def handle_optimize_db():
        """处理优化数据库"""
        print_header("优化数据库")
        
        try:
            from scripts.db_manager import optimize_database
            
            print("优化中，请稍候...")
            result = optimize_database()
            
            if result:
                print_success("数据库优化完成")
            else:
                print_error("优化数据库失败")
        except ImportError:
            print_error("无法导入优化函数，请确保 db_manager.py 文件存在")
        except Exception as e:
            print_error(f"发生错误: {str(e)}")
        
        input("\n按回车键继续...")
    
    # 主交互循环
    while True:
        os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
        print_menu()
        
        choice = input("请选择操作 [0-8]: ")
        
        if choice == '0':
            print("退出程序...")
            break
        elif choice == '1':
            handle_db_info()
        elif choice == '2':
            handle_data_analysis()
        elif choice == '3':
            handle_fix_missing()
        elif choice == '4':
            handle_batch_process()
        elif choice == '5':
            handle_merge_db()
        elif choice == '6':
            handle_generate_report()
        elif choice == '7':
            handle_backup_db()
        elif choice == '8':
            handle_optimize_db()
        else:
            print_error("无效的选择")
            input("\n按回车键继续...")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高级数据库管理工具')
    
    # 交互式模式选项
    parser.add_argument('--interactive', '-i', action='store_true', help='启动交互式模式')
    
    # 数据分析选项
    analyze_group = parser.add_argument_group('数据分析')
    analyze_group.add_argument('--analyze', metavar='TABLE', help='分析表数据分布')
    analyze_group.add_argument('--columns', metavar='COLUMNS', help='要分析的列（逗号分隔）')
    analyze_group.add_argument('--output-dir', metavar='DIR', help='分析结果输出目录')
    
    # 数据修复选项
    fix_group = parser.add_argument_group('数据修复')
    fix_group.add_argument('--fix-missing', metavar='TABLE', help='修复表中的缺失数据')
    fix_group.add_argument('--column', metavar='COLUMN', help='要修复的列')
    fix_group.add_argument('--strategy', choices=['mean', 'median', 'mode', 'zero', 'previous'], default='mean',
                         help='修复策略（默认为均值填充）')
    
    # 批量处理选项
    batch_group = parser.add_argument_group('批量处理')
    batch_group.add_argument('--batch', metavar='TABLE', help='批量处理表数据')
    batch_group.add_argument('--operation', choices=['update', 'delete'], help='操作类型')
    batch_group.add_argument('--condition', metavar='CONDITION', help='SQL WHERE 条件')
    batch_group.add_argument('--params', metavar='PARAMS', help='操作参数（JSON格式）')
    
    # 数据库合并选项
    merge_group = parser.add_argument_group('数据库合并')
    merge_group.add_argument('--merge', action='store_true', help='合并数据库')
    merge_group.add_argument('--target-db', metavar='TARGET_DB', help='目标数据库路径')
    merge_group.add_argument('--source-db', metavar='SOURCE_DB', help='源数据库路径')
    merge_group.add_argument('--merge-strategy', choices=['replace', 'append'], default='replace',
                           help='合并策略（默认为覆盖重复记录）')
    
    # 报告生成选项
    report_group = parser.add_argument_group('报告生成')
    report_group.add_argument('--report', action='store_true', help='生成数据库完整报告')
    report_group.add_argument('--report-file', metavar='FILE', help='报告输出文件路径')
    
    args = parser.parse_args()
    
    # 如果指定了交互式模式，则启动交互式界面
    if args.interactive:
        interactive_mode()
        return
    
    # 如果没有参数，显示帮助信息
    if len(sys.argv) == 1:
        parser.print_help()
        return

if __name__ == "__main__":
    main() 